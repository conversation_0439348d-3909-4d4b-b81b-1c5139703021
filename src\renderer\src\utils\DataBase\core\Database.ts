import Dexie from 'dexie'
import { CryptoHelper } from './CryptoHelper'
import DefaultDataBase from './DefaultDataBase.json'
import { TableOperations } from './TableOperations'
import { TableOptions } from './types'

/**
 * Dexie数据库服务类
 */
class DatabaseMain {
  private db: Dexie | null = null
  private static instance: DatabaseMain | null = null
  public static getInstance() {
    if (!this.instance) {
      this.instance = new DatabaseMain()
    }
    return this.instance
  }
  private loadCallback: any = null
  public onload(callback: any) {
    if (this.isLoad) {
      callback()
    } else {
      this.loadCallback = callback
    }
  }
  // 加载默认数据库数据
  private dbList: any = new Map()
  private isLoad: boolean = false
  public async loadDefaultData() {
    this.isLoad = false
    const dbList = Object.keys(DefaultDataBase)
    for (let i = 0; i < dbList.length; i++) {
      const dbName = dbList[i]
      const db = new Dexie(dbName)
      this.dbList.set(dbName, db)
      const database = DefaultDataBase[dbName]
      const tables: any = database.tables
      this.handleTableOptions(db, tables, database.version)
      this.setupMiddleware(db)
      await db.open()
    }
    if (this.loadCallback) {
      this.loadCallback()
    }
    this.isLoad = true
  }
  /**
   * 设置中间件处理加密/解密
   * @param db 数据库实例
   */
  private setupMiddleware(db: Dexie) {
    const tables: any = db.tables
    for (const tableName in tables) {
      const table = tables[tableName]
      table.hook('reading', (obj: any) => {
        const isEncrypted = table._tableParam.isEncrypted || false
        if (!isEncrypted) {
          return obj
        }
        const exclude = table._tableParam.encryptIndexes || []
        const encryptionKey = table._tableParam.encryptKey || ''
        for (const [key, value] of Object.entries(obj)) {
          if (exclude.includes(key as any)) {
            obj[key] = CryptoHelper.decrypt(value as string, encryptionKey)
          }
        }
        return obj
      })
      table.hook('creating', (_primKey: any, obj: any) => {
        const isEncrypted = table._tableParam.isEncrypted || false
        if (!isEncrypted) {
          return obj
        }
        const exclude = table._tableParam.encryptIndexes || []
        const encryptionKey = table._tableParam.encryptKey || ''
        for (const [key, value] of Object.entries(obj)) {
          if (exclude.includes(key as any)) {
            obj[key] = CryptoHelper.encrypt(value as string, encryptionKey)
          }
        }
        return obj
      })
      table.hook('updating', (modifications: any) => {
        const isEncrypted = table._tableParam.isEncrypted || false
        if (!isEncrypted) {
          return modifications
        }
        const exclude = table._tableParam.encryptIndexes || []
        const encryptionKey = table._tableParam.encryptKey || ''
        for (const [key, value] of Object.entries(modifications)) {
          if (exclude.includes(key as any)) {
            modifications[key] = CryptoHelper.encrypt(value as string, encryptionKey)
          }
        }
        return modifications
      })
    }
  }
  private handleTableOptions(db: Dexie, tables: TableOptions[], version: number | undefined) {
    const storesSchema = {}
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i]
      let syntax: string = ''
      for (let j = 0; j < table.indexes?.length; j++) {
        const col = table.indexes[j]
        if (col === table.primaryKey) {
          syntax += `++${col},`
        } else {
          syntax += `${col},`
        }
      }
      storesSchema[table.name] = syntax.slice(0, -1)
    }
    db.version(version || 1).stores(storesSchema)
    for (let i = 0; i < tables.length; i++) {
      const tableName = tables[i].name
      const table: any = db.table(tableName)
      table._tableParam = tables[i]
    }
  }
  private tableList: TableOptions[] = []
  /**
   * 动态添加表
   * @param options 表配置参数
   */
  public dbName(name: string) {
    this.db = this.dbList.get(name)
    return this.db
  }
  public defineTable(options: TableOptions) {
    this.tableList.push(options)
  }
  // public async initDatabase() {
  //   const dbList = this.getCurrentSchema()
  //   console.log(dbList)
  //   // await this.db.open()
  // }
  /**
   * 获取当前数据库的表结构
   */
  // private getCurrentSchema() {
  //   if (!this.db?.verno) return {}
  //   const schema = {}
  //   console.log(this.db, this.db?._dbSchema)
  //   // const version = this.db?._dbSchema.version.find((v) => v.version === this.db?.verno)
  //   // version.stores.forEach((store) => {
  //   //   schema[store.name] = store.schema
  //   // })
  //   return schema
  // }
  // 获取所有表
  public async getTableNames(): Promise<string[] | undefined> {
    return await this.db?.tables.map((table) => table.name)
  }
  // 获取表实例
  public getTable(tableName: string) {
    return this.db?.table(tableName)
  }
  // 获取表操作接口
  public table(tableName: string): TableOperations {
    const table = this.db?.table(tableName)
    return new TableOperations(table)
  }
  // 事务的封装
  public async transaction<T>(
    tables: any[],
    mode: 'readonly' | 'readwrite',
    operation: (tables: any[]) => Promise<T>,
  ): Promise<T | undefined> {
    return this.db?.transaction(mode, tables as string[], async () => {
      const txTables = Object.fromEntries(tables.map((name) => [name, this.table(name)]))
      return operation(txTables)
    })
  }
  // 关闭数据库
  public close() {
    this.db?.close()
  }
}
export const database = DatabaseMain.getInstance()
