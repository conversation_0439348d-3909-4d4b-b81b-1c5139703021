<template>
  <div class="p-4 rounded-lg bg-muted">
    <Label class="block mb-2 text-sm font-medium text-foreground dark:text-muted-foreground">
      预测圈数
    </Label>
    <div class="flex space-x-4">
      <Input v-model="cycleValue" type="number" :disabled="disabled" class="flex-1" />
      <Button :disabled="disabled || !cycleValue" variant="default" @click="onStartPrediction">
        开始预测
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  cycle: {
    type: Number,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:cycle', 'startPrediction'])

const cycleValue = computed({
  get: () => props.cycle,
  set: (value) => emit('update:cycle', value),
})

const onStartPrediction = () => {
  emit('startPrediction')
}
</script>
