import { MarkerType } from '@vue-flow/core'
import { useDebounceFn } from '@vueuse/core'
import { shallowRef } from 'vue'

/**
 * 工作流边管理 Hook
 * 用于处理工作流中的边连接、样式等
 */
export function useWorkflowEdges() {
  // 边删除对话框状态
  const isEdgeDeleteDialogOpen = shallowRef(false)
  const currentEdgeId = shallowRef('')

  // 警告提示状态
  const showWarning = shallowRef(false)
  const warningMessage = shallowRef('')

  /**
   * 处理连接验证
   * @param sourceNode 源节点
   * @param targetNode 目标节点
   * @returns 是否可以连接
   */
  const validateConnection = (sourceNode, targetNode) => {
    if (!sourceNode || !targetNode) {
      return false
    }
    // 检查类型兼容性
    const sourceType = sourceNode.data.type
    const targetType = targetNode.data.type

    // 获取节点的标签名称，用于显示在警告中
    const sourceLabel = sourceNode.data.label
    const targetLabel = targetNode.data.label

    // 只需检查目标节点的inputType是否包含源节点的类型
    const targetCanAcceptSource = targetNode.data.inputType?.includes(sourceType)

    if (!targetCanAcceptSource) {
      warningMessage.value = `节点 "${targetLabel}" 不能接收来自 "${sourceLabel}" 的输入`
      showWarning.value = true
      return false
    }

    return true
  }

  // 通过配置想中的inputGroups输入分组，来判断后续的连线是否合法
  const validateConnectionGroup = (params, elements) => {
    const targetNodeId = params.target
    const sourceNode: any = elements.find((el: any) => el.id === params.source)
    const targetNode: any = elements.find((el: any) => el.id === params.target)
    // 获取目标节点上面所有的边
    const sourceEdges = elements.filter(
      (el: any) => el.target === targetNodeId && el.type === 'bezier',
    )
    const sourceNodes = elements.filter((f: any) => {
      const findNode: any = sourceEdges.find((item: any) => item.source === f.id)
      if (findNode && f.type === 'custom') {
        return true
      } else {
        return false
      }
    })

    //根据第一个连接的类型
    if (sourceNodes.length > 0) {
      const fristNode: any = sourceNodes[0]
      const targetNodeData = targetNode.data
      if (targetNodeData.inputGroups) {
        const nodeGroup = targetNodeData.inputGroups
        const findNode = nodeGroup.find((item) => item.includes(fristNode.data.type))
        if (findNode && !findNode.includes(sourceNode.data.type)) {
          const labelType = fristNode.data.label
          const sourceLabelType = sourceNode.data.label
          warningMessage.value = `已经连接节点 "${labelType}" 不能连接 "${sourceLabelType}" 的输入`
          showWarning.value = true
          return false
        }
      }
    }
    return true
  }

  /**
   * 处理连接创建
   * @param params 连接参数
   * @param defaultEdgeOptions 默认边选项
   * @returns 新边数据
   */
  const handleConnect = useDebounceFn((params, defaultEdgeOptions) => {
    return {
      id: `edge-${Date.now()}`,
      source: params.source,
      target: params.target,
      type: defaultEdgeOptions.type,
      markerEnd: defaultEdgeOptions.showArrow
        ? {
            type: MarkerType.ArrowClosed,
            color: defaultEdgeOptions.style.stroke,
            width: 10,
            height: 10,
            strokeWidth: defaultEdgeOptions.markerEnd.strokeWidth,
          }
        : undefined,
      style: {
        strokeWidth: defaultEdgeOptions.style.strokeWidth,
        stroke: defaultEdgeOptions.style.stroke,
      },
      animated: defaultEdgeOptions.animated,
    }
  }, 200)

  /**
   * 处理边点击
   * @param edge 被点击的边
   * @param defaultEdgeStyle 默认边样式
   * @returns 更新后的边样式
   */
  const handleEdgeClick = (edge, defaultEdgeStyle) => {
    // 更新被点击的边样式
    return {
      id: edge.id,
      style: {
        strokeWidth: defaultEdgeStyle.style.strokeWidth + 1, // 点击时加粗
        stroke: '#3b82f6',
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        color: '#3b82f6',
        width: 10,
        height: 10,
        strokeWidth: defaultEdgeStyle.markerEnd.strokeWidth,
      },
      animated: true,
    }
  }

  /**
   * 处理边双击
   * @param edge 被双击的边
   */
  const handleEdgeDoubleClick = (edge) => {
    if (!edge || !edge.id) {
      console.error('点击的边没有有效的ID', edge)
      return
    }
    currentEdgeId.value = edge.id
    isEdgeDeleteDialogOpen.value = true
  }

  /**
   * 关闭警告
   */
  const closeWarning = () => {
    showWarning.value = false
  }

  return {
    isEdgeDeleteDialogOpen,
    currentEdgeId,
    showWarning,
    warningMessage,
    validateConnection,
    handleConnect,
    handleEdgeClick,
    handleEdgeDoubleClick,
    closeWarning,
    validateConnectionGroup,
  }
}
