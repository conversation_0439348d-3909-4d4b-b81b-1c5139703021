/**
 * 工作流生成模块
 */

/**
 * 从提供的节点模块中提取寿命预测节点
 * @param nodeModule 节点模块对象
 * @returns 提取的节点和节点映射
 */
export function extractLifePredictionNodes(nodeModule: any): {
  nodes: any[]
  nodeMap: Record<string, any>
} {
  if (!nodeModule) {
    window.logger.error('未找到豪鹏电池寿命预测模块')
    return { nodes: [], nodeMap: {} }
  }

  const nodes = []
  const nodeMap = {}

  // 按照指定顺序添加节点
  const orderedNodeTypes = [
    'HighpowerCellDataInput',
    'HighpowerBatteryModelCalibration',
    'HighpowerBatteryModel',
    'HighpowerCellLifePrediction',
  ]

  // 从各个类别中查找指定类型的节点
  orderedNodeTypes.forEach((nodeType, index) => {
    // 在所有类别中查找匹配的节点
    let foundNode = null

    nodeModule.categories.forEach((category) => {
      category.nodes.forEach((node) => {
        if (node.data.type === nodeType) {
          foundNode = node
        }
      })
    })

    if (foundNode) {
      // 完全保留原始节点结构，只添加位置信息
      const newNode = JSON.parse(JSON.stringify(foundNode)) // 深拷贝原始节点

      // 添加位置信息
      newNode.position = { x: 100 + index * 400, y: 200 }

      // 添加info属性，用于工作流显示
      newNode.info = {
        label: newNode.data.label,
        data: newNode.data.description || '节点数据',
      }

      // 第一个节点为起点，最后一个节点为终点
      if (index === 0) {
        newNode.info.is_start = true
      } else if (index === orderedNodeTypes.length - 1) {
        newNode.info.is_end = true
      }

      nodes.push(newNode)
      nodeMap[nodeType] = newNode
    }
  })

  window.logger.info('提取的寿命预测节点:', nodes)
  return { nodes, nodeMap }
}

/**
 * 创建寿命预测流程的边
 * @param nodeMap 节点映射
 * @returns 边数组
 */
export function createLifePredictionEdges(nodeMap: Record<string, any>): any[] {
  const edges = []
  const orderedNodeTypes = [
    'HighpowerCellDataInput',
    'HighpowerBatteryModelCalibration',
    'HighpowerBatteryModel',
    'HighpowerCellLifePrediction',
  ]

  // 按顺序连接节点
  for (let i = 0; i < orderedNodeTypes.length - 1; i++) {
    const sourceType = orderedNodeTypes[i]
    const targetType = orderedNodeTypes[i + 1]

    if (nodeMap[sourceType] && nodeMap[targetType]) {
      edges.push({
        id: `edge-${i}-${i + 1}`,
        source: nodeMap[sourceType].id,
        target: nodeMap[targetType].id,
      })
    }
  }

  window.logger.info('创建的寿命预测流程边:', edges)
  return edges
}
