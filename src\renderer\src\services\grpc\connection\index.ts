/**
 * GRPC连接服务
 * 负责管理与中台的长连接
 */
import { handleConnectionEvent } from '../handlers/eventHandlers/connectionEvents'
import { ConnectionConfig } from '../types'
import { dispatchGrpcEvent } from './eventDispatcher'
import { resetReconnectAttempts, scheduleReconnect, setConnectionInfo } from './reconnect'

class GrpcConnectionService {
  private clientConnection: any = null
  private isConnecting: boolean = false
  private connectionConfig: ConnectionConfig | null = null
  private hasRegistered: boolean = false

  constructor() {
    // 设置重连回调
    setConnectionInfo(null, this.reconnectWithConfig.bind(this))
  }

  /**
   * 建立与中台的长连接
   */
  connect(
    username: string,
    last_ip: string,
    password: string,
    expiredTime: number = 0,
    callback?: (success: boolean, message: string) => void,
  ): void {
    if (this.isConnecting) {
      window.logger?.info('已有连接正在建立中，忽略此次连接请求')
      return
    }

    this.disconnect()
    this.isConnecting = true
    this.hasRegistered = false

    // 保存连接信息用于重连
    this.connectionConfig = {
      username,
      lastIp: last_ip,
      password,
      expiredTime,
    }

    // 更新重连模块中的连接信息
    setConnectionInfo(this.connectionConfig, this.reconnectWithConfig.bind(this))

    window.logger?.info(`正在建立与中台的长连接: ${username}@${last_ip}`)

    try {
      this.clientConnection = window.grpcApi.setClient(
        username,
        last_ip,
        password,
        expiredTime,
        (data: any, type: string) => {
          // 处理连接事件
          handleConnectionEvent(data, type, this)

          // 如果是连接建立或错误，调用回调
          if (type === 'data' && data.message === 'Channel established') {
            callback?.(true, '连接已建立')
          } else if (type === 'error') {
            callback?.(false, typeof data === 'string' ? data : JSON.stringify(data))
          }
        },
      )
    } catch (error) {
      this.isConnecting = false
      window.logger?.error('建立连接时发生异常:', error)
      callback?.(false, error instanceof Error ? error.message : '建立连接时发生异常')
    }
  }

  /**
   * 使用保存的配置重新连接
   */
  private reconnectWithConfig(config: ConnectionConfig): void {
    if (!config) return

    this.connect(config.username, config.lastIp, config.password, config.expiredTime)
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.clientConnection) {
      try {
        window.grpcApi.closeClient()
        window.logger?.info('已断开与中台的连接')

        // 分发连接断开事件
        dispatchGrpcEvent('connection-closed', null)
      } catch (error) {
        window.logger?.error('断开连接时发生异常:', error)
      }
      this.clientConnection = null
    }
  }

  /**
   * 设置连接状态
   */
  setConnectingState(isConnecting: boolean): void {
    this.isConnecting = isConnecting
  }

  /**
   * 设置注册状态
   */
  setRegisteredState(hasRegistered: boolean): void {
    this.hasRegistered = hasRegistered
  }

  /**
   * 获取注册状态
   */
  getRegisteredState(): boolean {
    return this.hasRegistered
  }

  /**
   * 获取连接配置
   */
  getConnectionConfig(): ConnectionConfig | null {
    return this.connectionConfig
  }
}

// 导出连接服务实例
export const grpcConnectionService = new GrpcConnectionService()

// 导出重连相关函数
export { resetReconnectAttempts, scheduleReconnect }
