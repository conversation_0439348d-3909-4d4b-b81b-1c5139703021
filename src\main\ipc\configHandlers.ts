import { is } from '@electron-toolkit/utils'
import { app, ipcMain } from 'electron'

import { configService } from '../services/configService'
import logger from '../utils/logger'

// 跟踪处理程序是否已设置
let handlersSetup = false
/**
 * 设置配置管理相关的IPC处理程序
 */
export function setupConfigHandlers(): void {
  // 获取版本信息
  ipcMain.handle('get-versions', () => {
    const versions = {
      app: app.getVersion(),
      electron: process.versions.electron,
      chrome: process.versions.chrome,
      node: process.versions.node,
    }
    logger.info('Version info requested:', versions)
    return versions
  })

  // 获取环境变量
  ipcMain.handle('get-env-vars', () => {
    return configService.getEnvVars()
  })

  // 更新Linker API
  ipcMain.handle('update-linker-api', async (_event, newLinkerApi) => {
    return configService.updateLinkerApiConfig(newLinkerApi)
  })

  // 重启应用
  ipcMain.handle('restart-app', () => {
    if (is.dev) {
      logger.info('开发模式下收到重启指令，直接退出，请手动重启 Electron 应用')
      app.exit(0)
    } else {
      logger.info('生产环境下收到重启指令，relaunch')
      app.relaunch()
      app.exit(0)
    }
  })
  // 标记处理程序已设置
  handlersSetup = true
  logger.info('配置管理IPC处理程序已设置')
}
