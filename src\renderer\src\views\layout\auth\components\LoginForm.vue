<template>
  <div class="space-y-6">
    <!-- 设置按钮 -->
    <div class="absolute top-4 right-4">
      <Popover>
        <PopoverTrigger as-child>
          <Button variant="ghost" size="icon">
            <Settings2Icon class="h-5 w-5" />
          </Button>
        </PopoverTrigger>
        <PopoverContent class="w-80">
          <div class="space-y-4">
            <h4 class="font-medium">中台连接设置</h4>
            <div class="space-y-2">
              <Label for="platform-address">中台地址</Label>
              <div class="flex space-x-2">
                <Input
                  id="platform-address"
                  v-model="platformAddress"
                  placeholder="例如: ***********:20001"
                />
                <Button size="sm" :disabled="!isValidUrl" @click="savePlatformAddress">保存</Button>
              </div>
              <p class="text-xs text-muted-foreground">格式: IP地址:端口 或 域名:端口</p>
            </div>

            <!-- 当前连接状态 -->
            <div class="p-2 border rounded-md">
              <div class="flex items-center mb-1">
                <div
                  class="w-2 h-2 rounded-full mr-2"
                  :class="grpcStatus ? 'bg-green-500' : 'bg-red-500'"
                ></div>
                <p class="text-sm">
                  {{ grpcStatus ? '已连接' : '未连接' }}
                </p>
              </div>
              <p class="text-xs text-muted-foreground">当前地址: {{ currentLinkerApi }}</p>
              <p class="text-xs text-muted-foreground">版本: {{ serverVersion || '未知' }}</p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
    <!-- 表单标题 -->
    <div class="space-y-2">
      <h3 class="text-2xl font-medium">登录账户</h3>
      <p class="text-sm text-muted-foreground">请输入您的账号信息</p>
    </div>

    <!-- 登录表单 -->
    <form class="space-y-5" @submit="onSubmit">
      <FormField v-slot="{ componentField, errorMessage }" name="username">
        <FormItem>
          <FormLabel>用户名</FormLabel>
          <FormControl>
            <div class="relative">
              <UserIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"
              />
              <Input
                v-bind="componentField"
                placeholder="请输入用户名"
                class="pl-10"
                :disabled="isLoading"
                autocomplete="username"
              />
            </div>
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField, errorMessage }" name="password">
        <FormItem>
          <FormLabel>密码</FormLabel>
          <FormControl>
            <div class="relative">
              <KeyIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"
              />
              <Input
                v-bind="componentField"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                class="pl-10"
                :disabled="isLoading"
                autocomplete="current-password"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                @click="togglePasswordVisibility"
              >
                <EyeIcon v-if="showPassword" class="h-4 w-4" />
                <EyeOffIcon v-else class="h-4 w-4" />
              </button>
            </div>
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- 记住我和忘记密码 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <Checkbox id="remember" v-model:checked="rememberMe" />
          <label for="remember" class="ml-2 text-sm text-muted-foreground cursor-pointer">
            记住我
          </label>
        </div>
        <a href="#" class="text-sm text-primary hover:underline">忘记密码?</a>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessage" class="text-sm font-medium text-destructive mt-2">
        {{ errorMessage }}
      </div>

      <!-- 登录按钮 -->
      <Button type="submit" class="w-full" :disabled="isLoading">
        <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
        <span v-if="isLoading">登录中...</span>
        <span v-else>登录</span>
      </Button>
      <!-- 当前中台地址信息 -->
      <div class="text-center mt-4">
        <p class="text-xs text-muted-foreground">
          当前中台地址: {{ currentLinkerApi }} | 版本: {{ serverVersion || '未知' }}
        </p>
      </div>
      <!-- 注册提示 -->
      <div class="text-center mt-4">
        <p class="text-sm text-muted-foreground">
          还没有账号?
          <a
            class="text-primary hover:underline cursor-pointer font-medium"
            @click="emits('register-click')"
          >
            立即注册
          </a>
        </p>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore, useNodeModulesStore } from '@renderer/store'
import { toTypedSchema } from '@vee-validate/zod'
import CryptoJS from 'crypto-js'
import { EyeIcon, EyeOffIcon, KeyIcon, Loader2, Settings2Icon, UserIcon } from 'lucide-vue-next'
import { useForm } from 'vee-validate'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { z } from 'zod'
import { registerLifePredictionWorkflow } from '@renderer/services/nodeRegistrationService'
// 定义事件
const emits = defineEmits<{
  (e: 'register-click'): void
}>()

// 表单验证模式
const formSchema = toTypedSchema(
  z.object({
    username: z.string().min(3, '用户名至少需要3个字符').max(20, '用户名不能超过20个字符'),
    password: z
      .string()
      .min(6, '密码至少需要6个字符')
      .max(30, '密码不能超过30个字符')
      .regex(/^[a-zA-Z0-9]+$/, '密码只能包含字母和数字，不允许任何特殊字符'),
  }),
)

// 获取保存的用户名（如果有）
const savedRememberMe = localStorage.getItem('rememberMe')
const savedUsername = savedRememberMe === 'true' ? localStorage.getItem('savedUsername') : null

const router = useRouter()
const authStore = useAuthStore()
const nodeModulesStore = useNodeModulesStore()

const showPassword = ref(false)
const errorMessage = ref('')
const isLoading = ref(false)
const rememberMe = ref(savedRememberMe === 'true')
const localIp = ref('127.0.0.1') // 本机IP地址

// 中台地址相关
const platformAddress = ref('')
const currentLinkerApi = ref('')
const grpcStatus = ref(false)
const serverVersion = ref('')

// 验证URL是否有效
const isValidUrl = computed(() => {
  if (!platformAddress.value) return false

  // 验证格式为 域名:端口 或 IP:端口
  const regex =
    /^((([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])|((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:([1-9][0-9]{0,4}))?$/
  return regex.test(platformAddress.value)
})

// 使用useForm配置初始值和验证规则
const { handleSubmit } = useForm({
  validationSchema: formSchema,
  initialValues: {
    username: savedUsername || 'admin',
    password: 'mattverse999',
  },
})

const onSubmit = handleSubmit(async (values) => {
  try {
    errorMessage.value = ''
    isLoading.value = true

    // 对密码进行哈希处理
    const hashedPassword = hashPassword(values.password)

    const loginParams = {
      username: values.username,
      password: hashedPassword,
      last_ip: localIp.value,
    }
    // 使用 auth store 的 login 方法
    const result = await authStore.login(
      loginParams.username,
      loginParams.password,
      loginParams.last_ip,
    )

    if (result.success) {
      // 记住我逻辑
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true')
        localStorage.setItem('savedUsername', values.username)
      } else {
        localStorage.removeItem('rememberMe')
        localStorage.removeItem('savedUsername')
      }

      // 记录登录成功日志
      if (window.logger) {
        window.logger.info(`用户 ${values.username} 登录成功`)
      }

      // 跳转到首页
      router.push('/')
    } else {
      errorMessage.value = result.error || '登录失败，请检查用户名和密码'
      // 记录登录失败日志
      if (window.logger) {
        window.logger.warn(`用户 ${values.username} 登录失败: ${result.error}`)
      }
    }
  } catch (error) {
    errorMessage.value = error instanceof Error ? error.message : '登录失败，请检查用户名和密码'
    if (window.logger) {
      window.logger.error(`登录异常:`, error)
    }
  } finally {
    isLoading.value = false
  }
})

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

/**
 * 对密码进行哈希处理
 * @param password 原始密码
 * @returns 哈希后的密码
 */
function hashPassword(password: string): string {
  return CryptoJS.SHA256(password).toString(CryptoJS.enc.Hex)
}

// 保存中台地址
const savePlatformAddress = async () => {
  try {
    if (!isValidUrl.value) {
      toast.error('地址格式无效', {
        description: '请输入有效的IP地址:端口或域名:端口格式',
      })
      return
    }

    // 调用electron主进程保存配置
    const result = await window.grpcApi.updateLinkerApi(platformAddress.value)

    if (result.success) {
      toast.success('保存成功', {
        description: '中台地址已更新，应用将在2秒后重启...',
      })

      // 更新当前显示的地址
      currentLinkerApi.value = platformAddress.value

      // 重新获取连接状态
      setTimeout(() => {
        // getGrpcStatus()
        // getServerVersion()
        window.grpcApi.restartApp()
      }, 1000)
    } else {
      toast.error('保存失败', {
        description: result.message,
      })
    }
  } catch (error) {
    toast.error('保存失败', {
      description: error.message || '未知错误',
    })
  }
}

// 获取当前连接状态
const getGrpcStatus = async () => {
  try {
    const status = await window.grpcApi.getStatus()
    grpcStatus.value = status.connected
    currentLinkerApi.value = status.linkerUrl
  } catch (error) {
    console.error('获取gRPC状态失败:', error)
    grpcStatus.value = false
  }
}

// 获取服务器版本信息
const getServerVersion = async () => {
  try {
    const res = await window.grpcApi.call('getVersion', {})
    serverVersion.value = res.result
  } catch (error) {
    console.error('服务器版本获取失败')
    serverVersion.value = '未知'
  }
}

onMounted(async () => {
  try {
    // 获取环境变量
    const envVars = await window.api.getEnvVars()
    platformAddress.value = envVars.VITE_APP_LINKER_API || ''
    currentLinkerApi.value = envVars.VITE_APP_LINKER_API || ''

    // 获取gRPC连接状态
    getGrpcStatus()
    // 获取中台版本信息
    getServerVersion()

    if (window.appInfo && window.appInfo.getLocalIp) {
      localIp.value = window.appInfo.getLocalIp()
      if (window.logger) {
        window.logger.info(`获取到本机IP: ${localIp.value}`)
      }
    }
  } catch (error) {
    if (window.logger) {
      window.logger.error('获取本机IP失败:', error)
    }
  }
})
</script>

<style scoped>
.form-control {
  transition: all 0.2s ease;
}

.form-control:focus-within {
  transform: translateY(-2px);
}

/* 输入框焦点动画 */
input:focus {
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.3);
}

/* 按钮悬停效果 */
button {
  transition: all 0.2s ease;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
}

button:active:not(:disabled) {
  transform: translateY(0px);
}
</style>
