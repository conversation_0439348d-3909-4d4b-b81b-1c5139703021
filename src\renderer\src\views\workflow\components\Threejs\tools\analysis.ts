class Analysis {
  // 颜色数组转为十六进制颜色值
  arrayToHex(arr) {
    // 确保是长度为3或4的数组（RGB或RGBA）
    if (!Array.isArray(arr) || arr.length < 3) {
      throw new Error('输入必须是包含至少3个元素的数组')
    }

    // 将0~1的值转换为0~255的整数
    const [r, g, b] = arr.map((val) => {
      const clamped = Math.min(1, Math.max(0, Number(val))) // 限制在0~1范围
      return Math.round(clamped * 255)
    })

    // 转换为两位十六进制
    const toHex = (num) => num.toString(16).padStart(2, '0')
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  }
  // 优化版（自动处理RGB/RGBA）
  normalizeToHex(arr) {
    const validLength = arr.length === 3 || arr.length === 4
    if (!Array.isArray(arr) || !validLength) {
      throw new Error('输入必须是长度为3(RGB)或4(RGBA)的数组')
    }

    const hexParts = arr.slice(0, 3).map((val) => {
      const num = Math.round(Math.min(1, Math.max(0, Number(val))) * 255)
      return num.toString(16).padStart(2, '0')
    })

    let hex = `#${hexParts.join('')}`

    // 处理透明度（如果存在）
    if (arr.length === 4) {
      const alpha = Math.round(Math.min(1, Math.max(0, Number(arr[3]))) * 255)
      hex += alpha.toString(16).padStart(2, '0')
    }

    return hex
  }
  /**
   * 获取三维线段的长度和中心点
   * @param {number[]} start - 起点坐标 [x, y, z]
   * @param {number[]} end - 终点坐标 [x, y, z]
   * @returns {Object} { length: number, center: number[] }
   */
  getLineInfo(start, end) {
    // 校验输入
    if (!Array.isArray(start) || !Array.isArray(end) || start.length !== 3 || end.length !== 3) {
      throw new Error('输入必须是三维坐标数组 [x,y,z]')
    }

    // 计算长度
    const dx = end[0] - start[0]
    const dy = end[1] - start[1]
    const dz = end[2] - start[2]
    const length = Math.sqrt(dx * dx + dy * dy + dz * dz)

    // 计算中心点
    const center = [(start[0] + end[0]) / 2, (start[1] + end[1]) / 2, (start[2] + end[2]) / 2]

    return { length, center }
  }
  // 保留四位小数，因为六位小数点之后，threejs可能无法精确显示
  roundTo4(num: number): number {
    return Math.round(num * 10000) / 10000
  }
}
export default Analysis
