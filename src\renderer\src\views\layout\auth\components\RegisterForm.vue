<template>
  <div class="space-y-6">
    <!-- 表单标题 -->
    <div class="space-y-2">
      <h3 class="text-2xl font-medium">创建账户</h3>
      <p class="text-sm text-muted-foreground">请填写以下信息完成注册</p>
    </div>

    <!-- 注册表单 -->
    <form class="space-y-4" @submit="onSubmit">
      <FormField v-slot="{ componentField, errorMessage }" name="username">
        <FormItem>
          <FormLabel>用户名</FormLabel>
          <FormControl>
            <div class="relative">
              <UserIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"
              />
              <Input
                v-bind="componentField"
                placeholder="请输入用户名"
                class="pl-10"
                :disabled="isLoading"
              />
            </div>
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField, errorMessage }" name="email">
        <FormItem>
          <FormLabel>电子邮箱</FormLabel>
          <FormControl>
            <div class="relative">
              <MailIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"
              />
              <Input
                v-bind="componentField"
                type="email"
                placeholder="请输入邮箱"
                class="pl-10"
                :disabled="isLoading"
              />
            </div>
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField, errorMessage }" name="password">
        <FormItem>
          <FormLabel>密码</FormLabel>
          <FormControl>
            <div class="relative">
              <KeyIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"
              />
              <Input
                v-bind="componentField"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请设置密码"
                class="pl-10"
                :disabled="isLoading"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                @click="togglePasswordVisibility"
              >
                <EyeIcon v-if="showPassword" class="h-4 w-4" />
                <EyeOffIcon v-else class="h-4 w-4" />
              </button>
            </div>
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField, errorMessage }" name="confirmPassword">
        <FormItem>
          <FormLabel>确认密码</FormLabel>
          <FormControl>
            <div class="relative">
              <ShieldCheckIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4"
              />
              <Input
                v-bind="componentField"
                :type="showConfirmPassword ? 'text' : 'password'"
                placeholder="请再次输入密码"
                class="pl-10"
                :disabled="isLoading"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                @click="toggleConfirmPasswordVisibility"
              >
                <EyeIcon v-if="showConfirmPassword" class="h-4 w-4" />
                <EyeOffIcon v-else class="h-4 w-4" />
              </button>
            </div>
          </FormControl>
          <FormMessage>{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>

      <!-- 用户协议同意 -->
      <div class="flex items-center">
        <Checkbox id="terms" v-model="agreedToTerms" />
        <label for="terms" class="ml-2 text-sm text-muted-foreground cursor-pointer">
          我已阅读并同意
          <a href="#" class="text-primary hover:underline">服务条款</a>
          和
          <a href="#" class="text-primary hover:underline">隐私政策</a>
        </label>
      </div>

      <!-- 错误信息 -->
      <div v-if="formError" class="text-sm font-medium text-destructive mt-2">
        {{ formError }}
      </div>

      <!-- 注册按钮 -->
      <Button type="submit" class="w-full" :disabled="isLoading || !agreedToTerms">
        <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
        <span v-if="isLoading">注册中...</span>
        <span v-else>注册账户</span>
      </Button>

      <!-- 返回登录 -->
      <div class="text-center mt-4">
        <p class="text-sm text-muted-foreground">
          已有账号?
          <a
            class="text-primary hover:underline cursor-pointer font-medium"
            @click="emits('login-click')"
          >
            返回登录
          </a>
        </p>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@renderer/store'
import { toTypedSchema } from '@vee-validate/zod'
import {
  EyeIcon,
  EyeOffIcon,
  KeyIcon,
  Loader2,
  MailIcon,
  ShieldCheckIcon,
  UserIcon,
} from 'lucide-vue-next'
import { useForm } from 'vee-validate'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { z } from 'zod'

// 定义事件
const emits = defineEmits<{
  (e: 'login-click'): void
}>()

// 表单验证模式
const formSchema = toTypedSchema(
  z
    .object({
      username: z.string().min(3, '用户名至少需要3个字符').max(20, '用户名不能超过20个字符'),
      email: z.string().email('请输入有效的电子邮箱地址'),
      password: z.string().min(6, '密码至少需要6个字符').max(30, '密码不能超过30个字符'),
      confirmPassword: z.string().min(1, '请确认您的密码'),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: '两次输入的密码不一致',
      path: ['confirmPassword'],
    }),
)

const router = useRouter()
const authStore = useAuthStore()
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const formError = ref('')
const isLoading = ref(false)
const agreedToTerms = ref(false)

// 使用vee-validate的useForm
const { handleSubmit } = useForm({
  validationSchema: formSchema,
  initialValues: {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  },
})

const onSubmit = handleSubmit(async (values) => {
  if (!agreedToTerms.value) {
    formError.value = '请先同意服务条款和隐私政策'
    return
  }

  try {
    formError.value = ''
    isLoading.value = true

    // 模拟注册请求
    // 在实际应用中，这里应该调用authStore.register方法
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // 简单注册逻辑
    if (values.username === 'admin') {
      formError.value = '该用户名已被注册'
      return
    }

    // 注册成功
    console.log('注册成功:', values)

    // 显示注册成功的消息
    toast.success('注册成功！请使用您的账号登录')
    // 返回登录页面
    emits('login-click')
  } catch (error) {
    formError.value = error instanceof Error ? error.message : '注册失败，请稍后再试'
    console.error('注册错误:', error)
  } finally {
    isLoading.value = false
  }
})

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}
</script>

<style scoped>
.form-control {
  transition: all 0.2s ease;
}

.form-control:focus-within {
  transform: translateY(-2px);
}

/* 输入框焦点动画 */
input:focus {
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.3);
}

/* 按钮悬停效果 */
button {
  transition: all 0.2s ease;
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
}

button:active:not(:disabled) {
  transform: translateY(0px);
}
</style>
