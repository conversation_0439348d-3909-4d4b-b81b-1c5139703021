<template>
  <div class="space-y-3">
    <!-- 电化学参数状态提示 -->
    <Alert
      v-if="currentStep === 0 && elecParamsCompleted"
      variant="success"
      class="bg-green-50 border-green-200"
    >
      <CheckCircle2 class="h-4 w-4 text-green-500" />
      <AlertTitle class="text-green-700">电化学参数已提交</AlertTitle>
      <AlertDescription class="text-green-600">
        您可以继续修改参数并重新提交，或者点击"下一步"进入老化参数设置
      </AlertDescription>
    </Alert>

    <Alert
      v-else-if="currentStep === 0 && selectedElecCount === 0"
      variant="warning"
      class="bg-amber-50 border-amber-200"
    >
      <AlertCircle class="h-4 w-4 text-amber-500" />
      <AlertTitle class="text-amber-700">请选择参数</AlertTitle>
      <AlertDescription class="text-amber-600">请至少选择一个电化学参数进行标定</AlertDescription>
    </Alert>

    <!-- 老化参数状态提示 -->
    <Alert
      v-if="currentStep === 1 && !elecParamsCompleted"
      variant="warning"
      class="bg-amber-50 border-amber-200"
    >
      <AlertCircle class="h-4 w-4 text-amber-500" />
      <AlertTitle class="text-amber-700">请先完成电化学参数标定</AlertTitle>
      <AlertDescription class="text-amber-600">请返回上一步完成电化学参数标定</AlertDescription>
    </Alert>

    <Alert
      v-else-if="currentStep === 1 && agingParamsCompleted"
      variant="success"
      class="bg-green-50 border-green-200"
    >
      <CheckCircle2 class="h-4 w-4 text-green-500" />
      <AlertTitle class="text-green-700">老化参数已提交</AlertTitle>
      <AlertDescription class="text-green-600">老化参数已经提交请耐心等待结果</AlertDescription>
    </Alert>

    <Alert
      v-else-if="currentStep === 1 && selectedAgingCount === 0"
      variant="warning"
      class="bg-amber-50 border-amber-200"
    >
      <AlertCircle class="h-4 w-4 text-amber-500" />
      <AlertTitle class="text-amber-700">请选择参数</AlertTitle>
      <AlertDescription class="text-amber-600">请至少选择一个老化参数进行标定</AlertDescription>
    </Alert>
  </div>
</template>

<script setup>
import { AlertCircle, CheckCircle2 } from 'lucide-vue-next'

defineProps({
  // 当前步骤
  currentStep: {
    type: Number,
    required: true,
  },
  // 电化学参数是否已完成标定
  elecParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 老化参数是否已完成标定
  agingParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 选中的电化学参数数量
  selectedElecCount: {
    type: Number,
    default: 0,
  },
  // 选中的老化参数数量
  selectedAgingCount: {
    type: Number,
    default: 0,
  },
})
</script>
