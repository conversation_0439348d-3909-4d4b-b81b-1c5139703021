<template>
  <div class="space-y-4">
    <!-- 配置切换 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <RotateCcw class="h-4 w-4 mr-2" />
          配置切换
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <Card
          v-for="configType in availableConfigs"
          :key="configType.type"
          :class="[
            'cursor-pointer transition-all hover:shadow-md',
            appType === configType.type
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'hover:border-muted-foreground/50',
          ]"
          @click="switchConfig(configType.type)"
        >
          <CardContent class="p-3">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-sm font-medium">
                  {{ configType.title }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ configType.description }}
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <Badge v-if="appType === configType.type" variant="default" class="text-xs">
                  当前
                </Badge>
                <ChevronRight class="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>

    <!-- 开发工具 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Code class="h-4 w-4 mr-2" />
          开发工具
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <Button variant="outline" class="w-full justify-between" @click="exportConfig">
          <div class="flex items-center space-x-3">
            <Download class="h-4 w-4 text-green-600" />
            <div class="text-left">
              <div class="text-sm font-medium">导出配置</div>
              <div class="text-xs text-muted-foreground">下载当前配置为JSON文件</div>
            </div>
          </div>
          <ChevronRight class="h-4 w-4 text-muted-foreground" />
        </Button>

        <Button variant="outline" class="w-full justify-between" @click="copyConfig">
          <div class="flex items-center space-x-3">
            <Copy class="h-4 w-4 text-blue-600" />
            <div class="text-left">
              <div class="text-sm font-medium">复制配置</div>
              <div class="text-xs text-muted-foreground">复制配置到剪贴板</div>
            </div>
          </div>
          <ChevronRight class="h-4 w-4 text-muted-foreground" />
        </Button>

        <Button
          variant="outline"
          class="w-full justify-between border-red-200 hover:bg-red-50 dark:border-red-800 dark:hover:bg-red-900/20"
          @click="resetConfig"
        >
          <div class="flex items-center space-x-3">
            <RefreshCw class="h-4 w-4 text-red-600" />
            <div class="text-left">
              <div class="text-sm font-medium text-red-600 dark:text-red-400">重置配置</div>
              <div class="text-xs text-red-500 dark:text-red-400">恢复到默认配置</div>
            </div>
          </div>
          <ChevronRight class="h-4 w-4 text-muted-foreground" />
        </Button>
      </CardContent>
    </Card>

    <!-- 调试信息 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Bug class="h-4 w-4 mr-2" />
          调试信息
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <!-- 配置路径 -->
        <Card>
          <CardContent class="p-3">
            <div class="text-xs text-muted-foreground mb-1">配置文件路径</div>
            <div class="text-xs font-mono break-all">
              src/renderer/src/config/app/{{ appType }}.ts
            </div>
          </CardContent>
        </Card>

        <!-- 配置哈希 -->
        <Card>
          <CardContent class="p-3">
            <div class="text-xs text-muted-foreground mb-1">配置哈希</div>
            <div class="text-xs font-mono">
              {{ configHash }}
            </div>
          </CardContent>
        </Card>

        <!-- 最后更新时间 -->
        <Card>
          <CardContent class="p-3">
            <div class="text-xs text-muted-foreground mb-1">最后更新</div>
            <div class="text-xs">
              {{ lastUpdateTime }}
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>

    <!-- 快捷键说明 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Keyboard class="h-4 w-4 mr-2" />
          快捷键
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2 text-sm">
        <div class="flex justify-between items-center">
          <span class="text-muted-foreground">打开/关闭配置面板</span>
          <Badge variant="outline" class="font-mono text-xs">Ctrl+Shift+C</Badge>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-muted-foreground">关闭面板</span>
          <Badge variant="outline" class="font-mono text-xs">Esc</Badge>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Button } from '@renderer/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import {
  Bug,
  ChevronRight,
  Code,
  Copy,
  Download,
  Keyboard,
  RefreshCw,
  RotateCcw,
} from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'

const { appType, appConfig, switchAppConfig, resetConfig: resetAppConfig } = useAppConfig()

// 可用配置类型
const availableConfigs = [
  {
    type: 'mattverse',
    title: 'MattVerse 配置',
    description: '完整功能的电池设计自动化平台',
  },
  {
    type: 'highpower',
    title: 'Highpower 配置',
    description: '简化的电池寿命预测软件',
  },
]

// 配置哈希（简单的配置标识）
const configHash = computed(() => {
  const configStr = JSON.stringify(appConfig.value)
  let hash = 0
  for (let i = 0; i < configStr.length; i++) {
    const char = configStr.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16).toUpperCase().padStart(8, '0')
})

// 最后更新时间
const lastUpdateTime = ref(new Date().toLocaleString())

// 切换配置
const switchConfig = (type: string) => {
  try {
    switchAppConfig(type as any)
    lastUpdateTime.value = new Date().toLocaleString()
    toast.success(`已切换到 ${type} 配置`)
  } catch (error) {
    toast.error(`切换配置失败: ${error}`)
  }
}

// 导出配置
const exportConfig = () => {
  try {
    const configData = JSON.stringify(appConfig.value, null, 2)

    // 创建下载链接
    const blob = new Blob([configData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${appType.value}-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('配置已导出')
  } catch (error) {
    toast.error(`导出失败: ${error}`)
  }
}

// 复制配置
const copyConfig = async () => {
  try {
    const configData = JSON.stringify(appConfig.value, null, 2)
    await navigator.clipboard.writeText(configData)
    toast.success('配置已复制到剪贴板')
  } catch (error) {
    toast.error(`复制失败: ${error}`)
  }
}

// 重置配置
const resetConfig = () => {
  try {
    resetAppConfig()
    lastUpdateTime.value = new Date().toLocaleString()
    toast.success('配置已重置')
  } catch (error) {
    toast.error(`重置失败: ${error}`)
  }
}
</script>
