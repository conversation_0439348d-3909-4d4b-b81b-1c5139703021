syntax = "proto3";
package db;

import "common.proto";

enum UserStatus {
    Activated = 0;
    Deactivated = 1;
    Deleted = 2;
}

enum UserRole {
  Super = 0;
  Admin = 1;
  Normal = 2;
}

enum ServerStatus {
    Running = 0;
    Stopped = 1;
    Expired = 2;
    Overloaded = 3;
    Stay = 4; // 不是真正的服务器状态, 只是在传输时用来保证为原始状态
    Unknown = 5;
}

enum TaskStatus {
  Initializing = 0;
  Computing = 1;
  Pending = 2;
  Paused = 3;
  Finished = 4;
  Error = 5;
  TaskStay = 6; // 不是真正的任务状态，只是在传输时用来保存原有状态
  Abort = 7;
}

enum Operate {
    Add = 0;
    Update = 1;
    Delete = 2;
    Get = 3;
    GetAll = 4;
    Clean = 5;
}

message UserInfo {
  string user_id = 1;
  string user_name = 2;
  string password = 3;
  int32 access_level = 4;
  UserStatus user_status = 5;
  UserRole role = 6;
}

message User {
  string user_id = 1;
  string user_name = 2;
  string login_ip = 3;
  string token = 4;
  int64 expired_time = 5;
}


message Server {
  string server_id = 1;
  string server_name = 2;
  string url = 3; 
  ServerStatus server_status = 4;
  string region = 5;
  string version = 6;
  int32 access_level = 7;
  int64 create_time = 8;
  int64 update_time = 9;
  string server_type = 10;
}

message Service {
  string service_id = 1;
  string service_name = 2;
  string server_id = 3;
  string version = 4;
  string protocol_type = 5;
  int32 access_level = 6;
}

message Task {
    string task_id = 1;
    string user_id = 2;
    string service_id = 3;
    int64 start_time = 4;
    int64 end_time = 5;
    string task_log = 6;
    TaskStatus task_status = 7;
    float task_process = 8;
    int32 task_pid = 9;
    string result = 10;
    string file_path = 11;
    int64 create_time = 12;
    int64 update_time = 13;
}

message SessionContent {
  string role = 1;
  string content_type = 2;
  string content = 3;
  int64 create_time = 4;
}

message SessionMessage {
  uint32 message_id = 1;
  repeated SessionContent session_contents = 2;
}

message Session {
  string session_id = 1;
  string user_id = 2;
  string session_messages = 3;
}

message ServerRequest {
  string server_id = 1;
  string server_name = 2;
  string url = 3;
  int32 access_level = 4;
  string region = 5;
  string version = 6;
  ServerStatus server_status = 7;
  string server_type = 8;
}

message ServerResult {
  string server_id = 1;
  repeated Server server_list = 2;
}

message ServiceRequest {
  string service_id = 1;
  string server_id = 2;
  string service_name = 3;
  int32 access_level = 4;
  string version = 5;
  string protocol_type = 6;
}

message ServiceResult {
  string service_id = 1;
  repeated Service service_list = 2;
}

message UserInfoRequest {
  string user_id = 1;
  string user_info_id = 2;
  string user_name = 3;
  string password = 4;
  int32 access_level = 5;
  UserStatus user_status = 6;
}

message UserInfoResult {
  string user_info_id = 1;
  repeated UserInfo user_info_list = 2;
}

message UserRequest {
  string user_id = 1;
  string user_name = 2;
  string login_ip = 3;
  string password = 4;
  int64 expired_time = 5;
}

message UserResult {
  string token = 1;
  repeated User user_list = 2;
}

message TaskRequest {
  string user_id = 1;
  string service_id = 2;
  string task_id = 3;
  bool is_only_self = 4; 
  string result = 5;
  TaskStatus task_status = 6;
  float task_process = 7;
  int32 task_pid = 8;
  int64 start_time = 9;
  int64 end_time = 10;
  string file_path = 11;
  string task_log = 12;
}

message TaskResult {
  string task_id = 1;
  repeated Task task_list = 2;
}

message SessionRequest {
  string user_id = 1;
  string token = 2;
  string session_id = 3;
  string session_user_id = 4;
  string session_messages = 5;
}

message SessionResult {
  string session_id = 1;
  repeated Session session_list = 2;
}

message SessionAppendRequest {
  string user_id = 1;
  string token = 2;
  string session_id = 3;
  SessionMessage session_message = 4;
}

message DbRequest {
  Operate operate_code = 1;
  string user_id = 2;
  string token = 3;
  oneof request {
    ServerRequest server_request = 4;
    ServiceRequest service_request = 5;
    UserInfoRequest user_info_request = 6;
    UserRequest user_request = 7;
    TaskRequest task_request = 8;
    SessionRequest session_request = 9;
  }
}

message DbResponse {
  common.ResponseStatus status = 1;
  string message = 2;
  oneof result {
    ServerResult server_result = 3;
    ServiceResult service_result = 4;
    UserInfoResult user_info_result = 5;
    UserResult user_result= 6;
    TaskResult task_result = 7;
    SessionResult session_result = 8;
  }
}

service DbService {
  rpc user_info_db (DbRequest) returns (DbResponse);
  rpc user_db (DbRequest) returns (DbResponse);
  rpc server_db (DbRequest) returns (DbResponse);
  rpc service_db (DbRequest) returns (DbResponse);
  rpc task_db (DbRequest) returns (DbResponse);
  rpc session_db (DbRequest) returns (DbResponse);
  rpc ping(common.PingRequest) returns (common.PingResponse);
  rpc getServerStatus(common.GetRequest) returns (common.GetResponse);
  rpc checkServiceAccess(common.GetRequest) returns (common.GetResponse);
  rpc checkUserToken(common.GetRequest) returns (common.GetResponse);
  rpc appendSessionMessage (SessionAppendRequest) returns (common.OperateResponse);
  rpc getUserAllSessions (common.GetRequest) returns (DbResponse);
  rpc getUserRole (common.GetRequest) returns (common.GetResponse);
}