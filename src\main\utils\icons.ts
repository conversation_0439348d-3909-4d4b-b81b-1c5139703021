import { is } from '@electron-toolkit/utils'
import fs from 'fs'
import path from 'path'
import logger from '../utils/logger'

// 导入图标资源
import iconMattverstMac from '../../renderer/src/assets/logo/mattverse.icns?asset'
import iconMattverseWin from '../../renderer/src/assets/logo/mattverse.ico?asset'
import iconMattverseLinux from '../../renderer/src/assets/logo/mattverse.png?asset'

import iconHighpowerMac from '../../renderer/src/assets/logo/highpower.icns?asset'
import iconHighpowerWin from '../../renderer/src/assets/logo/highpower.ico?asset'
import iconHighpowerLinux from '../../renderer/src/assets/logo/highpower.png?asset'

/**
 * 获取应用图标路径
 */
export function getIconPath(isMattIcon: boolean): string {
  let iconPath: string

  if (process.platform === 'win32') {
    iconPath = path.join(
      __dirname,
      is.dev ? '../renderer/src/assets/logo' : '../renderer/assets/logo',
      isMattIcon ? 'mattverse.ico' : 'highpower.ico',
    )

    if (fs.existsSync(iconPath)) {
      logger.info(`使用图标: ${iconPath}`)
    } else {
      logger.warn(`图标文件不存在: ${iconPath}, 回退到导入资源`)
      iconPath = isMattIcon ? iconMattverseWin : iconHighpowerWin
    }
  } else if (process.platform === 'darwin') {
    iconPath = path.join(
      __dirname,
      is.dev ? '../renderer/src/assets/logo' : '../renderer/assets/logo',
      isMattIcon ? 'mattverse.icns' : 'highpower.icns',
    )

    if (fs.existsSync(iconPath)) {
      logger.info(`使用图标: ${iconPath}`)
    } else {
      logger.warn(`图标文件不存在: ${iconPath}`)
      iconPath = isMattIcon ? iconMattverstMac : iconHighpowerMac
    }
  } else {
    iconPath = path.join(
      __dirname,
      is.dev ? '../renderer/src/assets/logo' : '../renderer/assets/logo',
      isMattIcon ? 'mattverse.png' : 'highpower.png',
    )

    if (fs.existsSync(iconPath)) {
      logger.info(`使用图标: ${iconPath}`)
    } else {
      logger.warn(`图标文件不存在: ${iconPath}`)
      iconPath = isMattIcon ? iconMattverseLinux : iconHighpowerLinux
    }
  }

  return iconPath
}
