<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">标定起始圈设置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <ChevronDown
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="space-y-4">
              <!-- 标定起始圈类型选择 -->
              <div class="flex flex-col space-y-2">
                <Label for="start-cycle-type" class="text-sm font-medium">标定起始圈</Label>
                <Select
                  id="start-cycle-type"
                  v-model="localParams.startCycleType"
                  :disabled="isDisabled"
                  class="w-full"
                  @update:model-value="updateParams"
                >
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择标定起始圈类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">自动计算</SelectItem>
                    <SelectItem value="customize">手动输入</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- 手动输入起始圈数值 -->
              <div
                v-if="localParams.startCycleType === 'customize'"
                class="flex flex-col space-y-2 mb-6"
              >
                <Label for="start-cycle-value" class="text-sm font-medium">起始圈数值</Label>
                <Input
                  id="start-cycle-value"
                  v-model="localParams.startCycleValue"
                  type="number"
                  min="1"
                  placeholder="请输入起始圈数值"
                  class="h-10 w-full"
                  :disabled="isDisabled"
                  @change="updateParams"
                  @focus="onCycleInputFocus"
                />

                <!-- @blur="onCycleInputBlur" -->
                <Alert
                  v-if="isShowAlert"
                  class="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-300"
                >
                  <AlertTitle class="text-yellow-800 dark:text-yellow-300 font-medium">
                    数值输入提示!
                  </AlertTitle>
                  <AlertDescription class="text-yellow-700 dark:text-yellow-400">
                    循环圈数至少需要20圈进行参数标定。
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup>
import { ChevronDown } from 'lucide-vue-next'
import { ref, watch } from 'vue'

const props = defineProps({
  // 循环参数对象
  cycleParams: {
    type: Object,
    required: true,
    default: () => ({
      startCycleType: 'auto',
      startCycleValue: 0,
    }),
  },
  // 是否禁用输入
  isDisabled: {
    type: Boolean,
    default: false,
  },
  maxValue: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:cycle-params'])

const localParams = ref({
  startCycleType: props.cycleParams.startCycleType || 'auto',
  startCycleValue: props.cycleParams.startCycleValue || 0,
})

watch(
  () => props.cycleParams,
  (newParams) => {
    localParams.value = {
      startCycleType: newParams.startCycleType || 'auto',
      startCycleValue: newParams.startCycleValue || 0,
    }
  },
  { deep: true, immediate: true },
)
const isShowAlert = ref(false)
const onCycleInputFocus = () => {
  isShowAlert.value = true
}
const onCycleInputBlur = () => {
  isShowAlert.value = false
}
const updateParams = () => {
  // const cycleNum = localParams.value.startCycleValue % 20
  if (props.maxValue - localParams.value.startCycleValue < 20) {
    localParams.value.startCycleValue = props.maxValue - 20
  }
  if (localParams.value.startCycleType === 'auto') {
    localParams.value.startCycleValue = 0
  }
  emit('update:cycle-params', localParams.value)
}
</script>
