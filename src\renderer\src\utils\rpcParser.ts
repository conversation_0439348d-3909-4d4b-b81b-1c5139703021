interface OutResultType {
  [key:string]:Object
}

interface InputResultType {
  key_type_pairs: {[key:string]:number}
  key_value_pairs: {[key:string]:string}
}

export enum ValueType {
  String = 7,
  Json = 8,
  JsonArray = 9
}

export function outParse(response) {
  const parseResult:OutResultType = {};
  try{
    if (response.status_code == 200) {
      const resultNameEntries = Object.entries(response.key_type_pairs);
      resultNameEntries.forEach(([key, value]) => {
        parseResult[key] = parserSelector(value as string)(response.key_value_pairs[key] as any)
      });
    }else if(response.status_code == 107){
      parseResult["message"] = response.message;
    }
  }catch(error){
    console.log(error)
  };
  return parseResult;

}

export function inputParse(params, valueTypes?) {
  const parseResult: InputResultType = {
    key_type_pairs: {},
    key_value_pairs: {}
  }

  try {
    const paramsEntries = Object.entries(params)
    paramsEntries.forEach(([key, value]) => {
      // 添加类型检查
      if (value === undefined || value === null) {
        console.warn(`参数 ${key} 的值为空`)
        return
      }

      if (valueTypes && valueTypes[key]) {
        parseResult.key_type_pairs[key] = valueTypes[key]
      } else {
        // 根据值类型自动判断
        if (typeof value === 'object') {
          parseResult.key_type_pairs[key] = Array.isArray(value) ?
            ValueType.JsonArray : ValueType.Json
        } else {
          parseResult.key_type_pairs[key] = ValueType.String
        }
      }

      parseResult.key_value_pairs[key] =
        typeof value === 'object' ? JSON.stringify(value) : String(value)
    })
  } catch (error) {
    console.error('参数解析错误:', error)
    throw error
  }

  return parseResult
}

function parserSelector(type:string):Function {
  switch (type) {
    case "STRING":
      return stringPaser;
    case "JSON":
      return jsonPaser
    case "JSONARRAY":
      return jsonArrayPaser;
    default:
      //return ()=>{};
      return stringPaser;
  }

}

function stringPaser(content:any) {
  try {
    return content.toString();
  } catch (error) {
    console.error('Error parsing String:', error);
  }
  return ""
}

function jsonPaser(content:any) {
  try {
    return JSON.parse(content);
  } catch (error) {
    console.error('Error parsing JSON:', error);
  }
  return null
}

function jsonArrayPaser(content:any) {
  try {
    const jsonArray = JSON.parse(content);
    if (Array.isArray(jsonArray)) {
      return jsonArray
    } else {
      console.error('Invalid JSON Array');
    }
  } catch (error) {
    console.error('Error parsing JSON:', error);
  }
  return []
}
