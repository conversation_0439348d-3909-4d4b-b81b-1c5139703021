<template>
  <div class="flex flex-col h-full">
    <header>
      <Header />
    </header>
    <main class="flex-grow">
      <Home v-if="$route.path === '/ai'" />
      <router-view />
    </main>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Header, Home } from './components'
</script>

<style lang="scss" scoped></style>
