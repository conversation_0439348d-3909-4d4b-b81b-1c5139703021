<template>
  <Card class="border-muted/50 shadow-sm hover:shadow-md transition-all duration-200">
    <CardHeader class="pb-3">
      <div class="flex items-center justify-between">
        <CardTitle class="text-sm flex items-center">
          <div
            :class="[
              'flex items-center justify-center w-6 h-6 rounded-md mr-2 transition-colors',
              'bg-primary/10 text-primary',
            ]"
          >
            <component :is="iconComponent" class="h-3.5 w-3.5" />
          </div>
          {{ title }}
        </CardTitle>

        <!-- 可选的操作按钮 -->
        <div v-if="$slots.actions" class="flex items-center space-x-1">
          <slot name="actions" />
        </div>
      </div>

      <!-- 可选的描述 -->
      <CardDescription v-if="description" class="text-xs mt-1">
        {{ description }}
      </CardDescription>
    </CardHeader>

    <CardContent class="space-y-3">
      <slot />
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@renderer/components/ui/card'
import {
  Boxes,
  Layout,
  Menu,
  Navigation,
  Palette,
  Settings,
  Shield,
  Workflow,
  Wrench,
} from 'lucide-vue-next'
import { computed } from 'vue'

interface Props {
  title: string
  icon: string
  description?: string
}

const props = defineProps<Props>()

// 图标组件映射
const iconMap = {
  Layout,
  Menu,
  Workflow,
  Navigation,
  Boxes,
  Shield,
  Settings,
  Palette,
  Wrench,
}

const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || Settings
})
</script>
