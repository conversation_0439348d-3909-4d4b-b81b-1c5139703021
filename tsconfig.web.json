{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.vue",
    "src/preload/*.d.ts"
  ],
  "compilerOptions": {
    "noEmit": true,
    "baseUrl": ".",
    "paths": {
      "@renderer/*": ["src/renderer/src/*"],
      "@components/*": ["src/renderer/src/components/*"],
      "@lib/*": ["src/renderer/src/lib/*"],
      "@styles/*": ["src/renderer/src/config/styles/*"],
    }
  }
}
