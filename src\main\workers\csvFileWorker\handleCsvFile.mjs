// 根据表头信息，我们把新威，蓝电两种格式的文件，转化成我们自己的格式
// const neware = ['循环号', '电流(A)', '电压(V)', '容量(Ah)', '绝对时间'] // 新威
// const lanhe = ['电流/A', '容量/Ah', '电压/V', '系统时间', '循环序号'] // 蓝电
const getFileType = (header) => {
  if (header.includes('ocv') && header.includes('soc')) {
    return 'SOC-OCV'
  }
  const neware = ['循环号', '电流', '电压', '容量', '绝对时间'] // 新威
  const lanhe = ['电流', '容量', '电压', '系统时间', '循环序号'] // 蓝电

  // 统计匹配的关键词数量
  const newWareCount = neware.filter((keyword) => header.includes(keyword)).length
  const lanHeCount = lanhe.filter((keyword) => header.includes(keyword)).length

  // 判断类型
  if (newWareCount >= 4) return 'neware'
  if (lanHeCount >= 4) return 'lanhe'
  return 'default'
}
const headerList = ['cycle', 'time', 'current', 'voltage', 'capacity']
const headerType = ['float', 'float', 'float', 'float', 'float']
const headerUnit = ['/', 's', 'A', 'V', 'mAh']
const unit_value = {
  mA: 1e-3,
  mV: 1e-3,
  Ah: 1000,
  A: 1,
  V: 1,
  mAh: 1,
  '': 1,
}
// 提取单位
const extractUnit = (header) => {
  const unit = header.replace(/[^a-zA-Z]/g, '')
  return unit || ''
}
// 将时间差转化成 【时：分：秒】的格式
const formatTimeDiff = (milliseconds) => {
  // 计算总秒数
  const totalSeconds = Math.floor(milliseconds / 1000)
  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  // 格式化为两位数
  const pad = (num) => num.toString().padStart(2, '0')

  return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
}
const convertToCSV = (array) => {
  return array.map((row) => row.map((field) => String(field)).join(',')).join('\n')
}
// 将内容转化成后台需要的格式
const handleCsvFile = (dataList, callback) => {
  try {
    const headerCol = dataList[0]
    const fileType = getFileType(headerCol.join(''))
    //const fileType = 'SOC-OCV'
    // 如果是默认格式，直接转化为内容数据返回
    if (fileType === 'default') {
      const rowCount = dataList.length - 3
      const currentData = new Float32Array(rowCount)
      const voltageData = new Float32Array(rowCount)
      const timeData = new Float32Array(rowCount)
      const contentRows = dataList.slice(3)
      const headerMap = new Map()
      for (let i = 0; i < headerCol.length; i++) {
        const header = headerCol[i]
        if (header)
          headerMap.set(header, {
            idx: i,
            isTime: header === 'time',
            isCapacity: header === 'capacity',
            isCurrent: header === 'current',
            isVoltage: header == 'voltage',
          })
      }
      const lastRow = contentRows[contentRows.length - 1]
      const maxCycle = parseInt(lastRow[headerMap.get('cycle')?.idx])
      const capacityData = new Float32Array(maxCycle)

      for (let i = 0; i < rowCount; i++) {
        const row = contentRows[i]
        const currentCycle = row[headerMap.get('cycle')?.idx] - 1
        for (let j = 0; j < headerList.length; j++) {
          const key = headerList[j]
          const mapVal = headerMap.get(key)
          if (!mapVal) continue
          const rawValue = row[mapVal.idx]
          if (mapVal.isTime) {
            const [h, m, s] = rawValue.split(':')
            const date = new Date(0)
            date.setUTCHours(h)
            date.setUTCMinutes(m)
            date.setUTCSeconds(s)
            timeData[i] = date.getTime()
          } else if (mapVal.isCapacity) {
            if (rawValue > capacityData[currentCycle]) {
              capacityData[currentCycle] = Math.round(rawValue * 1e-3 * 100) / 100
            }
          } else {
            if (mapVal.isCurrent) {
              currentData[i] = rawValue
            } else if (mapVal.isVoltage) {
              voltageData[i] = rawValue
            }
          }
        }
      }
      callback({
        type: 'data',
        data: {
          type: 'default',
          current: currentData,
          voltage: voltageData,
          time: timeData,
          capacity: capacityData,
        },
        error: null,
      })
    } else if (fileType === 'SOC-OCV') {
      const headerMap = new Map()
      for (let i = 0; i < headerCol.length; i++) {
        const header = headerCol[i]
        if (header)
          headerMap.set(header, {
            idx: i,
            isSoc: header === 'soc',
            isOcv: header === 'ocv',
          })
      }
      const rowCount = dataList.length - 1
      const sococvData = new Array(rowCount)
      // const socData = new Float32Array(rowCount)
      // const ocvData = new Float32Array(rowCount)
      const contentRows = dataList.slice(1)
      for (let i = 0; i < rowCount; i++) {
        const row = contentRows[i]
        let soc = null
        let ocv = null

        for (let j = 0; j < headerCol.length; j++) {
          const key = headerCol[j]
          const mapVal = headerMap.get(key)
          if (!mapVal) continue
          const rawValue = row[mapVal.idx]
          if (mapVal.isSoc) {
            soc = parseFloat(rawValue)
          } else if (mapVal.isOcv) {
            ocv = parseFloat(rawValue)
          }
        }
        sococvData[i] = [soc, ocv]
      }
      callback({
        type: 'data',
        data: {
          type: 'SOC-OCV',
          sococv: sococvData,
        },
        error: null,
      })
    } else {
      const rowCount = dataList.length - 1
      const defaultData = new Array(rowCount + 3)
      const currentData = new Float32Array(rowCount)
      const voltageData = new Float32Array(rowCount)
      const timeData = new Float32Array(rowCount)
      const contentRows = dataList.slice(1)
      const headerMap = new Map()

      // 建立字段映射，识别各列对应字段
      for (let i = 0; i < headerCol.length; i++) {
        const header = headerCol[i]
        const unit = extractUnit(header)
        let key = ''
        if (header.includes('循环')) key = 'cycle'
        if (header.includes('时间')) key = 'time'
        if (header.includes('电流')) key = 'current'
        if (header.includes('电压')) key = 'voltage'
        if (header.includes('容量')) key = 'capacity'
        const unitFactor = unit_value[unit] || 1
        if (key)
          headerMap.set(key, {
            idx: i,
            unit: unitFactor,
            isTime: key === 'time',
            isCapacity: key === 'capacity',
            isCurrent: key === 'current',
            isVoltage: key == 'voltage',
          })
      }
      const lastRow = contentRows[contentRows.length - 1]
      const maxCycle = parseInt(lastRow[headerMap.get('cycle')?.idx])
      const capacityData = new Float32Array(maxCycle)

      // 相对时间计算基准
      let initTime = null
      const timeColumnIndex = headerMap.get('time')?.idx
      if (timeColumnIndex !== undefined)
        initTime = new Date(contentRows[0][timeColumnIndex]).getTime()

      for (let i = 0; i < rowCount; i++) {
        const row = contentRows[i]
        const newRow = []
        const currentCycle = row[headerMap.get('cycle')?.idx] - 1
        for (let j = 0; j < headerList.length; j++) {
          const key = headerList[j]
          const mapVal = headerMap.get(key)
          if (!mapVal) continue
          const rawValue = row[mapVal.idx]
          const unitFactor = mapVal.unit
          if (mapVal.isTime && initTime !== null) {
            const relativeTime = new Date(rawValue).getTime() - initTime
            timeData[i] = relativeTime
            newRow.push(formatTimeDiff(relativeTime))
          } else if (mapVal.isCapacity) {
            const capacityValue = Math.round(rawValue * unitFactor * 100) / 100
            newRow.push(capacityValue)
            if (capacityValue > capacityData[currentCycle]) {
              capacityData[currentCycle] = capacityValue
            }
          } else {
            const value = rawValue * unitFactor
            newRow.push(value)
            if (mapVal.isCurrent) {
              currentData[i] = value
            } else if (mapVal.isVoltage) {
              voltageData[i] = value
            }
          }
        }
        defaultData[i + 3] = newRow
      }
      // 添加标准头部信息
      defaultData[0] = headerList
      defaultData[1] = headerType
      defaultData[2] = headerUnit
      //const contentData = addHeaderKey(headerList, defaultData.slice(3))
      const csvData = convertToCSV(defaultData)
      callback({
        type: 'data',
        data: {
          type: 'other',
          csv: csvData,
          current: currentData,
          voltage: voltageData,
          time: timeData,
          capacity: capacityData,
        },
        error: null,
      })
    }

    //await uploadStringData2(csvdata, fileName, serverUrl, userId)
  } catch (error) {
    callback({ type: 'error', error: error, data: null })
  }
}
export default handleCsvFile
