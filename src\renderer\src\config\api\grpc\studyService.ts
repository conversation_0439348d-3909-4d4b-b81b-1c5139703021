import { BaseService } from './baseService'

// 同步服务请求响应
export interface DefaultServiceResponse {
  status: string
  message: string
  statusCode: string
  keyTypePairs: Record<string, string>
  keyValuePairs: Record<string, string>
}
export interface MaterialResponse {
  statusCode: number
  message: string
  keyTypePairs: Record<string, any>
  keyValuePairs: Record<string, any>
}

export interface SubmitResponse {
  statusCode: string | number
  message: string
  taskId: string
}

class StudyService extends BaseService {
  /**
   * 获取选择结构
   */
  async getHpMLModel(): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('getHpMLModel', '', false, {})
    return {
      statusCode: Number(response.statusCode),
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 获取模型详情
   */
  async parseModel(path: string): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>('parseModel', '', true, {
      key_type_pairs: {
        model_file: 'String',
      },
      key_value_pairs: {
        model_file: path,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }
  /**
   * 推理预测
   */
  // async eolPredict(cycles: string, modelName: string, testPath: string): Promise<SubmitResponse> {
  //   const response = await this.submit<SubmitResponse>('eolPredict', '', true, {
  //     key_type_pairs: {
  //       predict_used_cycles: 'Int',
  //       model_name: 'String',
  //       test_data_path: 'String',
  //     },
  //     key_value_pairs: {
  //       predict_used_cycles: cycles,
  //       model_name: modelName,
  //       test_data_path: testPath,
  //     },
  //   })
  //   return {
  //     statusCode: response.statusCode,
  //     message: response.message,
  //     taskId: response.taskId,
  //   }
  // }
  /**
   * 新的推理预测
   */
  async eolPredict(modelFile: string, inputDatas: string): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>('eolPredict', '', true, {
      key_type_pairs: {
        model_file: 'String',
        input_datas: 'Jsonarray',
      },
      key_value_pairs: {
        model_file: modelFile,
        input_datas: inputDatas,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }
  /**
   * 读圈数的接口
   */
  async checkInputBatteryData(path: string): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>('checkInputBatteryData', '', true, {
      key_type_pairs: {
        input_datas: 'Jsonarray',
      },
      key_value_pairs: {
        input_datas: path,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }

  /**
   * 模型训练接口
   */
  async eolTrainApi(
    serverName: string,
    serverId: string,
    isSave: boolean,
    modelParams: string,
    trainParams: string,
    inputDatas: string,
  ): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>(serverName, serverId, isSave, {
      key_type_pairs: {
        model_params: 'Jsonarray',
        train_params: 'Jsonarray',
        input_datas: 'Jsonarray',
      },
      key_value_pairs: {
        model_params: modelParams,
        train_params: trainParams,
        input_datas: inputDatas,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }

  /**
   * 模型训练结果导出
   */
  async exportEoLModelApi(taskId: string): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>('exportEoLModel', '', true, {
      key_type_pairs: {
        model_task_id: 'String',
      },
      key_value_pairs: {
        model_task_id: taskId,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }
}
/**
 * 创建 TaskService 实例
 */
export function createStudyService(): StudyService {
  return new StudyService()
}
