import { defineStore } from 'pinia'
import { ref } from 'vue'
import { createServerService } from '../../config/api/grpc/serverService'
import { Server, ServerStatus, ServerUsage } from '../../config/types/api/server'

export const useServerStore = defineStore('server', () => {
  const loading = ref(false)
  const loadingUsage = ref(false)
  const servers = ref<Server[]>([])
  const serverUsages = ref<ServerUsage[]>([])
  const refreshTimer = ref<number>(5) // 服务列表刷新间隔（秒）
  const serverService = createServerService() // 创建一次实例供所有方法使用

  // 刷新服务器列表
  const updateServerList = async (serverId?: string) => {
    loading.value = true
    const res = await serverService.getServerList(serverId)
    if (res.status === 'Success' && res.serverResult?.serverList) {
      servers.value = res.serverResult.serverList
    }
    loading.value = false
  }

  // 刷新服务器状态
  const updateServerUsage = async (serverId: string) => {
    loadingUsage.value = true
    const res = await serverService.getServerUsage(serverId)
    if (res.status === 'Success' && res.serverUsage) {
      const server = servers.value.find((s) => s.serverId === serverId)
      if (!server) return
      const serverUsage = res.serverUsage
      serverUsage.serverId = server.serverId
      serverUsage.serverName = server.serverName
      const index = serverUsages.value.findIndex((s) => s.serverId === serverUsage.serverId)
      if (index !== -1) {
        serverUsages.value = serverUsages.value.map((s) =>
          s.serverId === serverUsage.serverId ? serverUsage : s,
        )
      } else {
        serverUsages.value.push(serverUsage)
      }
    }
    loadingUsage.value = false
  }

  // 获取状态显示文本
  const getStatusText = (status: ServerStatus) => {
    const statusText: Record<ServerStatus, string> = {
      Running: '正常运行',
      Stopped: '已停止运行',
      Expired: '已过期',
      Overloaded: '服务器超载',
      Stay: '原始状态',
    }
    return statusText[status] || status
  }

  // 获取状态对应的样式
  const getStatusClass = (status: ServerStatus) => {
    const classes: Record<ServerStatus, string> = {
      Running: 'bg-green-500 hover:bg-green-600',
      Stopped: 'bg-blue-500 hover:bg-blue-600',
      Expired: 'bg-yellow-500 hover:bg-yellow-600',
      Overloaded: 'bg-orange-500 hover:bg-orange-600',
      Stay: 'bg-gray-500 hover:bg-gray-600',
    }
    return classes[status] || 'bg-gray-500 hover:bg-gray-600'
  }

  // 设置刷新间隔
  const setRefreshTimer = (interval: number) => {
    refreshTimer.value = interval
  }
  return {
    loading,
    loadingUsage,
    servers,
    serverUsages,
    refreshTimer,
    updateServerList,
    updateServerUsage,
    getStatusText,
    getStatusClass,
    setRefreshTimer,
  }
})
