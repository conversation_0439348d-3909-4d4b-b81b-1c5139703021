import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useFlowsStore } from '@renderer/store'
import emitter from '@renderer/utils/mitt'

/**
 * 节点参数持久化 Hook
 * @param nodeData 节点数据对象
 * @param options 配置选项
 * @returns 参数操作方法和状态
 */
export function useNodeParams<T = any>(
  nodeData: any,
  options: {
    autoLoad?: boolean // 是否自动加载参数
    autoSave?: boolean // 是否自动保存参数（监听变化）
    defaultValue?: T // 默认值
    onLoad?: (params: T) => void // 加载参数后的回调
    onSave?: (params: T) => void // 保存参数后的回调
    onDelete?: () => void // 删除参数后的回调
  } = {},
) {
  const {
    autoLoad = true,
    autoSave = false,
    defaultValue = {} as T,
    onLoad,
    onSave,
    onDelete,
  } = options

  const flowsStore = useFlowsStore()
  const params = ref<T>(defaultValue as T)
  const isLoaded = ref(false)
  const isSaving = ref(false)

  // 获取工作流ID和节点ID
  const getIds = () => {
    if (!nodeData) return { workflowId: null, nodeId: null }

    const workflowId = nodeData.data?.workflowId
    const nodeId = nodeData.id

    return { workflowId, nodeId }
  }

  // 加载参数
  const loadParams = () => {
    const { workflowId, nodeId } = getIds()
    if (!workflowId || !nodeId) return false

    try {
      const savedParams = flowsStore.getNodeParams(workflowId, nodeId)
      if (savedParams && Object.keys(savedParams).length > 0) {
        params.value = savedParams as T
        isLoaded.value = true
        onLoad?.(params.value)
        return true
      }
    } catch (error) {
      console.error('加载节点参数失败:', error)
    }
    return false
  }

  // 保存参数
  const saveParams = (newParams?: Partial<T>) => {
    const { workflowId, nodeId } = getIds()
    if (!workflowId || !nodeId) return false

    try {
      isSaving.value = true
      // 如果提供了新参数，则合并
      if (newParams) {
        params.value = {
          ...params.value,
          ...newParams,
        } as T
      }

      // 确保保存的是最新的参数值
      const paramsToSave = JSON.parse(JSON.stringify(params.value))
      flowsStore.saveNodeParams(workflowId, nodeId, paramsToSave)

      // 触发节点数据更新事件，确保UI能够反映变化
      if (nodeData) {
        // 更新节点数据中的params
        if (!nodeData.data) {
          // eslint-disable-next-line no-param-reassign
          nodeData.data = {}
        }
        if (!nodeData.data.params) {
          nodeData.data.params = {}
        }
        nodeData.data.params = paramsToSave

        // 触发自定义事件通知其他组件
        emitter.emit('node-params-updated', {
          detail: {
            nodeId,
            workflowId,
            params: paramsToSave,
          },
        })
      }

      onSave?.(params.value)
      return true
    } catch (error) {
      console.error('保存节点参数失败:', error)
      return false
    } finally {
      isSaving.value = false
    }
  }

  // 删除参数
  const deleteParams = () => {
    const { workflowId, nodeId } = getIds()
    if (!workflowId || !nodeId) return false

    try {
      flowsStore.deleteNodeParams(workflowId, nodeId)
      params.value = defaultValue as T
      isLoaded.value = false
      onDelete?.()
      return true
    } catch (error) {
      console.error('删除节点参数失败:', error)
      return false
    }
  }

  // 获取当前节点的类型
  const getCurrentNodeType = () => {
    if (!nodeData || !nodeData.data) return null
    return nodeData.data.type
  }

  // 获取当前节点的输入类型
  const getCurrentNodeInputTypes = () => {
    if (!nodeData || !nodeData.data) return []
    return nodeData.data.inputType || []
  }

  // 获取当前节点的输出类型
  const getCurrentNodeOutputTypes = () => {
    if (!nodeData || !nodeData.data) return []
    return nodeData.data.outputType || []
  }

  /**
   * 检查两个节点之间的类型兼容性
   * @param sourceNode 源节点
   * @param targetNode 目标节点
   * @returns 是否兼容
   */
  const checkNodesCompatibility = (sourceNode: any, targetNode: any) => {
    if (!sourceNode || !sourceNode.data || !targetNode || !targetNode.data) {
      return false
    }

    const sourceType = sourceNode.data.type
    const targetInputTypes = targetNode.data.inputType || []

    // 只检查目标节点的输入类型是否包含源节点的类型
    return targetInputTypes.includes(sourceType)
  }

  /**
   * 获取所有连接到当前节点的输入节点
   * @param filterByCompatibility 是否按类型兼容性过滤
   * @returns 输入节点数组
   */
  const getInputNodes = (filterByCompatibility = true) => {
    const { workflowId, nodeId } = getIds()
    if (!workflowId || !nodeId) return []

    try {
      // 获取当前工作流
      const workflow = flowsStore.getWorkflow(workflowId)
      if (!workflow || !workflow.edges || !workflow.nodes) return []

      // 获取当前节点
      const currentNode = workflow.nodes.find((n) => n.id === nodeId)
      if (!currentNode) return []

      // 查找所有指向当前节点的边（输入连接）
      const inputEdges = workflow.edges.filter((e) => e.target === nodeId)

      // 获取所有输入节点
      return inputEdges
        .map((edge) => {
          const sourceNodeId = edge.source
          const sourceNode = workflow.nodes.find((n) => n.id === sourceNodeId)

          if (!sourceNode) return null

          // 如果需要过滤兼容性
          if (filterByCompatibility && !checkNodesCompatibility(sourceNode, currentNode)) {
            return null
          }

          return {
            edgeId: edge.id,
            nodeId: sourceNodeId,
            nodeType: sourceNode.data?.type,
            node: sourceNode,
            params: flowsStore.getNodeParams(workflowId, sourceNodeId) || {},
          }
        })
        .filter(Boolean) // 过滤掉null值
    } catch (error) {
      console.error('获取输入节点失败:', error)
      return []
    }
  }

  /**
   * 获取所有从当前节点连出的输出节点
   * @param filterByCompatibility 是否按类型兼容性过滤
   * @returns 输出节点数组
   */
  const getOutputNodes = (filterByCompatibility = true) => {
    const { workflowId, nodeId } = getIds()
    if (!workflowId || !nodeId) return []

    try {
      // 获取当前工作流
      const workflow = flowsStore.getWorkflow(workflowId)
      if (!workflow || !workflow.edges || !workflow.nodes) return []

      // 获取当前节点
      const currentNode = workflow.nodes.find((n) => n.id === nodeId)
      if (!currentNode) return []

      // 查找所有从当前节点出发的边（输出连接）
      const outputEdges = workflow.edges.filter((e) => e.source === nodeId)

      // 获取所有输出节点
      return outputEdges
        .map((edge) => {
          const targetNodeId = edge.target
          const targetNode = workflow.nodes.find((n) => n.id === targetNodeId)

          if (!targetNode) return null

          // 如果需要过滤兼容性
          if (filterByCompatibility && !checkNodesCompatibility(currentNode, targetNode)) {
            return null
          }

          return {
            edgeId: edge.id,
            nodeId: targetNodeId,
            nodeType: targetNode.data?.type,
            node: targetNode,
            params: flowsStore.getNodeParams(workflowId, targetNodeId) || {},
          }
        })
        .filter(Boolean) // 过滤掉null值
    } catch (error) {
      console.error('获取输出节点失败:', error)
      return []
    }
  }

  /**
   * 获取特定类型的输入节点
   * @param type 节点类型
   * @returns 匹配类型的节点数组
   */
  const getInputNodesByType = (type: string) => {
    const inputs = getInputNodes(false) // 不过滤兼容性，只按类型过滤
    return inputs.filter((input) => input.nodeType === type)
  }

  /**
   * 获取特定类型的输入节点参数
   * @param type 节点类型
   * @returns 匹配类型的第一个节点，如果没有找到则返回null
   */
  const getInputParamsByType = (type: string) => {
    const matchedNodes = getInputNodesByType(type)
    return matchedNodes.length > 0 ? matchedNodes[0] : null
  }

  /**
   * 获取所有兼容的输入节点参数
   * 根据节点定义的inputType和outputType自动判断兼容性
   */
  const getCompatibleInputParams = () => {
    return getInputNodes(true) // 过滤兼容性
  }

  // 自动加载参数
  if (autoLoad) {
    onMounted(() => {
      loadParams()
    })
  }

  // 如果设置了自动保存，监听参数变化
  if (autoSave) {
    watch(
      params,
      () => {
        saveParams()
      },
      { deep: true },
    )
  }

  // 组件卸载时自动保存
  onUnmounted(() => {
    if (autoSave) {
      saveParams()
    }
  })

  return {
    params,
    isLoaded,
    isSaving,
    loadParams,
    saveParams,
    deleteParams,
    getCurrentNodeType,
    getCurrentNodeInputTypes,
    getCurrentNodeOutputTypes,
    getInputNodes,
    getOutputNodes,
    getInputNodesByType,
    getInputParamsByType,
    getCompatibleInputParams,
    checkNodesCompatibility,
  }
}
