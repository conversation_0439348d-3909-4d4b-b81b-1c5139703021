<!-- DynamicForm.vue -->
<template>
  <form @submit="onSubmit" class="space-y-4">
    <div v-for="(row, rowIndex) in computedRows" :key="rowIndex" class="grid gap-4" :class="rowClass">
      <FormField
        v-for="field in row"
        :key="field.name"
        v-slot="{ componentField, errorMessage }"
        :name="field.name"
      >
        <FormItem>
          <FormLabel v-if="field.label">{{ field.label }}</FormLabel>
          <FormControl>
            <!-- 非 Select 组件 -->
            <component
              v-if="!isSelect(field.component)"
              :is="resolveComponent(field.component)"
              v-bind="{
                ...componentField,
                ...field.props,
                placeholder: field.placeholder,
                disabled: field.disabled,
                'onUpdate:value': (val) => handleFieldUpdate(field.name, val)
              }"
            />
            <!-- shadcn-vue Select 组件 -->
            <Select
              v-else
              v-bind="{
                ...componentField,
                ...field.props,
                disabled: field.disabled,
                'onUpdate:value': (val) => handleFieldUpdate(field.name, val)
              }"
            >
              <SelectTrigger>
                <SelectValue :placeholder="field.placeholder" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup v-if="field.props?.options">
                  <SelectItem
                    v-for="option in field.props.options"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage v-if="errorMessage">{{ errorMessage }}</FormMessage>
        </FormItem>
      </FormField>
    </div>

    <div class="flex gap-2">
      <Button type="submit" :disabled="isSubmitting">提交</Button>
      <Button type="button" variant="outline" @click="resetFormToEmpty">重置</Button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { computed,onMounted } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'

// 定义字段配置的类型
interface FieldConfig {
  name: string
  label?: string
  component: string
  props?: Record<string, any>
  placeholder?: string
  disabled?: boolean
  validation?: z.ZodType<any>
  colSpan?: number
  defaultValue?: any
}

// 定义组件的 props
const props = withDefaults(
  defineProps<{
    fields: FieldConfig[]
    rows?: number
    cols?: number
    onSubmit?: (values: Record<string, any>) => void
  }>(),
  {
    rows: 1,
    cols: 2,
    onSubmit: () => {},
  }
)

// 解析组件
const resolveComponent = (component: string) => {
  return component || 'Input'
}

// 判断是否为 Select 组件
const isSelect = (component: string) => {
  return component === 'Select'
}

// 计算每行列数和布局
const rowClass = computed(() => `grid-cols-${props.cols}`)
const computedRows = computed(() => {
  const rows = []
  let currentRow: FieldConfig[] = []

  props.fields.forEach((field) => {
    if (currentRow.length >= props.cols) {
      rows.push(currentRow)
      currentRow = []
    }
    currentRow.push(field)
  })

  if (currentRow.length > 0) {
    rows.push(currentRow)
  }

  return rows.slice(0, props.rows)
})

// 从 fields 中提取初始值
const formInitialValues = computed(() => {
  const values: Record<string, any> = {}
  props.fields.forEach((field) => {
    if (field.defaultValue !== undefined) {
      values[field.name] = field.defaultValue
    }
  })
  console.log('Initial values:', values)
  return values
})

// 创建清空值对象
const emptyValues = computed(() => {
  const values: Record<string, any> = {}
  props.fields.forEach((field) => {
    if (field.component === 'Input' && field.props?.type === 'number') {
      values[field.name] = null // 数字类型清空为 null
    } else {
      values[field.name] = '' // 字符串或 Select 清空为 ''
    }
  })
  return values
})

// 创建动态校验 schema
const validationSchema = computed(() => {
  const schemaObj: Record<string, z.ZodType<any>> = {}
  props.fields.forEach((field) => {
    schemaObj[field.name] = field.validation || z.string().optional()
  })
  return toTypedSchema(z.object(schemaObj))
})

// 表单状态管理
const { handleSubmit, resetForm, values, isSubmitting, setFieldValue,setValues } = useForm({
  validationSchema,
  initialValues: formInitialValues,
})

// 处理字段更新
const handleFieldUpdate = (fieldName: string, value: any) => {
  console.log(`Field ${fieldName} updated to:`, value)
  setFieldValue(fieldName, value)
}

// 提交处理
const onSubmit = handleSubmit((formValues) => {
  console.log('Submitted values:', formValues)
  props.onSubmit?.(formValues)
})

// 重置表单到空值
const resetFormToEmpty = () => {
  console.log('Resetting to empty:', emptyValues.value)
  resetForm({ values: emptyValues.value })
}

onMounted(() => {
  setValues(formInitialValues.value)
})
defineExpose({ values, resetForm: resetFormToEmpty })
</script>
