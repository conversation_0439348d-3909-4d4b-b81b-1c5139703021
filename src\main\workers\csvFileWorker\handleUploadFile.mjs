import crypto from 'crypto'
import * as fs from 'fs'
import { Readable } from 'stream'

const handleUploadFile = (input, inputType, chunkSize, callBack) => {
  // 验证输入类型
  if (inputType !== 'file' && inputType !== 'data') {
    throw new Error('Invalid inputType. Must be "file" or "data"')
  }

  // 如果是文件类型但输入不是字符串，或者文件不存在
  if (inputType === 'file' && (typeof input !== 'string' || !fs.existsSync(input))) {
    throw new Error('Invalid file path')
  }
  let totalSize
  let totalSha256
  let chunkCount = 0

  if (inputType === 'file') {
    // 处理文件上传
    const fileStats = fs.statSync(input)
    totalSize = fileStats.size
    totalSha256 = crypto.createHash('sha256').update(fs.readFileSync(input)).digest('hex')
  } else {
    // 处理数据上传
    const stringData = typeof input === 'string' ? input : JSON.stringify(input)
    totalSize = Buffer.byteLength(stringData)
    totalSha256 = crypto.createHash('sha256').update(stringData).digest('hex')
  }

  chunkCount = Math.ceil(totalSize / chunkSize)
  let chunkId = 0
  if (inputType === 'file') {
    // 文件流处理
    const fileStream = fs.createReadStream(input, { highWaterMark: chunkSize })

    fileStream.on('data', (chunk) => {
      const isLast = chunkCount === chunkId + 1
      callBack({
        type: 'data',
        data: {
          chunk,
          isLast,
          chunkId,
          totalSize,
          totalSha256,
        },
        msg: null,
      })
      chunkId++
    })
    fileStream.on('end', () => {
      callBack({
        type: 'data',
        data: null,
        msg: null,
      })
    })

    fileStream.on('error', (error) => {
      callBack({
        type: 'error',
        data: null,
        msg: error,
      })
    })
  } else {
    // 数据处理
    const stringData = typeof input === 'string' ? input : JSON.stringify(input)
    const stringStream = new Readable({
      read() {
        const start = chunkId * chunkSize
        const end = Math.min(start + chunkSize, stringData.length)
        if (start >= stringData.length) {
          this.push(null) // 流结束
          return
        }

        const chunk = stringData.slice(start, end)
        const isLast = end >= stringData.length
        this.push(chunk)
        callBack({
          type: 'data',
          data: {
            chunk,
            isLast,
            chunkId,
            totalSize,
            totalSha256,
          },
          msg: null,
        })
        chunkId++
      },
    })

    stringStream.resume()

    stringStream.on('end', () => {
      callBack({
        type: 'end',
        data: null,
        msg: null,
      })
    })
    stringStream.on('error', (error) => {
      callBack({
        type: 'error',
        data: null,
        msg: error,
      })
    })
  }
}
export default handleUploadFile
