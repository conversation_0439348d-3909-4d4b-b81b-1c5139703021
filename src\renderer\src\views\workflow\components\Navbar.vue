<template>
  <div class="h-10 flex items-center flex-row bg-muted/50 border-b border-border px-2.5">
    <div
      class="h-full flex items-center flex-row px-2.5 overflow-x-auto overflow-y-hidden whitespace-nowrap scrollbar-thin scrollbar-track-muted scrollbar-thumb-border hover:scrollbar-thumb-muted-foreground scrollbar-thumb-rounded-sm"
    >
      <div
        v-for="tag in navbarStore.tags"
        :key="tag.id"
        class="flex flex-row items-center justify-between min-w-24 px-2 py-1.5 m-1 rounded-md cursor-pointer text-xs transition-all duration-200"
        :class="[
          tag.id === navbarStore.activeTagId
            ? 'bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-foreground ring-1 ring-primary/30 dark:ring-primary/50 font-medium'
            : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 ring-1 ring-gray-200 dark:ring-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700',
        ]"
        @click="handleTagClick(tag)"
        @contextmenu.prevent="handleContextMenu($event, tag.id)"
      >
        <div class="max-w-20 truncate block w-full">{{ tag.title }}</div>
        <div
          v-if="!tag.isFixed"
          class="inline-flex ml-2 cursor-pointer text-gray-500 dark:text-gray-400 hover:text-destructive dark:hover:text-destructive"
          @click.stop="handleTagClose(tag.id)"
        >
          <Icon icon="flowbite:close-outline" :width="16" :height="16" />
        </div>
      </div>
    </div>

    <!-- 右侧菜单按钮 -->
    <div class="ml-auto flex items-center">
      <Button variant="ghost" size="sm" class="h-8 w-8 p-0" @click="handleShowAllMenu">
        <Icon icon="flowbite:dots-horizontal-outline" :width="20" :height="20" />
      </Button>
    </div>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div
        v-if="navbarStore.showContextMenu"
        class="fixed z-50 bg-background border border-border rounded-md shadow-md py-1 min-w-[180px]"
        :style="{
          left: `${navbarStore.contextMenuPosition.x}px`,
          top: `${navbarStore.contextMenuPosition.y}px`,
        }"
      >
        <div class="px-3 py-2 text-xs text-muted-foreground border-b border-border">标签操作</div>
        <div
          class="px-3 py-2 text-sm hover:bg-accent cursor-pointer flex items-center"
          @click="handleTagClose(navbarStore.contextMenuTargetId)"
        >
          <Icon icon="flowbite:close-outline" :width="16" :height="16" class="mr-2" />
          关闭标签
        </div>
        <div
          class="px-3 py-2 text-sm hover:bg-accent cursor-pointer flex items-center"
          @click="handleCloseOtherTags(navbarStore.contextMenuTargetId)"
        >
          <Icon icon="flowbite:close-circle-outline" :width="16" :height="16" class="mr-2" />
          关闭其他标签
        </div>
        <div
          class="px-3 py-2 text-sm hover:bg-accent cursor-pointer flex items-center"
          @click="handleCloseLeftTags(navbarStore.contextMenuTargetId)"
        >
          <Icon icon="flowbite:arrow-left-outline" :width="16" :height="16" class="mr-2" />
          关闭左侧标签
        </div>
        <div
          class="px-3 py-2 text-sm hover:bg-accent cursor-pointer flex items-center"
          @click="handleCloseRightTags(navbarStore.contextMenuTargetId)"
        >
          <Icon icon="flowbite:arrow-right-outline" :width="16" :height="16" class="mr-2" />
          关闭右侧标签
        </div>
        <div
          class="px-3 py-2 text-sm hover:bg-accent cursor-pointer flex items-center"
          @click="handleCloseAllTags"
        >
          <Icon icon="flowbite:close-circle-outline" :width="16" :height="16" class="mr-2" />
          关闭所有标签
        </div>
      </div>
    </Teleport>

    <!-- 全局菜单 -->
    <DropdownMenu v-model:open="showAllMenu">
      <DropdownMenuContent>
        <DropdownMenuItem @click="handleRefreshCurrentPage">
          <Icon icon="flowbite:refresh-outline" :width="16" :height="16" class="mr-2" />
          刷新当前页面
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem @click="handleCloseAllTags">
          <Icon icon="flowbite:close-circle-outline" :width="16" :height="16" class="mr-2" />
          关闭所有标签
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useNavbarStore } from '@renderer/store'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const navbarStore = useNavbarStore()
const router = useRouter()
const showAllMenu = ref(false)

const handleTagClick = (tag) => {
  navbarStore.setActiveTag(tag.id)
  router.push(tag.path)
}

const handleTagClose = (id: string) => {
  navbarStore.removeTag(id)
  navbarStore.hideMenu()
}

// 右键菜单处理
const handleContextMenu = (event: MouseEvent, id: string) => {
  event.preventDefault()
  navbarStore.showMenu(id, event.clientX, event.clientY)

  // 点击其他区域关闭菜单
  document.addEventListener('click', handleDocumentClick, { once: true })
}

// 关闭右键菜单
const handleDocumentClick = () => {
  navbarStore.hideMenu()
}
// 关闭左侧标签
const handleCloseLeftTags = (id: string) => {
  navbarStore.closeLeftTags(id)
  navbarStore.hideMenu()
}
// 关闭其他标签
const handleCloseOtherTags = (id: string) => {
  navbarStore.closeOtherTags(id)
  navbarStore.hideMenu()
}

// 关闭右侧标签
const handleCloseRightTags = (id: string) => {
  navbarStore.closeRightTags(id)
  navbarStore.hideMenu()
}

// 关闭所有标签
const handleCloseAllTags = () => {
  navbarStore.closeAllTags()
  navbarStore.hideMenu()
  showAllMenu.value = false
}

// 显示全局菜单
const handleShowAllMenu = () => {
  showAllMenu.value = true
}

// 刷新当前页面
const handleRefreshCurrentPage = () => {
  const currentRoute = router.currentRoute.value
  router.replace({
    path: '/redirect' + currentRoute.fullPath,
  })
  showAllMenu.value = false
}

onMounted(() => {
  // 初始化逻辑
})

onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick)
})
</script>

<style scoped lang="scss">
/* 确保关闭按钮的悬停效果使用主题变量 */
:deep(.hover\:text-destructive:hover) {
  color: hsl(var(--destructive));
}
</style>
