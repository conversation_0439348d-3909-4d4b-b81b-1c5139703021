import { useAIChatStore, useAuthStore } from '@renderer/store'
import { nanoid } from 'nanoid'
import { BaseResponse, BaseService } from './baseService'

// 会话消息接口
export interface Message {
  id: string
  sessionId: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: number
  isLoading?: boolean
  error?: string
  typewriterContent?: string
  isTyping?: boolean
}

// 会话接口
export interface Session {
  id: string
  title: string
  messages: Message[]
  createdAt: number
  updatedAt: number
  workflowId?: string
  knowledgeBaseFiles?: any[]
}

export interface SessionItem {
  session_id?: string
  user_id?: string
  session_messages?: string
  sessionId?: string
  userId?: string
  sessionMessages?: string
}

// 会话列表响应
export interface SessionResponse extends BaseResponse {
  session_result?: {
    session_id: string
    session_list: SessionItem[]
  }
  // 兼容驼峰命名格式
  sessionResult?: {
    sessionId: string
    sessionList: SessionItem[]
  }
}

export class AIChatService extends BaseService {
  /**
   * 获取用户所有会话
   * @param userId 用户ID
   */
  async getUserAllSessions(userId: string, token: string): Promise<SessionResponse> {
    return this.call<SessionResponse>('getUserAllSessions', {
      user_id: userId,
      token: token,
      id: '',
    })
  }

  /**
   * 获取特定会话
   * @param userId 用户ID
   * @param sessionId 会话ID
   */
  async getSession(userId: string, token: string, sessionId: string): Promise<SessionResponse> {
    return this.call<SessionResponse>('getSession', {
      user_id: userId,
      token: token,
      id: sessionId,
    })
  }

  /**
   * 添加会话 - 只在创建新会话时调用一次
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @param sessionMessages 会话消息
   */
  async addSession(
    userId: string,
    token: string,
    sessionId: string,
    sessionMessages: string,
  ): Promise<BaseResponse> {
    return this.call<BaseResponse>('addSession', {
      user_id: userId,
      token: token,
      session_id: sessionId,
      session_user_id: userId,
      session_messages: sessionMessages,
    })
  }

  /**
   * 删除会话
   * @param userId 用户ID
   * @param sessionId 会话ID
   */
  async deleteSession(userId: string, token: string, sessionId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('deleteSession', {
      user_id: userId,
      token: token,
      id: sessionId,
    })
  }

  /**
   * 发送消息到智能体
   * @param chatMode 聊天模式
   * @param serverId 服务器ID
   * @param sessionId 会话ID
   * @param model 模型名称
   * @param isStreamResponse 是否流式响应
   * @param message 消息内容
   * @param callback 回调函数
   */
  async sendMessage(
    chatMode: 'chat' | 'work',
    serverId: string,
    sessionId: string,
    model: string,
    isStreamResponse: boolean,
    message: { role: string; content: string },
    callback: (type: string, chunk: any) => void,
  ): Promise<void> {
    window.grpcApi.agent(chatMode, serverId, sessionId, model, isStreamResponse, message, callback)
  }

  /**
   * 获取可用的AI模型
   * @param serverId 服务器ID
   */
  async getAvailableModels(
    serverId: string,
    sessionId: string | null,
  ): Promise<Record<string, string[]>> {
    return new Promise((resolve) => {
      window.grpcApi.agent(
        'getAvailableModels',
        serverId,
        sessionId || '',
        '',
        true,
        {} as any, // 显式类型转换解决类型问题
        (type: string, chunk: any) => {
          if (type === 'data' && chunk?.result) {
            try {
              const resultObj = JSON.parse(chunk.result)
              resolve(resultObj)
            } catch (error) {
              window.logger.error('解析模型数据失败:', error)
              resolve({ default: [] })
            }
          } else if (type === 'end' || type === 'error') {
            resolve({ default: [] })
          }
        },
      )
    })
  }

  /**
   * 将消息转换为服务器存储格式
   */
  formatMessagesForServer(messages: Message[]): string {
    const formattedMessages = messages.map((msg, index) => ({
      message_id: String(index + 1),
      session_contents: [
        {
          role: msg.role,
          content_type: 'text',
          content: msg.content,
          create_time: Math.floor(msg.timestamp / 1000),
        },
      ],
    }))
    return JSON.stringify(formattedMessages)
  }

  /**
   * 从服务器格式解析消息
   */
  parseMessagesFromServer(sessionContents: string | undefined, sessionId: string): Message[] {
    try {
      if (!sessionContents) {
        window.logger.warn(`会话 ${sessionId} 的内容为空或 undefined`)
        return []
      }

      window.logger.info(`开始解析会话消息: ${sessionContents.substring(0, 100)}...`)

      const parsed = JSON.parse(sessionContents)
      if (Array.isArray(parsed)) {
        const messages: Message[] = []
        parsed.forEach((item) => {
          // 兼容不同的消息结构格式
          const contents = item.session_contents || item.sessionContents
          if (contents && Array.isArray(contents)) {
            contents.forEach((content) => {
              // 确保必要的字段存在
              if (content && content.role && content.content) {
                const timestamp =
                  content.create_time || content.createTime || Math.floor(Date.now() / 1000)

                messages.push({
                  id: this.generateId(),
                  sessionId: sessionId,
                  role: content.role as 'user' | 'assistant' | 'system',
                  content: content.content,
                  timestamp: timestamp * 1000,
                })
              }
            })
          }
        })

        window.logger.info(`成功解析会话消息，共 ${messages.length} 条`)
        return messages
      }

      window.logger.warn(`会话 ${sessionId} 解析结果不是数组`)
      return []
    } catch (e) {
      window.logger.error(`解析会话内容失败: ${e}`, sessionContents)
      return []
    }
  }

  /**
   * 生成唯一ID
   */
  generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  }

  /**
   * 生成会话ID
   */
  generateSessionId(workflowId?: string): string {
    if (workflowId) {
      return `0::${workflowId}`
    }
    return `0::${Math.floor(Math.random() * 1000000000)}`
  }

  /**
   * 获取会话列表并返回
   * 不再更新本地状态，而是返回获取到的数据
   */
  async fetchAllSessions(): Promise<Session[]> {
    const aiChatStore = useAIChatStore()
    const authStore = useAuthStore()

    // 确保 userId 和 token 不为 null
    if (!authStore.userId || !authStore.token) {
      window.logger.error('获取会话列表失败: 用户ID或Token为空')
      return []
    }

    aiChatStore.setIsLoading(true)
    try {
      const response = await this.getUserAllSessions(authStore.userId, authStore.token)

      // 兼容两种格式的返回值
      const sessionList =
        response.session_result?.session_list || response.sessionResult?.sessionList

      if (response.status === 'Success' && sessionList && sessionList.length > 0) {
        const fetchedSessions: Session[] = []

        // 处理服务器返回的会话
        for (const serverSession of sessionList) {
          // 兼容两种格式的字段名
          const sessionId = serverSession.session_id || serverSession.sessionId
          const sessionMessages = serverSession.session_messages || serverSession.sessionMessages

          const serverMessages = this.parseMessagesFromServer(sessionMessages, sessionId)

          // 只有当消息不为空时才添加会话
          if (serverMessages.length > 0) {
            // 提取工作流ID
            let workflowId: string | undefined = undefined
            if (sessionId?.startsWith('0::')) {
              workflowId = sessionId.substring(3)
            }

            const session: Session = {
              id: sessionId,
              title: workflowId ? '工作流对话' : `会话 ${fetchedSessions.length + 1}`,
              messages: serverMessages,
              createdAt: serverMessages[0].timestamp,
              updatedAt: serverMessages[serverMessages.length - 1].timestamp,
              workflowId: workflowId,
            }

            fetchedSessions.push(session)
          }
        }

        if (fetchedSessions.length > 0) {
          // 按更新时间排序
          return fetchedSessions.sort((a, b) => b.updatedAt - a.updatedAt)
        }
      }

      return []
    } catch (error) {
      window.logger.error('获取会话列表失败:', error)
      return []
    } finally {
      aiChatStore.setIsLoading(false)
    }
  }

  /**
   * 获取特定会话
   */
  async fetchSession(sessionId: string): Promise<Session | null> {
    const aiChatStore = useAIChatStore()
    const authStore = useAuthStore()

    // 如果当前已加载此会话，直接返回
    if (aiChatStore.currentSessionId === sessionId && aiChatStore.currentSession) {
      return aiChatStore.currentSession
    }

    // 确保用户已登录
    if (!authStore.userId || !authStore.token) {
      window.logger.error(`获取会话 ${sessionId} 失败: 用户未登录`)
      return null
    }

    aiChatStore.setIsLoading(true)

    try {
      // 从服务器获取会话数据
      const response = await this.getSession(authStore.userId, authStore.token, sessionId)

      // 兼容两种格式的返回值
      const sessionList =
        response.session_result?.session_list || response.sessionResult?.sessionList

      if (response.status === 'Success' && sessionList && sessionList.length > 0) {
        const serverSession = sessionList[0]

        // 兼容两种格式的字段名
        const sessionId = serverSession.session_id || serverSession.sessionId
        const sessionMessages = serverSession.session_messages || serverSession.sessionMessages

        // 解析消息
        const messages = this.parseMessagesFromServer(sessionMessages, sessionId)

        // 提取工作流ID
        let workflowId: string | undefined = undefined
        if (sessionId?.startsWith('0::')) {
          workflowId = sessionId.substring(3)
        }

        // 构建会话数据
        const sessionData: Session = {
          id: sessionId,
          title: workflowId ? '工作流对话' : '会话',
          messages:
            messages.length > 0
              ? messages
              : [
                  {
                    id: nanoid(),
                    sessionId: sessionId,
                    content: '你好！这是一个新的对话。',
                    role: 'assistant',
                    timestamp: Date.now(),
                  },
                ],
          createdAt: messages.length > 0 ? messages[0].timestamp : Date.now(),
          updatedAt: messages.length > 0 ? messages[messages.length - 1].timestamp : Date.now(),
          workflowId: workflowId,
        }

        return sessionData
      }

      return null
    } catch (error) {
      window.logger.error(`获取会话 ${sessionId} 失败:`, error)
      return null
    } finally {
      aiChatStore.setIsLoading(false)
    }
  }

  /**
   * 选择会话
   */
  async selectSession(id: string): Promise<Session | null> {
    const aiChatStore = useAIChatStore()
    const authStore = useAuthStore()

    try {
      window.logger.info(`开始选择会话 ${id}`)

      // 每次都重新从服务器获取会话数据
      aiChatStore.setIsLoading(true)

      // 确保用户已登录
      if (!authStore.userId || !authStore.token) {
        window.logger.error(`获取会话 ${id} 失败: 用户未登录`)
        aiChatStore.setIsLoading(false)
        return null
      }

      // 从服务器获取会话数据
      const response = await this.getSession(authStore.userId, authStore.token, id)

      // 兼容两种格式的返回值
      const sessionList =
        response.session_result?.session_list || response.sessionResult?.sessionList

      if (response.status === 'Success' && sessionList && sessionList.length > 0) {
        const serverSession = sessionList[0]

        // 兼容两种格式的字段名
        const sessionId = serverSession.session_id || serverSession.sessionId
        const sessionMessages = serverSession.session_messages || serverSession.sessionMessages

        // 解析消息
        const messages = this.parseMessagesFromServer(sessionMessages, sessionId)

        // 提取工作流ID
        let workflowId: string | undefined = undefined
        if (sessionId?.startsWith('0::')) {
          workflowId = sessionId.substring(3)
        }

        // 构建会话数据
        const sessionData: Session = {
          id: id,
          title: workflowId ? '工作流对话' : '会话',
          messages:
            messages.length > 0
              ? messages
              : [
                  {
                    id: nanoid(),
                    sessionId: id,
                    content: '你好！这是一个新的对话。',
                    role: 'assistant',
                    timestamp: Date.now(),
                  },
                ],
          createdAt: messages.length > 0 ? messages[0].timestamp : Date.now(),
          updatedAt: messages.length > 0 ? messages[messages.length - 1].timestamp : Date.now(),
          workflowId: workflowId,
        }

        // 更新当前会话
        aiChatStore.setCurrentSessionId(id)
        aiChatStore.setCurrentSessionData(sessionData)
        aiChatStore.setIsLoading(false)

        return sessionData
      }

      // 如果会话不存在，可能需要创建新会话
      if (id.startsWith('0::')) {
        const workflowId = id.substring(3)
        aiChatStore.setIsLoading(false)
        return await this.createNewSession('工作流对话', workflowId)
      } else {
        aiChatStore.setIsLoading(false)
        return await this.createNewSession()
      }
    } catch (error) {
      window.logger.error(`选择会话 ${id} 失败:`, error)
      aiChatStore.setIsLoading(false)
      return await this.createNewSession()
    }
  }

  /**
   * 创建新会话
   */
  async createNewSession(title: string = '新的对话', workflowId?: string): Promise<Session | null> {
    const aiChatStore = useAIChatStore()
    const authStore = useAuthStore()

    // 确保 userId 和 token 不为 null
    if (!authStore.userId || !authStore.token) {
      window.logger.error('创建新会话失败: 用户ID或Token为空')
      return null
    }

    const newSessionId = this.generateSessionId(workflowId)
    window.logger.info(`准备创建新会话，生成会话ID: ${newSessionId}`)

    // 准备初始消息
    const initialMessages = [
      {
        id: nanoid(),
        sessionId: newSessionId,
        content: '你好！这是一个新的对话。',
        role: 'assistant' as const,
        timestamp: Date.now(),
      },
    ]

    try {
      window.logger.info(`创建新会话 ${newSessionId}，调用 addSession`)

      // 保存到服务器 - 只在创建新会话时调用一次
      const addSessionResult = await this.addSession(
        authStore.userId,
        authStore.token,
        newSessionId,
        this.formatMessagesForServer(initialMessages),
      )

      if (addSessionResult.status !== 'Success') {
        window.logger.error(`addSession 调用失败: ${addSessionResult.message || '未知错误'}`)
        throw new Error(`创建会话失败: ${addSessionResult.message}`)
      }

      window.logger.info(`addSession 调用成功，创建会话 ${newSessionId}`)

      const newSession: Session = {
        id: newSessionId,
        title,
        messages: initialMessages,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        workflowId,
      }

      // 更新当前会话
      aiChatStore.setCurrentSessionId(newSessionId)
      aiChatStore.setCurrentSessionData(newSession)

      return newSession
    } catch (error) {
      window.logger.error(`创建新会话 ${newSessionId} 失败:`, error)
      return null
    }
  }

  /**
   * 删除会话
   */
  async removeSession(sessionId: string): Promise<boolean> {
    const aiChatStore = useAIChatStore()
    const authStore = useAuthStore()

    // 确保 userId 和 token 不为 null
    if (!authStore.userId || !authStore.token) {
      window.logger.error(`删除会话 ${sessionId} 失败: 用户ID或Token为空`)
      return false
    }

    try {
      // 从服务器删除
      const response = await this.deleteSession(authStore.userId, authStore.token, sessionId)

      if (response.status === 'Success') {
        // 如果删除的是当前会话，重新获取会话列表并选择第一个
        if (aiChatStore.currentSessionId === sessionId) {
          const sessions = await this.fetchAllSessions()

          if (sessions.length > 0) {
            await this.selectSession(sessions[0].id)
          } else {
            await this.createNewSession()
          }
        }

        return true
      } else {
        throw new Error(response.message || '删除会话失败')
      }
    } catch (error) {
      window.logger.error(`删除会话 ${sessionId} 失败:`, error)
      return false
    }
  }

  /**
   * 更新会话 - 不再调用 addSession
   */
  async updateSession(sessionId: string, updates: Partial<Session>): Promise<boolean> {
    const aiChatStore = useAIChatStore()

    try {
      // 先获取当前会话
      const currentSession = aiChatStore.currentSession

      if (!currentSession || currentSession.id !== sessionId) {
        const session = await this.fetchSession(sessionId)
        if (!session) return false

        // 应用更新
        const updatedSession = {
          ...session,
          ...updates,
          updatedAt: Date.now(),
        }

        // 如果是当前会话，更新当前会话数据
        if (aiChatStore.currentSessionId === sessionId) {
          aiChatStore.setCurrentSessionData(updatedSession)
        }

        return true
      } else {
        // 应用更新到当前会话
        const updatedSession = {
          ...currentSession,
          ...updates,
          updatedAt: Date.now(),
        }

        // 更新当前会话数据
        aiChatStore.setCurrentSessionData(updatedSession)
        return true
      }
    } catch (error) {
      window.logger.error(`更新会话 ${sessionId} 失败:`, error)
      return false
    }
  }

  /**
   * 添加消息到会话 - 不再调用 addSession
   */
  async addMessage(
    sessionId: string,
    messageContent: string,
    role: 'user' | 'assistant' | 'system',
    isLoading: boolean = false,
  ): Promise<Message | null> {
    const aiChatStore = useAIChatStore()

    try {
      // 获取当前会话
      let session: Session | null = null

      if (aiChatStore.currentSessionId === sessionId && aiChatStore.currentSession) {
        session = aiChatStore.currentSession
      } else {
        session = await this.fetchSession(sessionId)
      }

      if (!session) {
        window.logger.error(`Session with id ${sessionId} not found. Cannot add message.`)
        return null
      }

      // 创建新消息
      const newMessage: Message = {
        id: nanoid(),
        sessionId,
        content: messageContent,
        role,
        timestamp: Date.now(),
        isLoading,
        typewriterContent: '',
        isTyping: false,
      }

      // 添加消息到会话
      const updatedMessages = [...session.messages, newMessage]
      const updatedSession = {
        ...session,
        messages: updatedMessages,
        updatedAt: Date.now(),
      }

      // 如果是当前会话，更新当前会话数据
      if (aiChatStore.currentSessionId === sessionId) {
        aiChatStore.setCurrentSessionData(updatedSession)
      }

      return newMessage
    } catch (error) {
      window.logger.error(`添加消息到会话 ${sessionId} 失败:`, error)
      return null
    }
  }

  /**
   * 发送消息并处理响应 - 不再调用 addSession
   */
  async sendUserMessage(content: string): Promise<void> {
    const aiChatStore = useAIChatStore()

    if (!aiChatStore.currentSessionId || !aiChatStore.currentSession) {
      console.warn('No active session to send message.')
      return
    }

    const sessionId = aiChatStore.currentSessionId
    const currentSession = aiChatStore.currentSession

    try {
      // 设置生成状态为true
      aiChatStore.setIsGenerating(true)
      // 添加用户消息
      const userMessage: Message = {
        id: nanoid(),
        sessionId,
        content: content,
        role: 'user',
        timestamp: Date.now(),
      }

      // 添加一个 loading 状态的 AI 消息
      const aiMessage: Message = {
        id: nanoid(),
        sessionId,
        content: '',
        role: 'assistant',
        timestamp: Date.now(),
        isLoading: true,
        typewriterContent: '',
        isTyping: false,
      }

      // 更新会话消息
      const updatedMessages = [...currentSession.messages, userMessage, aiMessage]
      const updatedSession = {
        ...currentSession,
        messages: updatedMessages,
        updatedAt: Date.now(),
      }

      // 更新当前会话数据
      aiChatStore.setCurrentSessionData(updatedSession)

      let responseText = ''
      const aiMessageIndex = updatedMessages.length - 1

      // 使用服务发送消息
      this.sendMessage(
        aiChatStore.chatMode,
        aiChatStore.selectedServerId,
        sessionId,
        aiChatStore.selectedModel,
        true,
        {
          role: 'user',
          content: JSON.stringify({
            type: 'text',
            text: content,
            temperature: aiChatStore.temperature[0],
          }),
        },
        async (type, chunk) => {
          if (type === 'data') {
            if (chunk && typeof chunk === 'object' && 'result' in chunk) {
              if (chunk.result !== '') {
                responseText += chunk.result as string

                // 更新AI消息内容
                const currentSessionData = aiChatStore.currentSession
                if (currentSessionData && currentSessionData.id === sessionId) {
                  const updatedMessages = [...currentSessionData.messages]
                  if (updatedMessages[aiMessageIndex]) {
                    updatedMessages[aiMessageIndex] = {
                      ...updatedMessages[aiMessageIndex],
                      content: responseText,
                      isLoading: true,
                    }

                    aiChatStore.setCurrentSessionData({
                      ...currentSessionData,
                      messages: updatedMessages,
                    })
                  }
                }
              }
            }
          }
          if (type === 'end') {
            aiChatStore.setIsGenerating(false)
            // 更新消息为非loading状态
            const currentSessionData = aiChatStore.currentSession
            if (currentSessionData && currentSessionData.id === sessionId) {
              const updatedMessages = [...currentSessionData.messages]
              if (updatedMessages[aiMessageIndex]) {
                updatedMessages[aiMessageIndex] = {
                  ...updatedMessages[aiMessageIndex],
                  isLoading: false,
                }

                aiChatStore.setCurrentSessionData({
                  ...currentSessionData,
                  messages: updatedMessages,
                })
              }
            }
            // 消息完成后，从服务器获取最新的会话数据
            const refreshedSession = await this.fetchSession(sessionId)
            if (refreshedSession) {
              aiChatStore.setCurrentSessionData(refreshedSession)
            }
          }
          if (type === 'error') {
            aiChatStore.setIsGenerating(false)

            // 更新AI消息为错误状态
            const currentSessionData = aiChatStore.currentSession
            if (currentSessionData && currentSessionData.id === sessionId) {
              const updatedMessages = [...currentSessionData.messages]
              if (updatedMessages[aiMessageIndex]) {
                updatedMessages[aiMessageIndex] = {
                  ...updatedMessages[aiMessageIndex],
                  error: '获取AI回复失败',
                  isLoading: false,
                }

                aiChatStore.setCurrentSessionData({
                  ...currentSessionData,
                  messages: updatedMessages,
                })
              }
            }

            // 出错时也尝试获取最新数据
            const refreshedSession = await this.fetchSession(sessionId)
            if (refreshedSession) {
              aiChatStore.setCurrentSessionData(refreshedSession)
            }
          }
        },
      )
    } catch (error) {
      window.logger.error(error)
    }
  }

  /**
   * 停止消息生成
   */
  /**
   * 停止消息生成
   */
  async stopMessageGeneration(): Promise<void> {
    const aiChatStore = useAIChatStore()

    if (!aiChatStore.currentSessionId || !aiChatStore.currentSession) {
      return
    }

    const sessionId = aiChatStore.currentSessionId

    try {
      // 设置生成状态为false
      aiChatStore.setIsGenerating(false)

      // 通知后端停止生成
      window.grpcApi.stopGeneration(sessionId)

      // 找到最后一条助手消息并标记为已停止
      const currentSession = aiChatStore.currentSession
      if (currentSession) {
        const messages = [...currentSession.messages]
        for (let i = messages.length - 1; i >= 0; i--) {
          if (messages[i].role === 'assistant' && messages[i].isLoading) {
            messages[i] = {
              ...messages[i],
              isLoading: false,
              content: messages[i].content + '\n\n[已停止生成]',
            }
            break
          }
        }

        aiChatStore.setCurrentSessionData({
          ...currentSession,
          messages,
          updatedAt: Date.now(),
        })
      }
    } catch (error) {
      window.logger.error('停止消息生成失败:', error)
    }
  }

  /**
   * 添加知识库引用到当前会话
   */
  async addKnowledgeReferenceToCurrentSession(file: any): Promise<boolean> {
    const aiChatStore = useAIChatStore()

    if (!aiChatStore.currentSession || !aiChatStore.currentSessionId) {
      console.warn('No current session to add knowledge reference.')
      return false
    }

    const currentSession = aiChatStore.currentSession

    // 创建新的知识库文件数组
    const knowledgeBaseFiles = currentSession.knowledgeBaseFiles
      ? [...currentSession.knowledgeBaseFiles, file]
      : [file]

    // 更新会话
    const updatedSession = {
      ...currentSession,
      knowledgeBaseFiles,
      updatedAt: Date.now(),
    }

    // 更新当前会话数据
    aiChatStore.setCurrentSessionData(updatedSession)
    return true
  }
}

/**
 * 创建 AIChatService 实例
 */
export function createAIChatService(): AIChatService {
  return new AIChatService()
}
