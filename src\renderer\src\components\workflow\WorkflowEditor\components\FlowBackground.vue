<template>
  <!-- HP Logo 背景 -->
  <div
    v-if="!isMatt"
    class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[40vw] h-[44.9775vh] bg-contain bg-no-repeat bg-center opacity-10 pointer-events-none z-0"
    :style="{ backgroundImage: `url(${hpLogoUrl})` }"
  ></div>

  <!-- 小地图 -->
  <MiniMap
    v-if="showMiniMap"
    :style="{
      background: miniMapConfig.backgroundColor,
      borderRadius: '0.5rem',
      border: '1px solid rgba(var(--border), 0.3)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    }"
    :node-stroke-color="miniMapConfig.nodeStrokeColor"
    :node-stroke-width="miniMapConfig.nodeStrokeWidth"
    :node-color="getNodeColor"
    class="scale-75 origin-bottom-right overflow-hidden"
  />

  <!-- 背景 -->
  <Background
    :pattern-color="backgroundConfig.patternColor"
    :gap="backgroundConfig.gap"
    :size="backgroundConfig.size"
    :variant="backgroundConfig.variant"
    class="vue-flow__background-optimized"
  />
</template>

<script setup lang="ts">
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'

interface MiniMapConfig {
  backgroundColor: string
  nodeStrokeColor: string
  nodeStrokeWidth: number
}

interface BackgroundConfig {
  patternColor: string
  gap: number
  size: number
  variant: string
}

interface Props {
  isMatt: boolean
  hpLogoUrl: string
  showMiniMap: boolean
  miniMapConfig: MiniMapConfig
  backgroundConfig: BackgroundConfig
  getNodeColor: (node: any) => string
}

const props = defineProps<Props>()
</script>

<style>
/* 优化背景渲染性能 */
.vue-flow__background-optimized {
  contain: strict;
  will-change: transform;
  backface-visibility: hidden;
}

/* 拖动时降低背景精度以提高性能 */
.workflow-drag-optimized .vue-flow__background {
  opacity: 0.8;
  filter: blur(0.5px);
  transition: none !important;
}
</style>
