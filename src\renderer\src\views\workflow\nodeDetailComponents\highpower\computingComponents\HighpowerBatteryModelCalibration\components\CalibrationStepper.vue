<template>
  <div class="mb-2 grid grid-cols-1 gap-3">
    <div class="w-full">
      <Stepper v-model="currentStepValue" class="w-full">
        <StepperItem
          v-for="(step, index) in steps"
          :key="index"
          :value="index"
          :step="index"
          class="basis-1/2"
        >
          <StepperTrigger as-child>
            <Button
              variant="ghost"
              class="flex items-center gap-1 p-0.5 h-auto min-w-0 max-w-full"
              :disabled="(index === 1 && !elecParamsCompleted) || isProcessing"
              @click="updateCurrentStep(index)"
            >
              <div
                class="shrink-0 w-5 h-5 text-xs flex items-center justify-center rounded-full border"
                :class="{
                  'bg-primary text-white border-primary': currentStepValue === index,
                  'border-primary':
                    currentStepValue > index &&
                    !(
                      (index === 0 && elecParamsCompleted) ||
                      (index === 1 && agingParamsCompleted)
                    ),
                  'bg-green-500 text-white border-green-500':
                    ((index === 0 && elecParamsCompleted) ||
                      (index === 1 && agingParamsCompleted)) &&
                    currentStepValue !== index,
                  'bg-gray-100 border-gray-300':
                    currentStepValue !== index &&
                    !(
                      (index === 0 && elecParamsCompleted) ||
                      (index === 1 && agingParamsCompleted)
                    ),
                }"
              >
                <component
                  :is="
                    ((index === 0 && elecParamsCompleted) ||
                      (index === 1 && agingParamsCompleted)) &&
                    currentStepValue !== index
                      ? Check
                      : step.icon
                  "
                  class="w-2.5 h-2.5"
                />
              </div>
              <div class="flex flex-col gap-0 ml-1 text-left overflow-hidden">
                <StepperTitle class="text-xs font-medium truncate">
                  {{ step.title }}
                  <span
                    v-if="index === 0 && elecParamsCompleted"
                    :class="getStatusClass(currentElecStatus)"
                    class="ml-1 px-1.5 py-0.5 rounded-sm text-white text-xs whitespace-nowrap"
                  >
                    ({{ getStatusText(currentElecStatus) }})
                  </span>
                  <span
                    v-if="index === 1 && agingParamsCompleted"
                    :class="getStatusClass(currentAgingStatus)"
                    class="ml-1 px-1.5 py-0.5 rounded-sm text-white text-xs whitespace-nowrap"
                  >
                    ({{ getStatusText(currentAgingStatus) }})
                  </span>
                </StepperTitle>
                <StepperDescription class="text-[10px] leading-tight truncate">
                  {{ step.description }}
                </StepperDescription>
              </div>
            </Button>
          </StepperTrigger>
          <StepperSeparator
            v-if="index < steps.length - 1"
            class="w-full h-[1px]"
            :class="{
              'bg-primary': currentStepValue > index,
              'bg-green-500': index === 0 && elecParamsCompleted && currentStepValue <= index,
              'bg-gray-300':
                !(currentStepValue > index) &&
                !(index === 0 && elecParamsCompleted && currentStepValue <= index),
            }"
          />
        </StepperItem>
      </Stepper>
    </div>

    <div class="grid grid-cols-2 gap-2 w-full">
      <Button
        size="xs"
        variant="outline"
        class="flex items-center justify-center"
        :disabled="currentStepValue === 0 || isProcessing"
        @click="prevStep"
      >
        <ChevronLeft class="w-4 h-4 mr-1" />
        上一步
      </Button>
      <Button
        size="xs"
        variant="outline"
        class="flex items-center justify-center"
        :disabled="currentStepValue === steps.length - 1 || !elecParamsCompleted || isProcessing"
        @click="nextStep"
      >
        下一步
        <ChevronRight class="w-4 h-4 ml-1" />
      </Button>
    </div>
  </div>
</template>

<script setup>
import { useTaskStore } from '@renderer/store'
import { Check, ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { computed } from 'vue'

const taskStore = useTaskStore()

const props = defineProps({
  // 当前步骤索引
  currentStep: {
    type: Number,
    required: true,
  },
  // 步骤配置数组，包含标题、描述和图标
  steps: {
    type: Array,
    required: true,
  },
  // 电化学参数是否已完成标定
  elecParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 老化参数是否已完成标定
  agingParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 是否正在处理中（标定计算过程中）
  isProcessing: {
    type: Boolean,
    default: false,
  },
  elecTaskId: {
    type: String,
    default: '',
  },
  agingTaskId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:current-step', 'prev-step', 'next-step'])

// 计算当前电化学参数任务状态
const currentElecStatus = computed(() => {
  if (!props.elecParamsCompleted) return 'TaskStay'
  if (!props.elecTaskId) return 'Finished'

  // 如果正在处理且当前是电化学参数步骤
  if (props.isProcessing && props.currentStep === 0) return 'Computing'

  // 获取任务状态
  const task = taskStore.tasks.find((t) => t.taskId === props.elecTaskId)
  if (task) return task.taskStatus

  // 尝试从结果中获取状态
  const elecResult = taskStore.getTaskResultById(props.elecTaskId).value
  if (elecResult) return 'Finished'

  return taskStore.getTaskStatus(props.elecTaskId).value || 'Finished'
})

// 计算当前老化参数任务状态
const currentAgingStatus = computed(() => {
  if (!props.agingParamsCompleted) return 'TaskStay'
  if (!props.agingTaskId) return 'Finished'

  // 如果正在处理且当前是老化参数步骤
  if (props.isProcessing && props.currentStep === 1) return 'Computing'

  // 获取任务状态
  const task = taskStore.tasks.find((t) => t.taskId === props.agingTaskId)
  if (task) return task.taskStatus

  // 尝试从结果中获取状态
  const agingResult = taskStore.getTaskResultById(props.agingTaskId).value
  if (agingResult) return 'Finished'

  return taskStore.getTaskStatus(props.agingTaskId).value || 'Finished'
})

// 获取状态文本
const getStatusText = (status) => {
  return taskStore.getStatusText(status)
}

// 获取状态样式类
const getStatusClass = (status) => {
  return taskStore.getStatusClass(status)
}
// 当前步骤值
const currentStepValue = computed({
  get: () => props.currentStep,
  set: (value) => emit('update:current-step', value),
})

const updateCurrentStep = (index) => {
  emit('update:current-step', index)
}
// 上一步
const prevStep = () => {
  emit('prev-step')
}
// 下一步
const nextStep = () => {
  emit('next-step')
}
</script>
