import { useSettingsStore } from '@renderer/store'
import { computed, onMounted } from 'vue'

export function useFont() {
  const settingsStore = useSettingsStore()

  // 使用设置存储中的字体状态
  const currentFont = computed(() => settingsStore.font)

  // 设置字体 - 通过设置存储
  const setFont = (font: string) => {
    settingsStore.setFont(font)
  }

  // 初始化时应用保存的字体
  onMounted(() => {
    // 确保设置已初始化
    settingsStore.initializeSettings()
  })

  return {
    currentFont,
    setFont,
  }
}
