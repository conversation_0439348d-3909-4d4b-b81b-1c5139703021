<template>
  <div class="json-data-renderer">
    <!-- 对象类型数据 -->
    <Card v-if="isObject && !isArray" class="border border-border shadow-sm bg-card/50">
      <CardHeader class="p-3 pb-0">
        <CardTitle class="text-sm font-medium flex items-center gap-2">
          <Database class="h-3.5 w-3.5 text-primary" />
          {{ propertyName }}
          <Badge v-if="objectSize > 0" variant="outline" class="text-xs ml-1">
            {{ objectSize }} 个属性
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-3 pt-2">
        <div v-if="isExpandable && !expanded" class="text-xs text-center">
          <Button variant="ghost" size="sm" class="w-full" @click="expanded = true">
            <ChevronDown class="h-3.5 w-3.5 mr-1" />
            展开 {{ objectSize }} 个属性
          </Button>
        </div>
        <div v-else-if="objectSize > 0" class="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div v-for="(propValue, propKey) in data" :key="propKey" class="text-xs">
            <!-- 递归处理嵌套对象 -->
            <div v-if="isNestedObject(propValue)">
              <div class="font-medium text-foreground mb-1">{{ propKey }}</div>
              <JsonDataRenderer
                :data="propValue"
                :property-name="propKey"
                :depth="depth + 1"
                class="ml-2 border-l-2 border-border/30 pl-2"
              />
            </div>
            <!-- 处理基本类型 -->
            <div v-else>
              <div class="font-medium text-foreground flex items-center justify-between">
                {{ propKey }}
                <Badge variant="outline" class="text-xs">
                  {{ typeof propValue }}
                </Badge>
              </div>
              <div class="text-muted-foreground mt-0.5 break-words whitespace-pre-wrap">
                {{ formatValue(propValue) }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-xs text-muted-foreground text-center py-2">空对象</div>
      </CardContent>
    </Card>

    <!-- 数组类型数据 -->
    <Card v-else-if="isArray" class="border border-border shadow-sm bg-card/50">
      <CardHeader class="p-3 pb-0">
        <CardTitle class="text-sm font-medium flex items-center gap-2">
          <ListOrdered class="h-3.5 w-3.5 text-primary" />
          {{ propertyName }}
          <Badge variant="outline" class="text-xs ml-1">{{ data.length }} 项</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-3 pt-2">
        <div v-if="data.length > 0 && isExpandable && !expanded" class="text-xs text-center">
          <Button variant="ghost" size="sm" class="w-full" @click="expanded = true">
            <ChevronDown class="h-3.5 w-3.5 mr-1" />
            展开 {{ data.length }} 项
          </Button>
        </div>
        <div v-else-if="data.length > 0" class="space-y-2">
          <div v-for="(item, index) in data" :key="index" class="text-xs">
            <!-- 数组元素是对象 -->
            <div
              v-if="isNestedObject(item)"
              class="border border-border/50 rounded-md mb-2 overflow-hidden"
            >
              <div
                class="bg-muted/30 px-2 py-1 border-b border-border/50 flex justify-between items-center"
              >
                <span class="font-medium">对象 {{ index + 1 }}</span>
                <Badge variant="outline" class="text-xs">
                  {{
                    isArray ? Object.keys(item).length + ' 项' : Object.keys(item).length + ' 属性'
                  }}
                </Badge>
              </div>
              <div class="p-2">
                <JsonDataRenderer
                  :data="item"
                  :property-name="'对象 ' + (index + 1)"
                  :depth="depth + 1"
                  :expanded="false"
                />
              </div>
            </div>
            <!-- 数组元素是基本类型 -->
            <div v-else class="p-2 border border-border/50 rounded-md mb-2">
              <div class="flex justify-between">
                <span class="font-medium">对象 {{ index + 1 }}</span>
                <Badge
                  v-if="
                    typeof item === 'string' ||
                    typeof item === 'number' ||
                    typeof item === 'undefined'
                  "
                  variant="outline"
                  class="text-xs"
                >
                  {{ typeof item === 'undefined' ? '未定义' : typeof item }}
                </Badge>
              </div>
              <div class="mt-1 whitespace-pre-wrap break-words">{{ formatValue(item) }}</div>
            </div>
          </div>
        </div>
        <div v-else class="text-xs text-muted-foreground text-center py-2">空数组</div>
      </CardContent>
    </Card>

    <!-- 基本类型数据 -->
    <div v-else class="p-3 border border-border rounded-md bg-card/30">
      <div class="text-sm">{{ formatValue(data) }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardHeader, CardTitle, CardContent, Badge, Button } from '@renderer/components/ui'
import { Database, ListOrdered, ChevronDown } from 'lucide-vue-next'

interface Props {
  data: any
  propertyName: string
  depth?: number
  expanded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  depth: 0,
  expanded: true,
})

// 控制展开/折叠状态
const expanded = ref(props.expanded)

// 计算数据类型
const isObject = computed(() => typeof props.data === 'object' && props.data !== null)
const isArray = computed(() => Array.isArray(props.data))
const objectSize = computed(() => {
  if (!isObject.value) return 0
  return isArray.value ? props.data.length : Object.keys(props.data).length
})

// 判断是否应该可展开
const isExpandable = computed(() => {
  // 对于深度大于1的对象或数组，默认折叠
  return props.depth > 0 && objectSize.value > 3
})

// 判断是否为嵌套对象或数组
const isNestedObject = (value: any) => {
  return typeof value === 'object' && value !== null
}

// 格式化基本类型值的显示
const formatValue = (value: any) => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'boolean') return value ? 'true' : 'false'
  if (typeof value === 'number') return value.toString()
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value, null, 2)
    } catch (e) {
      return String(value)
    }
  }
  // 处理字符串，如果是JSON字符串尝试格式化显示
  if (typeof value === 'string') {
    try {
      // 检查是否是JSON字符串
      if (value.trim().startsWith('{') || value.trim().startsWith('[')) {
        const parsed = JSON.parse(value)
        return JSON.stringify(parsed, null, 2)
      }
    } catch (e) {
      console.error('Error parsing JSON:', e)
    }
  }
  return String(value)
}
</script>

<style scoped>
.json-data-renderer {
  width: 100%;
}
</style>
