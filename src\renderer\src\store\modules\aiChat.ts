import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 从服务导入类型
import type { Message, Session } from '@renderer/config/api/grpc/aiChatService'

export type { Message, Session }

export const useAIChatStore = defineStore(
  'aiChat',
  () => {
    // State - 只保留UI相关状态和配置
    const currentSessionId = ref<string | null>(null)
    const selectedServerId = ref<string>('')
    const isChatOpenInWorkflow = ref<boolean>(false)
    const chatPositionInWorkflow = ref<{ x: number; y: number }>({ x: 100, y: 100 })
    const chatSizeInWorkflow = ref<{ width: number; height: number }>({ width: 600, height: 800 })
    const typingSpeed = ref<number>(30)
    const selectedModel = ref<string>('')
    const temperature = ref<number[]>([0.7])
    const chatMode = ref<'chat' | 'work'>('chat')
    const isLoading = ref<boolean>(false)
    const isGenerating = ref<boolean>(false) //判断AI是否正在生成回复

    // 临时存储当前会话和消息 - 这些不会被持久化，只在内存中使用
    const currentSessionData = ref<Session | null>(null)

    // Getters
    const currentSession = computed(() => currentSessionData.value)
    const currentMessages = computed((): Message[] => currentSessionData.value?.messages || [])

    // Actions - 仅UI状态更新相关
    const updateChatMode = (mode: 'chat' | 'work') => {
      chatMode.value = mode
    }

    const updateSelectedServer = (serverId: string) => {
      selectedServerId.value = serverId
    }

    const updateSelectedModel = (model: string) => {
      selectedModel.value = model
    }

    const updateTemperature = (temp: number[]) => {
      temperature.value = temp
    }

    const setIsLoading = (loading: boolean) => {
      isLoading.value = loading
    }

    const setCurrentSessionId = (sessionId: string | null) => {
      currentSessionId.value = sessionId
    }

    const setCurrentSessionData = (session: Session | null) => {
      currentSessionData.value = session
    }

    // 工作流相关UI状态
    const toggleChatInWorkflow = (open?: boolean) => {
      isChatOpenInWorkflow.value = open === undefined ? !isChatOpenInWorkflow.value : open
    }

    const updateChatPositionInWorkflow = (position: { x: number; y: number }) => {
      chatPositionInWorkflow.value = position
    }

    const updateChatSizeInWorkflow = (size: { width: number; height: number }) => {
      chatSizeInWorkflow.value = size
    }

    const setIsGenerating = (isGen: boolean) => {
      isGenerating.value = isGen
    }

    return {
      // 状态
      currentSessionId,
      selectedServerId,
      isChatOpenInWorkflow,
      chatPositionInWorkflow,
      chatSizeInWorkflow,
      typingSpeed,
      selectedModel,
      temperature,
      chatMode,
      isLoading,
      currentSessionData,
      isGenerating,

      // 计算属性
      currentSession,
      currentMessages,

      // 状态更新方法
      setIsLoading,
      setCurrentSessionId,
      setCurrentSessionData,
      setIsGenerating,
      updateChatMode,
      updateSelectedServer,
      updateSelectedModel,
      updateTemperature,
      toggleChatInWorkflow,
      updateChatPositionInWorkflow,
      updateChatSizeInWorkflow,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: [
        'selectedServerId',
        'isChatOpenInWorkflow',
        'chatPositionInWorkflow',
        'chatSizeInWorkflow',
        'typingSpeed',
        'temperature',
        'chatMode',
      ],
    },
  },
)
