import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { parse, stringify } from 'zipson'
import { flowsDB } from '@renderer/database/flowsDatabase'

export const useFlowsStore = defineStore(
  'flows',
  () => {
    // state
    // 存储所有工作流的数据，格式: { workflowId: { nodes: [], edges: [] } }
    const workflows = ref({})
    const currentWorkflowId = ref(null)

    // 大数据现在存储在 Dexie 数据库中，不再使用内存 Map

    // getters
    const currentWorkflow = computed(() => workflows.value[currentWorkflowId.value] || null)

    // actions
    function setCurrentWorkflow(workflowId) {
      currentWorkflowId.value = workflowId
    }
    // 监听节点链接
    let nodeLinkCallback = null
    function listenNodeLink(callback) {
      nodeLinkCallback = callback
    }
    function getListeners() {
      return {
        nodeLink: nodeLinkCallback,
      }
    }

    function saveWorkflow(workflowId, data) {
      const savedNodes = data.nodes.map((node: any) => ({
        ...node,
        position: node.position, // 确保保存位置信息
        data: {
          ...node.data,
          label: node.data.label || '未命名节点',
          description: node.data.description || '',
          // backgroundColor: node.data.backgroundColor || '#ffffff',
          icon: node.data.icon || {
            type: 'icon',
            value: 'flowbite:draw-square-outline',
          },
          params: node.data.params || {},
        },
      }))

      workflows.value = {
        ...workflows.value,
        [workflowId]: {
          nodes: savedNodes,
          edges: data.edges.map((edge: any) => ({
            ...edge,
            type: edge.type || 'smoothstep',
            animated: edge.animated !== undefined ? edge.animated : false, // 线条默认设置为 false
          })),
        },
      }
    }

    function getWorkflow(workflowId) {
      return workflows.value[workflowId] || { nodes: [], edges: [] }
    }

    function createWorkflow(workflowId) {
      if (!workflows.value[workflowId]) {
        workflows.value[workflowId] = {
          nodes: [],
          edges: [],
        }
      }
    }

    async function deleteWorkflow(workflowId: string) {
      if (workflows.value[workflowId]) {
        // 删除工作流的所有节点参数
        await flowsDB.deleteWorkflowParams(workflowId)

        // 删除工作流数据
        delete workflows.value[workflowId]
      }
    }

    async function saveNodeParams(workflowId, nodeId, params) {
      // console.log('saveNodeParams 调用:', { workflowId, nodeId })

      // 将大数据存储到 Dexie 数据库中
      const success = await flowsDB.saveNodeParams(workflowId, nodeId, params)

      if (success) {
        // 在工作流数据中存储引用标识
        const workflow = workflows.value[workflowId]
        if (workflow && workflow.nodes) {
          const nodeIndex = workflow.nodes.findIndex((node) => node.id === nodeId)
          if (nodeIndex !== -1) {
            // 确保 data 对象存在
            if (!workflow.nodes[nodeIndex].data) {
              workflow.nodes[nodeIndex].data = {}
            }

            // 只存储标识和时间戳，不存储实际的大数据
            workflow.nodes[nodeIndex].data.hasParams = true
            workflow.nodes[nodeIndex].data.lastUpdated = Date.now()
          }
        }
      }
    }

    async function getNodeParams(workflowId, nodeId) {
      // 从 Dexie 数据库中获取大数据
      const params = await flowsDB.getNodeParams(workflowId, nodeId)
      // console.log('数据库中获取大数据getNodeParams', params)
      return params || {}
    }

    async function deleteNodeParams(workflowId, nodeId) {
      // 从数据库中删除
      const success = await flowsDB.deleteNodeParams(workflowId, nodeId)

      if (success) {
        // 清理工作流数据中的引用标识
        const workflow = workflows.value[workflowId]
        if (workflow && workflow.nodes) {
          const nodeIndex = workflow.nodes.findIndex((node) => node.id === nodeId)
          if (nodeIndex !== -1 && workflow.nodes[nodeIndex].data) {
            workflow.nodes[nodeIndex].data.hasParams = false
            delete workflow.nodes[nodeIndex].data.lastUpdated
          }
        }
      }
    }

    return {
      // state
      workflows,
      currentWorkflowId,
      // getters
      currentWorkflow,
      // actions
      setCurrentWorkflow,
      saveWorkflow,
      createWorkflow,
      deleteWorkflow,
      getWorkflow,
      saveNodeParams,
      getNodeParams,
      deleteNodeParams,
      listenNodeLink,
      getListeners,
    }
  },
  {
    persist: {
      // key: 'flow-store',
      storage: localStorage,
      // serializer: {
      //   deserialize: parse,
      //   serialize: stringify,
      // },
      pick: ['workflows'],
    },
  },
)
