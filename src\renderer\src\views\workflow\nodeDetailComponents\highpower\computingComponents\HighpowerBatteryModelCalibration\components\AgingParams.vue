<template>
  <div class="space-y-4">
    <!-- 算法参数设置 -->
    <AlgorithmSettings
      param-type="aging"
      :algorithm-params="algorithmParams"
      :is-processing="isProcessing"
      @update:algorithm-params="updateAlgorithmParams"
    />

    <!-- 标定结果展示 -->
    <CalibrationResult
      param-type="aging"
      :task-id="taskId"
      :completed="completed"
      :server-info="serverInfo"
      :total-samples="totalSamples"
      :result-data="resultData"
      :task-status="taskStatus"
      :task-duration="taskDuration"
    />

    <div class="flex justify-between items-center my-4">
      <h2 class="text-xl font-semibold">老化参数</h2>
      <div class="flex gap-2">
        <Button size="sm" variant="outline" :disabled="isProcessing" @click="useRecommendedParams">
          <Sparkles class="w-4 h-4 mr-2 text-blue-500 animate-pulse" />
          使用推荐参数
        </Button>
      </div>
    </div>

    <!-- 老化参数列表 -->
    <div v-if="Object.keys(agingParams).length === 0" class="text-center py-8 text-gray-500">
      <EmptyState
        title="暂无老化参数数据"
        description="请点击下方按钮获取参数数据"
        icon="flowbite:clipboard-list-outline"
        :icon-size="80"
      >
        <template #action>
          <Button variant="outline" size="sm" :disabled="isProcessing" @click="getAgingParams">
            <Icon icon="tabler:refresh" class="w-4 h-4 mr-2" />
            获取老化参数
          </Button>
        </template>
      </EmptyState>
    </div>
    <div v-for="(params, category, categoryIndex) in agingParams" :key="categoryIndex">
      <Collapsible
        :open="agingOpenStates[categoryIndex]"
        @update:open="
          (isOpen) => $emit('update:agingOpenStates', updateOpenState(categoryIndex, isOpen))
        "
      >
        <CollapsibleTrigger class="transition-colors w-full" :disabled="isProcessing">
          <div
            class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm border border-gray-200"
          >
            <div class="flex items-center">
              <span
                class="w-2 h-2 rounded-full mr-2"
                :class="{
                  'bg-purple-500': categoryIndex % 4 === 0,
                  'bg-indigo-500': categoryIndex % 4 === 1,
                  'bg-pink-500': categoryIndex % 4 === 2,
                  'bg-teal-500': categoryIndex % 4 === 3,
                }"
              ></span>
              <div
                class="text-xl font-semibold text-gray-900 hover:text-gray-700 transition-colors"
              >
                {{ category }}
                <Badge variant="secondary">({{ params.length }}) 个参数</Badge>
                <Badge class="ml-2 bg-red-400 hover:bg-red-300">
                  已选择 {{ params.filter((p) => p.selected).length }} 个
                </Badge>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div
                class="flex items-center cursor-pointer"
                @click.stop="toggleParams(category, params)"
              >
                <Checkbox
                  class="mr-2"
                  :model-value="params.every((param) => param.selected)"
                  :disabled="isProcessing"
                  @update:model-value="() => toggleParams('agingParams', category, params)"
                />
                全选
              </div>
            </div>
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent class="mt-2">
          <div class="p-2">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              <ParamCard
                v-for="(param, paramIndex) in params"
                :key="paramIndex"
                :param="param"
                param-type="agingParams"
                :category="category"
                :param-index="paramIndex"
                :is-processing="isProcessing"
                @update-selection="updateParamSelection"
                @update-param="updateParamValues"
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  </div>
</template>

<script setup>
import { EmptyState } from '@renderer/components'
import { Icon } from '@iconify/vue'
import { Sparkles } from 'lucide-vue-next'
import { computed } from 'vue'
import AlgorithmSettings from './AlgorithmSettings.vue'
import CalibrationResult from './CalibrationResult.vue'
import ParamCard from './ParamCard.vue'
const props = defineProps({
  // 老化参数对象，包含多个分类和每个分类下的参数列表
  agingParams: {
    type: Object,
    required: true,
  },
  // 电化学参数是否已完成标定
  elecParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 老化参数是否已完成标定
  agingParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 各分类的展开状态数组
  agingOpenStates: {
    type: Array,
    required: true,
  },
  // 算法参数属性
  algorithmParams: {
    type: Object,
    required: true,
  },
  // 是否正在处理任务
  isProcessing: {
    type: Boolean,
    default: false,
  },
  // 任务ID
  taskId: {
    type: String,
    default: '',
  },
  // 是否已完成
  completed: {
    type: Boolean,
    default: false,
  },
  // 服务器信息
  serverInfo: {
    type: Object,
    default: () => ({ server_id: '', service_name: '' }),
  },
  totalSamples: {
    type: Number,
    default: 0,
  },
  // 结果数据，从父组件传递
  resultData: {
    type: Object,
    default: null,
  },
  // 任务状态，从父组件传递
  taskStatus: {
    type: String,
    default: 'Initializing',
  },
  // 任务持续时间，从父组件传递
  taskDuration: {
    type: String,
    default: '--',
  },
})
const agingParams = computed(() => props.agingParams)
const emit = defineEmits([
  'toggle-params',
  'update-param-selection',
  'use-recommended-params',
  'update:agingOpenStates',
  'update:algorithm-params',
  'get-params',
])
// 更新算法参数方法
const updateAlgorithmParams = (newParams) => {
  emit('update:algorithm-params', newParams)
}
// 更新参数值
const updateParamValues = ({ paramType, category, paramIndex, param }) => {
  // 更新参数值
  agingParams.value[category][paramIndex] = param
  console.log('agingParams更新参数值', agingParams.value)
  emit('update:aging-params', agingParams.value)
}
// 获取老化参数
const getAgingParams = () => {
  emit('get-params', 'Aging')
}
// 计算选中的参数数量
const selectedAgingCount = computed(() => {
  return Object.values(props.agingParams).reduce((total, params) => {
    return total + params.filter((p) => p.selected).length
  }, 0)
})

// 参数全选功能
const toggleParams = (paramType, category, params) => {
  if (!paramType || !category || !params || !Array.isArray(params)) {
    return
  }
  emit('toggle-params', paramType, category, params)
}

// 更新单个参数的选中状态
const updateParamSelection = ({ paramType, category, paramIndex, value }) => {
  emit('update-param-selection', paramType, category, paramIndex, value)
}

// 使用推荐参数功能
const useRecommendedParams = () => {
  emit('use-recommended-params', 'agingParams')
}
// 更新分类的展开状态
const updateOpenState = (index, isOpen) => {
  const newOpenStates = [...props.agingOpenStates]
  newOpenStates[index] = isOpen
  return newOpenStates
}
</script>
