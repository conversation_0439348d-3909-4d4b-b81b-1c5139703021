<template>
  <div>
    <Container>
      <div class="flex items-center justify-between">
        <HeaderTitle :title="t('server.title')" :tooltip="t('server.tooltip')" :show-icon="true" />
        <div class="flex items-center space-x-2">
          <label for="refresh-interval" class="text-sm">刷新时间间隔 (秒):</label>
          <Input
            v-model="refreshTimer"
            type="number"
            min="1"
            step="1"
            placeholder="刷新间隔"
            class="w-16 h-8"
          />
          <Button
            variant="outline"
            size="sm"
            :class="{
              'border-2 border-blue-500': refreshTimer !== currentRefreshTimer, // 动态添加高亮边框
              'border-gray-300': refreshTimer === currentRefreshTimer, // 正常边框
            }"
            @click="setRefreshTimer"
          >
            <RefreshCw class="w-4 h-4 mr-2" :class="{ 'animate-spin': serverStore.loading }" />
            {{ '应用' }}
          </Button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="w-full overflow-x-auto">
        <!-- <Loading v-if="serverStore.loading" /> -->
        <DataTable
          table-id="serverList"
          :data="serverStore.servers"
          :columns="columns"
          max-height="calc(100vh - 200px)"
          class="mt-4"
        >
          <!--服务器状态-->
          <template #serverStatus="{ item }">
            <TableCell class="max-w-24">
              <Badge :class="serverStore.getStatusClass((item as Server).serverStatus)">
                {{ serverStore.getStatusText((item as Server).serverStatus) }}
              </Badge>
            </TableCell>
          </template>

          <!-- 使用情况 -->
          <template #usage="{ item }">
            <TableCell class="max-w-12">
              <div class="flex space-x-1">
                <Button variant="ghost" title="点击查看" @click="openUsage(item as Server)">
                  <AlignEndHorizontal class="w-4 h-4 hover:animate-spin" stroke-width="2.5" />
                  <span class="text-gray-500">详情</span>
                </Button>
              </div>
            </TableCell>
          </template>

          <!-- 操作 -->
          <template #actions="{ item }">
            <TableCell class="text-right">
              <div class="flex justify-end space-x-1">
                <DropdownMenu>
                  <DropdownMenuTrigger as-child class="cursor-pointer">
                    <Ellipsis class="w-4 h-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem disabled @click="handleDropdown('close', item as Server)">
                      <Power class="w-4 h-4" />
                      <span>关闭</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem disabled @click="handleDropdown('restart', item as Server)">
                      <RotateCcw class="w-4 h-4" />
                      <span>重启</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem disabled @click="handleDropdown('pause', item as Server)">
                      <CirclePause class="w-4 h-4" />
                      <span>暂停</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="handleDropdown('delete', item as Server)">
                      <Trash class="w-4 h-4" />
                      <span>删除</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </template>
        </DataTable>
      </div>

      <!-- 资源使用对话框 -->
      <Dialog :open="isOpen" @update:open="closeUsage()">
        <DialogContent class="max-w-5xl mx-auto bg-background rounded-md shadow-sm border p-0 over">
          <DialogTitle></DialogTitle>
          <ServerUsage v-if="serverItem" :server-id="serverItem.serverId" />
        </DialogContent>
      </Dialog>
      <!-- <div class="p-4">
        <DynamicForm
          :fields="formFields"
          :rows="2"
          :cols="3"
          @submit="handleSubmit"
          ref="formRef"
        />
      </div> -->
      <!-- <div>
        <TableSearchForm
        :fields="searchFields" @search="handleSearch" @reset="handleReset"
        />
      </div> -->
    </Container>
  </div>
</template>

<script setup lang="ts">
import { Container, DataTable, HeaderTitle } from '@renderer/components'
import DialogTitle from '@renderer/components/ui/dialog/DialogTitle.vue'
import { createServerService } from '@renderer/config/api/grpc/serverService'
import { useLanguage } from '@renderer/config/hooks'
import { Server } from '@renderer/config/types/api/server'
import { useServerStore } from '@renderer/store'
import { formatDate } from '@renderer/utils/utils'
import { useConfirmDialog } from '@renderer/components/dialog/useConfirmDialog'
import {
  AlignEndHorizontal,
  CirclePause,
  Ellipsis,
  Power,
  RefreshCw,
  RotateCcw,
  Trash,
} from 'lucide-vue-next'
import { onMounted, onUnmounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import { ServerUsage } from './components/index'

const { t } = useLanguage()
const { openConfirm } = useConfirmDialog()

const serverStore = useServerStore()
const serverService = createServerService()
let refreshInterval: number | null = null
const refreshTimer = ref<number>(5)
const currentRefreshTimer = ref<number>(5)
const isOpen = ref(false)
// 选中的服务器基本信息
const serverItem = ref<Server>()
const isDeleteOpen = ref(false)

// 表格列配置
const columns = [
  {
    title: '服务器名称',
    field: 'serverName',
    copyable: true,
  },
  {
    title: '服务器类型',
    field: 'serverType',
    copyable: true,
  },
  {
    title: '服务器ID',
    field: 'serverId',
    copyable: true,
  },
  {
    title: '服务器地址',
    field: 'url',
    copyable: true,
  },
  {
    title: '服务器状态',
    field: 'serverStatus',
    useSlot: true,
  },
  {
    title: '所在地区',
    field: 'region',
    copyable: true,
  },
  {
    title: '服务器版本',
    field: 'version',
    copyable: true,
  },
  {
    title: '注册时间',
    field: 'createTime',
    formatter: (value: number) => (value ? formatDate(new Date(value * 1000)) : '-'),
  },
  {
    title: '更新时间',
    field: 'updateTime',
    formatter: (value: number) => (value ? formatDate(new Date(value * 1000)) : '-'),
  },
  {
    title: '使用情况',
    field: 'usage',
    useSlot: true,
  },
  {
    title: '操作',
    field: 'actions',
    useSlot: true,
    headerClassName: 'w-12',
  },
]

// 设置serverItem
const setServerItem = (item: Server) => {
  serverItem.value = item
}

// 打开关闭 资源用量 对话框
const closeUsage = () => {
  isOpen.value = false
}
const openUsage = async (item: Server) => {
  setServerItem(item)
  isOpen.value = true
}

// 删除服务器信息
const deleteServer = async (serverItem: Server) => {
  if (serverItem.serverStatus !== 'Expired') {
    toast.error('请先关闭服务器，再进行删除')
    // 刷新服务列表
    refreshServerList()
    return
  }
  const res = await serverService.deleteServerInfo(serverItem.serverId)
  if (res.status === 'Success') {
    toast.success('删除成功')
  } else {
    toast.error('删除失败')
  }
  refreshServerList()
}

const handleDropdown = async (val: string, item: Server) => {
  console.log('folder', val, item)
  switch (val) {
    case 'close':
      break
    case 'restart':
      break
    case 'pause':
      break
    case 'delete':
      // 关闭菜单
      setServerItem(item)
      openConfirm({
        title: '删除任务',
        description: '任务将被删除, 请保存任务结果。',
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: async () => {
          await deleteServer(serverItem.value)
        },
      })
      // 因为menu和dialog是兄弟节点，有bug，需要手动关闭
      document.body.style.pointerEvents = ''
      document.body.style.overflow = ''
      break
  }
}

// 刷新服务列表
const refreshServerList = async () => {
  if (serverStore.loading) return
  try {
    await serverStore.updateServerList()
  } catch (error) {
    console.error('刷新服务列表失败:', error)
  }
}

//  设置刷新时间
const setRefreshTimer = () => {
  if (refreshTimer.value < 2) {
    // 还原到当前的刷新时间
    refreshTimer.value = currentRefreshTimer.value
    toast.info('刷新间隔时间请大于2s')
    return
  }

  if (refreshInterval !== null) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
  refreshInterval = window.setInterval(() => {
    refreshServerList()
  }, refreshTimer.value * 1000)
  serverStore.setRefreshTimer(refreshTimer.value)
  currentRefreshTimer.value = refreshTimer.value
}

onMounted(() => {
  refreshTimer.value = serverStore.refreshTimer
  currentRefreshTimer.value = refreshTimer.value
  serverStore.updateServerList()
  setRefreshTimer()
})

onUnmounted(() => {
  if (refreshInterval !== null) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
})
</script>

<style lang="scss" scoped>
:deep(table) {
  table-layout: fixed;
  width: 100%;
}

:deep(th),
:deep(td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
