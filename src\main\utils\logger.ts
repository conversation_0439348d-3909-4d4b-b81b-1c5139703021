import { app } from 'electron'
import log from 'electron-log'
import fs from 'fs'
import path from 'path'

/**
 * 日志级别类型
 */
export type LogLevel = 'error' | 'warn' | 'info' | 'verbose' | 'debug' | 'silly'

/**
 * 初始化日志系统
 * @param options 日志配置选项
 */
export function initLogger(
  options: {
    processType?: string
    logLevel?: LogLevel
    consoleLevel?: LogLevel | false
    maxSize?: number
    showInConsole?: boolean
  } = {},
): typeof log {
  const {
    processType = 'main',
    logLevel = 'info',
    consoleLevel = 'debug',
    maxSize = 10 * 1024 * 1024, // 10MB
    showInConsole = false, // 默认不在控制台显示
  } = options

  try {
    // 获取项目根目录路径
    const rootPath = path.resolve(process.cwd())
    const logsPath = path.join(rootPath, 'logs')
    // 确保日志目录存在
    try {
      if (!fs.existsSync(logsPath)) {
        fs.mkdirSync(logsPath, { recursive: true })
      }
      console.log(`日志目录已创建: ${logsPath}`)
    } catch (dirError) {
      console.error('创建日志目录失败:', dirError)

      // 尝试使用临时目录
      const tempDir = app.getPath('temp')
      const tempLogsPath = path.join(tempDir, 'mattverse-logs')

      try {
        if (!fs.existsSync(tempLogsPath)) {
          fs.mkdirSync(tempLogsPath, { recursive: true })
        }
        console.log(`使用临时日志目录: ${tempLogsPath}`)

        // 配置日志文件路径为临时目录
        log.transports.file.resolvePath = () => {
          const now = new Date()
          const year = now.getFullYear()
          const month = String(now.getMonth() + 1).padStart(2, '0')
          const day = String(now.getDate()).padStart(2, '0')
          const dateStr = `${year}-${month}-${day}`

          return path.join(tempLogsPath, `${processType}-${dateStr}.log`)
        }
      } catch (tempDirError) {
        console.error('创建临时日志目录也失败:', tempDirError)
        // 如果连临时目录也创建失败，则禁用文件日志
        log.transports.file.level = false
        console.log('文件日志已禁用，只使用控制台日志')
      }
    }

    // 如果没有设置自定义路径解析函数，则设置默认的
    if (log.transports.file.level !== false && !log.transports.file.resolvePath) {
      // 配置日志文件路径
      log.transports.file.resolvePath = () => {
        // 获取当前日期，格式为 YYYY-MM-DD
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const dateStr = `${year}-${month}-${day}`

        // 构建日志文件路径：logs/processType-YYYY-MM-DD.log
        return path.join(logsPath, `${processType}-${dateStr}.log`)
      }
    }

    // 设置日志级别
    log.transports.file.level = logLevel
    // 根据 showInConsole 参数决定是否在控制台显示日志
    log.transports.console.level = showInConsole ? consoleLevel : false

    // 设置日志文件大小上限
    log.transports.file.maxSize = maxSize

    // 日志归档处理
    log.transports.file.archiveLog = (oldLog) => {
      try {
        // 归档文件格式: processType-YYYY-MM-DD.old.N.log
        const logFilePath = oldLog.path
        const dirName = path.dirname(logFilePath)
        const fileName = path.basename(logFilePath)
        const baseName = fileName.replace('.log', '')

        // 查找现有归档文件，确定新的序号
        const files = fs.readdirSync(dirName)
        const archiveFiles = files.filter(
          (f) => f.startsWith(`${baseName}.old.`) && f.endsWith('.log'),
        )
        const nextNumber = archiveFiles.length + 1

        const newName = path.join(dirName, `${baseName}.old.${nextNumber}.log`)
        fs.renameSync(logFilePath, newName)
        return newName
      } catch (e) {
        console.error('无法归档日志文件', e)
        return null
      }
    }

    // 记录初始化信息
    log.info(`${processType} 日志系统初始化完成`, {
      path: log.transports.file.getFile().path,
      level: logLevel,
      time: new Date().toISOString(),
    })
  } catch (error) {
    console.error('日志系统初始化失败:', error)

    // 确保控制台日志仍然可用
    log.transports.console.level = showInConsole ? consoleLevel : false

    // 禁用文件日志
    log.transports.file.level = false
    console.log('文件日志已禁用，只使用控制台日志')
  }

  return log
}

// 导出预配置的日志实例
export const logger = initLogger()

// 默认导出
export default logger
