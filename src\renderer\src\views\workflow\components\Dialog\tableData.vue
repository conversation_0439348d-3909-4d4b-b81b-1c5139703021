<template>
  <div class="w-full h-full">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead
            v-for="(column, index) in tableColumn"
            :key="index"
            :style="{ width: column.width || 'auto' }"
            :class="{ border: isBordered }"
          >
            {{ column.title }}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="(row, i) in tableData" :key="i">
          <TableCell v-for="(column, j) in tableColumn" :key="j" :class="{ border: isBordered }">
            <div v-if="column.isEdit">
              <div v-if="column.type === 'select'">
                <Select
                  v-model="row[column.key]"
                  @update:model-value="
                    column.options?.emits?.update!({
                      row,
                      column,
                      value: $event,
                      rowIndex: i,
                      columnIndex: j,
                    })
                  "
                >
                  <SelectTrigger>
                    <SelectValue :placeholder="column.options?.selectTrigger || 'Select'" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem
                        v-for="(opt, o) in column?.options?.selectOptions"
                        :key="o"
                        :value="opt.value"
                      >
                        {{ opt.label }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div v-if="column.type === 'number'">
                <NumberField
                  v-model="row[column.key]"
                  :min="column.options?.props?.min"
                  :max="column.options?.props?.max"
                  :step="column.options?.props?.step"
                  :default-value="row[column.key]"
                  @update:model-value="
                    column.options?.emits?.update!({
                      row,
                      column,
                      value: $event,
                      rowIndex: i,
                      columnIndex: j,
                    })
                  "
                >
                  <NumberFieldContent>
                    <NumberFieldDecrement />
                    <NumberFieldInput />
                    <NumberFieldIncrement />
                  </NumberFieldContent>
                </NumberField>
              </div>
            </div>
            <div v-else-if="column.isRender">
              <component
                :is="
                  column?.options?.customRender!({
                    row,
                    column,
                    value: '',
                    rowIndex: i,
                    columnIndex: j,
                  })
                "
              />
            </div>
            <div v-else>
              {{ row[column.key] }}
            </div>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
</template>
<script setup lang="ts">
import { FunctionalComponent, onMounted, PropType, Ref, ref, watch } from 'vue'
// 深层合并两个对象
import { merge } from 'lodash'
interface SelectOption {
  value: string
  label: string
}
interface Props {
  min: number
  max: number
  step: number
}
interface UpdateEvent {
  row: TableData
  column: TableColumn
  value: number | string
  rowIndex: number
  columnIndex: number
}
interface Emits {
  update: (value: UpdateEvent) => void
}
interface Options {
  selectOptions?: SelectOption[]
  selectTrigger?: string
  customRender?: (value: UpdateEvent) => FunctionalComponent
  props?: Props
  emits?: Emits
}
interface TableColumn {
  title: string
  key: string
  width?: number
  isEdit?: boolean
  isRender?: boolean
  type?: 'select' | 'number'
  options?: Options
}
interface TableData {
  [key: string]: any
}
const tableColumn: Ref<TableColumn[]> = ref([])
const tableData: Ref<TableData[]> = ref([])
const props = defineProps({
  columns: {
    type: Array as PropType<TableColumn[]>,
    default: () => [],
  },
  dataSources: {
    type: Array as PropType<TableData[]>,
    default: () => [],
  },
  isBordered: {
    type: Boolean,
    default: false,
  },
})
watch(
  () => props.columns,
  () => {
    tableColumn.value = props.columns
  },
)
watch(
  () => props.dataSources,
  () => {
    tableData.value = props.dataSources
  },
)
onMounted(() => {
  tableColumn.value = props.columns
  tableData.value = props.dataSources
})
// 更新表头
const updateColumn = (i: number, column: TableColumn) => {
  tableColumn.value[i] = merge(tableColumn.value[i], column)
}
// 更新数据
const updateData = (i: number, row: TableData) => {
  tableData.value[i] = merge(tableData.value[i], row)
}
// 添加行
const addRow = (row: TableData) => {
  tableData.value.push(row)
}
// 删除行
const removeRow = (i: number) => {
  tableData.value.splice(i, 1)
}
// 获取数据
const getData = () => {
  return tableData.value
}
defineExpose({
  updateColumn,
  updateData,
  addRow,
  removeRow,
  getData,
})
</script>
