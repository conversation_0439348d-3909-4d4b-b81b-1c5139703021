import { ref, computed, type Ref } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { useVueFlow } from '@vue-flow/core'

interface Node {
  id: string
  position: { x: number; y: number }
  dimensions?: { width: number; height: number }
  computed?: { width: number; height: number }
}

export const useSelectionBox = (workflowId?: string, nodesRef?: Ref<any[]>) => {
  const { getNodes, project } = useVueFlow(workflowId)

  // 状态管理
  const isSelecting = ref(false)
  const isMoving = ref(false)
  const selectedNodeIds = ref<Set<string>>(new Set())
  const justFinishedSelection = ref(false)

  // 选择框坐标（屏幕坐标用于显示，流程图坐标用于检测）
  const selectionBox = ref({
    visible: false,
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
  })
  const selectionBoxFlow = ref({
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
  })

  // 移动相关状态
  const moveStartPos = ref({ x: 0, y: 0 })
  const selectionBoxStartPos = ref({ startX: 0, startY: 0, endX: 0, endY: 0 })

  // 更新节点选择状态
  const updateNodeSelection = () => {
    const nodes = nodesRef?.value || getNodes.value
    if (!nodes?.length) return

    const newSelectedIds = new Set<string>()
    nodes.forEach((node: any) => {
      if (isNodeInSelectionBox(node)) {
        newSelectedIds.add(node.id)
      }
    })
    selectedNodeIds.value = newSelectedIds

    if (newSelectedIds.size > 0) {
      console.log(`Selected ${newSelectedIds.size} nodes:`, Array.from(newSelectedIds))
    }
  }

  // 防抖更新
  const debouncedUpdateSelection = useDebounceFn(updateNodeSelection, 100)

  // 计算选择框样式
  const selectionBoxStyle = computed(() => {
    if (!selectionBox.value.visible) return { display: 'none' }

    const { startX, startY, endX, endY } = selectionBox.value
    const left = Math.min(startX, endX)
    const top = Math.min(startY, endY)
    const width = Math.abs(endX - startX)
    const height = Math.abs(endY - startY)

    return {
      position: 'absolute',
      left: `${left}px`,
      top: `${top}px`,
      width: `${width}px`,
      height: `${height}px`,
      border: '2px dashed #3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      pointerEvents: isSelecting.value ? 'none' : 'all',
      cursor: isMoving.value ? 'grabbing' : 'grab',
      zIndex: 1000,
      opacity: width >= 10 && height >= 10 ? 1 : 0.5,
      transition: 'opacity 0.1s ease-out',
    }
  })

  // 开始选择
  const startSelection = (event: MouseEvent, containerElement: HTMLElement) => {
    // 检查是否点击在节点或其他交互元素上
    const target = event.target as HTMLElement
    if (
      target.closest('.vue-flow__node') ||
      target.closest('.vue-flow__edge') ||
      target.closest('.vue-flow__controls') ||
      target.closest('.vue-flow__minimap') ||
      target.closest('[data-testid]') ||
      target.closest('button') ||
      target.closest('.vue-flow__handle')
    ) {
      return
    }

    isSelecting.value = true
    selectionBox.value.visible = true

    const rect = containerElement.getBoundingClientRect()
    const clientX = event.clientX - rect.left
    const clientY = event.clientY - rect.top

    // 保存屏幕坐标（用于显示选择框）
    selectionBox.value.startX = clientX
    selectionBox.value.startY = clientY
    selectionBox.value.endX = clientX
    selectionBox.value.endY = clientY

    // 将屏幕坐标转换为流程图坐标（用于节点检测）
    const flowCoords = project({ x: clientX, y: clientY })
    selectionBoxFlow.value.startX = flowCoords.x
    selectionBoxFlow.value.startY = flowCoords.y
    selectionBoxFlow.value.endX = flowCoords.x
    selectionBoxFlow.value.endY = flowCoords.y

    // 清空之前的选择
    selectedNodeIds.value.clear()

    window.logger?.info('Selection started:', {
      screenCoords: { x: clientX, y: clientY },
      flowCoords: { x: flowCoords.x, y: flowCoords.y },
    })

    event.preventDefault()
    event.stopPropagation()
  }

  // 更新选择框
  const updateSelection = (event: MouseEvent, containerElement: HTMLElement) => {
    if (!isSelecting.value) return

    const rect = containerElement.getBoundingClientRect()
    const clientX = event.clientX - rect.left
    const clientY = event.clientY - rect.top

    // 更新屏幕坐标（用于显示选择框）
    selectionBox.value.endX = clientX
    selectionBox.value.endY = clientY

    // 将屏幕坐标转换为流程图坐标
    const startFlowCoords = project({ x: selectionBox.value.startX, y: selectionBox.value.startY })
    const endFlowCoords = project({ x: clientX, y: clientY })

    // 确保坐标顺序正确（左上角到右下角）
    selectionBoxFlow.value.startX = Math.min(startFlowCoords.x, endFlowCoords.x)
    selectionBoxFlow.value.startY = Math.min(startFlowCoords.y, endFlowCoords.y)
    selectionBoxFlow.value.endX = Math.max(startFlowCoords.x, endFlowCoords.x)
    selectionBoxFlow.value.endY = Math.max(startFlowCoords.y, endFlowCoords.y)

    // 防抖更新节点选择
    debouncedUpdateSelection()
  }

  // 结束选择
  const endSelection = () => {
    if (!isSelecting.value) return

    // 最后一次更新节点选择状态
    updateNodeSelection()

    window.logger?.info('Selection ended, selected nodes:', Array.from(selectedNodeIds.value))
    isSelecting.value = false
    // 隐藏选择框
    selectionBox.value.visible = false

    // 设置刚完成选择标志，防止立即被清除
    justFinishedSelection.value = selectedNodeIds.value.size > 0

    // 延迟重置标志
    if (justFinishedSelection.value) {
      setTimeout(() => {
        justFinishedSelection.value = false
      }, 100)
    }
  }

  // 检查节点是否在选择框内
  const isNodeInSelectionBox = (node: Node): boolean => {
    const { startX, startY, endX, endY } = selectionBoxFlow.value
    const width = Math.abs(endX - startX)
    const height = Math.abs(endY - startY)
    if (width < 10 || height < 10) return false

    const nodeWidth = node.dimensions?.width || node.computed?.width || 260
    const nodeHeight = node.dimensions?.height || node.computed?.height || 80

    const nodeRect = {
      left: node.position.x,
      top: node.position.y,
      right: node.position.x + nodeWidth,
      bottom: node.position.y + nodeHeight,
    }

    const boxRect = {
      left: Math.min(startX, endX),
      top: Math.min(startY, endY),
      right: Math.max(startX, endX),
      bottom: Math.max(startY, endY),
    }

    // 计算重叠区域
    const overlapLeft = Math.max(nodeRect.left, boxRect.left)
    const overlapTop = Math.max(nodeRect.top, boxRect.top)
    const overlapRight = Math.min(nodeRect.right, boxRect.right)
    const overlapBottom = Math.min(nodeRect.bottom, boxRect.bottom)

    if (overlapLeft >= overlapRight || overlapTop >= overlapBottom) return false

    const overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop)
    const nodeArea = nodeWidth * nodeHeight
    return overlapArea / nodeArea > 0.1
  }

  // 工具函数
  const clearSelection = () => {
    selectionBox.value.visible = false
    selectedNodeIds.value.clear()
    isSelecting.value = false
    console.log('Selection cleared - called from:', new Error().stack?.split('\n')[2])
  }

  const isNodeSelected = (nodeId: string) => selectedNodeIds.value.has(nodeId)

  const selectAll = () => {
    selectedNodeIds.value.clear()
    getNodes.value.forEach((node: any) => selectedNodeIds.value.add(node.id))
    window.logger?.info('Select all nodes:', Array.from(selectedNodeIds.value))
  }

  const toggleNodeSelection = (nodeId: string) => {
    selectedNodeIds.value.has(nodeId)
      ? selectedNodeIds.value.delete(nodeId)
      : selectedNodeIds.value.add(nodeId)
  }

  // 移动选择框相关
  const startMoveSelection = (event: MouseEvent) => {
    if (!selectionBox.value.visible) return
    isMoving.value = true
    moveStartPos.value = { x: event.clientX, y: event.clientY }
    selectionBoxStartPos.value = { ...selectionBox.value }
    window.logger?.info('Start moving selection box')
    event.preventDefault()
    event.stopPropagation()
  }

  const moveSelection = (event: MouseEvent) => {
    if (!isMoving.value) return
    const deltaX = event.clientX - moveStartPos.value.x
    const deltaY = event.clientY - moveStartPos.value.y

    Object.assign(selectionBox.value, {
      startX: selectionBoxStartPos.value.startX + deltaX,
      startY: selectionBoxStartPos.value.startY + deltaY,
      endX: selectionBoxStartPos.value.endX + deltaX,
      endY: selectionBoxStartPos.value.endY + deltaY,
    })
    debouncedUpdateSelection()
  }

  const endMoveSelection = () => {
    if (!isMoving.value) return
    isMoving.value = false
    updateNodeSelection()
    window.logger?.info('End moving selection box')
  }

  // 样式计算
  const tooltipStyle = computed(() => {
    if (!selectionBox.value.visible || selectedNodeIds.value.size === 0) {
      return { display: 'none' }
    }
    const left = Math.min(selectionBox.value.startX, selectionBox.value.endX)
    const top = Math.min(selectionBox.value.startY, selectionBox.value.endY)
    return {
      position: 'absolute',
      left: `${left}px`,
      top: `${top - 30}px`,
      backgroundColor: '#3b82f6',
      color: 'white',
      padding: '4px 8px',
      borderRadius: '4px',
      fontSize: '12px',
      fontWeight: '500',
      zIndex: 1001,
      pointerEvents: 'none',
      whiteSpace: 'nowrap',
    }
  })

  const tooltipText = computed(() => {
    const count = selectedNodeIds.value.size
    return count > 0 ? `已选择 ${count} 个节点` : ''
  })

  return {
    // 状态
    isSelecting,
    isMoving,
    selectionBox,
    selectedNodeIds,
    justFinishedSelection,
    selectionBoxStyle,
    tooltipStyle,
    tooltipText,

    // 方法
    startSelection,
    updateSelection,
    endSelection,
    clearSelection,
    isNodeSelected,
    selectAll,
    toggleNodeSelection,
    startMoveSelection,
    moveSelection,
    endMoveSelection,
  }
}
