<template>
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold text-gray-900">{{ title }}</h2>
      <Button v-if="hasData" variant="destructive" size="sm" @click="$emit('reset')">
        重新上传
      </Button>
    </div>

    <!-- SOC-OCV曲线类型 - 双上传面板 -->
    <template v-if="chartType === 'SOCOCVCurve'">
      <!-- 正极上传面板 -->
      <div class="mb-6 border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-lg font-medium text-gray-800">正极数据</h3>
          <Button
            v-if="positiveData.length"
            variant="outline"
            size="sm"
            @click="$emit('reset-positive')"
          >
            重置
          </Button>
        </div>
        <FileDropUpload
          v-if="!positiveData.length || isResettingPositive"
          :accept-types="['.csv']"
          :max-size="20"
          @file-selected="(file) => $emit('file-selected-positive', file)"
          @error="(error) => $emit('upload-error', error)"
          @progress="(progress) => $emit('upload-progress', progress)"
          @upload-complete="$emit('upload-complete')"
        />
        <div v-if="positiveData.length && !isResettingPositive" class="h-48">
          <div ref="positiveChartRef" class="w-full h-full chart-container"></div>
        </div>
      </div>

      <!-- 负极上传面板 -->
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-lg font-medium text-gray-800">负极数据</h3>
          <Button
            v-if="negativeData.length"
            variant="outline"
            size="sm"
            @click="$emit('reset-negative')"
          >
            重置
          </Button>
        </div>
        <FileDropUpload
          v-if="!negativeData.length || isResettingNegative"
          :accept-types="['.csv']"
          :max-size="20"
          @file-selected="(file) => $emit('file-selected-negative', file)"
          @error="(error) => $emit('upload-error', error)"
          @progress="(progress) => $emit('upload-progress', progress)"
          @upload-complete="$emit('upload-complete')"
        />
        <div v-if="negativeData.length && !isResettingNegative" class="h-48">
          <div ref="negativeChartRef" class="w-full h-full chart-container"></div>
        </div>
      </div>
    </template>

    <!-- 其他图表类型 - 单上传面板 -->
    <template v-else>
      <FileDropUpload
        v-if="!data.length || isResetting"
        :accept-types="['.csv']"
        :max-size="20"
        @file-selected="(file) => $emit('file-selected', file)"
        @error="(error) => $emit('upload-error', error)"
        @progress="(progress) => $emit('upload-progress', progress)"
        @upload-complete="$emit('upload-complete')"
      />
      <div v-if="data.length && !isResetting" class="grid grid-cols-1 gap-6">
        <!-- 循环数据分开展示 -->
        <template v-if="chartType === 'CycleData'">
          <!-- 电流图表 -->
          <div class="border border-gray-200 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-800 mb-2">电流数据</h3>
            <div class="h-48">
              <div ref="currentChartRef" class="w-full h-full chart-container"></div>
            </div>
          </div>

          <!-- 电压图表 -->
          <div class="border border-gray-200 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-800 mb-2">电压数据</h3>
            <div class="h-48">
              <div ref="voltageChartRef" class="w-full h-full chart-container"></div>
            </div>
          </div>

          <!-- 容量图表 -->
          <div class="border border-gray-200 rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-800 mb-2">容量数据</h3>
            <div class="h-48">
              <div ref="capacityChartRef" class="w-full h-full chart-container"></div>
            </div>
          </div>
        </template>

        <!-- 其他类型图表 -->
        <template v-else>
          <div>
            <div ref="chartRef" class="w-full h-48 chart-container"></div>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script setup>
import { onMounted, watch, ref, computed, onUnmounted, nextTick } from 'vue'
import { FileDropUpload } from '@renderer/components'
import * as echarts from 'echarts'

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    required: true,
  },
  // 图表类型，限定为SOCOCVCurve或CycleData
  chartType: {
    type: String,
    required: true,
    validator: (value) => ['SOCOCVCurve', 'CycleData'].includes(value),
  },
  // 图表数据数组
  data: {
    type: Array,
    required: true,
  },
  // 正极数据（仅SOCOCVCurve类型使用）
  positiveData: {
    type: Array,
    default: () => [],
  },
  // 负极数据（仅SOCOCVCurve类型使用）
  negativeData: {
    type: Array,
    default: () => [],
  },
  // 是否处于重置状态
  isResetting: {
    type: Boolean,
    default: false,
  },
  // 正极数据是否处于重置状态
  isResettingPositive: {
    type: Boolean,
    default: false,
  },
  // 负极数据是否处于重置状态
  isResettingNegative: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'reset', // 重置事件
  'reset-positive', // 重置正极数据事件
  'reset-negative', // 重置负极数据事件
  'file-selected', // 文件选择事件
  'file-selected-positive', // 正极文件选择事件
  'file-selected-negative', // 负极文件选择事件
  'upload-error', // 上传错误事件
  'upload-progress', // 上传进度事件
  'upload-complete', // 上传完成事件
  'chart-ready', // 图表准备就绪事件
])

// 判断是否有数据
const hasData = computed(() => {
  if (props.chartType === 'SOCOCVCurve') {
    return props.positiveData.length > 0 || props.negativeData.length > 0
  }
  return props.data.length > 0
})

const chartRef = ref(null)
const positiveChartRef = ref(null)
const negativeChartRef = ref(null)
const currentChartRef = ref(null)
const voltageChartRef = ref(null)
const capacityChartRef = ref(null)

// 图表实例
const chartInstance = ref(null)
const positiveChartInstance = ref(null)
const negativeChartInstance = ref(null)
const currentChartInstance = ref(null)
const voltageChartInstance = ref(null)
const capacityChartInstance = ref(null)

// 标记图表是否处于激活状态
const isActive = ref(true)

// 安全地调整图表大小
const safeResize = (chart) => {
  if (!chart || !isActive.value) return
  try {
    // 首先检查chart对象是否有resize方法，以及DOM是否存在
    if (typeof chart.resize === 'function' && chart.getDom && chart.getDom()) {
      const dom = chart.getDom()
      // 检查DOM元素是否仍在文档中
      if (dom && document.body.contains(dom) && dom.offsetParent !== null) {
        chart.resize()
      }
    }
  } catch (e) {
    console.error('调整图表大小失败:', e)
  }
}

// 安全地清理图表实例
const safeDispose = (chart) => {
  if (!chart) return null
  try {
    if (typeof chart.dispose === 'function') {
      chart.dispose()
    }
  } catch (e) {
    console.error('清理图表实例失败:', e)
  }
  return null
}

// 清理所有图表实例
const cleanupCharts = () => {
  try {
    // 清理所有图表实例
    chartInstance.value = safeDispose(chartInstance.value)
    positiveChartInstance.value = safeDispose(positiveChartInstance.value)
    negativeChartInstance.value = safeDispose(negativeChartInstance.value)
    currentChartInstance.value = safeDispose(currentChartInstance.value)
    voltageChartInstance.value = safeDispose(voltageChartInstance.value)
    capacityChartInstance.value = safeDispose(capacityChartInstance.value)
  } catch (error) {
    console.error('清理图表实例失败:', error)
  }
}

// 初始化图表
const initChart = () => {
  // 防止非激活状态下初始化
  if (!isActive.value) return

  // 确保DOM元素已经渲染
  nextTick(() => {
    try {
      // 先清理旧图表
      cleanupCharts()

      if (props.chartType === 'SOCOCVCurve') {
        // 初始化正极图表
        if (props.positiveData.length && !props.isResettingPositive && positiveChartRef.value) {
          try {
            positiveChartInstance.value = echarts.init(positiveChartRef.value)

            const socData = props.positiveData.map((item) => item.soc)
            const ocvData = props.positiveData.map((item) => item.ocv)

            // 设置图表选项
            const positiveOption = {
              title: {
                text: '正极 SOC-OCV曲线',
                left: 'center',
                top: 5,
                textStyle: { fontSize: 14 },
              },
              tooltip: {
                trigger: 'axis',
                formatter: '{a} <br/>{b}: {c} V',
                axisPointer: {
                  type: 'cross',
                  label: {
                    backgroundColor: '#6a7985',
                  },
                },
              },
              grid: { top: 40, right: 50, bottom: 50, left: 60 },
              xAxis: {
                type: 'category',
                name: 'SOC',
                data: socData,
                nameLocation: 'center',
                nameGap: 30,
                nameTextStyle: {
                  fontSize: 14,
                  fontWeight: 'bold',
                },
              },
              yAxis: { type: 'value', name: 'OCV (V)' },
              series: [
                {
                  name: 'OCV',
                  type: 'line',
                  data: ocvData,
                  smooth: true,
                  symbol: 'none',
                  lineStyle: { color: '#f56c6c' },
                },
              ],
            }

            positiveChartInstance.value.setOption(positiveOption)
          } catch (e) {
            console.error('初始化正极图表失败:', e)
            positiveChartInstance.value = null
          }
        }

        // 初始化负极图表
        if (props.negativeData.length && !props.isResettingNegative && negativeChartRef.value) {
          try {
            negativeChartInstance.value = echarts.init(negativeChartRef.value)

            const socData = props.negativeData.map((item) => item.soc)
            const ocvData = props.negativeData.map((item) => item.ocv)

            // 设置图表选项
            const negativeOption = {
              title: {
                text: '负极 SOC-OCV曲线',
                left: 'center',
                top: 5,
                textStyle: { fontSize: 14 },
              },
              tooltip: {
                trigger: 'axis',
                formatter: '{a} <br/>{b}: {c} V',
                axisPointer: {
                  type: 'cross',
                  label: {
                    backgroundColor: '#6a7985',
                  },
                },
              },
              grid: { top: 40, right: 50, bottom: 50, left: 60 },
              xAxis: {
                type: 'category',
                name: 'SOC',
                data: socData,
                nameLocation: 'center',
                nameGap: 30,
                nameTextStyle: {
                  fontSize: 14,
                  fontWeight: 'bold',
                },
              },
              yAxis: { type: 'value', name: 'OCV (V)' },
              series: [
                {
                  name: 'OCV',
                  type: 'line',
                  data: ocvData,
                  smooth: true,
                  symbol: 'none',
                  lineStyle: { color: '#409eff' },
                },
              ],
            }

            negativeChartInstance.value.setOption(negativeOption)
          } catch (e) {
            console.error('初始化负极图表失败:', e)
            negativeChartInstance.value = null
          }
        }

        // 发出图表准备就绪事件
        if (positiveChartInstance.value || negativeChartInstance.value) {
          emit('chart-ready', {
            positive: positiveChartInstance.value,
            negative: negativeChartInstance.value,
          })
        }
      } else if (props.chartType === 'CycleData' && props.data.length && !props.isResetting) {
        // 获取数据
        const timeData = props.data.map((item) => item.time)
        const currentData = props.data.map((item) => item.current)
        const voltageData = props.data.map((item) => item.voltage)
        const capacityData = props.data.map((item) => item.capacity)

        // 初始化电流图表
        if (currentChartRef.value) {
          try {
            currentChartInstance.value = echarts.init(currentChartRef.value)

            // 设置图表选项
            const currentOption = {
              tooltip: {
                trigger: 'axis',
                formatter: '{a} <br/>{b}: {c} A',
                axisPointer: {
                  type: 'cross',
                  label: {
                    backgroundColor: '#6a7985',
                  },
                },
              },
              grid: { top: 30, right: 50, bottom: 50, left: 60 },
              xAxis: {
                type: 'category',
                name: '时间 (s)',
                data: timeData,
                nameLocation: 'center',
                nameGap: 30,
                nameTextStyle: {
                  fontSize: 14,
                  fontWeight: 'bold',
                },
              },
              yAxis: { type: 'value', name: '电流 (A)' },
              series: [
                {
                  name: '电流',
                  type: 'line',
                  data: currentData,
                  smooth: true,
                  symbol: 'none',
                  lineStyle: { color: '#f56c6c' },
                },
              ],
            }

            currentChartInstance.value.setOption(currentOption)
          } catch (e) {
            console.error('初始化电流图表失败:', e)
            currentChartInstance.value = null
          }
        }

        // 初始化电压图表
        if (voltageChartRef.value) {
          try {
            voltageChartInstance.value = echarts.init(voltageChartRef.value)

            // 设置图表选项
            const voltageOption = {
              tooltip: {
                trigger: 'axis',
                formatter: '{a} <br/>{b}: {c} V',
                axisPointer: {
                  type: 'cross',
                  label: {
                    backgroundColor: '#6a7985',
                  },
                },
              },
              grid: { top: 30, right: 50, bottom: 50, left: 60 },
              xAxis: {
                type: 'category',
                name: '时间 (s)',
                data: timeData,
                nameLocation: 'center',
                nameGap: 30,
                nameTextStyle: {
                  fontSize: 14,
                  fontWeight: 'bold',
                },
              },
              yAxis: { type: 'value', name: '电压 (V)' },
              series: [
                {
                  name: '电压',
                  type: 'line',
                  data: voltageData,
                  smooth: true,
                  symbol: 'none',
                  lineStyle: { color: '#409eff' },
                },
              ],
            }

            voltageChartInstance.value.setOption(voltageOption)
          } catch (e) {
            console.error('初始化电压图表失败:', e)
            voltageChartInstance.value = null
          }
        }

        // 初始化容量图表
        if (capacityChartRef.value) {
          try {
            capacityChartInstance.value = echarts.init(capacityChartRef.value)

            // 设置图表选项
            const capacityOption = {
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'cross',
                  label: {
                    backgroundColor: '#6a7985',
                  },
                },
              },
              grid: { top: 30, right: 50, bottom: 50, left: 60 },
              xAxis: {
                type: 'category',
                name: '时间 (s)',
                data: timeData,
                nameLocation: 'center',
                nameGap: 30,
                nameTextStyle: {
                  fontSize: 14,
                  fontWeight: 'bold',
                },
              },
              yAxis: { type: 'value', name: '容量 (Ah)' },
              series: [
                {
                  name: '容量',
                  type: 'line',
                  data: capacityData,
                  smooth: true,
                  symbol: 'none',
                  lineStyle: { color: '#67c23a' },
                },
              ],
            }

            capacityChartInstance.value.setOption(capacityOption)
          } catch (e) {
            console.error('初始化容量图表失败:', e)
            capacityChartInstance.value = null
          }
        }

        // 发出图表准备就绪事件
        if (
          currentChartInstance.value ||
          voltageChartInstance.value ||
          capacityChartInstance.value
        ) {
          emit('chart-ready', {
            current: currentChartInstance.value,
            voltage: voltageChartInstance.value,
            capacity: capacityChartInstance.value,
          })
        }
      } else if (props.data.length && !props.isResetting && chartRef.value) {
        // 处理其他类型的图表
        try {
          chartInstance.value = echarts.init(chartRef.value)

          // 这里可以根据不同的图表类型设置不同的选项
          // 示例配置
          const option = {
            tooltip: { trigger: 'axis' },
            grid: { top: 30, right: 50, bottom: 50, left: 60 },
            xAxis: { type: 'category', data: props.data.map((item, index) => index) },
            yAxis: { type: 'value' },
            series: [
              {
                type: 'line',
                data: props.data,
                smooth: true,
                symbol: 'none',
              },
            ],
          }

          chartInstance.value.setOption(option)

          emit('chart-ready', { chart: chartInstance.value })
        } catch (e) {
          console.error('初始化图表失败:', e)
          chartInstance.value = null
        }
      }
    } catch (error) {
      console.error('初始化图表过程中出错:', error)
    }
  })
}

// 添加窗口大小变化监听器
const handleResize = () => {
  safeResize(chartInstance.value)
  safeResize(positiveChartInstance.value)
  safeResize(negativeChartInstance.value)
  safeResize(currentChartInstance.value)
  safeResize(voltageChartInstance.value)
  safeResize(capacityChartInstance.value)
}

// 在组件挂载后初始化图表
onMounted(() => {
  isActive.value = true

  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleResize)

  // 首次初始化图表时添加延迟，确保DOM已完全渲染
  setTimeout(() => {
    if (isActive.value) {
      initChart()
    }
  }, 300)
})

// 监听数据变化，重新渲染图表
watch(
  [
    () => props.data,
    () => props.positiveData,
    () => props.negativeData,
    () => props.isResetting,
    () => props.isResettingPositive,
    () => props.isResettingNegative,
  ],
  () => {
    // 如果正在重置，先清理图表
    if (props.isResetting || props.isResettingPositive || props.isResettingNegative) {
      cleanupCharts()
    }

    // 只有在激活状态下才重新初始化图表
    if (isActive.value) {
      // 添加延迟，确保DOM状态已更新
      setTimeout(() => {
        initChart()
      }, 300)
    }
  },
  { deep: true },
)

// 在组件销毁前清理图表实例和事件监听器
onUnmounted(() => {
  isActive.value = false

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize)

  // 清理图表实例
  cleanupCharts()
})
</script>

<style scoped>
.chart-container {
  transition: all 0.3s ease;
}
</style>
