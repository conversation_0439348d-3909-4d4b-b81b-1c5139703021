// composables/useInitWorkflowOnLoad.ts
import { onBeforeMount } from 'vue'
import { useRoute } from 'vue-router'
import { useWorkflowStore } from '@renderer/store'
export function useInitWorkflowOnLoad() {
  const route = useRoute()
  const workflowStore = useWorkflowStore()

  onBeforeMount(() => {
    const id = route.params.id as string
    if (!id) return

    const exists = workflowStore.workflows.find((wf) => wf.id === id)
    if (exists) return

    const title = (route.query.title as string) || 'Untitled'
    const description = (route.query.description as string) || ''
    const folderId = workflowStore.currentFolderId

    workflowStore.workflows.push({
      id,
      title,
      description,
      folderId,
      createTime: new Date().toLocaleString(),
    })
  })
}
