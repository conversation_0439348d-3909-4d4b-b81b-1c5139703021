<h1 align="center">Mattverse-Vite 项目结构说明</h1>

## 快速入门

1、安装对应版本node和yarn

> yarn install

2、项目运行命令：

> yarn dev

3、项目提交步骤
**项目成员husky首先需要初始化：yarn prepare**

```bash
# 提交代码
git add .
# 使用cz-git进行提交
yarn commit
# 推送到远程仓库
git push
```

4、项目打包运行命令：
**警告：** 需要以管理员打开终端运行打包命令，不然有权限问题，导致打包失败。

> win版本打包构建 yarn build:mattverse:win (MattVerse) | yarn build:highpower:win (Highpower)
> mac版本打包构建 yarn build:mattverse:mac (MattVerse) | yarn build:highpower:mac (Highpower)

## 使用的 UI 库

#### shadcn-vue

使用[shadcn-vue](https://www.shadcn-vue.com) 组件库进行项目开发，这不是一个组件库。它是一个可重用组件的集合，您可以复制粘贴或使用 CLI添加到您的应用程序中。不是组件库是什么意思?这意味着您不能将其作为一个依赖项来安装。它不通过 npm 提供或发布，也没有发布计划。选择你需要的组件。使用 CLI 自动添加组件，或将代码复制并粘贴到项目中然后根据自己的需要进行定制。代码是你的。

#### TawidCSS

[TawidCSS](https://v3.tailwindcss.com/docs/margin)TawidCSS 是一个用于构建响应式网页的 CSS 框架，在不需要从头开始编写 CSS 的情况下，快速实现常见的网页布局和设计元素。
[TawidCSS速查表1](https://xunyidian.com/t/TailwindcssManual)
[TawidCSS速查表2](https://nerdcave.com/tailwind-cheat-sheet)

#### 表单校验

[vee-validate](https://vee-validate.logaretm.com/v4/guide/composition-api/getting-started/)

#### 流程图库

[Vue Flow](https://vueflow.dev/)

## 使用的 icon 库

本项目使用[Lucide Vue Next](https://lucide.dev/icons/)和 [Iconify](https://iconify.design/) 作为图标库,

## 项目结构

```javascript

mattverse-vite/
├── build/                      # 构建相关资源
│   └── icon.icns               # macOS 应用图标
│   └── entitlements.mac.plist  # macOS 应用权限配置
├── src/                        # 源代码目录
│   ├── main/                   # Electron 主进程代码
│   │   ├── index.ts            # 主进程入口文件
│   │   ├── ipc/                # IPC 通信处理
│   │   └── grpc/               # gRPC 相关代码
│   │       ├── client.ts       # gRPC 客户端
│   │       └── protos/         # Protocol Buffers 定义文件
│   │           ├── hello.proto # 示例协议文件
│   │           ├── matt_pb.js  # 生成的 JS 协议文件
│   │           └── matt_grpc_web_pb.d.ts # 生成的 TS 类型定义
│   ├── preload/                # Electron 预加载脚本
│   │   └── index.ts            # 预加载脚本入口文件
│   └── renderer/               # 渲染进程代码 (Vue 应用)
│       ├── index.html          # HTML 入口文件
│       └── src/                # Vue 应用源码
│           ├── App.vue         # 根组件
│           ├── main.ts         # 应用入口
│           ├── assets/         # 静态资源
│           │   ├── base.css    # 基础样式
│           │   └── svg/        # SVG 图标
│           │       ├── materialDesign/ # 材料设计图标
│           │       └── iconfont/      # 字体图标
│           ├── components/     # 公共组件
│           │   ├── Container.vue      # 容器组件
│           │   ├── HeaderTitle.vue    # 标题组件
│           │   ├── WorkflowDialog.vue # 工作流对话框
│           │   ├── ui/         # UI 组件 (shadcn-vue)
│           │   │   ├── button/        # 按钮组件
│           │   │   ├── input/         # 输入框组件
│           │   │   ├── tabs/          # 标签页组件
│           │   │   ├── dropdown-menu/ # 下拉菜单组件
│           │   │   ├── table/         # 表格组件
│           │   │   └── badge/         # 徽章组件
│           │   └── workflow/   # 工作流相关组件
│           │       ├── NodeSettingTabs.vue # 节点设置标签页
│           │       └── WorkflowToolbar.vue # 工作流工具栏
│           ├── constants/      # 常量定义
│           │   ├── common.ts   # 通用常量
│           │   └── svg.ts      # SVG 图标常量
│           ├── router/         # 路由配置
│           │   └── index.ts    # 路由定义
│           ├── store/          # Pinia 状态管理
│           │   ├── index.ts    # 状态管理入口
│           │   ├── useUserStore.ts # 用户状态
│           │   └── modules/    # 状态模块
│           │       ├── flows.ts        # 流程图状态
│           │       ├── navbar.ts       # 导航栏状态
│           │       ├── nodeNavbar.ts   # 节点导航栏状态
│           │       └── workflow.ts     # 工作流状态
│           ├── styles/         # 样式文件
│           │   └── index.scss  # 主样式文件
│           ├── utils/          # 工具函数
│           │   └── index.ts    # 工具函数入口
│           └── views/          # 页面视图
│               ├── plan/       # 计划页面
│               │   └── index.vue # 计划页面组件
│               └── workflow/   # 工作流页面
│                   ├── index.vue # 工作流主页面
│                   ├── editor.vue # 工作流编辑器
│                   ├── components/ # 工作流组件
│                   │   └── Dashboard/ # 仪表盘组件
│                   │       ├── index.vue # 仪表盘主组件
│                   │       └── components/ # 仪表盘子组件
│                   │           ├── Left.vue # 左侧面板
│                   │           └── Right.vue # 右侧面板
│                   └── nodeDetailComponents/ # 节点详情组件
│                       └── index.ts # 节点详情组件注册
├── scripts/                    # 脚本文件
│   └── gen-proto.sh            # 生成 Protocol Buffers 代码的脚本
├── .editorconfig               # 编辑器配置
├── .env.development            # 开发环境配置
├── .env.production             # 生产环境配置
├── .eslintignore               # ESLint 忽略配置
├── .eslintrc.cjs               # ESLint 配置
├── .gitignore                  # Git 忽略配置
├── .npmrc                      # npm 配置
├── .nvmrc                      # Node 版本配置
├── .prettierignore             # Prettier 忽略配置
├── .prettierrc.yaml            # Prettier 配置
├── components.json             # shadcn-vue 组件配置
├── electron-builder.yml        # Electron Builder 配置
├── electron.vite.config.ts     # Electron Vite 配置
├── package.json                # 项目依赖和脚本
├── postcss.config.js           # PostCSS 配置
├── tailwind.config.js          # Tailwind CSS 配置
├── tsconfig.json               # TypeScript 配置
├── tsconfig.node.json          # Node TypeScript 配置
├── tsconfig.web.json           # Web TypeScript 配置
├── vite.config.ts              # Vite 配置
└── README.md                   # 项目说明文档
```

## 项目说明

1. 技术栈 :

   - Vue 3.5
   - Electron 28.2.0
   - Node.js v20.18.2
   - Yarn 1.22.22
   - Tailwind CSS 3.4.14
   - shadcn-vue 组件库 0.11.4
   - vueflow

2. 主要功能模块 :

   - 工作流系统 : 包含工作流的创建、编辑、导入导出功能
   - 节点编辑器 : 用于构建和编辑流程图
   - 文件夹管理 : 组织和管理工作流

3. 状态管理 :

   - 使用 Pinia 进行状态管理
   - 主要状态模块:
     - workflow : 管理工作流和文件夹
     - flows : 管理流程图数据
     - navbar : 管理导航标签
     - nodeNavbar : 管理节点设置面板

4. UI 组件 :

   - 使用 shadcn-vue 组件库
   - 自定义工作流相关组件

5. 后端通信 :

   - 使用 gRPC 进行客户端-服务器通信

   - (非必要) 在 windows 中编译 C++库并且使用 grpc，需要以下命令

   yarn install --global --production windows-build-tools@4.0.0
   yarn global add ffi-napi
   yarn global add node-gyp
   yarn global add @grpc/grpc-js
   yarn global add @grpc/proto-loader

6. 数据持久化 :

   - 使用 localStorage 进行数据持久化存储
   - 支持工作流的导入导出功能

7. 构建与打包 :
   - 使用 Electron Builder 进行应用打包
   - 支持 Windows NSIS 安装程序
