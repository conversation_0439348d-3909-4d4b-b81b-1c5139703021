const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const isMatt = process.env.VITE_APP_IS_MATT === '1'
console.log(`构建模式: ${isMatt ? 'mattverse' : 'highpower'}`)

// 动态修改 .env.production 文件
const envPath = path.resolve(__dirname, '../.env.production')
let envContent = fs.readFileSync(envPath, 'utf8')
// 替换 VITE_APP_IS_MATT 的值
envContent = envContent.replace(/VITE_APP_IS_MATT=\d/, `VITE_APP_IS_MATT=${isMatt ? '1' : '0'}`)
fs.writeFileSync(envPath, envContent)
console.log(`已更新 .env.production 文件，设置 VITE_APP_IS_MATT=${isMatt ? '1' : '0'}`)

const appId = isMatt ? 'com.mattverse.app' : 'com.highpower.app'
const productName = isMatt ? 'MattVerse' : 'highpower'
const executableName = isMatt ? 'mattverse' : 'highpower'
const packageName = isMatt ? 'mattverse' : 'highpower'

// 设置不同的输出目录
const outputDir = isMatt ? 'dist/mattverse' : 'dist/highpower'
console.log(`输出目录: ${outputDir}`)

const winIconPath = isMatt
  ? 'src/renderer/src/assets/logo/mattverse.ico'
  : 'src/renderer/src/assets/logo/highpower.ico'
const macIconPath = isMatt
  ? 'src/renderer/src/assets/logo/mattverse.icns'
  : 'src/renderer/src/assets/logo/highpower.icns'
const linuxIconPath = isMatt
  ? 'src/renderer/src/assets/logo/mattverse.png'
  : 'src/renderer/src/assets/logo/highpower.png'

const entitlementsPath = 'src/renderer/src/assets/build/entitlements.mac.plist'

const resourcesDir = path.resolve(__dirname, '../src/renderer/src/assets/logo')
if (!fs.existsSync(resourcesDir)) {
  fs.mkdirSync(resourcesDir, { recursive: true })
}

const checkIconExists = (iconPath) => {
  const fullPath = path.resolve(__dirname, '..', iconPath)
  if (!fs.existsSync(fullPath)) {
    console.warn(`警告: 图标文件不存在: ${fullPath}`)
    return false
  }
  console.log(`图标文件存在: ${fullPath}`)
  return true
}

const checkFileExists = (filePath) => {
  const fullPath = path.resolve(__dirname, '..', filePath)
  if (!fs.existsSync(fullPath)) {
    console.warn(`警告: 文件不存在: ${fullPath}`)
    return false
  }
  console.log(`文件存在: ${fullPath}`)
  return true
}

const winIconExists = checkIconExists(winIconPath)
const macIconExists = checkIconExists(macIconPath)
const linuxIconExists = checkIconExists(linuxIconPath)
const entitlementsExists = checkFileExists(entitlementsPath)

console.log(`macOS 授权文件: ${entitlementsPath} (${entitlementsExists ? '存在' : '不存在'})`)

const config = {
  appId: appId,
  productName: productName,
  directories: {
    output: outputDir,
    buildResources: 'src/renderer/src/assets/logo',
  },
  extraMetadata: {
    name: packageName,
  },
  files: [
    'out/**/*',
    '!**/.vscode/*',
    '!src/*',
    '!electron.vite.config.{js,ts,mjs,cjs}',
    '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}',
    '!{.env,.env.*,.npmrc,pnpm-lock.yaml}',
    '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}',
    '!**/*.{map,md,ts,tsx}',
    '!**/node_modules/.cache/**',
    '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples,docs}/**',
    '!**/node_modules/*/{.bin,.github,.vscode,coverage,benchmark}/**',
  ],
  extraResources: [
    {
      from: 'src/main/grpc/protos',
      to: 'protos',
      filter: '**/*.proto',
    },
    {
      from: 'src/renderer/src/assets/logo',
      to: 'assets/logo',
      filter: '**/*.{ico,icns,png}',
    },
  ],
  asarUnpack: ['assets/logo/**'],
  compression: 'maximum', // 启用文件压缩
  asar: {
    smartUnpack: true, // 智能解包
  },
  win: {
    icon: path.resolve(__dirname, '..', winIconPath),
    executableName: executableName,
    target: [{ target: 'nsis', arch: ['x64'] }],
  },
  nsis: {
    artifactName: '${name}-${version}-setup.${ext}',
    shortcutName: productName,
    uninstallDisplayName: productName,
    createDesktopShortcut: true,
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    perMachine: false,
    deleteAppDataOnUninstall: true,
    installerIcon: path.resolve(__dirname, '..', winIconPath),
    uninstallerIcon: path.resolve(__dirname, '..', winIconPath),
  },
  mac: {
    icon: path.resolve(__dirname, '..', macIconPath),
    entitlementsInherit: 'src/renderer/src/assets/build/entitlements.mac.plist',
    extendInfo: {
      NSCameraUsageDescription: "Application requests access to the device's camera.",
      NSMicrophoneUsageDescription: "Application requests access to the device's microphone.",
      NSDocumentsFolderUsageDescription:
        "Application requests access to the user's Documents folder.",
      NSDownloadsFolderUsageDescription:
        "Application requests access to the user's Downloads folder.",
    },
    notarize: false,
  },
  dmg: {
    artifactName: '${name}-${version}.${ext}',
  },
  linux: {
    icon: path.resolve(__dirname, '..', linuxIconPath),
    target: ['AppImage', 'snap', 'deb'],
    maintainer: 'electronjs.org',
    category: 'Utility',
  },
  appImage: {
    artifactName: '${name}-${version}.${ext}',
  },
  npmRebuild: false,
  publish: null,
  electronDownload: {
    mirror: 'https://npmmirror.com/mirrors/electron/',
  },
}

const tempConfigPath = path.resolve(__dirname, '../electron-builder.temp.json')
fs.writeFileSync(tempConfigPath, JSON.stringify(config, null, 2))

process.env.ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES = 'true'

console.log('开始打包应用...')
console.log(`应用 ID: ${appId}`)
console.log(`产品名称: ${productName}`)
console.log(`可执行文件名: ${executableName}`)
console.log(`Windows 图标: ${winIconPath} (${winIconExists ? '存在' : '不存在'})`)
console.log(`macOS 图标: ${macIconPath} (${macIconExists ? '存在' : '不存在'})`)
console.log(`Linux 图标: ${linuxIconPath} (${linuxIconExists ? '存在' : '不存在'})`)

try {
  process.env.NODE_ENV = 'production'

  // 清理输出目录
  if (fs.existsSync(outputDir)) {
    console.log(`清理当前文件输出目录: ${outputDir}`)
    fs.rmSync(outputDir, { recursive: true, force: true })
  } else {
    // 确保 dist 目录存在
    const distDir = path.resolve(__dirname, '../dist')
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true })
    }
  }

  // 清理 electron-builder 缓存
  const cacheDir = path.resolve(
    __dirname,
    `../.electron-builder-cache-${isMatt ? 'mattverse' : 'highpower'}`,
  )
  if (fs.existsSync(cacheDir)) {
    console.log(`清理特定文件夹缓存: ${cacheDir}`)
    fs.rmSync(cacheDir, { recursive: true, force: true })
  }

  // 清理 out 目录
  const outDir = path.resolve(__dirname, '../out')
  if (fs.existsSync(outDir)) {
    console.log(`清理 out 目录: ${outDir}`)
    fs.rmSync(outDir, { recursive: true, force: true })
  }

  // 清理 node_modules/.cache 目录
  const nodeModulesCacheDir = path.resolve(__dirname, '../node_modules/.cache')
  if (fs.existsSync(nodeModulesCacheDir)) {
    console.log(`清理 node_modules 缓存: ${nodeModulesCacheDir}`)
    fs.rmSync(nodeModulesCacheDir, { recursive: true, force: true })
  }

  execSync('electron-vite build --mode production', {
    stdio: 'inherit',
    env: process.env,
  })

  // 创建输出目录（如果不存在）
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  process.env.ELECTRON_BUILDER_SKIP_SYMLINKS = 'true' // 跳过符号链接解压

  // 获取命令行参数，确定要打包的平台
  const buildPlatform = process.argv[2] || '' // 默认为空，根据当前系统决定
  console.log(`请求的打包平台: ${buildPlatform || '默认'}`)

  // 检测当前操作系统
  const isMacOS = process.platform === 'darwin'
  const isWindows = process.platform === 'win32'
  const isLinux = process.platform === 'linux'

  console.log(`当前操作系统: ${process.platform}`)

  let buildCommand = ''

  // 根据当前操作系统决定实际构建命令
  if (isMacOS) {
    // 在 macOS 上构建 macOS 应用
    if (buildPlatform === 'win') {
      console.log('警告: 在 macOS 上请求构建 Windows 应用，将改为构建 macOS 应用')
    }
    buildCommand = `electron-builder --config ${tempConfigPath} --mac --x64 --publish never`
    console.log('正在为 macOS 平台构建...')
  } else if (isWindows) {
    // 在 Windows 上构建 Windows 应用
    if (buildPlatform === 'mac') {
      console.log('警告: 在 Windows 上无法构建 macOS 应用，将改为构建 Windows 应用')
    }
    buildCommand = `electron-builder --config ${tempConfigPath} --win --x64 --publish never --c.compression=maximum --c.asar.smartUnpack=true`
    console.log('正在为 Windows 平台构建...')
  } else if (isLinux) {
    // 在 Linux 上构建 Linux 应用
    buildCommand = `electron-builder --config ${tempConfigPath} --linux --x64 --publish never`
    console.log('正在为 Linux 平台构建...')
  }

  execSync(buildCommand, {
    stdio: 'inherit',
    env: process.env,
  })

  console.log('应用打包成功!!!!')

  if (isWindows) {
    console.log(`Windows 安装程序: ${packageName}-1.0.0-setup.exe`)
    console.log(`Windows 输出目录: ${outputDir}/win-unpacked`)
    console.log(`Windows 可执行文件: ${outputDir}/win-unpacked/${executableName}.exe`)

    // 验证文件是否存在
    const setupFile = path.resolve(__dirname, `../${outputDir}/${packageName}-1.0.0-setup.exe`)
    if (fs.existsSync(setupFile)) {
      const fileSizeInMB = (fs.statSync(setupFile).size / (1024 * 1024)).toFixed(2)
      console.log(`安装包大小: ${fileSizeInMB} MB`)
    } else {
      console.warn(`警告: 安装包文件不存在: ${setupFile}`)
    }
  }

  if (isMacOS) {
    console.log(`macOS 安装程序: ${packageName}-1.0.0.dmg`)
    console.log(`macOS 输出目录: ${outputDir}/mac`)
    console.log(`macOS 应用程序: ${outputDir}/mac/${productName}.app`)

    // 验证文件是否存在
    const dmgFile = path.resolve(__dirname, `../${outputDir}/${packageName}-1.0.0.dmg`)
    if (fs.existsSync(dmgFile)) {
      const fileSizeInMB = (fs.statSync(dmgFile).size / (1024 * 1024)).toFixed(2)
      console.log(`安装包大小: ${fileSizeInMB} MB`)
    } else {
      console.warn(`警告: 安装包文件不存在: ${dmgFile}`)
    }
  }

  // 列出当前可用的所有构建
  const distDir = path.resolve(__dirname, '../dist')
  if (fs.existsSync(distDir)) {
    const types = ['mattverse', 'highpower']
    console.log('\n可用的构建:')

    types.forEach((brand) => {
      const typeDir = path.resolve(distDir, brand)
      if (fs.existsSync(typeDir)) {
        console.log(`\n${brand === 'mattverse' ? 'mattverse' : 'highpower'} 构建:`)

        // 检查 Windows 安装包
        const winSetup = path.resolve(
          typeDir,
          `${brand === 'mattverse' ? 'mattverse' : 'highpower'}-1.0.0-setup.exe`,
        )
        if (fs.existsSync(winSetup)) {
          const fileSizeInMB = (fs.statSync(winSetup).size / (1024 * 1024)).toFixed(2)
          console.log(`  Windows 安装包: ${winSetup} (${fileSizeInMB} MB)`)
        }

        // 检查 macOS 安装包
        const macDmg = path.resolve(
          typeDir,
          `${brand === 'mattverse' ? 'mattverse' : 'highpower'}-1.0.0.dmg`,
        )
        if (fs.existsSync(macDmg)) {
          const fileSizeInMB = (fs.statSync(macDmg).size / (1024 * 1024)).toFixed(2)
          console.log(`  macOS 安装包: ${macDmg} (${fileSizeInMB} MB)`)
        }
      }
    })
  }

  fs.unlinkSync(tempConfigPath)
} catch (error) {
  console.error('打包过程中出错:', error)
  if (fs.existsSync(tempConfigPath)) {
    fs.unlinkSync(tempConfigPath)
  }
  process.exit(1)
}
