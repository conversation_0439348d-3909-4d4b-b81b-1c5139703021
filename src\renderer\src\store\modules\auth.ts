import { createAuthService } from '@renderer/config/api/grpc/authService'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 用户角色枚举
export enum UserRole {
  Super = 0, // 超级管理员
  Admin = 1, // 管理员
  Normal = 2, // 普通用户
}

// 用户状态枚举
export enum UserStatus {
  Activated = 0, // 已激活
  Deactivated = 1, // 已停用
  Deleted = 2, // 已删除
}

// 服务器状态枚举
export enum ServerStatus {
  Running = 0, // 运行中
  Stopped = 1, // 已停止
  Expired = 2, // 已过期
  Overloaded = 3, // 过载
  Stay = 4, // 保持原状态
  Unknown = 5, // 未知
}

// 任务状态枚举
export enum TaskStatus {
  Initializing = 0, // 初始化
  Computing = 1, // 计算中
  Pending = 2, // 等待中
  Paused = 3, // 已暂停
  Finished = 4, // 已完成
  Error = 5, // 错误
  TaskStay = 6, // 保持原状态
  Abort = 7, // 终止
}

// 用户信息接口
export interface UserInfo {
  user_id: string
  user_name: string
  access_level: number
  user_status: UserStatus
  user_role: UserRole
}

// 用户接口
export interface User {
  user_id: string
  token: string
  expired_time: number
}

// 服务器接口
export interface Server {
  server_id: string
  server_name: string
  url: string
  server_status: ServerStatus
  region: string
  version: string
  access_level: number
  create_time: number
  update_time: number
}

// 服务接口
export interface Service {
  service_id: string
  service_name: string
  server_id: string
  version: string
  protocol_type: string
  access_level: number
}

// 任务接口
export interface Task {
  task_id: string
  user_id: string
  service_id: string
  start_time: number
  end_time: number
  task_log: string
  task_status: TaskStatus
  task_process: number
  task_pid: number
  result: string
  file_path: string
  create_time: number
  update_time: number
}

// 会话接口
export interface Session {
  session_id: string
  user_id: string
  session_contents: string
}

export const useAuthStore = defineStore(
  'auth',
  () => {
    const authService = createAuthService()

    // 状态
    const userInfo = ref<UserInfo | null>(null)
    const token = ref<string | null>(null)
    const userId = ref<string>('0')
    const loading = ref<boolean>(false)
    const error = ref<string | null>(null)
    const serverList = ref<Server[]>([])
    const serviceList = ref<Service[]>([])
    const taskList = ref<Task[]>([])
    const sessionList = ref<Session[]>([])
    const userList = ref<User[]>([])
    // 添加用于重连的状态
    const userName = ref<string>('')
    const passwordHash = ref<string | null>(null)
    const lastIp = ref<string>('127.0.0.1')

    // 计算属性
    const isLoggedIn = computed(() => !!token.value)

    // 用户角色相关的计算属性
    const userRole = computed(() => userInfo.value?.user_role ?? UserRole.Normal)
    const isAdmin = computed(
      () => userRole.value === UserRole.Admin || userRole.value === UserRole.Super,
    )
    const isSuper = computed(() => userRole.value === UserRole.Super)

    // 从本地存储初始化状态
    const initFromStorage = async () => {
      try {
        const hasToken = !!token.value

        // window.logger?.info(
        //   `检查登录状态: hasToken=${hasToken}, userId=${userId.value}, username=${userName.value}`,
        // )
        // window.logger?.info(
        //   `密码哈希状态: ${passwordHash.value ? '已保存' : '未保存'}, IP: ${lastIp.value}`,
        // )

        // 如果已登录，将认证信息发送到主进程并重建连接
        if (hasToken && userId.value) {
          window.logger.info('检测到登录状态，准备同步认证信息并重连')

          try {
            // 同步认证信息到主进程
            const result = await authService.updateAuthInfo(userId.value, token.value)

            if (result.success) {
              window.logger.info('启动时：认证信息已同步到主进程')

              // // 如果有存储的密码哈希和用户名，重新建立长连接
              // if (passwordHash.value && userName.value) {
              //   window.logger.info('尝试重新建立gRPC长连接', {
              //     username: userName.value,
              //     ip: lastIp.value,
              //   })

              //   // 添加延迟，确保认证信息已完全同步
              //   setTimeout(() => {
              //     authService.connect(userName.value, lastIp.value, passwordHash.value!, 0)
              //   }, 500)
              // } else {
              //   window.logger.warn('缺少重连所需信息，无法自动建立gRPC长连接', {
              //     hasPasswordHash: !!passwordHash.value,
              //     hasUsername: !!userName.value,
              //   })
              // }
            } else {
              window.logger.warn('启动时：同步认证信息到主进程失败:', result.message)
            }
          } catch (err) {
            window.logger.error('启动时：同步认证信息到主进程时出错:', err)
          }
        } else {
          //window.logger.info('用户未登录或登录信息不完整，跳过连接')
        }

        return hasToken
      } catch (error) {
        window.logger?.error('initFromStorage 执行出错:', error)
        return false
      }
    }

    // 清除用户信息
    const clearUserInfo = () => {
      userInfo.value = null
      token.value = null
      error.value = null
      userId.value = '0'
      serverList.value = []
      serviceList.value = []
      taskList.value = []
      sessionList.value = []
      userList.value = []
      // 清除连接信息
      passwordHash.value = null
      userName.value = ''
      lastIp.value = '127.0.0.1'
    }

    // 登录操作
    const login = async (username: string, password: string, last_ip: string = '127.0.0.1') => {
      loading.value = true
      error.value = null

      try {
        // 调用 登录 接口
        const response = await authService.login({
          userName: username,
          password,
          lastIp: last_ip,
        })

        window.logger.info('调用 登录 接口 response', response)

        // 处理响应
        if (response && response.status === 'Success') {
          // 1. 处理用户信息
          if (response.userInfoResult?.userInfoList?.length > 0) {
            const userInfoData = response.userInfoResult.userInfoList[0]

            // 创建用户信息对象
            const userData: UserInfo = {
              user_id: userInfoData.userId,
              user_name: userInfoData.userName,
              access_level: userInfoData.accessLevel,
              user_status: userInfoData.userStatus,
              user_role: userInfoData.userRole,
            }

            userInfo.value = userData
          }

          // 2. 处理令牌信息
          if (response.userResult) {
            token.value = response.userResult.token
            // 保存用户列表
            if (response.userResult.userList?.length > 0) {
              const userListItem = response.userResult.userList[0]
              userList.value = userListItem
              if (userListItem.userId) {
                userId.value = userListItem.userId
              }
            } else {
              // 当 user_list 为空时，默认使用 '0' 作为用户ID
              userId.value = '0'
              window.logger.info('用户列表为空，使用默认用户ID: 0')
            }

            // 将认证信息发送到主进程
            try {
              const result = await authService.updateAuthInfo(
                userId.value,
                response.userResult.token,
              )
              if (result.success) {
                window.logger.info('认证信息已同步到主进程')
              } else {
                window.logger.warn('同步认证信息到主进程失败:', result.message)
              }
            } catch (err) {
              window.logger.error('同步认证信息到主进程时出错:', err)
            }

            // 保存密码哈希和IP地址，用于重连
            userName.value = username
            passwordHash.value = password
            lastIp.value = last_ip

            // 建立gRPC长连接
            // authService.connect(username, last_ip, password, 0)
          }

          // 3. 处理服务器列表
          if (response.serverResult?.serverList?.length > 0) {
            serverList.value = response.serverResult.serverList
          }

          // 4. 处理服务列表
          if (response.serviceResult?.serviceList?.length > 0) {
            serviceList.value = response.serviceResult.serviceList
          }

          // 5. 处理任务列表
          if (response.taskResult?.taskList?.length > 0) {
            taskList.value = response.taskResult.taskList
          }

          // 6. 处理会话列表
          if (response.sessionResult?.sessionList?.length > 0) {
            sessionList.value = response.sessionResult.sessionList
          }

          return {
            success: true,
            data: userInfo.value,
            fullResponse: response,
          }
        } else {
          throw new Error(response?.message || '登录失败')
        }
      } catch (err) {
        const message = err instanceof Error ? err.message : '登录时发生错误'
        error.value = message
        return { success: false, error: message }
      } finally {
        loading.value = false
      }
    }

    // 注册操作
    const register = async (
      username: string,
      password: string,
      role: UserRole = UserRole.Normal,
    ) => {
      loading.value = true
      error.value = null

      try {
        // 调用注册服务
        const response = await authService.register(username, password, role)

        // 处理响应
        if (response && response.status === 'Success') {
          // 成功状态
          return { success: true, message: response.message || '注册成功' }
        } else {
          throw new Error(response?.message || '注册失败')
        }
      } catch (err) {
        const message = err instanceof Error ? err.message : '注册时发生错误'
        error.value = message
        return { success: false, error: message }
      } finally {
        loading.value = false
      }
    }

    // 更新用户角色
    const updateUserRole = (role: UserRole) => {
      if (!userInfo.value) return false

      userInfo.value = {
        ...userInfo.value,
        user_role: role,
      }

      return true
    }

    // 获取角色名称
    const getRoleName = (role: UserRole): string => {
      switch (role) {
        case UserRole.Super:
          return '超级管理员'
        case UserRole.Admin:
          return '管理员'
        case UserRole.Normal:
          return '普通用户'
        default:
          return '未知角色'
      }
    }

    // 登出操作
    const logout = () => {
      // 断开gRPC连接
      authService.disconnect()
      clearUserInfo()
      return { success: true }
    }

    // 检查登录状态
    const checkLoginStatus = async (): Promise<boolean> => {
      //window.logger?.info('开始检查登录状态...')
      return await initFromStorage()
    }

    return {
      userInfo,
      token,
      userId,
      loading,
      error,
      isLoggedIn,
      userRole,
      isAdmin,
      isSuper,
      serverList,
      serviceList,
      taskList,
      sessionList,
      userList,
      userName,
      passwordHash,
      lastIp,
      login,
      register,
      logout,
      checkLoginStatus,
      clearUserInfo,
      updateUserRole,
      getRoleName,
      UserRole,
      UserStatus,
      ServerStatus,
      TaskStatus,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: [
        'token',
        'userId',
        'userInfo',
        'serverList',
        'serviceList',
        'taskList',
        'sessionList',
        'userList',
        'userName',
        'passwordHash',
        'lastIp',
      ],
    },
  },
)
