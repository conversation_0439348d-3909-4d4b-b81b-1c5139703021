/**
 * MattVerse 应用配置
 * 电池设计自动化平台配置
 */

import { APP_TYPES, type AppConfig } from './types'

export const mattverseConfig: AppConfig = {
  type: APP_TYPES.MATTVERSE,
  meta: {
    name: 'mattverse',
    title: 'MattVerse 电池设计自动化平台',
    description: '专业的电池设计自动化平台',
    version: '1.0.0',
    icon: 'mattverse.ico',
    logo: 'mattverse.png',
    author: 'MattVerse',
    homepage: 'https://mattverse.com',
  },
  features: {
    // 布局配置
    layout: {
      showSidebar: true, // 显示侧边栏
      showTitleBar: true, // 显示标题栏
      showStatusBar: true, // 显示状态栏
    },

    // 侧边栏菜单配置
    sidebar: {
      showSidebar: true, // 全局控制侧边栏显示/隐藏
      menuItems: {
        // 主菜单项配置
        workflow: true, // 工作流
        task: true, // 任务
        server: true, // 服务器
        logger: true, // 日志
        tools: true, // 工具
        setting: true, // 设置
        // 底部菜单项配置
        ai: true, // AI
        user: true, // 用户
        about: true, // 关于
      },
    },

    // 工作流编辑器配置
    workflowEditor: {
      showNavbar: true, // 显示工作流导航栏
      showAIFloatingBox: true, // 显示AI浮动框
      showToolbar: true, // 显示工具栏
      showMiniMap: true, // 显示小地图
      showLeftControls: true, // 显示左侧控制面板
      showRightControls: true, // 显示右侧控制面板
      enableNodeDrag: true, // 启用节点拖拽
      enableNodeResize: true, // 启用节点调整大小
      showBackground: true, // 显示背景
      backgroundType: 'dots', // 背景类型：点状
    },

    // 导航配置
    navigation: {
      showMainNavigation: true, // 显示主导航
      showBreadcrumb: true, // 显示面包屑
      enableRouteGuard: true, // 启用路由守卫
      defaultRoute: '/', // 默认路由：首页
    },

    // 节点工具栏配置
    nodeToolbar: {
      showNodeToolbar: true, // 显示节点工具栏
      enableNodeCategories: [
        // 允许的节点分类
        'materialDesign', // 材料设计
        'batterySimulation', // 电池模拟
        'dataAnalysis', // 数据分析
      ],
      enableCustomNodes: true, // 启用自定义节点
    },

    // 认证配置
    auth: {
      enableAuth: true, // 启用认证系统
      showLoginForm: true, // 显示登录表单
      showRegisterForm: true, // 显示注册表单
      enableGuestMode: false, // 禁用访客模式
    },

    // 主题配置
    theme: {
      enableThemeSwitch: true, // 启用主题切换
      enableDarkMode: true, // 启用暗色模式
      defaultTheme: 'city-light', // 默认主题
      allowedThemes: [
        // 允许的主题列表
        'city-light',
        'forest-light',
        'lake-light',
        'city-dark',
        'forest-dark',
        'lake-dark',
      ],
    },

    // 工具配置
    tools: {
      showToolsPanel: true, // 显示工具面板
      enabledTools: [
        // 启用的工具
        'calculator', // 计算器
        'converter', // 转换器
        'analyzer', // 分析器
      ],
    },

    // 其他功能配置
    features: {
      enableI18n: true, // 启用国际化
      enableNotifications: true, // 启用通知
      enableHotkeys: true, // 启用快捷键
      enableAutoSave: true, // 启用自动保存
      enableExport: true, // 启用导出功能
      enableImport: true, // 启用导入功能
    },
  },
}
