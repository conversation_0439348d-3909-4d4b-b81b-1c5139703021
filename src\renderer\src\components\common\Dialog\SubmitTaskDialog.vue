<template>
  <Dialog :open="isOpen" @update:open="updateOpen">
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>提交预测任务</DialogTitle>
        <DialogDescription>提交服务请求，执行特定任务。</DialogDescription>
      </DialogHeader>
      <Form :validation-schema="validationSchema" @submit="onSubmit">
        <div class="grid gap-4 py-4">
          <!-- 服务器 -->
          <div class="grid items-center grid-cols-4 gap-4">
            <div class="col-span-1">
              <Label for="server" class="text-right">
                服务器
                <span class="text-red-500">*</span>
              </Label>
            </div>
            <div class="col-span-3">
              <Field v-slot="{ field, errorMessage }" name="server_id">
                <ServerSelect
                  :model-value="values.server_id"
                  :servers="availableServers"
                  :error-message="errorMessage"
                  @update:model-value="(val) => setFieldValue('server_id', val)"
                />
                <p v-if="errorMessage" class="mt-1 text-sm text-red-500">{{ errorMessage }}</p>
              </Field>
            </div>
          </div>
          <!-- 服务名称 -->
          <div class="grid items-center grid-cols-4 gap-4">
            <div class="col-span-1">
              <Label for="service_name" class="text-right">
                服务名称
                <span class="text-red-500">*</span>
              </Label>
            </div>
            <div class="col-span-3">
              <Field v-slot="{ field, errorMessage }" name="service_name">
                <Input
                  v-bind="field"
                  id="service_name"
                  :model-value="values.service_name"
                  type="text"
                  placeholder="请输入服务名称"
                  :class="{ 'border-red-500': errorMessage }"
                  @update:model-value="(val) => setFieldValue('service_name', val)"
                />
                <p v-if="errorMessage" class="mt-1 text-sm text-red-500">{{ errorMessage }}</p>
              </Field>
            </div>
          </div>
          <!-- 是否保存 -->
          <div class="grid items-center grid-cols-4 gap-4">
            <div class="flex items-center col-span-4 space-x-2">
              <Field v-slot="{ field }" name="is_save" type="checkbox">
                <Checkbox
                  v-bind="field"
                  id="is_save"
                  :model-value="values.is_save"
                  @update:model-value="(val) => setFieldValue('is_save', val)"
                />
              </Field>
              <Label for="is_save">保存计算结果</Label>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" @click="closeDialog">取消</Button>
          <Button type="submit">
            <Loader2 v-if="isSubmitting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isSubmitting ? '提交中...' : '确定' }}
          </Button>
        </DialogFooter>
      </Form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ServerSelect } from '@renderer/components'
import { useServerStore } from '@renderer/store'
import { toTypedSchema } from '@vee-validate/zod'
import { Loader2 } from 'lucide-vue-next'
import { Field, Form, useForm } from 'vee-validate'
import { computed, nextTick, onMounted, watch, watchEffect } from 'vue'
import { toast } from 'vue-sonner'
import * as z from 'zod'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  serviceName: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:isOpen', 'submit'])

// 使用服务器 store
const serverStore = useServerStore()

// 过滤掉已过期的服务器
const availableServers = computed(() => {
  return serverStore.servers.filter(
    (server) => server.serverStatus !== 'Expired' && server.serverType !== 'agentServer',
  )
})

// 定义表单验证规则
const validationSchema = toTypedSchema(
  z.object({
    server_id: z
      .string({ required_error: '请选择服务器' })
      .min(1, '请选择服务器')
      .default(() => availableServers.value[0]?.serverId || ''),
    service_name: z
      .string({ required_error: '请输入服务名称' })
      .min(1, '请输入服务名称')
      .default(props.serviceName),
    is_save: z.boolean().optional().default(true),
  }),
)

// 使用useForm钩子初始化表单
const { handleSubmit, resetForm, isSubmitting, values, setFieldValue } = useForm({
  validationSchema,
  initialValues: {
    server_id: '',
    service_name: '',
    is_save: true,
  },
})

// 表单提交处理
const onSubmit = handleSubmit(async (values) => {
  console.log('表单提交处理', values)
  try {
    // 将表单数据传递给父组件
    await emit('submit', { ...values })
    closeDialog()
    resetForm({
      values: {
        server_id: values.server_id || '',
        service_name: values.service_name || props.serviceName || '',
        is_save: true,
      },
    })
  } catch (error) {
    toast.error('提交任务失败')
  }
})

// 更新对话框状态
const updateOpen = (value: boolean) => {
  emit('update:isOpen', value)
}

// 关闭对话框
const closeDialog = () => {
  emit('update:isOpen', false)
}

watch(
  availableServers,
  (newServers) => {
    if (newServers.length > 0 && !values.server_id) {
      setFieldValue('server_id', newServers[0].serverId)
      // console.log('已自动选择第一个可用服务器:', values.server_id)
    }
  },
  { immediate: true },
)

watchEffect(() => {
  if (props.serviceName) {
    nextTick(() => {
      setFieldValue('service_name', props.serviceName)
    })
  }
})
onMounted(async () => {
  try {
    await serverStore.updateServerList()
    console.log('%c服务器列表', 'color:green;fontSize:bold', serverStore.servers)
  } catch (error) {
    toast.error('获取服务器列表失败')
  }
})

defineExpose({
  values,
})
</script>
