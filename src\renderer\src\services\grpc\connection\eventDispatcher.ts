/**
 * 事件分发工具
 * 不使用emitter，直接通过CustomEvent分发事件
 */

/**
 * 分发GRPC事件
 * @param eventName 事件名称
 * @param data 事件数据
 */
export function dispatchGrpcEvent(eventName: string, data: any): void {
  try {
    // 使用CustomEvent分发到全局
    const event = new CustomEvent(`grpc-${eventName}`, {
      detail: data,
      bubbles: true,
      cancelable: true,
    })
    window.dispatchEvent(event)

    // 分发通用消息事件
    const messageEvent = new CustomEvent('grpc-message', {
      detail: { type: eventName, data },
      bubbles: true,
      cancelable: true,
    })
    window.dispatchEvent(messageEvent)
  } catch (error) {
    window.logger?.error(`分发事件 ${eventName} 失败:`, error)
  }
}

/**
 * 添加GRPC事件监听器
 * @param eventName 事件名称
 * @param listener 监听器函数
 */
export function addGrpcEventListener(
  eventName: string,
  listener: (event: CustomEvent) => void,
): void {
  window.addEventListener(`grpc-${eventName}`, listener as EventListener)
}

/**
 * 移除GRPC事件监听器
 * @param eventName 事件名称
 * @param listener 监听器函数
 */
export function removeGrpcEventListener(
  eventName: string,
  listener: (event: CustomEvent) => void,
): void {
  window.removeEventListener(`grpc-${eventName}`, listener as EventListener)
}

/**
 * 添加通用GRPC消息监听器
 * @param listener 监听器函数
 */
export function addGrpcMessageListener(listener: (event: CustomEvent) => void): void {
  window.addEventListener('grpc-message', listener as EventListener)
}

/**
 * 移除通用GRPC消息监听器
 * @param listener 监听器函数
 */
export function removeGrpcMessageListener(listener: (event: CustomEvent) => void): void {
  window.removeEventListener('grpc-message', listener as EventListener)
}

/**
 * 显示提示消息
 * 替代原来使用emitter的toast显示方式
 */
export function showToast(
  title: string,
  description: string,
  type: 'success' | 'error' | 'info' = 'info',
): void {
  // 创建并分发toast事件
  const toastEvent = new CustomEvent('app-toast', {
    detail: { title, description, type },
    bubbles: true,
    cancelable: true,
  })
  window.dispatchEvent(toastEvent)
}
