import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { Task, TaskStatus } from '@renderer/config/types/api/task'
import emitter from '@renderer/utils/mitt'
import { formatDuration } from '@renderer/utils/utils'
import { useInterval } from '@vueuse/core'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 定义轮询控制接口
interface PollingControl {
  stop: () => void
  resume: () => void
}

export const useTaskStore = defineStore('task', () => {
  const taskService = createTaskService()

  // 状态
  const tasks = ref<Task[]>([]) // 任务列表
  const loading = ref(false) // 加载状态
  const taskResult = ref<string | null>(null) // 任务结果
  const refreshInterval = ref<number>(5) // 任务列表刷新间隔（秒）
  const taskStatusMap = ref<Map<string, TaskStatus>>(new Map()) // 任务状态缓存
  const taskResultsMap = ref<Map<string, any>>(new Map()) // 任务结果缓存
  // 存储所有正在轮询的任务
  const pollingControls = ref<Map<string, PollingControl>>(new Map())

  // 获取特定任务
  const getTaskById = (taskId: string) =>
    computed(() => {
      return tasks.value.find((task) => task.taskId === taskId)
    })

  // 获取特定任务结果
  const getTaskResultById = (taskId: string) =>
    computed(() => {
      return taskResultsMap.value.get(taskId)
    })

  // 获取任务进度
  const getTaskProgress = (taskId: string) =>
    computed(() => {
      const task = tasks.value.find((t) => t.taskId === taskId)
      return task?.taskProcess || 0
    })

  // 获取任务状态
  const getTaskStatus = (taskId: string) =>
    computed(() => {
      return taskStatusMap.value.get(taskId) || 'Unknown'
    })

  // 获取状态显示文本
  const getStatusText = (status: TaskStatus): string => {
    const statusText: Record<TaskStatus, string> = {
      Initializing: '初始化中',
      Computing: '计算中',
      Pending: '等待调度',
      Paused: '暂停',
      Finished: '任务完成',
      Error: '任务失败',
      TaskStay: '原始状态',
      Abort: '已终止',
    }
    return statusText[status] || status
  }

  // 获取状态对应的样式
  const getStatusClass = (status: TaskStatus): string => {
    const classes: Record<TaskStatus, string> = {
      Initializing: 'bg-blue-400 hover:bg-blue-500 dark:bg-blue-900/20',
      Computing: 'bg-blue-400 hover:bg-blue-500 dark:bg-blue-900/20',
      Pending: 'bg-yellow-400 hover:bg-yellow-500 dark:bg-yellow-900/20',
      Paused: 'bg-orange-400 hover:bg-orange-500 dark:bg-orange-900/20',
      Finished: 'bg-green-400 hover:bg-green-500 dark:bg-green-900/20',
      Error: 'bg-red-400 hover:bg-red-500 dark:bg-red-900/20',
      TaskStay: 'bg-purple-400 hover:bg-purple-500 dark:bg-purple-900/20',
      Abort: 'bg-red-400 hover:bg-red-500 dark:bg-red-900/20',
    }
    return classes[status] || 'bg-blue-400 hover:bg-blue-500 dark:bg-blue-900/20'
  }

  // 解析任务结果
  const parseTaskResult = (result: string): any => {
    try {
      return JSON.parse(result)
    } catch (e) {
      console.error('解析任务结果失败:', e)
      return null
    }
  }

  // 更新任务结果
  const updateResult = (taskId: string, result: string) => {
    const taskIndex = tasks.value.findIndex((t) => t.taskId === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].result = result
    }
    const parsedResult = parseTaskResult(result)
    if (parsedResult) {
      taskResultsMap.value.set(taskId, parsedResult)
      emitter.emit('task-updated', { taskId, result: parsedResult })
    }
  }

  // 更新任务列表
  const updateTaskList = async (id?: string) => {
    loading.value = true
    try {
      const res = await taskService.getTaskList(id)
      if (res.status === 'Success' && res.taskResult?.taskList) {
        const newTasks = res.taskResult.taskList
        if (id) {
          // 合并新旧任务
          const tasksMap = new Map(tasks.value.map((t) => [t.taskId, t]))
          newTasks.forEach((task) => {
            task.duration = formatDuration(task.startTime, task.endTime)
            tasksMap.set(task.taskId, task)
            taskStatusMap.value.set(task.taskId, task.taskStatus)
          })
          tasks.value = Array.from(tasksMap.values())
        } else {
          // 获取所有任务
          newTasks.forEach((task) => {
            task.duration = formatDuration(task.startTime, task.endTime)
            taskStatusMap.value.set(task.taskId, task.taskStatus)
          })
          tasks.value = newTasks
        }
      }
    } finally {
      loading.value = false
    }
  }

  // 获取任务结果
  const updateTaskResult = async (id: string) => {
    const res = await taskService.getTaskResult(id)
    if (res.status !== 'Success' || !res.result) {
      return null
    }

    // 处理批量请求
    if (id.includes(',')) {
      const taskIds = id.split(',')
      const results = res.result.split('},{').map((str, index) => {
        if (index === 0) return str + '}'
        if (index === res.result.split('},{').length - 1) return '{' + str
        return '{' + str + '}'
      })

      // 确保结果数量与任务ID数量匹配
      if (results.length === taskIds.length) {
        taskIds.forEach((taskId, index) => {
          const result = results[index]
          if (result) {
            updateResult(taskId, result)
          }
        })
      }
    } else {
      // 处理单个任务
      updateResult(id, res.result)
    }
    return res.result
  }

  // 启动轮询
  const startPolling = (taskId: string) => {
    stopPolling(taskId)
    const { pause, resume } = useInterval(refreshInterval.value * 1000, {
      immediate: true,
      controls: true,
      callback: async () => {
        try {
          await updateTaskList(taskId)
          const status = taskStatusMap.value.get(taskId)
          if (['Computing', 'Pending', 'Initializing'].includes(status as TaskStatus)) {
            await updateTaskResult(taskId)
          }
          if (['Finished', 'Error', 'Abort', 'Paused'].includes(status as TaskStatus)) {
            await updateTaskResult(taskId)
            stopPolling(taskId)
            emitter.emit('task-completed', { taskId, status: status as TaskStatus })
          }
        } catch (error) {
          console.error(`轮询任务 ${taskId} 出错:`, error)
        }
      },
    })
    pollingControls.value.set(taskId, { stop: pause, resume })
    resume()
  }

  // 停止轮询
  const stopPolling = (taskId: string) => {
    const polling = pollingControls.value.get(taskId)
    if (polling) {
      polling.stop()
      pollingControls.value.delete(taskId)
    }
  }

  // 停止所有轮询
  const stopAllPolling = () => {
    for (const [taskId, polling] of pollingControls.value.entries()) {
      polling.stop()
      pollingControls.value.delete(taskId)
    }
  }

  // 设置刷新间隔
  const setRefreshInterval = (interval: number) => {
    refreshInterval.value = interval
  }

  // 清除任务结果
  const clearTaskResult = (taskId: string) => {
    stopPolling(taskId)
    taskResultsMap.value.delete(taskId)
    taskStatusMap.value.delete(taskId)
    tasks.value = tasks.value.filter((task) => task.taskId !== taskId)
  }

  // 清除所有任务结果
  const clearAllTaskResults = () => {
    stopAllPolling()
    taskResultsMap.value.clear()
    taskStatusMap.value.clear()
    tasks.value = []
    taskResult.value = null
  }

  // 判断任务是否正在轮询
  const isPolling = (taskId: string): boolean => {
    return pollingControls.value.has(taskId)
  }

  return {
    tasks,
    loading,
    taskResult,
    taskResultsMap,
    refreshInterval,
    getTaskById,
    getTaskResultById,
    getTaskProgress,
    getTaskStatus,
    getStatusText,
    getStatusClass,
    updateTaskList,
    updateTaskResult,
    startPolling,
    stopPolling,
    stopAllPolling,
    setRefreshInterval,
    clearTaskResult,
    clearAllTaskResults,
    isPolling,
  }
})
