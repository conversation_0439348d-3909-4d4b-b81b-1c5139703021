<template>
  <div class="folder-dialog">
    <Dialog v-model:open="visible">
      <DialogContent :class="width">
        <DialogHeader>
          <DialogTitle class="text-lg font-semibold">{{ title }}</DialogTitle>
        </DialogHeader>

        <div class="py-4">
          <form @submit.prevent="onConfirm">
            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="name">名称</Label>
                <Input id="name" v-model="form.name" placeholder="请输入名称" class="w-full" />
              </div>
            </div>
          </form>
        </div>

        <DialogFooter class="flex space-x-2">
          <Button variant="outline" class="w-full" @click="visible = false">
            {{ cancelText }}
          </Button>
          <Button class="w-full text-white" @click="onConfirm">
            {{ confirmText }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const emit = defineEmits(['confirm'])

interface Props {
  width?: string
  cancelText?: string
}

withDefaults(defineProps<Props>(), {
  width: 'max-w-md',
  cancelText: '取消',
})

const visible = ref(false)
const typeOption = ref('')
const title = ref('新建文件夹')
const confirmText = ref('添加')

const form = reactive({
  name: '',
})

const onConfirm = () => {
  if (!form.name.trim()) return
  emit('confirm', form.name, typeOption.value)
  close()
}

const open = (type, name: string = '') => {
  visible.value = true
  typeOption.value = type
  if (type === 'add') {
    title.value = '新建文件夹'
    confirmText.value = '添加'
  } else {
    title.value = '重命名文件夹'
    form.name = name
    confirmText.value = '重命名'
  }
}

const close = () => {
  visible.value = false
  form.name = ''
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.folder-dialog {
  :deep(.dialog-overlay) {
    @apply bg-black/50;
  }
}
</style>
