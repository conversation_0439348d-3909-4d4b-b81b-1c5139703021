// 分段读取文件
// 读取文件
import * as fs from 'fs'
import iconv from 'iconv-lite'
import Papa from 'papaparse'

const readCsvFile = (filePath, callback) => {
  try {
    const READ_CHUNK_SIZE = 10 * 1024 * 1024

    const buffer = fs.readFileSync(filePath)
    const hasUtf8Bom =
      buffer.length >= 3 && buffer[0] === 0xef && buffer[1] === 0xbb && buffer[2] === 0xbf
    const csvBuffer = hasUtf8Bom ? buffer.slice(3) : buffer
    const csvText = iconv.decode(csvBuffer, 'GB18030')
    if (csvText.length < READ_CHUNK_SIZE) {
      const parsed = Papa.parse(csvText, {
        skipEmptyLines: true,
      })
      callback({
        type: 'data',
        data: parsed.data,
        msg: '',
      })
    } else {
      // let chunkId = 0
      // const totalChunkNums = Math.ceil(csvText.length / READ_CHUNK_SIZE)
      Papa.parse(csvText, {
        skipEmptyLines: true,
        chunkSize: READ_CHUNK_SIZE,
        // chunk: function (results) {
        //   chunkId += 1
        //   const process = Math.round((0.3 + (chunkId / totalChunkNums) * 0.7) * 100)
        //   sendMsg({
        //     uploadId,
        //     step: 'read',
        //     progress: process,
        //     status: 'processing',
        //     message: '',
        //   })
        // },
        complete: function (results) {
          callback({
            type: 'data',
            data: results.data,
            msg: '',
          })
        },
      })
    }
  } catch (error) {
    callback({
      type: 'error',
      data: null,
      msg: error,
    })
  }
}
export default readCsvFile
