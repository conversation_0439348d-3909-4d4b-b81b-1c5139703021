<template>
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold text-foreground">{{ title }}</h2>
    </div>
    <FileDropUpload
      :accept-types="acceptTypes"
      :max-size="maxSize"
      :is-disabled="isDisabled"
      @file-selected="(file) => $emit('file-selected', file)"
      @error="(error) => $emit('upload-error', error)"
      @progress="(progress) => $emit('upload-progress', progress)"
      @upload-complete="$emit('upload-complete')"
    />
  </div>
</template>

<script setup>
import { FileDropUpload } from '@renderer/components'

defineProps({
  // 面板标题
  title: {
    type: String,
    required: true,
  },
  // 可接受的文件类型数组
  acceptTypes: {
    type: Array,
    default: () => ['.xlsx', '.xls', '.csv'],
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 20,
  },
  //参数是否提取
  isDisabled: {
    type: Boolean,
    default: false,
  },
})

defineEmits([
  'file-selected', // 文件选择事件
  'upload-error', // 上传错误事件
  'upload-progress', // 上传进度事件
  'upload-complete', // 上传完成事件
])
</script>
