<template>
  <Card class="my-6">
    <CardHeader>
      <CardTitle>{{ t('settings.baseSetting.fileEncrypt.title') }}</CardTitle>
      <CardDescription>{{ t('settings.baseSetting.fileEncrypt.description') }}</CardDescription>
    </CardHeader>
    <CardContent>
      <!-- 文件上传区域 -->
      <div class="space-y-4">
        <FileDropUpload
          :accept-types="['.json']"
          :title="t('settings.baseSetting.fileEncrypt.uploadTitle')"
          :description="t('settings.baseSetting.fileEncrypt.uploadDescription')"
          @file-selected="handleFileSelected"
          @error="handleUploadError"
        />

        <!-- 已上传文件列表 -->
        <div v-if="uploadedFiles.length > 0" class="mt-6">
          <h3 class="text-sm font-medium mb-3">
            {{ t('settings.baseSetting.fileEncrypt.uploadedFiles') }}
          </h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div
              v-for="(file, index) in uploadedFiles"
              :key="index"
              class="relative group border rounded-md p-3 bg-card hover:bg-accent/50 transition-colors"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 truncate">
                  <Icon icon="mdi:file-document-outline" class="h-5 w-5 text-primary" />
                  <span class="text-sm font-medium truncate" :title="file.name">
                    {{ file.name }}
                  </span>
                </div>
                <div
                  class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Button variant="ghost" size="icon" @click="exportFile(file, 'model')">
                    <Icon icon="mdi:export" class="h-4 w-4" />
                    <span class="sr-only">导出</span>
                  </Button>
                  <Button variant="ghost" size="icon" @click="removeFile(index)">
                    <Icon icon="mdi:delete-outline" class="h-4 w-4 text-destructive" />
                    <span class="sr-only">删除</span>
                  </Button>
                </div>
              </div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ formatFileSize(file.size) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 导出类型选择对话框 -->
  <Dialog :open="isExportDialogOpen" @update:open="isExportDialogOpen = $event">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ t('settings.baseSetting.fileEncrypt.exportTitle') }}</DialogTitle>
        <DialogDescription>
          {{ t('settings.baseSetting.fileEncrypt.exportDescription') }}
        </DialogDescription>
      </DialogHeader>
      <div class="grid gap-4 py-4">
        <RadioGroup v-model="exportType" class="grid grid-cols-2 gap-4">
          <div>
            <RadioGroupItem id="model" value="model" class="peer sr-only" />
            <Label
              for="model"
              class="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              <Icon icon="mdi:cube-outline" class="mb-3 h-6 w-6" />
              <div class="text-center space-y-1">
                <h3 class="font-medium">{{ t('settings.baseSetting.fileEncrypt.modelFile') }}</h3>
                <p class="text-xs text-muted-foreground">
                  {{ t('settings.baseSetting.fileEncrypt.modelFileDesc') }}
                </p>
              </div>
            </Label>
          </div>
          <div>
            <RadioGroupItem id="plugin" value="plugin" class="peer sr-only" />
            <Label
              for="plugin"
              class="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              <Icon icon="mdi:puzzle-outline" class="mb-3 h-6 w-6" />
              <div class="text-center space-y-1">
                <h3 class="font-medium">{{ t('settings.baseSetting.fileEncrypt.pluginFile') }}</h3>
                <p class="text-xs text-muted-foreground">
                  {{ t('settings.baseSetting.fileEncrypt.pluginFileDesc') }}
                </p>
              </div>
            </Label>
          </div>
        </RadioGroup>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="isExportDialogOpen = false">
          {{ t('settings.baseSetting.fileEncrypt.cancel') }}
        </Button>
        <Button @click="confirmExport">{{ t('settings.baseSetting.fileEncrypt.confirm') }}</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import { useLanguage } from '@renderer/config/hooks'
import { toast } from 'vue-sonner'
import { saveFile, openFile, readFile, encryptAndSaveFile } from '@renderer/utils/utils'
import type { EncryptFileOptions } from '@renderer/config/interface/file'
import { FileDropUpload } from '@renderer/components'

const { t } = useLanguage()

// 文件列表
interface UploadedFile {
  name: string
  size: number
  content: string
  path: string
}

const uploadedFiles = ref<UploadedFile[]>([])
const isExportDialogOpen = ref(false)
const exportType = ref('model')
const currentFile = ref<UploadedFile | null>(null)

// 处理文件上传
const handleFileSelected = async (file: File) => {
  try {
    const content = await readFileAsText(file)

    // 验证是否为有效的JSON
    try {
      JSON.parse(content)
    } catch (e) {
      toast.error(t('settings.baseSetting.fileEncrypt.invalidJson'))
      return
    }

    // 添加到文件列表
    uploadedFiles.value.push({
      name: file.name,
      size: file.size,
      content,
      path: file.path || '',
    })

    toast.success(t('settings.baseSetting.fileEncrypt.uploadSuccess'))
  } catch (error) {
    console.error('读取文件失败:', error)
    toast.error(t('settings.baseSetting.fileEncrypt.uploadFailed'))
  }
}

// 处理上传错误
const handleUploadError = (error: string) => {
  toast.error(error)
}

// 读取文件内容
const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsText(file)
  })
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' B'
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
  else return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
}

// 删除文件
const removeFile = (index: number) => {
  uploadedFiles.value.splice(index, 1)
  toast.success(t('settings.baseSetting.fileEncrypt.fileRemoved'))
}

// 导出文件
const exportFile = (file: UploadedFile, defaultType = 'model') => {
  currentFile.value = file
  exportType.value = defaultType
  isExportDialogOpen.value = true
}

// 确认导出
const confirmExport = async () => {
  if (!currentFile.value) return

  try {
    const fileExtension = exportType.value === 'model' ? '.model' : '.plugin'
    const outputName = currentFile.value.name.replace(/\.json$/, '') + fileExtension
    const options: EncryptFileOptions = {
      jsonData: currentFile.value.content,
      fileType: exportType.value,
      defaultPath: outputName,
      title: t('settings.baseSetting.fileEncrypt.saveFileTitle'),
      filters: [
        {
          name: exportType.value === 'model' ? 'Model Files' : 'Plugin Files',
          extensions: [exportType.value],
        },
      ],
    }
    const result = await encryptAndSaveFile(options)

    if (result.success) {
      toast.success(t('settings.baseSetting.fileEncrypt.exportSuccess'))
      isExportDialogOpen.value = false
    } else if (!result.canceled) {
      toast.error(t('settings.baseSetting.fileEncrypt.exportFailed'))
    }
  } catch (error) {
    console.error('导出文件失败:', error)
    toast.error(t('settings.baseSetting.fileEncrypt.exportFailed'))
  }
}
</script>
