import { defineStore } from 'pinia'
import { ref } from 'vue'
import { parse, stringify } from 'zipson'
import { useFlowsStore } from './flows'
import { useNavbarStore } from './navbar'
interface WorkflowItem {
  id: string
  title: string
  description: string
  createTime: string
  folderId: string
}

interface Folder {
  id: string
  name: string
}

export const useWorkflowStore = defineStore(
  'workflow',
  () => {
    const flowsStore = useFlowsStore()
    const navbarStore = useNavbarStore()

    const folders = ref<Folder[]>([{ id: 'all', name: 'All' }])
    const workflows = ref<WorkflowItem[]>([])
    const currentFolderId = ref('all')
    const currentWfId = ref('')

    // 添加文件夹
    const addFolder = (name: string) => {
      let folderName = name
      let count = 1

      // 检查是否有相同名称的文件夹
      while (folders.value.some((folder) => folder.name === folderName)) {
        folderName = `${name} (${count})`
        count++
      }

      const newFolder: Folder = {
        id: `folder-${Date.now()}`,
        name: folderName,
      }
      folders.value.push(newFolder)
    }
    // 编辑文件夹
    const editFolder = (folderId: string, newName: string) => {
      const folder = folders.value.find((folder) => folder.id === folderId)
      if (folder) {
        folder.name = newName
      }
    }
    // 删除文件夹
    const deleteFolder = (folderId: string) => {
      const index = folders.value.findIndex((folder) => folder.id === folderId)
      // console.log('folderId--', folderId)
      if (index !== -1) {
        // 获取该文件夹下的所有工作流
        const folderWorkflows = workflows.value.filter((workflow) => workflow.folderId === folderId)
        // 删除 flows store 中的相关数据
        folderWorkflows.forEach((workflow) => {
          flowsStore.deleteWorkflow(workflow.id) // 删除相关的工作流
          navbarStore.removeWorkflowTags(workflow.id) // 删除相关的导航标签
        })
        // 删除工作流数据
        workflows.value = workflows.value.filter((workflow) => workflow.folderId !== folderId)

        // 删除文件夹
        folders.value.splice(index, 1)
      }
      // console.log('workflows', workflows.value)
    }

    // 添加工作流
    const addWorkflow = (data: Omit<WorkflowItem, 'id' | 'createTime'>) => {
      let workflowTitle = data.title
      let count = 1

      // 检查是否有相同名称的工作流，并生成新的名称
      while (
        workflows.value.some(
          (workflow) =>
            workflow.title === workflowTitle && workflow.folderId === currentFolderId.value,
        )
      ) {
        workflowTitle = `${data.title} (${count})`
        count++
      }

      const newWorkflow: WorkflowItem = {
        id: `workflow-${Date.now()}`,
        createTime: new Date().toLocaleString(),
        title: workflowTitle,
        description: data.description,
        folderId: currentFolderId.value, // 确保将当前文件夹 ID 赋值给工作流
      }

      workflows.value.push(newWorkflow) // 添加新工作流
    }
    const editWorkflow = (form: { id: string; title: string; description: string }) => {
      const workflow = workflows.value.find((workflow) => workflow.id === form.id)
      if (workflow) {
        workflow.title = form.title
        workflow.description = form.description
        // console.log('工作流已更新:', workflow)
      } else {
        console.log('未找到对应的工作流')
      }
    }
    const deleteWorkflow = (workflowId: string) => {
      const index = workflows.value.findIndex((workflow) => workflow.id === workflowId)
      if (index !== -1) {
        // 删除 flows store 中的数据
        const flowsStore = useFlowsStore()
        flowsStore.deleteWorkflow(workflowId)

        // 删除相关的导航标签
        const navbarStore = useNavbarStore()
        navbarStore.removeWorkflowTags(workflowId)

        // 删除工作流数据
        workflows.value.splice(index, 1)
      }
    }

    // 获取当前文件夹的工作流
    const getCurrentFolderWorkflows = () => {
      if (currentFolderId.value === 'all') {
        return workflows.value
      }
      return workflows.value.filter((item) => item.folderId === currentFolderId.value)
    }
    // 搜索工作流
    const searchWorkflow = (keyword: string) => {
      if (!keyword) {
        return getCurrentFolderWorkflows()
      }

      const currentFolderWorkflows = getCurrentFolderWorkflows()
      return currentFolderWorkflows.filter(
        (item) =>
          item.title.toLowerCase().includes(keyword.toLowerCase()) ||
          item.description.toLowerCase().includes(keyword.toLowerCase()),
      )
    }

    return {
      folders,
      workflows,
      currentFolderId,
      currentWfId,
      addFolder,
      editFolder,
      deleteFolder,
      addWorkflow,
      editWorkflow,
      deleteWorkflow,
      getCurrentFolderWorkflows,
      searchWorkflow,
    }
  },
  {
    persist: {
      // key: 'workflow-store',
      storage: localStorage,
      serializer: {
        deserialize: parse,
        serialize: stringify,
      },
      pick: ['folders', 'workflows', 'currentFolderId', 'currentWfId'],
      afterHydrate: (context) => {
        console.log('工作流状态已恢复:', context.store.$id)
      },
    },
  },
)
