/**
 * 工作流服务入口
 */
import { addWorkflowDataToCanvas } from './canvas'
import { createLifePredictionEdges, extractLifePredictionNodes } from './generation'
import { registerLifePredictionWorkflow, registerNodeModules } from './registration'

/**
 * 工作流事件处理服务
 */
class WorkflowEventService {
  private functionCallListener: (event: CustomEvent) => void
  private messageListener: (event: CustomEvent) => void

  constructor() {
    // 创建事件监听器
    this.functionCallListener = this.handleGrpcFunctionCall.bind(this)
    this.messageListener = this.handleGrpcMessage.bind(this)
  }

  /**
   * 初始化工作流事件监听
   */
  initialize(): void {
    // 添加事件监听器
    window.addEventListener('grpc-function-call', this.functionCallListener)
    window.addEventListener('grpc-message', this.messageListener)

    window.logger?.info('工作流事件服务已初始化')
  }

  /**
   * 处理gRPC功能调用事件
   */
  private handleGrpcFunctionCall(event: CustomEvent): void {
    // 事件处理已移至专门的处理器中，这里只需要保持接口兼容性
  }

  /**
   * 处理通用gRPC消息事件
   */
  private handleGrpcMessage(event: CustomEvent): void {
    // 事件处理已移至专门的处理器中，这里只需要保持接口兼容性
  }

  /**
   * 清理事件监听
   */
  cleanup(): void {
    // 移除事件监听器
    window.removeEventListener('grpc-function-call', this.functionCallListener)
    window.removeEventListener('grpc-message', this.messageListener)
  }
}

// 导出工作流服务实例
export const workflowEventService = new WorkflowEventService()

// 导出工作流相关函数
export {
  addWorkflowDataToCanvas,
  createLifePredictionEdges,
  extractLifePredictionNodes,
  registerLifePredictionWorkflow,
  registerNodeModules,
}
