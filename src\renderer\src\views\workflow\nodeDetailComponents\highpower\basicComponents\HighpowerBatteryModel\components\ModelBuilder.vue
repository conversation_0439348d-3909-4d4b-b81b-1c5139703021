<template>
  <div>
    <!-- 模型构建完成提示 -->
    <Alert
      v-if="modelBuilt"
      class="mb-4 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-300"
    >
      <LucideIcon name="CheckCircle" class="h-4 w-4 text-green-600 dark:text-green-400" />
      <AlertTitle class="text-green-800 dark:text-green-300 font-medium">模型构建完成</AlertTitle>
      <AlertDescription class="text-green-700 dark:text-green-400">
        电池模型已成功构建，可以进行后续操作。
      </AlertDescription>
    </Alert>

    <!-- 模型构建进度条 -->
    <div v-if="isBuilding" class="mb-4 space-y-2">
      <div class="flex justify-between text-sm">
        <span>模型构建进度</span>
        <span>{{ progress }}%</span>
      </div>
      <Progress :model-value="progress" class="w-full" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

defineProps({
  modelBuilt: {
    type: Boolean,
    default: false,
  },
  isBuilding: {
    type: Boolean,
    default: false,
  },
  progress: {
    type: Number,
    default: 0,
  },
})
</script>
