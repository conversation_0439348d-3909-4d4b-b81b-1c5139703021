import { computed, nextTick, onMounted, onUnmounted, ref, watch, type Ref } from 'vue'
import * as echarts from 'echarts'
import { toast } from 'vue-sonner'
import { useDebounceFn } from '@vueuse/core'
import { getChartDataForItem } from '../utils/datasetUtils'

// 定义数据集项的接口
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][] | null
    error: any
  }
}

export function useDatasetChart(
  dataset: Ref<DatasetItem | undefined>,
  isOpen: Ref<boolean>,
  emit: (
    event: 'update-chart-settings',
    itemId: string,
    startCycle: number,
    endCycle: number,
  ) => void,
) {
  // 响应式数据
  const chartContainer = ref<HTMLDivElement>()
  const rangeValues = ref<[number, number]>([0, 100])
  const startValue = ref(0)
  const endValue = ref(100)

  // 计算属性
  const datasetName = computed(() => dataset.value?.name || '未知数据集')

  const chartData = computed(() => {
    if (!dataset.value?.id) return []
    return getChartDataForItem(dataset.value.id)
  })

  const minCycle = computed(() => {
    if (chartData.value.length === 0) return 0
    return Math.min(...chartData.value.map((item) => item[0]))
  })

  const maxCycle = computed(() => {
    if (chartData.value.length === 0) return 100
    return Math.max(...chartData.value.map((item) => item[0]))
  })

  // 数据采样优化：减少渲染数据点数量
  const MAX_DISPLAY_POINTS = 1000 // 最大显示点数

  const sampledChartData = computed(() => {
    const data = chartData.value
    if (data.length <= MAX_DISPLAY_POINTS) return data

    // 使用简化版LTTB采样算法
    const sampled: [number, number][] = []
    const step = Math.floor(data.length / MAX_DISPLAY_POINTS)

    for (let i = 0; i < data.length; i += step) {
      sampled.push(data[i])
    }

    // 确保包含最后一个点
    if (sampled[sampled.length - 1] !== data[data.length - 1]) {
      sampled.push(data[data.length - 1])
    }

    return sampled
  })

  //使用缓存避免重复计算
  const filteredData = computed(() => {
    const [start, end] = rangeValues.value
    return sampledChartData.value.filter((item) => item[0] >= start && item[0] <= end)
  })

  // 检查范围是否小于20圈
  const isRangeTooSmall = computed(() => {
    const [start, end] = rangeValues.value
    return end - start < 20
  })

  // ECharts 实例
  let chart: echarts.ECharts | null = null
  const resizeObserver = new ResizeObserver(() => {
    if (chart) {
      chart.resize()
    }
  })

  // 静态图表配置
  const staticChartConfig = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151',
        fontSize: 12,
      },
      formatter: (params: any) => {
        const point = params[0]
        return `循环圈数: ${point.data[0]}<br>容量: ${point.data[1].toFixed(4)}`
      },
      // 减少tooltip计算频率
      enterable: false,
      hideDelay: 100,
    },
    grid: {
      left: '7%',
      right: '8%',
      bottom: '10%',
      top: '18%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '循环圈数',
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        color: '#000',
        fontSize: 12,
        fontWeight: 'normal',
      },
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        align: 'center',
        showMinLabel: true,
        showMaxLabel: true,
        color: '#000',
        fontSize: 11,
      },
      axisLine: {
        lineStyle: { color: '#e5e7eb' },
      },
      splitLine: {
        lineStyle: { color: '#f3f4f6', type: 'dashed' },
      },
    },
    yAxis: [
      // 左侧y轴 - 容量显示
      {
        type: 'value',
        name: '容量（Ah）',
        nameLocation: 'end',
        nameGap: 22,
        nameTextStyle: {
          padding: [0, 30, 0, 0],
          align: 'center',
          fontSize: 12,
          color: '#666',
        },
        axisLine: {
          lineStyle: { color: '#e5e7eb' },
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11,
          formatter: (value: number) => value.toFixed(0),
        },
        splitLine: {
          lineStyle: { color: '#f3f4f6', type: 'dashed' },
        },
        min: 0,
      },
      // 右侧y轴 - 容量(%)显示
      {
        type: 'value',
        name: '容量(%)',
        nameLocation: 'end',
        nameGap: 22,
        nameTextStyle: {
          padding: [0, 0, 0, 50],
          align: 'right',
          fontSize: 12,
          color: '#666',
        },
        position: 'right',
        axisLine: {
          lineStyle: { color: '#e5e7eb' },
          show: true,
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11,
          formatter: (value: number) => `${value.toFixed(0)}%`,
          show: true,
        },
        axisTick: {
          show: true,
        },
        splitLine: {
          show: false,
        },
        min: 0,
        max: 100,
        alignTicks: true,
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomLock: false,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
        preventDefaultMouseMove: true,
        // 减少缩放时的重绘频率
        throttle: 100,
      },
    ],
    //禁用动画以提升大数据量渲染性能
    animation: false,
    // 启用渐进式渲染
    progressive: 1000,
    progressiveThreshold: 2000,
  }

  // 图表配置 - 动态部分
  const getChartOption = () => ({
    title: {
      text: `${datasetName.value} - 循环容量曲线`,
      left: 'center',
      top: '8%',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
      },
    },
    ...staticChartConfig,
    visualMap: {
      type: 'piecewise',
      dimension: 0,
      pieces: [
        {
          gte: rangeValues.value[0],
          lte: rangeValues.value[1],
          color: '#ef4444',
        },
        {
          lt: rangeValues.value[0],
          color: '#3b82f6',
        },
        {
          gt: rangeValues.value[1],
          color: '#3b82f6',
        },
      ],
      show: false,
    },
    series: [
      {
        name: '循环容量',
        type: 'line',
        data: sampledChartData.value, // 使用采样后的数据
        smooth: false, // 禁用平滑以提升性能
        symbol: 'none',
        sampling: 'lttb', // 启用内置采样
        large: true, // 启用大数据量优化
        largeThreshold: 500, // 大数据量阈值
        lineStyle: {
          width: 1.5, // 减少线宽以提升渲染性能
        },
        markLine: {
          silent: true,
          symbol: 'none',
          data: [
            {
              xAxis: rangeValues.value[0],
              lineStyle: {
                width: 1,
                type: 'dashed',
                color: '#22c55e',
              },
              label: {
                formatter: `起始: ${rangeValues.value[0]}`,
                position: 'end',
                color: '#22c55e',
              },
            },
            {
              xAxis: rangeValues.value[1],
              lineStyle: {
                width: 1,
                type: 'dashed',
                color: '#ef4444',
              },
              label: {
                formatter: `终止: ${rangeValues.value[1]}`,
                position: 'end',
                color: '#ef4444',
              },
            },
          ],
        },
      },
    ],
  })

  // 创建快速更新函数 - 仅更新 markLine
  const updateMarkLine = () => {
    if (chart) {
      chart.setOption(
        {
          series: [
            {
              markLine: {
                data: [
                  {
                    xAxis: rangeValues.value[0],
                    lineStyle: {
                      width: 1,
                      type: 'dashed',
                      color: '#22c55e',
                    },
                    label: {
                      formatter: `起始: ${rangeValues.value[0]}`,
                      position: 'end',
                      color: '#22c55e',
                    },
                  },
                  {
                    xAxis: rangeValues.value[1],
                    lineStyle: {
                      width: 1,
                      type: 'dashed',
                      color: '#ef4444',
                    },
                    label: {
                      formatter: `终止: ${rangeValues.value[1]}`,
                      position: 'end',
                      color: '#ef4444',
                    },
                  },
                ],
              },
            },
          ],
          visualMap: {
            pieces: [
              {
                gte: rangeValues.value[0],
                lte: rangeValues.value[1],
                color: '#ef4444',
              },
              {
                lt: rangeValues.value[0],
                color: '#3b82f6',
              },
              {
                gt: rangeValues.value[1],
                color: '#3b82f6',
              },
            ],
          },
        },
        {
          notMerge: false,
          lazyUpdate: false, // 立即更新
        },
      )
    }
  }

  // 优化的图表更新
  const updateChart = useDebounceFn(() => {
    if (chart) {
      chart.setOption(getChartOption(), {
        notMerge: false,
        lazyUpdate: true,
      })
    }
  }, 200)

  const initChart = async () => {
    if (!chartContainer.value) return

    await nextTick()

    if (chart) {
      chart.dispose()
    }

    // 性能优化：使用canvas渲染器和优化配置
    chart = echarts.init(chartContainer.value, null, {
      renderer: 'canvas', // 使用canvas渲染器
      useDirtyRect: true, // 启用脏矩形优化
      useCoarsePointer: true, // 启用粗糙指针优化
    })

    chart.setOption(getChartOption(), {
      notMerge: true, // 不合并配置，提升性能
      lazyUpdate: true, // 延迟更新
    })

    resizeObserver.observe(chartContainer.value)
  }

  const resetRange = () => {
    // 重置到数据集的实际最小最大值
    rangeValues.value = [minCycle.value, maxCycle.value]
    startValue.value = minCycle.value
    endValue.value = maxCycle.value
  }

  const initializeRange = () => {
    // 初始化时优先使用保存的值，如果没有则使用实际范围
    const defaultStart = dataset.value?.startCycle || minCycle.value
    const defaultEnd = dataset.value?.endCycle || maxCycle.value

    rangeValues.value = [defaultStart, defaultEnd]
    startValue.value = defaultStart
    endValue.value = defaultEnd
  }

  const updateRangeFromInput = () => {
    rangeValues.value = [startValue.value, endValue.value]
  }

  const confirmSelection = () => {
    const [startCycle, endCycle] = rangeValues.value

    // 检查范围是否有效
    if (isRangeTooSmall.value) {
      toast.error('圈数范围过小，请重新选择！至少20圈！')
      return
    }

    // 发送图表设置更新事件
    if (dataset.value?.id) {
      emit('update-chart-settings', dataset.value.id, startCycle, endCycle)
      console.log('确认选择范围:', { itemId: dataset.value.id, startCycle, endCycle })

      // 获取文件名
      const filePath = dataset.value?.rawData?.file_path || ''
      const fileName = filePath ? filePath.split(/[/\\]/).pop() || '未知文件' : '未知文件'

      toast.success(fileName, {
        description: `已设置图表范围：${startCycle} - ${endCycle}`,
      })
    }

    // 关闭对话框
    isOpen.value = false
  }

  const exportData = () => {
    if (filteredData.value.length === 0) {
      toast.error('没有数据可导出')
      return
    }

    const csvContent = [
      'Cycle,Capacity',
      ...filteredData.value.map((item) => `${item[0]},${item[1]}`),
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${datasetName.value}_data.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success('数据导出成功')
  }

  // 监听器
  watch(isOpen, async (newValue) => {
    if (newValue && dataset.value) {
      await nextTick()
      initializeRange()
      await initChart()
    }
  })

  // 监听 dataset 变化，重置范围
  watch(
    () => dataset.value,
    (newDataset) => {
      if (newDataset && isOpen.value) {
        resetRange()
        updateChart()
      }
    },
    { deep: true },
  )

  // 监听范围变化 - 使用快速更新 markLine
  watch(
    rangeValues,
    (newValues) => {
      startValue.value = newValues[0]
      endValue.value = newValues[1]
      updateMarkLine() // 立即更新 markLine
      updateChart() // 防抖更新完整图表
    },
    { deep: true },
  )

  // 生命周期
  onMounted(() => {
    if (isOpen.value && dataset.value) {
      initChart()
    }
  })

  onUnmounted(() => {
    if (chart) {
      chart.dispose()
    }
    resizeObserver.disconnect()
  })

  return {
    // 状态
    chartContainer,
    rangeValues,
    startValue,
    endValue,

    // 计算属性
    datasetName,
    chartData,
    minCycle,
    maxCycle,
    filteredData,
    isRangeTooSmall,

    // 方法
    initChart,
    updateChart,
    resetRange,
    updateRangeFromInput,
    confirmSelection,
    exportData,
  }
}
