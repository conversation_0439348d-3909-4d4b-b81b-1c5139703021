import { is } from '@electron-toolkit/utils'
import { app } from 'electron'
import path from 'path'

/**
 * 获取应用程序根目录
 */
export function getAppRootPath(): string {
  // 在开发环境中使用当前目录
  if (is.dev) {
    return app.getAppPath()
  }

  try {
    // 在生产环境中使用应用程序根目录
    const exePath = app.getPath('exe')
    return path.dirname(exePath)
  } catch (error) {
    // 如果获取可执行文件路径失败，使用用户数据目录
    console.error('获取应用根目录失败，使用用户数据目录作为替代:', error)
    return app.getPath('userData')
  }
}

/**
 * 获取配置文件路径
 */
export function getConfigPath(): string {
  try {
    const appRoot = getAppRootPath()
    return path.join(appRoot, 'config.json')
  } catch (error) {
    console.error('获取配置路径失败，使用备用路径:', error)
    return getFallbackConfigPath()
  }
}

/**
 * 获取备用配置文件路径
 */
export function getFallbackConfigPath(): string {
  return path.join(app.getPath('userData'), 'config.json')
}
