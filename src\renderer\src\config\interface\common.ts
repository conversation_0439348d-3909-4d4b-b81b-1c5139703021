import type { Props } from 'tippy.js'

interface SideBarItem {
  icon: string
  tooltip: string | Partial<Props>
  showTopLine: boolean
}

// 定义节点数据接口
interface WorkflowNodeData {
  label: string
  description: string
  icon: string
  // 可以添加其他节点特定的数据
  params?: Record<string, any>
}

// 定义节点模板接口
interface WorkflowNodeTemplate {
  id: string
  type: string
  data: WorkflowNodeData
  // vue-flow 节点配置
  defaultConfig?: Partial<Node>
}

// 节点分类接口
interface WorkflowNodeCategory {
  name: string
  nodes: WorkflowNodeTemplate[]
}

export type { SideBarItem, WorkflowNodeData, WorkflowNodeTemplate, WorkflowNodeCategory }
