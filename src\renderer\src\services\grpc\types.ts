/**
 * GRPC服务相关类型定义
 */

export interface GrpcEvent {
  type: string
  data: any
}

export interface FunctionCallData {
  functionName: string
  data: any
  sessionId?: string
  taskId?: string
}

export interface WorkflowData {
  workflow_name: string
  nodes: any[]
  edges?: any[]
}

export interface ConnectionConfig {
  username: string
  lastIp: string
  password: string
  expiredTime?: number
}

export type EventListener = (event: CustomEvent) => void
