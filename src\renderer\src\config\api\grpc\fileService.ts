import CryptoJS from 'crypto-js'
export class FileService {
  async uploadFile(
    file: File,
    uploadBack?: (data: any) => void,
    callback?: (progress: number) => void,
    chunkSize: number = 1024 * 1024,
  ) {
    // console.log('uploading file', file, chunkSize)
    // 计算文件SHA256
    const totalSha256 = await this.calculateFileSHA256(file)
    // 发送元数据
    let chunkId = 0
    // 分块发送
    const chunkCount = Math.ceil(file.size / chunkSize)
    const reader = new FileReader()
    let progress = 0
    reader.onload = (e) => {
      const isLast = chunkId === chunkCount - 1
      const content = e.target?.result as ArrayBuffer
      if (content) {
        window.grpcApi.fileUpload(
          {
            fileName: file.name,
            sha256: totalSha256,
            chunkId: chunkId,
            content: new Uint8Array(content), // 使用八进制数组，处理二进制数据
            isLast: isLast,
          },
          (data) => {
            if (uploadBack) {
              uploadBack(data)
            }
          },
        )
      }

      progress = (++chunkId / chunkCount) * 100
      if (callback) {
        callback(progress)
      }
      if (chunkId < chunkCount) readNextChunk()
      else console.log('end of file')
    }

    function readNextChunk() {
      const start = chunkId * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      reader.readAsArrayBuffer(file.slice(start, end))
    }
    readNextChunk()
  }
  // 计算文件 SHA256 完整性的完整实现方案
  async calculateFileSHA256(file: File, chunkSize = 1024 * 1024): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      const hash = CryptoJS.algo.SHA256.create()
      let offset = 0
      // 分段读取文件并计算哈希
      const readChunk = () => {
        const slice = file.slice(offset, offset + chunkSize)
        reader.readAsArrayBuffer(slice)
      }

      reader.onload = (e) => {
        if (e.target?.result) {
          // 将ArrayBuffer转换为WordArray
          const wordArray = this.arrayBufferToWordArray(e.target.result as ArrayBuffer)
          hash.update(wordArray)

          offset += chunkSize
          if (offset < file.size) {
            readChunk() // 继续读取下一块
          } else {
            // 最终计算哈希
            const result = hash.finalize()
            resolve(result.toString(CryptoJS.enc.Hex))
          }
        }
      }

      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }

      readChunk() // 开始读取第一块
    })
  }

  // ArrayBuffer 转 CryptoJS WordArray
  arrayBufferToWordArray(buffer: ArrayBuffer): CryptoJS.lib.WordArray {
    const uint8Array = new Uint8Array(buffer)
    const len = uint8Array.length
    const wordCount = Math.ceil(len / 4)
    const words: number[] = new Array(wordCount).fill(0)

    for (let i = 0; i < len; i++) {
      words[i >>> 2] |= uint8Array[i] << (24 - (i % 4) * 8)
    }

    return CryptoJS.lib.WordArray.create(words, len)
  }

  /**
   * 下载文件
   * @param storedFilepath 存储的文件路径
   * @param progressCallback 进度回调函数
   * @param chunkSize 分块大小，默认1MB
   * @returns Promise<{fileName: string, content: Uint8Array, sha256: string}>
   */
  async downloadFile(
    storedFilepath: string,
    progressCallback?: (progress: number, fileName?: string) => void,
    chunkSize: number = 1024 * 1024,
  ): Promise<{ fileName: string; content: Uint8Array; sha256: string }> {
    return new Promise((resolve, reject) => {
      try {
        console.log('开始下载文件:', storedFilepath)

        // 存储文件块的数组
        const chunks: Uint8Array[] = []
        let fileName = ''
        let totalSha256 = ''
        let totalSize = 0
        let receivedSize = 0

        // 调用 gRPC 下载接口
        const downloadControl = window.grpcApi.downloadFile(
          storedFilepath,
          chunkSize,
          (result: any, type: string) => {
            if (type === 'data') {
              // 处理文件块数据
              const chunk = result

              console.log(`接收到文件块 ${chunk.chunk_id}:`, {
                file_name: chunk.file_name,
                total_sha256: chunk.total_sha256,
                chunk_id: chunk.chunk_id,
                is_last: chunk.is_last,
                content_length: chunk.content ? chunk.content.length : 0,
              })

              // 保存文件信息
              if (!fileName && chunk.file_name) {
                fileName = chunk.file_name
                console.log(`下载文件: ${fileName}`)
              }

              // 将 bytes 内容转换为 Uint8Array
              let chunkContent: Uint8Array
              if (chunk.content instanceof Uint8Array) {
                chunkContent = chunk.content
              } else if (chunk.content && chunk.content.data) {
                // 处理可能的 Buffer 格式
                chunkContent = new Uint8Array(chunk.content.data)
              } else if (typeof chunk.content === 'string') {
                // 处理 base64 编码的字符串
                const binaryString = atob(chunk.content)
                chunkContent = new Uint8Array(binaryString.length)
                for (let i = 0; i < binaryString.length; i++) {
                  chunkContent[i] = binaryString.charCodeAt(i)
                }
              } else {
                console.error('未知的内容格式:', chunk.content)
                chunkContent = new Uint8Array(0)
              }

              chunks.push(chunkContent)
              receivedSize += chunkContent.length

              // 更新进度
              if (progressCallback) {
                // 如果是最后一块，进度为100%，否则根据已接收大小估算
                const progress = chunk.is_last
                  ? 100
                  : Math.min(95, (receivedSize / (receivedSize + chunkSize)) * 100)
                progressCallback(progress, fileName)
              }

              console.log(
                `接收文件块 ${chunk.chunk_id}, 大小: ${chunkContent.length}, 是否最后: ${chunk.is_last}`,
              )

              // 如果是最后一块，合并所有块
              if (chunk.is_last) {
                try {
                  // 从最后一个块获取 SHA256
                  if (chunk.total_sha256) {
                    totalSha256 = chunk.total_sha256
                    console.log(`从最后一个块获取到文件SHA256: ${totalSha256}`)
                  }
                  // 计算总大小
                  totalSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0)

                  // 合并所有块
                  const mergedContent = new Uint8Array(totalSize)
                  let offset = 0
                  for (const chunk of chunks) {
                    mergedContent.set(chunk, offset)
                    offset += chunk.length
                  }
                  console.log(`合并后文件大小: ${mergedContent.length} 字节`)
                  // 验证文件完整性
                  this.verifyFileIntegrity(mergedContent, totalSha256)
                    .then((isValid) => {
                      if (isValid) {
                        console.log('文件下载完成，完整性验证通过')
                        if (progressCallback) {
                          progressCallback(100, fileName)
                        }
                        resolve({
                          fileName,
                          content: mergedContent,
                          sha256: totalSha256,
                        })
                      } else {
                        reject(new Error('文件完整性验证失败'))
                      }
                    })
                    .catch((error) => {
                      reject(new Error(`文件完整性验证失败: ${error.message}`))
                    })
                } catch (error) {
                  reject(new Error(`合并文件块失败: ${(error as Error).message}`))
                }
              }
            } else if (type === 'end') {
              console.log('下载流结束')
              // 如果没有收到 is_last=true 的块，这里可能需要处理
            } else if (type === 'error') {
              console.error('下载错误:', result)
              reject(new Error(`下载失败: ${result}`))
            }
          },
        )

        // 可以返回下载控制对象，用于取消下载
        // return downloadControl
      } catch (error) {
        console.error('下载文件异常:', error)
        reject(new Error(`下载文件失败: ${(error as Error).message}`))
      }
    })
  }

  /**
   * 验证文件完整性
   * @param content 文件内容
   * @param expectedSha256 期望的SHA256值
   * @returns Promise<boolean>
   */
  private async verifyFileIntegrity(content: Uint8Array, expectedSha256: string): Promise<boolean> {
    try {
      console.log(`开始验证文件完整性，文件大小: ${content.length} 字节`)

      // 对于大文件使用分块处理
      const CHUNK_SIZE = 64 * 1024 * 1024 // 64MB 分块

      if (content.length > CHUNK_SIZE) {
        // 分块计算 SHA256
        const hash = CryptoJS.algo.SHA256.create()

        for (let offset = 0; offset < content.length; offset += CHUNK_SIZE) {
          const end = Math.min(offset + CHUNK_SIZE, content.length)
          const chunk = content.slice(offset, end)
          const wordArray = this.uint8ArrayToWordArray(chunk)
          hash.update(wordArray)
        }

        const calculatedSha256 = hash.finalize().toString(CryptoJS.enc.Hex)
        console.log(`文件完整性验证: 期望=${expectedSha256}, 实际=${calculatedSha256}`)
        return calculatedSha256.toLowerCase() === expectedSha256.toLowerCase()
      } else {
        // 小文件一次性处理
        const wordArray = this.uint8ArrayToWordArray(content)
        const calculatedSha256 = CryptoJS.SHA256(wordArray).toString(CryptoJS.enc.Hex)
        console.log(`文件完整性验证: 期望=${expectedSha256}, 实际=${calculatedSha256}`)
        return calculatedSha256.toLowerCase() === expectedSha256.toLowerCase()
      }
    } catch (error) {
      console.error('计算文件SHA256失败:', error)
      return false
    }
  }

  /**
   * Uint8Array 转 CryptoJS WordArray
   * @param uint8Array Uint8Array数据
   * @returns CryptoJS.lib.WordArray
   */
  private uint8ArrayToWordArray(uint8Array: Uint8Array): CryptoJS.lib.WordArray {
    const len = uint8Array.length

    // 添加调试信息
    console.log(`uint8ArrayToWordArray: len=${len}, type=${typeof len}`)

    // 安全检查
    if (!Number.isFinite(len) || len < 0) {
      console.error(`无效的数组长度: ${len}`)
      throw new Error(`无效的数组长度: ${len}`)
    }

    const wordCount = Math.ceil(len / 4)
    console.log(`wordCount=${wordCount}`)

    // 检查 wordCount 是否在合理范围内
    const MAX_ARRAY_LENGTH = 2 ** 31 - 1 // 约 2GB 的 32位整数数组
    if (wordCount > MAX_ARRAY_LENGTH) {
      console.error(`数组长度超出限制: ${wordCount} > ${MAX_ARRAY_LENGTH}`)
      throw new Error(`文件过大，无法处理: ${len} 字节`)
    }

    const words: number[] = new Array(wordCount).fill(0)

    for (let i = 0; i < len; i++) {
      words[i >>> 2] |= uint8Array[i] << (24 - (i % 4) * 8)
    }

    return CryptoJS.lib.WordArray.create(words, len)
  }
}
