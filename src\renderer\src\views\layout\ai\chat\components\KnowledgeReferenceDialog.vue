<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent class="sm:max-w-4xl max-h-[85vh] flex flex-col bg-card text-card-foreground">
      <DialogHeader class="border-b border-border pb-4">
        <DialogTitle class="text-lg">引用知识库资源</DialogTitle>
        <DialogDescription>从您的知识库中选择文件进行引用。</DialogDescription>
      </DialogHeader>

      <div class="p-1 flex-1 flex flex-col overflow-hidden">
        <!-- 搜索和筛选 -->
        <div class="flex items-center space-x-3 mb-4 px-3 pt-3">
          <div class="relative flex-1">
            <Input
              v-model="searchTerm"
              type="text"
              placeholder="搜索知识库文件..."
              class="w-full pl-10 py-2 text-sm h-10 bg-background border-border focus:border-primary"
            />
            <Search
              class="w-4 h-4 text-muted-foreground absolute left-3 top-1/2 transform -translate-y-1/2"
            />
          </div>
          <Select v-model="selectedFileType">
            <SelectTrigger class="w-[150px] h-10 bg-background border-border focus:border-primary">
              <SelectValue placeholder="所有类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <SelectItem value="PDF">PDF</SelectItem>
              <SelectItem value="DOCX">Word (DOCX)</SelectItem>
              <SelectItem value="XLSX">Excel (XLSX)</SelectItem>
              <SelectItem value="MD">Markdown</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 文件列表 -->
        <ScrollArea class="flex-1 px-3 pb-3">
          <div class="space-y-2.5">
            <div
              v-for="(file, index) in filteredFiles"
              :key="index"
              class="p-3 border border-border rounded-lg hover:bg-muted/50 cursor-pointer flex items-center transition-colors duration-150 ease-in-out"
              @click="referenceFile(file)"
            >
              <div
                class="w-10 h-10 rounded-md flex items-center justify-center mr-3 shrink-0"
                :class="getFileIconBgClass(file.type)"
              >
                <component
                  :is="getFileIcon(file.type)"
                  class="w-5 h-5"
                  :class="getFileIconColorClass(file.type)"
                />
              </div>
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-foreground truncate">{{ file.name }}</h4>
                <p class="text-xs text-muted-foreground">
                  {{ file.size }} · 上传于 {{ file.uploadDate }}
                </p>
              </div>
              <div class="flex items-center ml-3 shrink-0">
                <Badge
                  variant="secondary"
                  class="mr-3 text-xs font-normal"
                  :class="getFileBadgeClass(file.type)"
                >
                  {{ file.type }}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  class="text-xs h-8 border-primary/50 text-primary hover:bg-primary/10 hover:text-primary"
                  @click.stop="referenceFile(file)"
                >
                  引用
                </Button>
              </div>
            </div>
            <div v-if="filteredFiles.length === 0" class="text-center py-8 text-muted-foreground">
              <Archive class="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>未找到匹配的文件。</p>
            </div>
          </div>
        </ScrollArea>
      </div>

      <DialogFooter
        class="border-t border-border pt-4 flex-col sm:flex-row sm:justify-between items-center"
      >
        <p class="text-sm text-muted-foreground mb-2 sm:mb-0">
          显示 {{ (currentPage - 1) * itemsPerPage + 1 }}-{{
            Math.min(currentPage * itemsPerPage, totalFilteredFiles)
          }}
          共 {{ totalFilteredFiles }} 个文件
        </p>
        <Pagination
          v-if="totalPages > 1"
          :total="totalFilteredFiles"
          :items-per-page="itemsPerPage"
          :sibling-count="1"
          show-edges
          :page="currentPage"
          class="justify-center sm:justify-end"
          @update:page="goToPage"
        />
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

import {
  Search,
  FileText,
  FileCode2,
  FileSpreadsheet,
  FileArchive,
  Archive,
  FileUp,
  FileDown,
  FileImage,
  FileAudio,
  FileVideo,
} from 'lucide-vue-next'

interface KnowledgeFile {
  id: string
  name: string
  type: string
  size: string
  uploadDate: string
  path: string
}

const props = defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits<{
  (e: 'update:isOpen', value: boolean): void
  (e: 'close'): void
  (e: 'reference', file: KnowledgeFile): void
}>()

const searchTerm = ref('')
const selectedFileType = ref('all')

// 模拟更多文件数据
const allKnowledgeFiles = ref<KnowledgeFile[]>(
  Array.from({ length: 24 }, (_, i) => {
    const types = ['PDF', 'DOCX', 'XLSX', 'MD', 'PNG', 'ZIP', 'MP3', 'MP4']
    const type = types[i % types.length]
    return {
      id: `file-${i + 1}`,
      name: `知识文件示例 ${i + 1}.${type.toLowerCase()}`,
      type: type,
      size: `${(Math.random() * 10).toFixed(1)} MB`,
      uploadDate: `2023-1${Math.floor(i / 10)}-${(i % 28) + 1}`,
      path: `/path/to/file-${i + 1}.${type.toLowerCase()}`,
    }
  }),
)

const filteredFilesByType = computed(() => {
  if (selectedFileType.value === 'all') {
    return allKnowledgeFiles.value
  }
  return allKnowledgeFiles.value.filter((file) => file.type === selectedFileType.value)
})

const filteredFiles = computed(() => {
  const term = searchTerm.value.toLowerCase()
  if (!term) {
    return filteredFilesByType.value
  }
  return filteredFilesByType.value.filter((file) => file.name.toLowerCase().includes(term))
})

const itemsPerPage = 5
const currentPage = ref(1)

const totalFilteredFiles = computed(() => filteredFiles.value.length)
const totalPages = computed(() => Math.ceil(totalFilteredFiles.value / itemsPerPage))

const paginatedFiles = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredFiles.value.slice(start, end)
})

const getFileIcon = (type: string) => {
  const typeMap: Record<string, any> = {
    PDF: FileText,
    DOCX: FileText, // Could use a specific Word icon if available
    XLSX: FileSpreadsheet,
    MD: FileCode2,
    PNG: FileImage,
    JPG: FileImage,
    GIF: FileImage,
    ZIP: FileArchive,
    RAR: FileArchive,
    MP3: FileAudio,
    WAV: FileAudio,
    MP4: FileVideo,
    MOV: FileVideo,
    UPLOAD: FileUp,
    DOWNLOAD: FileDown,
  }
  return typeMap[type.toUpperCase()] || FileText
}

const getFileIconBgClass = (type: string) => {
  const typeMap: Record<string, string> = {
    PDF: 'bg-red-100 dark:bg-red-900/30',
    DOCX: 'bg-blue-100 dark:bg-blue-900/30',
    XLSX: 'bg-green-100 dark:bg-green-900/30',
    MD: 'bg-gray-100 dark:bg-gray-700/30',
    PNG: 'bg-purple-100 dark:bg-purple-900/30',
    JPG: 'bg-purple-100 dark:bg-purple-900/30',
    GIF: 'bg-purple-100 dark:bg-purple-900/30',
    ZIP: 'bg-yellow-100 dark:bg-yellow-800/30',
    RAR: 'bg-yellow-100 dark:bg-yellow-800/30',
    MP3: 'bg-pink-100 dark:bg-pink-900/30',
    WAV: 'bg-pink-100 dark:bg-pink-900/30',
    MP4: 'bg-indigo-100 dark:bg-indigo-900/30',
    MOV: 'bg-indigo-100 dark:bg-indigo-900/30',
  }
  return typeMap[type.toUpperCase()] || 'bg-muted'
}

const getFileIconColorClass = (type: string) => {
  const typeMap: Record<string, string> = {
    PDF: 'text-red-600 dark:text-red-400',
    DOCX: 'text-blue-600 dark:text-blue-400',
    XLSX: 'text-green-600 dark:text-green-400',
    MD: 'text-gray-600 dark:text-gray-400',
    PNG: 'text-purple-600 dark:text-purple-400',
    JPG: 'text-purple-600 dark:text-purple-400',
    GIF: 'text-purple-600 dark:text-purple-400',
    ZIP: 'text-yellow-600 dark:text-yellow-400',
    RAR: 'text-yellow-600 dark:text-yellow-400',
    MP3: 'text-pink-600 dark:text-pink-400',
    WAV: 'text-pink-600 dark:text-pink-400',
    MP4: 'text-indigo-600 dark:text-indigo-400',
    MOV: 'text-indigo-600 dark:text-indigo-400',
  }
  return typeMap[type.toUpperCase()] || 'text-muted-foreground'
}

const getFileBadgeClass = (type: string) => {
  const typeMap: Record<string, string> = {
    PDF: 'border-red-500/50 bg-red-500/10 text-red-700 dark:text-red-400',
    DOCX: 'border-blue-500/50 bg-blue-500/10 text-blue-700 dark:text-blue-400',
    XLSX: 'border-green-500/50 bg-green-500/10 text-green-700 dark:text-green-400',
    MD: 'border-gray-500/50 bg-gray-500/10 text-gray-700 dark:text-gray-400',
  }
  return typeMap[type.toUpperCase()] || 'border-border'
}

const handleOpenChange = (open: boolean) => {
  if (!open) {
    emit('close')
  }
  emit('update:isOpen', open)
}

const referenceFile = (file: KnowledgeFile) => {
  emit('reference', file)
  handleOpenChange(false)
}

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// Reset current page when filters change
watch([searchTerm, selectedFileType], () => {
  currentPage.value = 1
})
</script>
