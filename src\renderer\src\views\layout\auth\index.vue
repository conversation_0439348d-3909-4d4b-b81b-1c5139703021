<template>
  <!-- 移除 min-h-screen，使用 h-screen 并添加 overflow-hidden 防止滚动 -->
  <div class="h-screen w-full flex bg-background overflow-hidden">
    <!-- 移除外层容器的 flex items-center justify-center 并让内容直接铺满 -->
    <div class="w-full h-full flex bg-background dark:bg-gray-900">
      <!-- 左侧区域 - 渐变背景和文字 -->
      <div
        class="w-1/2 bg-gradient-to-br from-primary/90 to-primary-foreground/90 text-white p-8 flex flex-col justify-between relative overflow-hidden"
      >
        <!-- 装饰性图形 -->
        <div class="absolute -bottom-16 -left-16 w-64 h-64 rounded-full bg-background/10"></div>
        <div class="absolute top-20 -right-10 w-40 h-40 rounded-full bg-background/10"></div>
        <div class="absolute top-1/2 left-1/3 w-24 h-24 rounded-full bg-background/10"></div>

        <!-- 内容 -->
        <div class="relative z-10">
          <div class="w-16 h-16 rounded-2xl bg-background/50 flex items-center justify-center mb-6">
            <img :src="activeContent.logoSrc" :alt="activeContent.title" class="w-12 h-12" />
          </div>
          <h1 class="text-3xl font-bold tracking-tight mb-2">{{ activeContent.title }}</h1>
          <h2 class="text-xl font-medium text-white/80 mb-6">{{ activeContent.subtitle }}</h2>
          <p class="text-white/70 mb-6 text-base">
            {{ activeContent.description }}
          </p>
          <ul class="space-y-3 text-white/70">
            <li
              v-for="(feature, index) in activeContent.features"
              :key="index"
              class="flex items-center"
            >
              <component :is="feature.icon" class="w-5 h-5 mr-2 text-white/90" />
              <span>{{ feature.text }}</span>
            </li>
          </ul>
        </div>

        <!-- 底部版权信息 -->
        <div class="text-sm text-white/50 relative z-10">{{ activeContent.copyright }}</div>
      </div>

      <!-- 右侧区域 - 登录/注册表单 -->
      <div class="w-1/2 flex items-center justify-center p-8">
        <div class="w-full max-w-md">
          <!-- 表单区域 - 使用transition组件实现动画 -->
          <transition name="slide-fade" mode="out-in">
            <LoginForm v-if="isLogin" key="login" @register-click="isLogin = false" />
            <RegisterForm v-else key="register" @login-click="isLogin = true" />
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HPLogo from '@renderer/assets/images/HPlogo.png'
import MattLogo from '@renderer/assets/images/logo-text.png'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { CheckCircle, Globe, Zap } from 'lucide-vue-next'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import LoginForm from './components/LoginForm.vue'
import RegisterForm from './components/RegisterForm.vue'

// 定义内容类型
interface Feature {
  icon: any
  text: string
}

interface ContentConfig {
  id: string
  logoSrc: string
  title: string
  subtitle: string
  description: string
  features: Feature[]
  copyright: string
}

// 定义不同的内容配置
const contentConfigs: Record<string, ContentConfig> = {
  mattverse: {
    id: 'mattverse',
    logoSrc: MattLogo,
    title: 'MattVerse',
    subtitle: '电池设计自动化平台',
    description:
      '欢迎使用MattVerse，这是一个专为电池研发设计的智能化平台，为您提供全方位的电池设计解决方案。',
    features: [
      { icon: CheckCircle, text: '智能电池设计工作流' },
      { icon: Zap, text: '高效数据分析与可视化' },
      { icon: Globe, text: '多平台兼容与无缝协作' },
    ],
    copyright: '© 2025 MattVerse. 保留所有权利。',
  },
  highpower: {
    id: 'highpower',
    logoSrc: HPLogo,
    title: '豪鹏科技',
    subtitle: '电池寿命预测软件',
    description:
      '欢迎使用豪鹏电池寿命预测软件，这是一款专业的电池性能分析工具，助您精确预测电池寿命周期。',
    features: [
      { icon: CheckCircle, text: '精确的寿命预测算法' },
      { icon: Zap, text: '实时性能监测与分析' },
      { icon: Globe, text: '全面的数据报告与导出' },
    ],
    copyright: '© 2025 豪鹏科技. 保留所有权利。',
  },
}

// 使用应用配置
const { appType } = useAppConfig()
// 当前激活的内容ID
const activeContentId = ref(appType.value)

// 计算当前激活的内容
const activeContent = computed(() => {
  return contentConfigs[activeContentId.value]
})

// 是否显示登录表单（否则显示注册表单）
const isLogin = ref(true)

// 确保窗口以登录大小显示
onMounted(() => {
  if (window.windowControl && window.windowControl.setLoginSize) {
    window.windowControl.setLoginSize()
  }

  // 添加 dragable 类，使窗口可拖动
  document.body.classList.add('dragable')
})

// 组件卸载时移除 dragable 类
onUnmounted(() => {
  document.body.classList.remove('dragable')
})

// 切换内容函数
const switchContent = (contentId: string) => {
  if (contentConfigs[contentId]) {
    activeContentId.value = contentId
  }
}

// 暴露切换内容的方法，以便外部组件可以调用
defineExpose({
  switchContent,
})
</script>

<style>
/* 允许窗口拖动的样式 */
.dragable {
  -webkit-app-region: drag;
}

/* 表单控件等不应该被拖动 */
input,
button,
a,
select,
textarea,
[role='button'],
.tabs-list,
.tab-trigger,
.form-control,
.form-item {
  -webkit-app-region: no-drag !important;
}

/* 淡入淡出加滑动动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}
.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}
</style>
