import { electronAPI } from '@electron-toolkit/preload'
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import log from 'electron-log'
/**
 * 设置安全的IPC通信
 */
export function setupSecureIpc(validChannels: string[]): void {
  contextBridge.exposeInMainWorld('electron', {
    ...electronAPI,
    ipcRenderer: {
      invoke: (channel: string, data: any) => {
        log.log('Invoking channel:', channel)
        if (validChannels.includes(channel)) {
          return ipcRenderer.invoke(channel, data)
        }
        throw new Error(`Unauthorized ipcRenderer channel: ${channel}`)
      },
      send: (channel: string, ...args: any[]) => {
        if (validChannels.includes(channel)) {
          ipcRenderer.send(channel, ...args)
        }
      },
      on: (channel: string, func: (...args: any[]) => void) => {
        if (validChannels.includes(channel)) {
          ipcRenderer.on(channel, (_event, ...args) => func(...args))
        }
      },
      once: (channel: string, func: (...args: any[]) => void) => {
        if (validChannels.includes(channel)) {
          ipcRenderer.once(channel, (_event, ...args) => func(...args))
        }
      },
      removeListener: (channel: string, func: (...args: any[]) => void) => {
        if (validChannels.includes(channel)) {
          ipcRenderer.removeListener(channel, func)
        }
      },
    },
  })
}
