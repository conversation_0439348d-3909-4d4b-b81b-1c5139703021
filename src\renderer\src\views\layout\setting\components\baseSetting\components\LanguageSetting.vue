<template>
  <Card>
    <CardHeader>
      <CardTitle>{{ t('settings.baseSetting.language.title') }}</CardTitle>
      <CardDescription>{{ t('settings.baseSetting.language.description') }}</CardDescription>
    </CardHeader>
    <CardContent>
      <div class="space-y-2">
        <Label>{{ t('settings.baseSetting.language.select') }}</Label>
        <Select v-model="currentLanguage" @update:model-value="setLanguage">
          <SelectTrigger class="w-full sm:w-[240px]">
            <SelectValue :placeholder="t('settings.baseSetting.language.placeholder')" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>{{ t('settings.baseSetting.language.select') }}</SelectLabel>
              <SelectItem v-for="lang in languages" :key="lang.value" :value="lang.value">
                <div class="flex items-center gap-2">
                  <img :src="lang.flag" :alt="lang.label" class="w-4 h-4 rounded-sm" />
                  <span>{{ lang.label }}</span>
                </div>
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { useLanguage } from '@renderer/config/hooks'
import { useSettingsStore } from '@renderer/store'
import { computed } from 'vue'

const { t } = useLanguage()
const settingsStore = useSettingsStore()

// 使用 store 中的状态
const currentLanguage = computed(() => settingsStore.language)
const languages = computed(() => settingsStore.languageConfigs)

const setLanguage = (lang: string) => {
  settingsStore.setLanguage(lang)
}
</script>
