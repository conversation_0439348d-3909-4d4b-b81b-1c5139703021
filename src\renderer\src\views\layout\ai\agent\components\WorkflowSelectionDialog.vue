<template>
  <AlertDialog :open="open" @update:open="$emit('update:open', $event)">
    <AlertDialogContent class="max-w-3xl">
      <AlertDialogHeader>
        <AlertDialogTitle>选择工作流</AlertDialogTitle>
        <AlertDialogDescription>
          请选择要将模板"{{ template?.name }}"添加到的工作流
        </AlertDialogDescription>
      </AlertDialogHeader>

      <div class="py-4">
        <div v-if="workflows.length === 0" class="text-center p-6 bg-muted/20 rounded-md">
          <p class="text-muted-foreground">暂无可用的工作流，请先创建工作流</p>
          <Button class="mt-4" @click="createNewWorkflow">创建新工作流</Button>
        </div>

        <div
          v-else
          class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto scrollbar p-1"
        >
          <Card
            v-for="workflow in workflows"
            :key="workflow.id"
            class="cursor-pointer transition-all hover:shadow-md"
            :class="{ 'ring-2 ring-primary': selectedWorkflowId === workflow.id }"
            @click="selectedWorkflowId = workflow.id"
          >
            <CardHeader class="pb-2">
              <CardTitle class="text-base">{{ workflow.title }}</CardTitle>
              <CardDescription class="text-xs">{{ workflow.description }}</CardDescription>
            </CardHeader>
            <CardContent class="pb-3">
              <div class="text-xs text-muted-foreground">创建于 {{ workflow.createTime }}</div>
              <div class="mt-2 flex items-center text-xs">
                <LucideIcon name="Workflow" class="h-3 w-3 mr-1" />
                <span>{{ getNodesCount(workflow.id) }} 个节点</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <AlertDialogFooter>
        <AlertDialogCancel @click="$emit('update:open', false)">取消</AlertDialogCancel>
        <AlertDialogAction
          :disabled="!selectedWorkflowId && workflows.length > 0"
          @click="handleAddToWorkflow"
        >
          添加到工作流
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useWorkflowStore } from '@renderer/store/modules/workflow'
import { useFlowsStore } from '@renderer/store/modules/flows'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { LucideIcon } from '@renderer/components'
import type { Template } from '@renderer/store/modules/templates'
import { Position } from '@vue-flow/core'

const props = defineProps<{
  open: boolean
  template: Template | null
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  added: [workflowId: string]
}>()

const workflowStore = useWorkflowStore()
const flowsStore = useFlowsStore()
const router = useRouter()

const selectedWorkflowId = ref('')

// 获取所有工作流
const workflows = computed(() => workflowStore.workflows)

// 获取工作流中的节点数量
const getNodesCount = (workflowId: string) => {
  const workflow = flowsStore.getWorkflow(workflowId)
  return workflow?.nodes?.length || 0
}

// 创建新工作流
const createNewWorkflow = () => {
  // 关闭当前对话框
  emit('update:open', false)

  // 跳转到工作流创建页面
  router.push('/workflow')
}

// 处理添加到工作流
const handleAddToWorkflow = () => {
  if (!selectedWorkflowId.value || !props.template) return

  const workflow = flowsStore.getWorkflow(selectedWorkflowId.value)

  // 确保工作流存在
  if (!workflow) {
    flowsStore.createWorkflow(selectedWorkflowId.value)
  }

  // 计算新节点的偏移位置，避免重叠
  const baseOffset = { x: 50, y: 50 }
  const existingNodesCount = workflow?.nodes?.length || 0

  // 复制模板中的节点和边，并分配新的ID
  const nodeIdMap = {} // 用于映射旧节点ID到新节点ID

  // 复制节点
  const newNodes = props.template.nodes.map((node, index) => {
    const oldId = node.id
    const newId = `node-${Date.now()}-${index}`
    nodeIdMap[oldId] = newId

    // 计算新位置，根据现有节点数量添加偏移
    const position = {
      x: (node.position?.x || 0) + baseOffset.x + existingNodesCount * 20,
      y: (node.position?.y || 0) + baseOffset.y + existingNodesCount * 20,
    }

    // 确保节点有完整的数据结构，与拖拽添加的节点一致
    return {
      ...node,
      id: newId,
      position,
      type: 'custom', // 确保使用已注册的节点类型
      sourcePosition: Position.Right, // 添加源连接点位置
      targetPosition: Position.Left, // 添加目标连接点位置
      data: {
        ...node.data,
        workflowId: selectedWorkflowId.value, // 添加工作流ID
        params: node.data.params || {}, // 确保params对象存在
        label: node.data.label || '未命名节点', // 确保有标签
        description: node.data.description || '', // 确保有描述
        icon: node.data.icon || {
          // 确保有图标
          type: 'icon',
          value: 'flowbite:draw-square-outline',
        },
      },
    }
  })

  // 复制边，并更新源和目标节点ID
  const newEdges = props.template.edges.map((edge, index) => {
    return {
      ...edge,
      id: `edge-${Date.now()}-${index}`,
      source: nodeIdMap[edge.source],
      target: nodeIdMap[edge.target],
      type: edge.type || 'smoothstep', // 确保有边的类型
      animated: edge.animated !== undefined ? edge.animated : false, // 确保动画属性存在
    }
  })

  // 将新节点和边添加到工作流中
  flowsStore.saveWorkflow(selectedWorkflowId.value, {
    nodes: [...(workflow?.nodes || []), ...newNodes],
    edges: [...(workflow?.edges || []), ...newEdges],
  })

  // 设置当前工作流ID
  workflowStore.currentWfId = selectedWorkflowId.value
  flowsStore.setCurrentWorkflow(selectedWorkflowId.value)

  // 发送成功通知
  toast.success('添加成功', {
    description: `模板"${props.template.name}"已添加到工作流`,
  })

  // 关闭对话框并触发添加完成事件
  emit('update:open', false)
  emit('added', selectedWorkflowId.value)
}
</script>
