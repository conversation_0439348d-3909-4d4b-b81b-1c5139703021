<template>
  <div
    class="bg-background border border-gray-200 rounded-md shadow-sm p-3 relative flex items-center justify-between w-[260px] min-h-[5vh] transition-all duration-200 select-none"
    :class="[
      selected ? 'border-blue-500 ring-2 ring-blue-500/50' : '',
      'hover:shadow-md',
      isBoxSelected ? 'z-10 border-[2.5px] border-dashed border-red-500 bg-red-50!' : '',
    ]"
    :style="{ backgroundColor: nodeBackgroundColor || '#ffffff' }"
    v-bind="$attrs"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @dblclick="handleDblClick"
  >
    <!-- 工具栏 -->
    <div
      class="absolute left-1 -top-10 flex bg-background text-foreground gap-1 p-1 rounded transition-opacity duration-200 z-10"
      :class="isToolbarVisible ? 'opacity-100' : 'opacity-0'"
    >
      <button
        class="flex items-center justify-center p-1 rounded hover:bg-gray-300 transition-colors duration-200"
        title="设置"
        @click="handleSettings"
      >
        <Icon icon="flowbite:cog-outline" :width="16" :height="16" />
      </button>
      <button
        class="flex items-center justify-center p-1 rounded hover:bg-gray-300 transition-colors duration-200"
        title="删除"
        @click="openDeleteConfirm"
      >
        <Icon icon="flowbite:trash-bin-outline" :width="16" :height="16" />
      </button>
    </div>

    <!-- 节点头部 -->
    <div class="flex items-center justify-between font-medium w-full">
      <span class="flex items-center justify-center mr-2 w-8 h-8 flex-shrink-0">
        <template v-if="data.icon">
          <Icon v-if="data.icon.type === 'icon'" :icon="data.icon.value" class="w-6 h-6" />
          <img
            v-else-if="data.icon.type === 'svg'"
            :src="data.icon.value"
            class="w-6 h-6 object-contain"
          />
          <img
            v-else-if="data.icon.type === 'image'"
            :src="data.icon.value"
            class="w-6 h-6 object-contain"
          />
        </template>
        <Icon v-else icon="flowbite:draw-square-outline" :width="28" :height="28" />
      </span>
      <span class="text-foreground text-xl font-semibold flex-1 truncate leading-8">
        {{ data.label || '未命名节点' }}
      </span>
    </div>

    <!-- 节点描述 -->
    <div v-if="data.description" class="mt-2 pt-2">
      <div class="text-sm border-t pt-2 border-gray-100 text-gray-600">
        {{ data.description || '暂无描述' }}
      </div>
    </div>

    <!-- Handle 连接器 -->
    <div class="absolute top-1/2 left-[-10px] -translate-y-1/2 w-3 h-3 z-[100]">
      <Handle
        type="target"
        class="w-full h-full rounded-full bg-background border-2 border-gray-600 transition-all duration-300 origin-center hover:border-blue-500 transform-none top-auto"
        :position="Position.Left"
      />
    </div>

    <div class="absolute top-1/2 right-[-10px] -translate-y-1/2 w-3 h-3 z-[100]">
      <Handle
        type="source"
        class="w-full h-full rounded-full bg-gray-600 transition-all duration-300 origin-center hover:bg-blue-500 transform-none top-auto"
        :position="Position.Right"
      />
    </div>
  </div>
  <!-- 任务状态面板 - 仅在节点有关联任务时显示 -->
  <TaskStatusPanel
    v-if="data.taskId"
    :node-id="id"
    :task-id="data.taskId"
    :task-status="taskStatus"
    :task-process="taskProcess"
    :execution-time="formattedDuration"
    :input-data="data.taskInputData || {}"
    :output-data="taskOutput || ''"
  />
  <!-- 删除确认对话框 -->
  <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>确认删除</AlertDialogTitle>
        <AlertDialogDescription>您确定要删除此节点吗？此操作无法撤销。</AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
        <AlertDialogAction class="bg-red-500 hover:bg-red-600" @click="confirmDelete">
          确认删除
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useSettingsStore, useTaskStore } from '@renderer/store'
import { Handle, Position } from '@vue-flow/core'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import TaskStatusPanel from '../components/TaskStatusPanel.vue'
import { useNodeTheme } from '../composables/useNodeTheme'

const taskStore = useTaskStore()
const settingsStore = useSettingsStore()
const { getNodeBackgroundColor } = useNodeTheme()

defineOptions({
  inheritAttrs: false, // 禁用自动属性继承
})
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  isBoxSelected: {
    type: Boolean,
    default: false,
  },
})
// console.log('======================>data', props.data)

// 当前主题 - 使用设置存储
const currentTheme = computed(() => settingsStore.theme || 'city-light')
const isToolbarVisible = ref(false)
const isTaskCompleted = ref(false) //判断任务是否已完成
const isDeleteDialogOpen = ref(false) //删除确认对话框的打开状态

const taskStartTime = ref(0) // 任务开始时间戳
const taskElapsedSeconds = ref(0) // 任务已运行的秒数
const timerInterval = ref(null) // 计时器

const emit = defineEmits(['delete', 'settings', 'dblclick', 'updateNodeInternals'])

// 获取当前任务状态
const taskStatus = computed(() => {
  if (!props.data.taskId) return 'TaskStay'
  return taskStore.getTaskStatus(props.data.taskId).value
})
//获取任务结果
const taskOutput = computed(() => {
  if (!props.data.taskId) return ''
  return taskStore.getTaskResultById(props.data.taskId).value
})
// 获取任务进度
const taskProcess = computed(() => {
  if (!props.data.taskId) return 0
  return taskStore.getTaskProgress(props.data.taskId).value
})

// 计算当前任务执行时间
const taskDuration = computed(() => {
  if (!props.data.taskId) return '0s'

  // 获取任务信息
  const task = taskStore.tasks.find((t) => t.taskId === props.data.taskId)

  if (task && task.duration && task.duration !== '0s' && task.duration !== '未开始') {
    // 如果任务已完成，将持续时间保存到节点数据中
    if (
      ['Finished', 'Error', 'Abort'].includes(task.taskStatus) &&
      (!props.data.savedDuration || props.data.savedDuration === '0s')
    ) {
      // 使用 nextTick 确保视图更新
      nextTick(() => {
        if (props.data) {
          // eslint-disable-next-line vue/no-mutating-props
          props.data.savedDuration = task.duration
        }
      })
    }
    return task.duration
  }

  // 如果当前任务没有持续时间，但节点数据中有保存的持续时间，则使用保存的值
  if (props.data.savedDuration && props.data.savedDuration !== '0s') {
    return props.data.savedDuration
  }

  return '0s'
})

// 格式化时间为 HH:mm:ss
const formatTimeHMS = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(
    secs,
  ).padStart(2, '0')}`
}

// 格式化的持续时间
const formattedDuration = computed(() => {
  // 如果任务已完成且有保存的持续时间
  if (props.data.savedDuration && ['Finished', 'Error', 'Abort'].includes(taskStatus.value)) {
    return props.data.savedDuration
  }

  // 否则使用计时器计算的时间
  return formatTimeHMS(taskElapsedSeconds.value)
})

// 启动计时器
const startTimer = () => {
  // 先停止可能存在的计时器
  stopTimer()

  // 如果有开始时间，计算已经过去的秒数
  if (taskStartTime.value > 0) {
    const now = Math.floor(Date.now() / 1000)
    taskElapsedSeconds.value = Math.max(0, now - taskStartTime.value)
  }

  // 启动新的计时器，每秒更新一次
  timerInterval.value = setInterval(() => {
    taskElapsedSeconds.value++
  }, 1000)
}

// 停止计时器
const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

// 打开删除确认对话框
const openDeleteConfirm = () => {
  isDeleteDialogOpen.value = true
}

// 确认删除
const confirmDelete = () => {
  isDeleteDialogOpen.value = false
  nextTick(() => {
    emit('delete')
  })
}

// 设置按钮
const handleSettings = () => {
  emit('settings')
}

// 双击事件
const handleDblClick = (event) => {
  event.stopPropagation()
  emit('settings') // 直接调用设置事件
}

// 添加鼠标事件处理器
const handleMouseEnter = () => {
  isToolbarVisible.value = true
}

const handleMouseLeave = () => {
  isToolbarVisible.value = false
}

// 计算节点背景色
const nodeBackgroundColor = computed(() => {
  // 如果节点自定义了背景色，优先使用
  if (props.data.backgroundColor) {
    return props.data.backgroundColor
  }

  // 如果没有背景色，则根据节点类型和主题计算
  const nodeType = props.data.nodeType || props.data.type
  const nodeCategory = props.data.category || ''

  return getNodeBackgroundColor(props.id, nodeType, nodeCategory)
})

// 监听参数变化
watch(
  () => props.data.params,
  (newParams) => {
    // 参数变化时的逻辑
  },
  { deep: true },
)

// 初始化任务状态
const initTaskPolling = (taskId) => {
  if (!taskId) return

  // 获取任务状态
  const status = taskStore.getTaskStatus(taskId).value

  // 获取任务信息
  const task = taskStore.tasks.find((t) => t.taskId === taskId)

  if (task && task.startTime && task.startTime !== '0') {
    taskStartTime.value = Number(task.startTime)

    // 如果任务已完成，保存最终持续时间
    if (['Finished', 'Error', 'Abort'].includes(task.taskStatus)) {
      if (task.endTime && task.endTime !== '0') {
        const duration = formatTimeHMS(Number(task.endTime) - Number(task.startTime))
        // eslint-disable-next-line vue/no-mutating-props
        props.data.savedDuration = duration
      }
    }
    // 如果任务正在运行，启动计时器
    else if (['Computing', 'Pending', 'Initializing'].includes(task.taskStatus)) {
      startTimer()
    }
  }

  // 如果任务正在运行，启动轮询
  if (['Computing', 'Pending', 'Initializing'].includes(status)) {
    taskStore.startPolling(taskId)
  } else if (['Finished', 'Error', 'Abort'].includes(status)) {
    // 如果任务已完成，确保获取最终结果
    taskStore.updateTaskResult(taskId)
  }
}

// 监听任务ID变化，保存持续时间
watch(
  () => props.data.taskId,
  (newTaskId, oldTaskId) => {
    // 如果有旧任务ID，停止旧任务的轮询
    if (oldTaskId) {
      taskStore.stopPolling(oldTaskId)
      stopTimer()
    }

    // 如果有新任务ID，初始化任务轮询
    if (newTaskId) {
      // 先获取任务列表，确保任务数据存在
      taskStore.updateTaskList(newTaskId).then(() => {
        initTaskPolling(newTaskId)
      })
    }
  },
  { immediate: true },
)

// 监听任务状态变化，自动处理轮询
watch(taskStatus, (newStatus, oldStatus) => {
  if (!props.data.taskId) return

  // 如果任务状态变为运行中，确保启动轮询和计时器
  if (['Computing', 'Pending', 'Initializing'].includes(newStatus)) {
    if (!taskStore.isPolling(props.data.taskId)) {
      taskStore.startPolling(props.data.taskId)
    }

    // 如果计时器未启动，且有开始时间，则启动计时器
    if (!timerInterval.value && taskStartTime.value > 0) {
      startTimer()
    }
  }
  // 如果任务状态变为已完成，停止轮询和计时器，保存最终持续时间
  else if (['Finished', 'Error', 'Abort'].includes(newStatus)) {
    taskStore.stopPolling(props.data.taskId)
    taskStore.updateTaskResult(props.data.taskId)

    // 停止计时器
    stopTimer()

    // 获取任务信息以保存最终持续时间
    const task = taskStore.tasks.find((t) => t.taskId === props.data.taskId)
    if (task && task.startTime && task.startTime !== '0') {
      const endTime =
        task.endTime && task.endTime !== '0' ? Number(task.endTime) : Math.floor(Date.now() / 1000)
      const duration = formatTimeHMS(endTime - Number(task.startTime))
      // eslint-disable-next-line vue/no-mutating-props
      props.data.savedDuration = duration
    }
  }
})

// 组件挂载后初始化
onMounted(() => {
  // 初始化任务状态
  if (props.data && props.data.taskId) {
    taskStore.updateTaskList(props.data.taskId).then(() => {
      initTaskPolling(props.data.taskId)
    })
  }
})

onBeforeUnmount(() => {
  // 停止任务轮询
  if (props.data && props.data.taskId) {
    taskStore.stopPolling(props.data.taskId)
    stopTimer()
  }
})
</script>
