<template>
  <div class="p-4 rounded-lg bg-muted">
    <div class="flex justify-between mb-4">
      <span class="text-sm font-medium text-blue-400 dark:text-blue-900 flex">
        <LucideIcon name="Loader" class="w-4 h-4 mr-2 animate-spin" />
        图表加载中...
      </span>
    </div>
    <div class="space-y-4">
      <div v-for="i in count" :key="i" class="w-full aspect-[4/3] relative">
        <Skeleton class="absolute inset-0 rounded-lg" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

defineProps({
  count: {
    type: Number,
    default: 4,
  },
})
</script>
