<template>
  <!-- 边删除确认对话框 -->
  <EdgeDeleteDialog
    v-model:is-open="edgeDeleteDialogOpen"
    :edge-id="currentEdgeId"
    @confirm="handleEdgeDelete"
  />

  <!-- 日志对话框 -->
  <Logger v-model:is-open="loggerOpen" :current-workflow-id="workflowId" />

  <!-- 警告提示组件 -->
  <AlertMessage
    v-model:show="warningVisible"
    :message="warningMessage"
    title="当前节点不支持连接"
    variant="destructive"
    position="top-center"
    :duration="3000"
  />

  <!-- 引入 AI 聊天浮动组件 -->
  <AIChatFloating v-if="canShowAIFloatingBox" />
</template>

<script setup lang="ts">
import { AlertMessage } from '@renderer/components'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import Logger from '@renderer/views/layout/logger/index.vue'
import { computed } from 'vue'
import AIChatFloating from '../../components/AIChatFloating.vue'
import EdgeDeleteDialog from '../../components/EdgeDeleteDialog.vue'

interface Props {
  isEdgeDeleteDialogOpen: boolean
  currentEdgeId: string
  isLoggerOpen: boolean
  workflowId: string
  showWarning: boolean
  warningMessage: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:isEdgeDeleteDialogOpen': [value: boolean]
  'update:isLoggerOpen': [value: boolean]
  'update:showWarning': [value: boolean]
  'edge-delete': [edgeId: string]
}>()

// 使用应用配置
const { canShowAIFloatingBox } = useAppConfig()

// 计算属性处理双向绑定
const edgeDeleteDialogOpen = computed({
  get: () => props.isEdgeDeleteDialogOpen,
  set: (value: boolean) => emit('update:isEdgeDeleteDialogOpen', value),
})

const loggerOpen = computed({
  get: () => props.isLoggerOpen,
  set: (value: boolean) => emit('update:isLoggerOpen', value),
})

const warningVisible = computed({
  get: () => props.showWarning,
  set: (value: boolean) => emit('update:showWarning', value),
})

const handleEdgeDelete = (edgeId: string) => {
  emit('edge-delete', edgeId)
}
</script>
