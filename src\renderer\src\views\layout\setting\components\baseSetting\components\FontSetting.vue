<template>
  <Card class="mt-6">
    <CardHeader>
      <CardTitle>{{ t('settings.baseSetting.font.title') }}</CardTitle>
      <CardDescription>{{ t('settings.baseSetting.font.description') }}</CardDescription>
    </CardHeader>
    <CardContent>
      <div class="space-y-2">
        <Label>{{ t('settings.baseSetting.font.select') }}</Label>
        <Select v-model="currentFont" @update:model-value="setFont">
          <SelectTrigger class="w-full sm:w-[240px]">
            <SelectValue :placeholder="t('settings.baseSetting.font.placeholder')" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>{{ t('settings.baseSetting.font.select') }}</SelectLabel>
              <SelectItem v-for="font in fonts" :key="font.value" :value="font.value">
                <div class="flex items-center gap-2">
                  <span :style="{ fontFamily: font.value }">Aa</span>
                  <span>{{ t(`settings.baseSetting.font.fonts.${font.value}`) }}</span>
                </div>
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        <!-- 字体预览 -->
        <div class="mt-4 p-4 border rounded-md">
          <p class="text-sm text-muted-foreground mb-2">
            {{ t('settings.baseSetting.font.preview') || '预览' }}:
          </p>
          <p :style="{ fontFamily: currentFont }" class="text-base">
            The quick brown fox jumps over the lazy dog.
            <br />
            中文示例：永和九年，岁在癸丑，暮春之初，会于会稽山阴之兰亭。
          </p>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { useLanguage } from '@renderer/config/hooks'
import { useSettingsStore } from '@renderer/store'
import { computed } from 'vue'

const { t } = useLanguage()
const settingsStore = useSettingsStore()

// 使用 store 中的状态
const currentFont = computed(() => settingsStore.font)
const fonts = computed(() => settingsStore.fontConfigs)

const setFont = (font: string) => {
  settingsStore.setFont(font)
}
</script>
