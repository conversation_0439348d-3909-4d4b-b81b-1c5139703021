<template>
  <div class="w-72 border-l border-border bg-card hidden lg:flex flex-col overflow-hidden">
    <ScrollArea class="flex-1">
      <div class="p-4">
        <h3 class="text-lg font-semibold text-foreground mb-4">对话信息</h3>
        <Accordion type="single" collapsible class="w-full space-y-3" default-value="item-1">
          <AccordionItem value="item-1" class="border-border bg-muted/30 rounded-lg shadow-sm">
            <AccordionTrigger
              class="px-4 py-3 text-sm font-medium text-foreground hover:no-underline hover:bg-muted/50 rounded-t-lg transition-colors"
            >
              <div class="flex items-center">
                <Tags class="w-4 h-4 mr-2 text-primary" />
                相关术语
              </div>
            </AccordionTrigger>
            <AccordionContent class="px-4 pb-3 pt-1 border-t border-border/50">
              <div class="space-y-2 mt-2">
                <div
                  v-for="(term, index) in relatedTerms"
                  :key="index"
                  class="bg-background p-2.5 rounded-md text-sm border border-border/70 shadow-xs hover:shadow-sm transition-shadow"
                >
                  <span class="font-semibold block text-foreground">{{ term.name }}</span>
                  <span class="text-muted-foreground text-xs leading-relaxed">
                    {{ term.description }}
                  </span>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-2" class="border-border bg-muted/30 rounded-lg shadow-sm">
            <AccordionTrigger
              class="px-4 py-3 text-sm font-medium text-foreground hover:no-underline hover:bg-muted/50 rounded-t-lg transition-colors"
            >
              <div class="flex items-center">
                <Paperclip class="w-4 h-4 mr-2 text-primary" />
                相关资源
              </div>
            </AccordionTrigger>
            <AccordionContent class="px-4 pb-3 pt-1 border-t border-border/50">
              <div class="space-y-1.5 mt-2">
                <Button
                  v-for="(resource, index) in relatedResources"
                  :key="index"
                  variant="ghost"
                  size="sm"
                  class="w-full justify-start text-muted-foreground hover:text-foreground hover:bg-background/80 h-auto py-2 px-2.5 rounded-md transition-colors"
                  as-child
                >
                  <a href="#" class="flex items-center">
                    <FileText
                      v-if="resource.type === 'file'"
                      class="w-4 h-4 mr-2.5 flex-shrink-0 text-primary/80"
                    />
                    <ExternalLink v-else class="w-4 h-4 mr-2.5 flex-shrink-0 text-primary/80" />
                    <span class="truncate text-sm">{{ resource.title }}</span>
                  </a>
                </Button>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-3" class="border-border bg-muted/30 rounded-lg shadow-sm">
            <AccordionTrigger
              class="px-4 py-3 text-sm font-medium text-foreground hover:no-underline hover:bg-muted/50 rounded-t-lg transition-colors"
            >
              <div class="flex items-center">
                <Bot class="w-4 h-4 mr-2 text-primary" />
                相关智能体
              </div>
            </AccordionTrigger>
            <AccordionContent class="px-4 pb-3 pt-1 border-t border-border/50">
              <div class="space-y-2 mt-2">
                <Button
                  v-for="(agent, index) in relatedAgents"
                  :key="index"
                  variant="ghost"
                  size="sm"
                  class="w-full justify-start text-muted-foreground hover:text-foreground hover:bg-background/80 h-auto py-2 px-2.5 rounded-md transition-colors"
                  @click="openAgent(agent)"
                >
                  <div class="flex items-center space-x-2.5">
                    <div
                      class="w-7 h-7 rounded-md flex items-center justify-center bg-gradient-to-br from-primary to-primary/70 text-primary-foreground shadow-inner"
                    >
                      <component :is="agent.icon" class="w-4 h-4" />
                    </div>
                    <span class="font-medium text-sm">{{ agent.name }}</span>
                  </div>
                </Button>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </ScrollArea>
  </div>
</template>

<script setup lang="ts">
import { ref, type Component } from 'vue'

import { FileText, ExternalLink, Activity, Zap, Tags, Paperclip, Bot } from 'lucide-vue-next'

interface Term {
  name: string
  description: string
}

interface Resource {
  title: string
  type: 'file' | 'link'
}

interface Agent {
  name: string
  icon: Component // Changed from 'any' to 'Component' for better type safety
}

// 相关术语
const relatedTerms = ref<Term[]>([
  {
    name: '锂离子电池',
    description: '一种使用锂离子作为主要电荷载体的可充电电池。',
  },
  {
    name: '循环寿命',
    description: '电池可以充放电的次数，直到其容量降至额定容量的80%。',
  },
  {
    name: 'SEI膜',
    description: '固体电解质界面膜，影响电池性能和寿命的关键因素。',
  },
])

// 相关资源
const relatedResources = ref<Resource[]>([
  {
    title: '电池寿命优化指南.pdf',
    type: 'file',
  },
  {
    title: '新型电池材料研究报告.docx',
    type: 'file',
  },
  {
    title: '电池技术最新进展 - TechCrunch',
    type: 'link',
  },
])

// 相关智能体
const relatedAgents = ref<Agent[]>([
  {
    name: '电池性能分析器',
    icon: Activity,
  },
  {
    name: '充电策略优化器',
    icon: Zap,
  },
])

// 打开智能体
const openAgent = (agent: Agent) => {
  console.log('打开智能体:', agent.name)
  // 在这里实现打开智能体的逻辑，例如导航到特定页面或显示一个模态框
}
</script>

<style scoped>
/* Tailwind CSS handles most styling. Specific overrides can go here if needed. */
:deep() .accordion-item:last-child {
  border-bottom: none;
}
:deep() .accordion-content {
  overflow: visible; /* Ensure content like tooltips are not clipped */
}
</style>
