export default {
  //工作流
  workflows: {
    title: 'workflow',
    tooltip: 'workflow',
  },
  //计算任务
  tasks: {
    title: 'Computational tasks',
    tooltip: 'Computational tasks',
  },
  //服务器状态
  server: {
    title: 'Server Status',
    tooltip: 'Server Status',
  },
  //日志
  logger: {
    title: 'logger',
    tooltip: 'logger',
  },
  //工具
  tools: {
    title: 'tools',
    tooltip: 'tools',
  },
  //设置
  settings: {
    title: 'Setting',
    tooltip: 'This is the settings page',
    //基本设置
    baseSetting: {
      title: 'Base Settings',
      theme: {
        title: 'Theme Settings',
        description: 'Customize the appearance and theme of the app',
        mode: 'Theme Mode',
        light: 'Light',
        dark: 'Dark',
        system: 'Follow the system',
        colors: {
          'city-light': 'City-Light',
          'forest-light': 'Forest-Light',
          'lake-light': 'Lake-Light',
          'desert-light': 'Desert-Light',
          'farm-light': 'Farm-Light',
          'garden-light': 'Garden-Light',
          'city-dark': 'City-Dark',
          'forest-dark': 'Forest-Dark',
          'lake-dark': 'Lake-Dark',
          'desert-dark': 'Desert-Dark',
          'farm-dark': 'Farm-Dark',
          'garden-dark': 'Garden-Dark',
        },
      },
      language: {
        title: 'Language settings',
        description: 'Select the application interface language',
        select: 'Interface language',
        placeholder: 'Select language',
      },
      font: {
        title: 'Font Settings',
        description: 'Select the application interface font',
        select: 'Interface Font',
        placeholder: 'Select font',
        preview: 'Preview',
        fonts: {
          AlibabaPuHuiTi: 'Alibaba PuHuiTi',
          AlibabaSansHK: 'Alibaba Sans HK',
          'NotoSansSC-Black': 'Noto Sans SC Black',
          'NotoSansSC-ExtraBold': 'Noto Sans SC ExtraBold',
          'NotoSansSC-Light': 'Noto Sans SC Light',
          'NotoSansSC-Medium': 'Noto Sans SC Medium',
          SourceHanSans: 'Source Han Sans',
          SourceHanSansHC: 'Source Han Sans HC',
          SourceHanSansHK: 'Source Han Sans HK',
          SourceHanSansK: 'Source Han Sans K',
          SourceHanSansSC: 'Source Han Sans SC',
          SourceHanSansTC: 'Source Han Sans TC',
        },
      },
      fontSize: {
        title: 'Font Size Settings',
        description: 'Adjust the text size of the application',
        select: 'Select Font Size',
        placeholder: 'Please select font size',
        preview: 'Preview',
        sizes: {
          'extra-small': 'Extra Small',
          small: 'Small',
          medium: 'Medium',
          large: 'Large',
          'extra-large': 'Extra Large',
        },
      },
      // 缓存设置
      cache: {
        title: 'Cache Settings',
        description: 'Manage application cache data, clear unnecessary caches',
        select: 'Select caches to clear',
        selectAll: 'Select All',
        deselectAll: 'Deselect All',
        clearSelected: 'Clear Selected',
        clearAll: 'Clear All',
        confirmClearSelected: 'Confirm Clear Selected Caches',
        confirmClearSelectedDesc:
          'Are you sure you want to clear the following selected caches? This will reset related settings.',
        confirmClearAll: 'Confirm Clear All Caches',
        confirmClearAllDesc:
          'Are you sure you want to clear all caches? This will reset all application settings and data.',
        warning: 'Warning',
        warningDesc:
          'Clearing all caches will reset the application to its initial state, including all workflows, settings, and preferences.',
        cancel: 'Cancel',
        confirm: 'Confirm',
        clearSuccess: 'Cleared Successfully',
        clearSuccessDesc:
          'Selected caches have been cleared successfully, page will refresh automatically.',
        clearAllSuccess: 'Cleared Successfully',
        clearAllSuccessDesc:
          'All caches have been cleared successfully, page will refresh automatically.',
        clearPartial: 'Partially Cleared',
        clearSuccessCount: 'items cleared successfully',
        clearFailCount: 'items failed to clear',
        types: {
          title: 'Storage Types',
          localStorage: 'Local Storage',
          sessionStorage: 'Session Storage',
          cookies: 'Cookies',
          indexedDB: 'IndexedDB Database',
          applicationCache: 'Application Cache',
          serviceWorkers: 'Service Workers',
        },
        typesDesc: {
          localStorage: 'Stores data that persists long-term, like user preferences',
          sessionStorage: 'Stores temporary session data, cleared when browser closes',
          cookies: 'Small pieces of data stored by websites in your browser',
          indexedDB: 'Client-side database for storing large amounts of structured data',
          applicationCache: 'Caching mechanism for offline access to applications',
          serviceWorkers: 'Background scripts that cache and intercept network requests',
        },
        items: {
          title: 'Cache Items',
          theme: 'Theme Settings',
          themeDesc: 'App theme and color preferences',
          language: 'Language Settings',
          languageDesc: 'Interface language preferences',
          font: 'Font Settings',
          fontDesc: 'App font preferences',
          fontSize: 'Font Size Settings',
          fontSizeDesc: 'App font size preferences',
          workflow: 'Workflow',
          workflowDesc: 'Workflow folders and basic information',
          flows: 'Flow Nodes',
          flowsDesc: 'Flowchart nodes and edges data',
          navbar: 'Navigation Bar',
          navbarDesc: 'Workflow navigation tabs',
          tools: 'Tool Settings',
          toolsDesc: 'Tool status and configuration',
        },
      },
      fileEncrypt: {
        title: 'File Encryption',
        description: 'Upload JSON files and encrypt them for export as model or plugin files',
        uploadTitle: 'Click or drag to upload JSON files',
        uploadDescription: 'Supports .json format',
        uploadedFiles: 'Uploaded Files',
        uploadSuccess: 'File uploaded successfully',
        uploadFailed: 'File upload failed',
        invalidJson: 'Invalid JSON file',
        fileRemoved: 'File removed',
        exportTitle: 'Select Export Type',
        exportDescription: 'Please select the type of file to export',
        modelFile: 'Model File',
        modelFileDesc: 'Export as .model format',
        pluginFile: 'Plugin File',
        pluginFileDesc: 'Export as .plugin format',
        cancel: 'Cancel',
        confirm: 'Confirm',
        saveFileTitle: 'Save Encrypted File',
        exportSuccess: 'Export successful',
        exportFailed: 'Export failed',
      },
    },
    //基本设置
    middlePlatformSetting: {
      title: 'middlePlatformSetting',
      address: {
        title: 'Middle Platform Service Address',
        description:
          'Set the address to connect to the middle platform service. Restart required after modification.',
        input: 'Service Address',
        placeholder: 'Enter service address, e.g.: 127.0.0.1:9999',
        save: 'Save',
        saveSuccess: 'Saved Successfully',
        saveFailed: 'Save Failed',
        invalidUrl: 'Invalid Address',
        pleaseEnterValidUrl: 'Please enter a valid server address and port',
        unknownError: 'Unknown error occurred',
        currentStatus: 'Current Connection Status',
        connected: 'Connected',
        disconnected: 'Disconnected',
        currentAddress: 'Current Service Address',
        confirmTitle: 'Confirm Address Change',
        confirmDescription: 'Are you sure you want to change the service address to:',
        restartWarning: 'The application will restart automatically to apply the new settings',
        restartNow: 'Application will restart in 2 seconds',
        cancel: 'Cancel',
        confirm: 'Confirm',
      },
      server: {
        version: 'Server Version',
      },
    },
    //流程图设置
    flowSetting: {
      title: 'Flow Setting',
      viewport: {
        title: 'View Settings',
        description: 'Configure view and zoom options for the flowchart',
        defaultViewport: 'Default Viewport',
        xCoordinate: 'X Coordinate',
        yCoordinate: 'Y Coordinate',
        zoomRatio: 'Zoom Ratio',
        zoomRange: 'Zoom Range',
        minZoom: 'Min Zoom',
        maxZoom: 'Max Zoom',
      },
      edge: {
        title: 'Edge Settings',
        description: 'Configure edge styles and behaviors',
        type: 'Edge Type',
        typePlaceholder: 'Select edge type',
        types: {
          default: 'Default',
          straight: 'Straight',
          step: 'Step',
          smoothstep: 'Smooth Step',
          bezier: 'Bezier',
        },
        animation: 'Enable Edge Animation',
        arrow: 'Show Edge Arrow',
        color: 'Edge Color',
        width: 'Edge Width',
      },
      alignment: {
        title: 'Alignment Settings',
        description: 'Configure node alignment and grid options',
        snapToLines: 'Show Alignment Lines',
        snapToGrid: 'Enable Grid Snap',
        gridSpacingX: 'X Grid Spacing',
        gridSpacingY: 'Y Grid Spacing',
      },
      interface: {
        title: 'Interface Elements',
        description: 'Configure flowchart interface elements',
        fitView: 'Auto Fit View',
        showMiniMap: 'Show Mini Map',
        showLeftControls: 'Show Left Controls',
        showRightControls: 'Show Right Controls',
      },
      miniMap: {
        title: 'Mini Map Style',
        description: 'Configure mini map display style',
        backgroundColor: 'Background Color',
        nodeStrokeColor: 'Node Stroke Color',
        nodeStrokeWidth: 'Node Stroke Width',
      },
      background: {
        title: 'Background Style',
        description: 'Configure flowchart background style',
        patternColor: 'Pattern Color',
        gap: 'Gap',
        size: 'Size',
        variant: 'Pattern Type',
        variantPlaceholder: 'Select pattern type',
        variants: {
          dots: 'Dots',
          lines: 'Lines',
          cross: 'Cross',
        },
      },
    },
    //关于
    about: {
      title: 'About',
    },
  },
}
