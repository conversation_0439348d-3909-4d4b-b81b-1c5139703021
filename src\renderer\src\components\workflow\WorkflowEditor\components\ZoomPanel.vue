<template>
  <Panel v-if="showRightControls" position="bottom-left" class="z-10">
    <div
      class="flex items-center gap-3 bg-background/80 backdrop-blur-sm p-2 rounded-lg shadow-lg dark:bg-background/60 dark:shadow-none border border-border/30"
    >
      <!-- 重置缩放 -->
      <button
        class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80"
        title="重置缩放"
        @click="fitView"
      >
        <Icon icon="flowbite:expand-outline" :width="20" :height="20" />
      </button>

      <div class="flex gap-1">
        <!-- 放大 -->
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80"
          title="放大"
          @click="zoomIn"
        >
          <Icon icon="flowbite:plus-outline" :width="20" :height="20" />
        </button>
        <!-- 缩小 -->
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80"
          title="缩小"
          @click="zoomOut"
        >
          <Icon icon="flowbite:minus-outline" :width="20" :height="20" />
        </button>
      </div>
    </div>
  </Panel>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { Panel } from '@vue-flow/core'

interface Props {
  showRightControls: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'fit-view': []
  'zoom-in': []
  'zoom-out': []
}>()

const fitView = () => {
  emit('fit-view')
}

const zoomIn = () => {
  emit('zoom-in')
}

const zoomOut = () => {
  emit('zoom-out')
}
</script>
