<!-- components/ui/ConfirmDialog.vue -->
<template>
  <AlertDialog :open="visible" @update:open="(v) => emit('update:visible', v)">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>{{ title }}</AlertDialogTitle>
        <AlertDialogDescription>{{ description }}</AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel :disabled="loading" @click="emit('cancelled')">
          {{ cancelText }}
        </AlertDialogCancel>
        <AlertDialogAction
          :disabled="loading"
          class="bg-red-500 hover:bg-red-600"
          @click="handleConfirm"
        >
          <span v-if="loading">处理中...</span>
          <span v-else>{{ confirmText }}</span>
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@renderer/components/ui/alert-dialog'

const props = defineProps<{
  visible: boolean
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  onConfirm?: () => Promise<void> | void
}>()

const emit = defineEmits<{
  (e: 'update:visible', v: boolean): void
  (e: 'confirmed'): void
  (e: 'cancelled'): void
}>()

const loading = ref(false)

const handleConfirm = async () => {
  if (loading.value) return
  loading.value = true
  try {
    await props.onConfirm?.()
    emit('confirmed')
    emit('update:visible', false)
  } finally {
    loading.value = false
  }
}
</script>
