import { computed, ref, type Ref, shallowRef, watch } from 'vue'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  allData: DatasetItem[]
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

export function useDatasetSplit(datasetData: Ref<DatasetData>) {
  // 图表弹框相关状态
  const showChartDialog = ref(false)
  const selectedDataset = ref<DatasetItem | undefined>(undefined)

  // 当前选中的tabs（支持多选）
  const selectedTabs = ref(['allData'])

  // tabs配置
  const tabs = [
    { key: 'allData', label: '全部' },
    { key: 'trainData', label: '训练集' },
    { key: 'testData', label: '测试集' },
    { key: 'valData', label: '验证集' },
    { key: 'supportData', label: '支持集' },
  ]

  // 计算属性：检查是否有任何数据
  const hasAnyData = computed(() => {
    return (
      datasetData.value.trainData.length > 0 ||
      datasetData.value.testData.length > 0 ||
      datasetData.value.valData.length > 0 ||
      datasetData.value.supportData.length > 0
    )
  })

  // 使用 shallowRef 缓存合并后的数据，减少深度响应式开销
  const allDataCache = shallowRef<DatasetItem[]>([])

  // 优化缓存计算结果 - 使用 Map 存储选中的 ID Set
  const selectedItemsMap = new Map<string, Set<string>>()
  const selectedCountCache = new Map<string, number>()
  const allSelectedCache = new Map<string, boolean>()
  const indeterminateCache = new Map<string, boolean>()

  // 初始化选中项 Set
  const initializeSelectedItems = (tabKey: string, items: DatasetItem[]) => {
    const selectedSet = new Set<string>()
    items.forEach((item) => {
      if (item.selected) {
        selectedSet.add(item.id)
      }
    })
    selectedItemsMap.set(tabKey, selectedSet)
    selectedCountCache.set(tabKey, selectedSet.size)
  }

  // 手动更新缓存，避免频繁重新计算
  const updateAllDataCache = () => {
    allDataCache.value = [
      ...datasetData.value.trainData,
      ...datasetData.value.testData,
      ...datasetData.value.valData,
      ...datasetData.value.supportData,
    ]

    // 重新初始化所有选中项
    const dataKeys = ['trainData', 'testData', 'valData', 'supportData'] as const
    dataKeys.forEach((key) => {
      initializeSelectedItems(key, datasetData.value[key])
    })
    initializeSelectedItems('allData', allDataCache.value)

    // 清空其他缓存
    allSelectedCache.clear()
    indeterminateCache.clear()
  }

  // 监听数据变化，更新缓存
  watch(
    () => [
      datasetData.value.trainData.length,
      datasetData.value.testData.length,
      datasetData.value.valData.length,
      datasetData.value.supportData.length,
    ],
    updateAllDataCache,
    { immediate: true },
  )

  // 获取指定tab的数据项
  const getTabItems = (tabKey: string): DatasetItem[] => {
    if (tabKey === 'allData') {
      return allDataCache.value
    }
    return datasetData.value[tabKey as keyof DatasetData] || []
  }

  // 获取指定tab的总数量
  const getTotalCount = (tabKey: string): number => {
    return getTabItems(tabKey).length
  }

  // 获取指定tab的选中数量 直接从 Set 获取
  const getSelectedCount = (tabKey: string): number => {
    if (selectedCountCache.has(tabKey)) {
      return selectedCountCache.get(tabKey)!
    }

    // 如果缓存中没有，从 selectedItemsMap 获取或重新计算
    if (selectedItemsMap.has(tabKey)) {
      const count = selectedItemsMap.get(tabKey)!.size
      selectedCountCache.set(tabKey, count)
      return count
    }

    // 最后的备选方案：重新计算并缓存
    const items = getTabItems(tabKey)
    initializeSelectedItems(tabKey, items)
    return selectedCountCache.get(tabKey)!
  }

  // 获取指定tab的标签
  const getTabLabel = (tabKey: string): string => {
    const tab = tabs.find((t) => t.key === tabKey)
    return tab?.label || ''
  }

  // 获取选中tabs的显示标签
  const getSelectedTabsLabel = (): string => {
    if (selectedTabs.value.length === 0) {
      return '选择数据集'
    } else if (selectedTabs.value.length === 1) {
      return getTabLabel(selectedTabs.value[0])
    } else {
      return `已选择 ${selectedTabs.value.length} 个数据集`
    }
  }

  // 处理tab选择（多选）
  const handleTabSelection = (tabKey: string, checked: boolean) => {
    if (checked) {
      if (!selectedTabs.value.includes(tabKey)) {
        selectedTabs.value.push(tabKey)
      }
    } else {
      const index = selectedTabs.value.indexOf(tabKey)
      if (index > -1) {
        selectedTabs.value.splice(index, 1)
      }
    }
  }

  // 检查是否全选
  const isAllSelected = (tabKey: string): boolean => {
    if (allSelectedCache.has(tabKey)) {
      return allSelectedCache.get(tabKey)!
    }

    const items = getTabItems(tabKey)
    const allSel = items.length > 0 && items.every((item) => item.selected)
    allSelectedCache.set(tabKey, allSel)
    return allSel
  }

  // 检查是否半选状态
  const isIndeterminate = (tabKey: string): boolean => {
    if (indeterminateCache.has(tabKey)) {
      return indeterminateCache.get(tabKey)!
    }

    const items = getTabItems(tabKey)
    const selectedCount = getSelectedCount(tabKey)
    const indet = selectedCount > 0 && selectedCount < items.length
    indeterminateCache.set(tabKey, indet)
    return indet
  }

  // 优化后的单个项目选中状态更新
  const handleUpdateSelectedItem = (tabKey: string, itemId: string, selected: boolean) => {
    // 更新 selectedItemsMap
    if (!selectedItemsMap.has(tabKey)) {
      selectedItemsMap.set(tabKey, new Set())
    }

    const selectedSet = selectedItemsMap.get(tabKey)!

    if (selected) {
      selectedSet.add(itemId)
    } else {
      selectedSet.delete(itemId)
    }

    // 直接更新对应的 item
    const items = getTabItems(tabKey)
    const item = items.find((item) => item.id === itemId)
    if (item) {
      item.selected = selected
    }

    // 更新缓存
    selectedCountCache.set(tabKey, selectedSet.size)
    allSelectedCache.delete(tabKey)
    indeterminateCache.delete(tabKey)
  }

  // 兼容原有的批量更新方法（用于全选等场景）
  const handleUpdateSelectedItems = (tabKey: string, selectedItems: string[]) => {
    const selectedSet = new Set(selectedItems)
    selectedItemsMap.set(tabKey, selectedSet)

    const items = getTabItems(tabKey)
    items.forEach((item) => {
      item.selected = selectedSet.has(item.id)
    })

    // 更新缓存
    selectedCountCache.set(tabKey, selectedSet.size)
    allSelectedCache.delete(tabKey)
    indeterminateCache.delete(tabKey)
  }

  // 优化后的全选/取消全选处理
  const handleSelectAll = (tabKey: string, selectAll: boolean) => {
    if (tabKey === 'allData') {
      // 优化：批量更新所有数据集
      const dataKeys: (keyof DatasetData)[] = ['trainData', 'testData', 'valData', 'supportData']
      dataKeys.forEach((key) => {
        const items = datasetData.value[key]
        const selectedSet = selectedItemsMap.get(key) || new Set<string>()

        items.forEach((item) => {
          item.selected = selectAll
          if (selectAll) {
            selectedSet.add(item.id)
          } else {
            selectedSet.delete(item.id)
          }
        })

        // 更新缓存
        selectedItemsMap.set(key, selectedSet)
        selectedCountCache.set(key, selectedSet.size)
        allSelectedCache.delete(key)
        indeterminateCache.delete(key)
      })

      // 更新 allData 的缓存
      const allSelectedSet = new Set<string>()
      if (selectAll) {
        allDataCache.value.forEach((item) => allSelectedSet.add(item.id))
      }
      selectedItemsMap.set('allData', allSelectedSet)
      selectedCountCache.set('allData', allSelectedSet.size)
    } else {
      const items = getTabItems(tabKey)
      const selectedSet = new Set<string>()

      items.forEach((item) => {
        item.selected = selectAll
        if (selectAll) {
          selectedSet.add(item.id)
        }
      })

      // 更新缓存
      selectedItemsMap.set(tabKey, selectedSet)
      selectedCountCache.set(tabKey, selectedSet.size)
      allSelectedCache.delete(tabKey)
      indeterminateCache.delete(tabKey)
    }
  }

  // 处理图表点击
  const handleChartClick = (item: DatasetItem) => {
    console.log('点击图表按钮:', item)
    selectedDataset.value = item
    showChartDialog.value = true
  }

  return {
    // 状态
    showChartDialog,
    selectedDataset,
    selectedTabs,
    tabs,

    // 计算属性
    hasAnyData,

    // 方法
    getTabItems,
    getTotalCount,
    getSelectedCount,
    getTabLabel,
    getSelectedTabsLabel,
    handleTabSelection,
    isAllSelected,
    isIndeterminate,
    handleUpdateSelectedItem,
    handleUpdateSelectedItems,
    handleSelectAll,
    handleChartClick,
  }
}
