//材料设计
import conductivity from '@renderer/assets/svg/materialDesign/conductivity.svg'
import diffusion from '@renderer/assets/svg/materialDesign/diffusion.svg'
import electrode from '@renderer/assets/svg/materialDesign/electrode.svg'
import electrolyte from '@renderer/assets/svg/materialDesign/electrolyte.svg'
import energy from '@renderer/assets/svg/materialDesign/energy.svg'
import materialCalculation from '@renderer/assets/svg/materialDesign/materialCalculation.svg'
import md from '@renderer/assets/svg/materialDesign/md.svg'
import phaseDiagram from '@renderer/assets/svg/materialDesign/phaseDiagram.svg'
import solvatedStructure from '@renderer/assets/svg/materialDesign/solvatedStructure.svg'
import spring from '@renderer/assets/svg/materialDesign/spring.svg'
import structure from '@renderer/assets/svg/materialDesign/structure.svg'
import viscosity from '@renderer/assets/svg/materialDesign/viscosity.svg'

import batteryCalibration from '@renderer/assets/svg/batterySimulation/batteryCalibration.svg'
import batteryDataInput from '@renderer/assets/svg/batterySimulation/batteryDataInput.svg'
import batteryModel from '@renderer/assets/svg/batterySimulation/batteryModel.svg'
import lifePrediction from '@renderer/assets/svg/batterySimulation/lifePrediction.svg'
// 电池模拟
import anode from '@renderer/assets/svg/batterySimulation/anode.svg'
import batterySimulation from '@renderer/assets/svg/batterySimulation/batterySimulation.svg'
import BElectrolyte from '@renderer/assets/svg/batterySimulation/BElectrolyte.svg'
import cathode from '@renderer/assets/svg/batterySimulation/cathode.svg'
import dataBase from '@renderer/assets/svg/batterySimulation/dataBase.svg'
import featureExtraction from '@renderer/assets/svg/batterySimulation/featureExtraction.svg'
import lifespanPrediction from '@renderer/assets/svg/batterySimulation/lifespanPrediction.svg'
import separator from '@renderer/assets/svg/batterySimulation/separator.svg'
import simulationCalculation from '@renderer/assets/svg/batterySimulation/simulationCalculation.svg'

//工艺优化
import anodeFormulation from '@renderer/assets/svg/processOptimization/anodeFormulation.svg'
import cathodeFormulation from '@renderer/assets/svg/processOptimization/cathodeFormulation.svg'
import coordinateData from '@renderer/assets/svg/processOptimization/coordinateData.svg'
import densityCalculation from '@renderer/assets/svg/processOptimization/densityCalculation.svg'
import dryingProcess from '@renderer/assets/svg/processOptimization/dryingProcess.svg'
import homogenizationProcess from '@renderer/assets/svg/processOptimization/homogenizationProcess.svg'
import lineChart from '@renderer/assets/svg/processOptimization/lineChart.svg'
import numeric from '@renderer/assets/svg/processOptimization/numeric.svg'
import porosity from '@renderer/assets/svg/processOptimization/porosity.svg'
import processOptimization from '@renderer/assets/svg/processOptimization/processOptimization.svg'
import rollingProcess from '@renderer/assets/svg/processOptimization/rollingProcess.svg'
import slurryViscosity from '@renderer/assets/svg/processOptimization/slurryViscosity.svg'
import threeDStructure from '@renderer/assets/svg/processOptimization/threeDStructure.svg'
// 学习组件
import InferentialPrediction from '@renderer/assets/svg/study/InferentialPrediction.svg'
import StudyInputData from '@renderer/assets/svg/study/StudyInputData.svg'
import StudyModelConfig from '@renderer/assets/svg/study/StudyModelConfig.svg'
import ModelTrain from '@renderer/assets/svg/study/modelTrain.svg'

import condition from '@renderer/assets/svg/condition.svg'
import mysql from '@renderer/assets/svg/mysql.svg'
import output from '@renderer/assets/svg/output.svg'
import sequence from '@renderer/assets/svg/sequence.svg'
import splice from '@renderer/assets/svg/splice.svg'

import enUs from '@renderer/assets/svg/iconfont/en-us.svg'
import jaJp from '@renderer/assets/svg/iconfont/jp.svg'
import koKr from '@renderer/assets/svg/iconfont/ko-kr.svg'
import zhCn from '@renderer/assets/svg/iconfont/zh-cn.svg'

export default {
  lineChart,
  coordinateData,
  threeDStructure,
  numeric,
  porosity,
  slurryViscosity,
  densityCalculation,
  rollingProcess,
  dryingProcess,
  homogenizationProcess,
  anodeFormulation,
  cathodeFormulation,
  processOptimization,
  materialCalculation,
  batterySimulation,
  simulationCalculation,
  lifespanPrediction,
  dataBase,
  structure,
  featureExtraction,
  electrolyte,
  electrode,
  energy,
  spring,
  diffusion,
  conductivity,
  viscosity,
  solvatedStructure,
  splice,
  phaseDiagram,
  md,
  anode,
  cathode,
  separator,
  BElectrolyte,
  batteryModel,
  lifePrediction,
  output,
  condition,
  sequence,
  mysql,
  zhCn,
  enUs,
  jaJp,
  koKr,
  batteryDataInput,
  batteryCalibration,
  StudyInputData,
  StudyModelConfig,
  InferentialPrediction,
  ModelTrain,
}
