<template>
  <div class="bg-white border-r border-gray-200 w-64 flex-shrink-0 h-screen overflow-y-auto">
    <div class="p-4">
      <div class="flex items-center space-x-2 mb-6">
        <i data-lucide="battery-charging" class="text-green-500 w-6 h-6"></i>
        <span class="font-bold text-xl text-gray-800">电池AI助手</span>
      </div>

      <nav class="space-y-1">
        <router-link to="/ai" class="sidebar-link flex gap-2" active-class="sidebar-link active">
          <i data-lucide="home" class="w-5 h-5"></i>
          <span>首页</span>
        </router-link>
        <router-link
          to="/ai/chat"
          class="sidebar-link flex gap-2"
          active-class="sidebar-link active"
        >
          <i data-lucide="message-circle" class="w-5 h-5"></i>
          <span>AI聊天</span>
        </router-link>
        <router-link
          to="/ai/knowledge"
          class="sidebar-link flex gap-2"
          active-class="sidebar-link active"
        >
          <i data-lucide="book-open" class="w-5 h-5"></i>
          <span>知识库</span>
        </router-link>
        <router-link
          to="/ai/agent"
          class="sidebar-link flex gap-2"
          active-class="sidebar-link active"
        >
          <i data-lucide="cpu" class="w-5 h-5"></i>
          <span>智能体</span>
        </router-link>
        <router-link
          to="/ai/setting"
          class="sidebar-link flex gap-2"
          active-class="sidebar-link active"
        >
          <i data-lucide="settings" class="w-5 h-5"></i>
          <span>设置</span>
        </router-link>
      </nav>
    </div>

    <div class="border-t border-gray-200 p-4">
      <div class="mb-4">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">最近对话</h3>
        <div class="space-y-1">
          <a
            href="#"
            class="flex items-center px-2 py-1.5 text-sm text-gray-700 rounded-md hover:bg-gray-100"
          >
            <i data-lucide="message-square" class="w-4 h-4 mr-2 text-gray-500"></i>
            <span>电池寿命优化</span>
          </a>
          <a
            href="#"
            class="flex items-center px-2 py-1.5 text-sm text-gray-700 rounded-md hover:bg-gray-100"
          >
            <i data-lucide="message-square" class="w-4 h-4 mr-2 text-gray-500"></i>
            <span>固态电池材料</span>
          </a>
        </div>
      </div>

      <div>
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
          我的智能体
        </h3>
        <div class="space-y-1">
          <a
            href="#"
            class="flex items-center px-2 py-1.5 text-sm text-gray-700 rounded-md hover:bg-gray-100"
          >
            <i data-lucide="activity" class="w-4 h-4 mr-2 text-green-500"></i>
            <span>电池性能分析</span>
          </a>
          <a
            href="#"
            class="flex items-center px-2 py-1.5 text-sm text-gray-700 rounded-md hover:bg-gray-100"
          >
            <i data-lucide="zap" class="w-4 h-4 mr-2 text-blue-500"></i>
            <span>充电策略优化</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const recentConversations = ref([
  { title: '电池寿命优化', lastActive: '今天' },
  { title: '固态电池材料', lastActive: '昨天' },
])

const myAgents = ref([
  { title: '电池性能分析', icon: 'activity' },
  { title: '充电策略优化', icon: 'zap' },
])
</script>

<style scoped>
.sidebar-link {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  color: #4b5563;
}
.sidebar-link:hover {
  background-color: #f9fafb;
  color: #10b981;
}
.sidebar-link.active {
  background-color: #ecfdf5;
  color: #10b981;
}
</style>
