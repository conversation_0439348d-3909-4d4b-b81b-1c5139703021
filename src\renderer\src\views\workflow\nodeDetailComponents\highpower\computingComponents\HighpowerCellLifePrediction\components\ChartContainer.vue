<template>
  <div
    v-chart-resize-multi="chartInstances"
    class="flex flex-col w-full"
    :class="{ hidden: !chartsReady }"
  >
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
      <div
        v-for="(series, index) in chartSeries"
        :key="index"
        class="chart-container show w-full aspect-[4/3] relative"
      >
        <div
          :ref="
            (el) => {
              if (el) chartRefs[index] = el
            }
          "
          class="w-full h-full absolute inset-0"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  chartSeries: {
    type: Array,
    required: true,
  },
  resultData: {
    type: Object,
    default: () => ({}),
  },
  isReady: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:isReady'])

const chartRefs = ref([])
const chartInstances = ref({})
const chartsReady = ref(false)

// 图表配置
const getChartOptions = (seriesName, resultData, index) => {
  const colors = [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
  ]
  let yUnitName = seriesName
  let titleName = seriesName
  const matchChinese = seriesName.match(/(.*?)\((.*?)\)/)
  const matchEnglish = seriesName.match(/(.*?)\[(.*?)\]/)
  if (matchChinese) {
    titleName = matchChinese[1].trim()
    yUnitName = matchChinese[2]
  } else if (matchEnglish) {
    titleName = matchEnglish[1].trim()
    yUnitName = matchEnglish[2]
  }
  const cycleData = resultData['循环圈数'] || resultData['Cycle number']
  const seriesData = resultData[seriesName]
  const lastIndex = seriesData.length - 1
  const lastValue = lastIndex >= 0 ? seriesData[lastIndex] : null
  return {
    title: {
      text: titleName,
      left: 'center',
      top: '2%',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        return `循环圈数: ${params[0].axisValue}<br>${seriesName}: ${params[0].value.toFixed(4)}`
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '18%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: cycleData,
      name: matchEnglish ? `Cycle number` : `循环圈数`,
      nameLocation: 'middle',
      nameGap: 30,
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        align: 'center',
      },
    },
    yAxis: {
      type: 'value',
      name: matchEnglish ? `unit[${yUnitName}]` : `单位(${yUnitName})`,
      nameLocation: 'end',
      nameGap: 15,
      nameTextStyle: {
        padding: [0, 30, 0, 0],
        align: 'center',
        fontSize: 14,
        color: '#666',
      },
    },
    series: [
      {
        name: seriesName,
        type: 'line',
        smooth: true,
        data: seriesData,
        symbol: 'none',
        itemStyle: { color: colors[index % colors.length] },
        sampling: 'lttb',
        markPoint:
          lastValue !== null
            ? {
                symbol: 'pin',
                symbolSize: 15,
                data: [
                  {
                    coord: [lastIndex, lastValue],
                    name: '最新值',
                    value: lastValue.toFixed(4),
                    itemStyle: {
                      color: colors[index % colors.length],
                    },
                    label: {
                      formatter: `{c}`,
                      position: 'right',
                      distance: 4,
                      fontSize: 12,
                      fontWeight: 'bold',
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      padding: [4, 8],
                      borderRadius: 4,
                    },
                  },
                ],
              }
            : null,
        markLine:
          lastValue !== null
            ? {
                silent: true,
                symbol: ['none', 'none'],
                lineStyle: {
                  type: 'dashed',
                  color: colors[index % colors.length],
                  width: 1,
                },
                data: [
                  [
                    {
                      coord: [lastIndex, 0],
                      symbol: 'none',
                    },
                    {
                      coord: [lastIndex, lastValue],
                      symbol: 'circle',
                      symbolSize: 5,
                      label: {
                        show: true,
                        fontSize: 12,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        padding: [2, 4],
                        borderRadius: 2,
                        distance: 10,
                      },
                    },
                  ],
                ],
              }
            : null,
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomLock: false,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
        preventDefaultMouseMove: true,
      },
      {
        type: 'slider',
        show: false,
        start: 0,
        end: 100,
        height: 12,
        bottom: 1,
      },
    ],
    animation: false,
  }
}

// 初始化图表
const initCharts = () => {
  if (!props.resultData || !props.chartSeries.length) return

  // 清理已有的图表实例
  Object.values(chartInstances.value).forEach((chart) => {
    if (chart) chart.dispose()
  })
  chartInstances.value = {}

  // 使用 setTimeout 确保 DOM 已完全渲染
  setTimeout(() => {
    try {
      props.chartSeries.forEach((seriesName, index) => {
        if (!chartRefs.value[index]) return

        const seriesData = props.resultData[seriesName]
        if (!seriesData || !Array.isArray(seriesData)) return

        // 确保 DOM 元素有尺寸
        const el = chartRefs.value[index]
        if (!el.offsetWidth || !el.offsetHeight) return

        const chart = echarts.init(el)
        chartInstances.value[index] = chart

        // 设置图表配置
        chart.setOption(getChartOptions(seriesName, props.resultData, index))
      })

      // 设置图表已准备好
      chartsReady.value = true
      emit('update:isReady', true)
    } catch (error) {
      console.error('初始化图表失败:', error)
    }
  }, 100)
}

// 更新图表
const updateCharts = () => {
  if (!props.resultData || !props.chartSeries.length) return

  props.chartSeries.forEach((seriesName, index) => {
    const chart = chartInstances.value[index]
    if (chart && props.resultData[seriesName]) {
      try {
        chart.setOption(getChartOptions(seriesName, props.resultData, index))
      } catch (error) {
        console.error(`更新图表 ${seriesName} 失败:`, error)
      }
    }
  })
}

// 监听结果数据变化
watch(
  () => props.resultData,
  () => {
    if (chartsReady.value) {
      updateCharts()
    } else {
      initCharts()
    }
  },
  { deep: true },
)

// 监听图表系列变化
watch(
  () => props.chartSeries,
  () => {
    initCharts()
  },
  { deep: true },
)

// 组件挂载时初始化图表
onMounted(() => {
  if (props.resultData && props.chartSeries.length > 0) {
    initCharts()
  }
})

// 组件卸载时销毁图表实例
onUnmounted(() => {
  Object.values(chartInstances.value).forEach((chart) => {
    if (chart && typeof chart.dispose === 'function') {
      try {
        chart.dispose()
      } catch (e) {
        console.error('销毁图表失败:', e)
      }
    }
  })
  chartInstances.value = {}
})
</script>

<style scoped>
.chart-container.show {
  opacity: 1;
  transform: translateY(0);
}
</style>
