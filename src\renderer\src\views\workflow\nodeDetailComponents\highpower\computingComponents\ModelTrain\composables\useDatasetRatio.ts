import { computed, reactive, watch, type Ref } from 'vue'
import { useDebounceFn } from '@vueuse/core'

// 定义比例配置接口
interface DatasetRatios {
  train: number
  test: number
  val: number
  support: number
}

export function useDatasetRatio(
  totalDataCount: Ref<number>,
  initialRatios: Ref<DatasetRatios | undefined>,
  isOpen: Ref<boolean>,
  emit: ((event: 'confirm', ratios: DatasetRatios) => void) & ((event: 'cancel') => void),
) {
  // 默认比例配置
  const defaultRatios: DatasetRatios = {
    train: 60,
    test: 20,
    val: 10,
    support: 10,
  }

  // 当前比例配置
  const ratios = reactive<DatasetRatios>({
    ...defaultRatios,
    ...(initialRatios.value || {}),
  })

  // 计算总比例
  const totalRatio = computed(() => {
    return ratios.train + ratios.test + ratios.val + ratios.support
  })

  // 计算每个比例对应的数据量
  const dataCounts = computed(() => {
    const total = totalDataCount.value
    return {
      train: Math.floor((total * ratios.train) / 100),
      test: Math.floor((total * ratios.test) / 100),
      val: Math.floor((total * ratios.val) / 100),
      support: Math.floor((total * ratios.support) / 100),
    }
  })

  // 计算剩余数据量
  const remainingCount = computed(() => {
    const total = totalDataCount.value
    const used =
      dataCounts.value.train +
      dataCounts.value.test +
      dataCounts.value.val +
      dataCounts.value.support
    return total - used
  })

  // 验证单个比例值
  const validateRatioValue = (value: number): number => {
    const num = Number(value)
    if (isNaN(num) || num < 0) return 0
    if (num > 100) return 100
    return num
  }

  // 优化的比例变化处理 - 减少防抖时间
  const handleRatioChange = useDebounceFn((key: keyof DatasetRatios, value: number) => {
    ratios[key] = validateRatioValue(value)
  }, 300)

  // 重置为默认值
  const handleReset = () => {
    Object.assign(ratios, defaultRatios)
  }

  // 确认配置
  const handleConfirm = () => {
    if (totalRatio.value === 100) {
      emit('confirm', { ...ratios })
      isOpen.value = false
    }
  }

  // 取消配置
  const handleCancel = () => {
    emit('cancel')
    isOpen.value = false
  }

  // 恢复初始值的函数
  const restoreInitialValues = () => {
    Object.assign(ratios, { ...defaultRatios, ...(initialRatios.value || {}) })
  }

  // 监听对话框打开状态，恢复为初始值
  watch(isOpen, (newValue) => {
    if (newValue) {
      restoreInitialValues()
    }
  })

  // 监听 initialRatios 变化，更新当前比例
  watch(
    () => initialRatios.value,
    (newRatios) => {
      if (newRatios && isOpen.value) {
        Object.assign(ratios, { ...defaultRatios, ...newRatios })
      }
    },
    { immediate: true },
  )

  return {
    // 状态
    ratios,
    defaultRatios,

    // 计算属性
    totalRatio,
    dataCounts,
    remainingCount,

    // 方法
    handleRatioChange,
    handleReset,
    handleConfirm,
    handleCancel,
  }
}
