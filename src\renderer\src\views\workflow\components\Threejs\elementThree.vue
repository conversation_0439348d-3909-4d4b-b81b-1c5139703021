<template>
  <div class="w-full h-full relative">
    <div class="absolute top-[10px] left-[10px]">
      <div v-for="(text, index) in textList" :key="index" class="w-full h-full text-[12px]">
        <div v-for="(line, j) in text.list" :key="j">{{ line }}</div>
      </div>
    </div>
    <div class="absolute bottom-[10px] left-[10px]">
      <div
        v-for="(item, index) in textLegend"
        :key="index"
        class="w-full h-full text-[12px] flex justify-start items-center"
      >
        <div
          class="w-[10px] h-[10px] rounded-[50%]"
          :style="{ backgroundColor: item.cirColor }"
        ></div>
        <div class="ml-[8px]">{{ item.text }}</div>
      </div>
    </div>

    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <Loader2 v-if="loading" class="w-10 h-10 animate-spin text-blue-400" />
    </div>
    <ThreeVue :data="threeData" />
  </div>
</template>
<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next'
import { ref, watch } from 'vue'
import ThreeVue from './three.vue'
import AnalysisTool from './tools/analysis'
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {},
  },
})
const threeData = ref({
  lines: [],
  points: [],
  cylinders: [],
})
const textList: any = ref([])
const textLegend: any = ref([])
const analysis = new AnalysisTool()
watch(
  () => props.data,
  (val) => {
    threeData.value = {
      lines: val.lines,
      points: val.ps,
      cylinders: val.bondlines,
    }
    const list: any = val.text.slice(0, 1)
    list.map((itm: any) => {
      itm.list = itm.text.split('\n')
      itm.color = analysis.arrayToHex(itm.color)
      return itm
    })
    textList.value = list
    const lgList: any = val.text.slice(1, val.text.length)
    const circle: any = val.circle
    lgList.map((itm: any, idx: number) => {
      itm.cirColor = analysis.arrayToHex(circle[idx].color)
      return itm
    })
    textLegend.value = lgList
  },
)
</script>
