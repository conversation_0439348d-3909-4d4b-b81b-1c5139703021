<template>
  <div
    v-if="sidebarConfig.showSidebar"
    class="min-w-[72px] h-full bg-background border-r border-border flex flex-col"
  >
    <div class="h-16 flex items-center justify-center border-b border-border">
      <router-link to="/">
        <img class="w-[50px] h-[50px] mt-3" :src="images.logoTextBlack" />
      </router-link>
    </div>
    <div class="flex-1 flex flex-col justify-between py-4 overflow-y-auto">
      <!-- 主要图标列表 -->
      <div class="flex flex-col gap-5 items-center justify-center cursor-pointer">
        <template v-for="menuItem in mainMenus" :key="menuItem.path">
          <div class="relative w-full flex justify-center" @click="handleNavigate(menuItem.path)">
            <div v-if="menuItem.meta?.showTopLine" class="absolute -top-2 w-8 h-px bg-border"></div>
            <div
              v-tooltip="{
                content: t(menuItem.meta?.tooltip.content),
                placement: menuItem.meta?.tooltip.placement,
                theme: menuItem.meta?.tooltip.theme,
              }"
              class="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-200"
              :class="[
                isRouteActive(menuItem.path)
                  ? 'bg-accent text-accent-foreground'
                  : 'hover:bg-accent hover:text-accent-foreground',
              ]"
            >
              <Icon :icon="menuItem.meta?.icon" :width="24" :height="24" />
            </div>
          </div>
        </template>
      </div>
      <!-- 底部图标列表 -->
      <div class="flex flex-col gap-5 items-center justify-center cursor-pointer">
        <template v-for="menuItem in bottomMenus" :key="menuItem.path">
          <!-- 用户菜单项特殊处理，添加Popover -->
          <div v-if="menuItem.path === '/user'" class="relative w-full flex justify-center">
            <div v-if="menuItem.meta?.showTopLine" class="absolute -top-2 w-8 h-px bg-border"></div>
            <Popover>
              <PopoverTrigger as-child>
                <div
                  v-tooltip="{
                    content: t(menuItem.meta?.tooltip.content),
                    placement: menuItem.meta?.tooltip.placement,
                    theme: menuItem.meta?.tooltip.theme,
                  }"
                  class="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-200"
                  :class="[
                    currentPath === menuItem.path
                      ? 'bg-accent text-accent-foreground'
                      : 'hover:bg-accent hover:text-accent-foreground',
                  ]"
                >
                  <Icon :icon="menuItem.meta?.icon" :width="24" :height="24" />
                </div>
              </PopoverTrigger>
              <PopoverContent class="w-72 p-0" side="right">
                <div class="flex flex-col">
                  <!-- 用户信息 -->
                  <div class="p-4 border-b border-border">
                    <div class="flex items-center gap-3">
                      <div class="h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                        <Icon icon="flowbite:user-circle-outline" :width="32" :height="32" />
                      </div>
                      <div>
                        <h3 class="font-medium">{{ authStore.userInfo?.user_name || '未登录' }}</h3>
                        <p class="text-sm text-muted-foreground">
                          {{ authStore.getRoleName(authStore.userRole) }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <!-- 用户状态 -->
                  <div class="p-4 border-b border-border">
                    <div class="flex items-center justify-between">
                      <span class="text-sm">状态</span>
                      <Badge :variant="getUserStatusVariant(authStore.userInfo?.user_status)">
                        {{ getUserStatusText(authStore.userInfo?.user_status) }}
                      </Badge>
                    </div>
                  </div>
                  <!-- 操作按钮 -->
                  <div class="p-4">
                    <Button variant="outline" class="w-full" @click="handleLogout">
                      <LogOutIcon class="mr-2 h-4 w-4" />
                      退出登录
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div
            v-else
            class="relative w-full flex justify-center"
            @click="handleNavigate(menuItem.path)"
          >
            <div v-if="menuItem.meta?.showTopLine" class="absolute -top-2 w-8 h-px bg-border"></div>
            <div
              v-tooltip="{
                content: t(menuItem.meta?.tooltip.content),
                placement: menuItem.meta?.tooltip.placement,
                theme: menuItem.meta?.tooltip.theme,
              }"
              class="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-200"
              :class="[
                currentPath === menuItem.path
                  ? 'bg-accent text-accent-foreground'
                  : 'hover:bg-accent hover:text-accent-foreground',
              ]"
            >
              <Icon :icon="menuItem.meta?.icon" :width="24" :height="24" />
            </div>
          </div>
        </template>
      </div>
    </div>
    <LoggerDialog v-model:is-open="showLoggerDialog" />
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { images } from '@renderer/config/constants'
import { useLanguage } from '@renderer/config/hooks'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { routes } from '@renderer/router'
import { useAuthStore, useNavbarStore, UserStatus } from '@renderer/store'
import LoggerDialog from '@views/layout/logger/index.vue'
import { LogOutIcon } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const navbarStore = useNavbarStore()
const authStore = useAuthStore()
const { t } = useLanguage()
const router = useRouter()
const route = useRoute()
const { sidebarConfig } = useAppConfig()

const showLoggerDialog = ref(false)
const currentPath = computed(() => route.path)
const isRouteActive = (routePath: string) => {
  return currentPath.value.startsWith(routePath)
}
// 路由名称到配置键的映射
const routeToConfigMap: Record<string, keyof typeof sidebarConfig.value.menuItems> = {
  '/workflow': 'workflow',
  '/task': 'task',
  '/server': 'server',
  '/logger': 'logger',
  '/tools': 'tools',
  '/setting': 'setting',
  '/ai': 'ai',
  '/user': 'user',
  '/about': 'about',
}

// 检查菜单项是否应该显示
const isMenuItemEnabled = (routePath: string): boolean => {
  const configKey = routeToConfigMap[routePath]
  if (!configKey) return true // 如果没有配置映射，默认显示
  return sidebarConfig.value.menuItems[configKey]
}

// 从路由配置中获取菜单数据
const mainMenus = computed(() => {
  // 确保routes[1]是包含children的主布局路由
  const layoutRoute = routes.find((r) => r.path === '/' && r.children)
  if (!layoutRoute || !layoutRoute.children) {
    console.error('未找到主布局路由或其子路由')
    return []
  }
  return (
    layoutRoute.children.filter(
      (routeItem) =>
        routeItem.meta?.showInMenu &&
        !routeItem.meta?.isBottomMenu &&
        isMenuItemEnabled(routeItem.path),
    ) || []
  )
})

const bottomMenus = computed(() => {
  const layoutRoute = routes.find((r) => r.path === '/' && r.children)
  if (!layoutRoute || !layoutRoute.children) {
    return []
  }
  return (
    layoutRoute.children.filter(
      (routeItem) =>
        routeItem.meta?.showInMenu &&
        routeItem.meta?.isBottomMenu &&
        isMenuItemEnabled(routeItem.path),
    ) || []
  )
})

const handleNavigate = (path: string) => {
  if (path === '/about') {
    navbarStore.setActiveTag('home')
    router.push({
      path: '/setting',
      query: { component: 'AboutSetting' },
    })
  } else if (path === '/logger') {
    showLoggerDialog.value = true
  } else {
    navbarStore.setActiveTag('home')
    router.push(path)
  }
}

// 处理登出
const handleLogout = () => {
  authStore.logout()
  router.push('/auth')
}
// 获取用户状态文本
const getUserStatusText = (status: UserStatus | undefined) => {
  if (status === undefined) return '未知'

  switch (status) {
    case UserStatus.Activated:
      return '已激活'
    case UserStatus.Deactivated:
      return '已停用'
    case UserStatus.Deleted:
      return '已删除'
    default:
      return '未知'
  }
}
// 获取用户状态对应的Badge样式
const getUserStatusVariant = (status: UserStatus | undefined) => {
  if (status === undefined) return 'outline'

  switch (status) {
    case UserStatus.Activated:
      return 'success'
    case UserStatus.Deactivated:
      return 'warning'
    case UserStatus.Deleted:
      return 'destructive'
    default:
      return 'outline'
  }
}
onMounted(() => {
  // console.log('路由选项:', router.options.routes)
  // console.log('导入的routes:', routes)
  // console.log('主菜单项:', mainMenus.value)
  // console.log('底部菜单项:', bottomMenus.value)
})
</script>

<style scoped lang="scss"></style>
