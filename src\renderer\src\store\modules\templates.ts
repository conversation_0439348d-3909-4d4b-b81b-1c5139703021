import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { nanoid } from 'nanoid'

// 模板接口定义
export interface Template {
  id: string
  name: string
  description: string
  status: 'active' | 'running' | 'idle' | 'error'
  nodes: any[]
  edges: any[]
  createdAt: Date
  updatedAt?: Date
}

export const useTemplatesStore = defineStore(
  'templates',
  () => {
    // 模板列表
    const templates = ref<Template[]>([])

    // 获取所有模板
    const allTemplates = computed(() => templates.value)

    // 获取活跃模板数量
    const activeTemplatesCount = computed(
      () => templates.value.filter((t) => t.status === 'active' || t.status === 'running').length,
    )

    // 创建新模板
    const createTemplate = (
      templateData: Omit<Template, 'id' | 'status' | 'createdAt' | 'updatedAt'>,
    ) => {
      const newTemplate: Template = {
        id: nanoid(),
        ...templateData,
        status: 'idle',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      templates.value.push(newTemplate)
      return newTemplate
    }

    // 更新模板
    const updateTemplate = (id: string, data: Partial<Omit<Template, 'id' | 'createdAt'>>) => {
      const index = templates.value.findIndex((t) => t.id === id)
      if (index !== -1) {
        templates.value[index] = {
          ...templates.value[index],
          ...data,
          updatedAt: new Date(),
        }
        return templates.value[index]
      }
      return null
    }

    // 删除模板
    const deleteTemplate = (id: string) => {
      const index = templates.value.findIndex((t) => t.id === id)
      if (index !== -1) {
        templates.value.splice(index, 1)
        return true
      }
      return false
    }

    // 更新模板状态
    const updateTemplateStatus = (id: string, status: Template['status']) => {
      const index = templates.value.findIndex((t) => t.id === id)
      if (index !== -1) {
        templates.value[index].status = status
        templates.value[index].updatedAt = new Date()
        return true
      }
      return false
    }

    // 运行模板
    const runTemplate = (id: string) => {
      const template = templates.value.find((t) => t.id === id)
      if (template) {
        template.status = 'running'
        template.updatedAt = new Date()

        // 这里可以添加实际的运行逻辑，现在只是模拟
        setTimeout(() => {
          template.status = Math.random() > 0.2 ? 'active' : 'error'
          template.updatedAt = new Date()
        }, 2000)

        return true
      }
      return false
    }

    return {
      templates,
      allTemplates,
      activeTemplatesCount,
      createTemplate,
      updateTemplate,
      deleteTemplate,
      updateTemplateStatus,
      runTemplate,
    }
  },
  {
    persist: true, // 持久化存储
  },
)
