import * as grpc from '@grpc/grpc-js'
import * as protoLoader from '@grpc/proto-loader'
import { EventEmitter } from 'events'
import path from 'path'
import logger from '../utils/logger'

export class LinkerClient extends EventEmitter {
  private client: any
  private userId: string = ''
  private token: string = ''
  private serverName: string = 'workflowService'
  private url: string = ''
  private version: string = '1.0.0'
  private services: any = {}
  private isInitialized: boolean = false
  private port: string = '50051'

  private heartbeatInterval: NodeJS.Timeout | null = null
  private heartbeatIntervalTime: number = 15000 // 15秒发送一次心跳
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 5000 // 5秒重连一次
  private connectionStatus: boolean = false

  constructor() {
    super()
  }
  // 设置端口
  setPort(port: string) {
    this.port = port
  }
  // 设置认证信息
  setAuthInfo(userId: string, token: string) {
    this.userId = userId
    this.token = token

    // 如果之前没有初始化过客户端，现在初始化
    if (!this.isInitialized && this.userId && this.token) {
      this.initClient()
      this.isInitialized = true
      this.startHeartbeat() // 启动心跳
    }
  }

  // 设置服务实例
  setServices(services: any) {
    this.services = services
  }

  // 设置服务器地址
  setServerUrl(url: string) {
    this.url = url

    // 如果已经初始化过客户端，需要重新初始化
    if (this.isInitialized) {
      this.initClient()
    }
  }

  // 初始化gRPC客户端
  private initClient() {
    if (!this.url) {
      logger.error('初始化客户端失败: URL为空')
      throw new Error('初始化客户端失败: URL为空')
    }
    const PROTO_PATH = path.resolve(__dirname, './protos/linker.proto')
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
      includeDirs: [path.resolve(__dirname, './protos')],
    })

    const linkerProto = grpc.loadPackageDefinition(packageDefinition).linker
    this.client = new linkerProto.LinkerService(this.url, grpc.credentials.createInsecure())
  }

  // 启动心跳
  startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat()
    }, this.heartbeatIntervalTime)

    logger.info(`心跳机制已启动，间隔: ${this.heartbeatIntervalTime}ms`)
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
      logger.info('心跳机制已停止')
    }
  }

  // 重启心跳
  restartHeartbeat() {
    this.stopHeartbeat()
    this.startHeartbeat()
  }

  // 发送心跳
  private async sendHeartbeat() {
    if (!this.isInitialized || !this.client) {
      logger.warn('客户端未初始化，无法发送心跳')
      return
    }

    try {
      logger.debug(`发送心跳到 ${this.url}`)

      // 使用 ping 方法发送心跳
      this.client.ping({}, (err: any, response: any) => {
        if (err) {
          logger.warn(`心跳失败: ${err.message}`)
          this.connectionStatus = false
          this.emit('heartbeat-failed', err)
          this.handleConnectionFailure()
          return
        }

        if (response && response.success) {
          if (!this.connectionStatus) {
            logger.info('心跳成功，连接已恢复')
          } else {
            logger.debug('心跳成功')
          }
          this.connectionStatus = true
          this.emit('heartbeat-success')
          this.reconnectAttempts = 0 // 重置重连次数
        } else {
          logger.warn('心跳响应异常')
          this.connectionStatus = false
          this.emit('heartbeat-failed', new Error('心跳响应异常'))
          this.handleConnectionFailure()
        }
      })
    } catch (error) {
      logger.error(`心跳发送异常: ${error}`)
      this.connectionStatus = false
      this.emit('heartbeat-error', error)
      this.handleConnectionFailure()
    }
  }

  // 处理连接失败
  private handleConnectionFailure() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error(`已达到最大重连次数(${this.maxReconnectAttempts})，停止重连`)
      this.emit('max-reconnect-attempts-reached')
      return
    }

    this.reconnectAttempts++
    logger.info(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)

    setTimeout(() => {
      logger.info(`重新初始化客户端连接到 ${this.url}`)
      try {
        this.initClient()
        // 立即发送一次心跳检查连接
        this.sendHeartbeat()
      } catch (error) {
        logger.error(`重连失败: ${error}`)
      }
    }, this.reconnectDelay)
  }

  // 获取连接状态
  isConnected(): boolean {
    return this.connectionStatus
  }

  // 获取注册请求
  private getRegisterRequest() {
    // 获取服务配置
    const workflowService = this.services.workflowService
    const servicesConfig = workflowService.getServicesConfig()

    // 构建注册请求
    const request = {
      user_id: this.userId,
      token: this.token,
      server_name: this.serverName,
      url: this.url,
      region: 'China',
      access_level: 1,
      protocol_type: 'grpc',
      version: this.version,
      is_force_to_register: false,
      service_name_list: servicesConfig.service_name_list,
      service_version_list: servicesConfig.service_version_list,
      service_access_level_list: servicesConfig.service_access_level_list,
    }

    return request
  }

  // 注册服务
  async register() {
    if (!this.isInitialized) {
      throw new Error('LinkerClient not initialized. Call setAuthInfo first.')
    }

    // 保存当前可用的URL
    const currentUrl = this.url

    // 先尝试获取最新的客户端URL
    try {
      const response = await this.call('getClientUrl', {})
      if (response && response.client_url) {
        logger.info(`获取到新的服务器地址: ${response.client_url}`)

        // 验证新URL是否可连接
        const isNewUrlConnectable = await this.testConnection(response.client_url)

        if (isNewUrlConnectable) {
          logger.info(`新地址 ${response.client_url} 可连接，将使用此地址注册`)
          this.setServerUrl(response.client_url)
        } else {
          logger.warn(`新地址 ${response.client_url} 连接失败，将继续使用当前地址 ${currentUrl}`)
        }
      }
    } catch (error) {
      logger.warn('获取客户端URL失败，将使用当前URL:', error)
    }

    return new Promise((resolve, reject) => {
      const request = this.getRegisterRequest()
      logger.info('注册请求参数:', request)

      this.client.register(request, (err: any, response: any) => {
        if (err) {
          logger.error('服务注册失败:', err)
          reject(err)
          return
        }

        if (response.status && response.status.code !== 0) {
          logger.error('服务注册失败:', response.message)
          reject(new Error(response.message))
          return
        }

        logger.info('服务注册成功, 服务器ID:', response.server_id)
        this.emit('registered', response.server_id)
        resolve(response)
      })
    })
  }

  // 测试URL连接
  private async testConnection(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        logger.info(`测试连接到 ${url}`)

        // 创建临时客户端测试连接
        const PROTO_PATH = path.resolve(__dirname, './protos/linker.proto')
        const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
          keepCase: true,
          longs: String,
          enums: String,
          defaults: true,
          oneofs: true,
          includeDirs: [path.resolve(__dirname, './protos')],
        })

        const linkerProto = grpc.loadPackageDefinition(packageDefinition).linker
        const testClient = new linkerProto.LinkerService(url, grpc.credentials.createInsecure())

        // 设置连接超时
        const timeout = setTimeout(() => {
          logger.warn(`连接到 ${url} 超时`)
          resolve(false)
        }, 5000)

        // 测试连接
        testClient.ping({}, (err: any, response: any) => {
          clearTimeout(timeout)

          if (err) {
            logger.warn(`连接到 ${url} 失败: ${err.message}`)
            resolve(false)
            return
          }

          logger.info(`成功连接到 ${url}`)
          resolve(true)
        })
      } catch (error) {
        logger.error(`测试连接时发生错误: ${error}`)
        resolve(false)
      }
    })
  }

  // 调用服务方法
  async call(method: string, params: any) {
    if (!this.isInitialized) {
      throw new Error('LinkerClient not initialized. Call setAuthInfo first.')
    }

    return new Promise((resolve, reject) => {
      this.client[method](
        {
          user_id: this.userId,
          token: this.token,
          ...params,
        },
        (err: any, response: any) => {
          if (err) {
            logger.error(`调用 ${method} 失败:`, err)
            reject(err)
            return
          }

          resolve(response)
        },
      )
    })
  }

  // 处理默认服务调用
  handleDefaultService(call: any, callback: any) {
    try {
      const request = call.request
      const methodName = request.method

      // 遍历所有服务，查找对应的方法
      for (const serviceName in this.services) {
        const service = this.services[serviceName]
        if (typeof service[methodName] === 'function') {
          // 调用对应的方法
          service[methodName](request)
            .then((result: any) => {
              callback(null, result)
            })
            .catch((error: any) => {
              callback(null, {
                status: { code: 1 },
                message: error.message,
                result: '{}',
              })
            })
          return
        }
      }

      // 如果没有找到对应的方法
      callback(null, {
        status: { code: 1 },
        message: `Method ${methodName} not found`,
        result: '{}',
      })
    } catch (error) {
      callback(null, {
        status: { code: 1 },
        message: error.message,
        result: '{}',
      })
    }
  }
}
