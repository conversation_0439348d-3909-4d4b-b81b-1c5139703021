<template>
  <div class="workflows h-full w-full">
    <Navbar v-if="canShowNavbar" />
    <div class="flex-1">
      <Container v-if="!isEditorRoute">
        <Dashboard />
      </Container>
      <Editor v-else :workflow-id="workflowId" :workflow-title="workflowTitle" />
    </div>
  </div>
</template>
<script setup>
import { Container } from '@renderer/components'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { useWorkflowStore } from '@renderer/store'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Dashboard, Editor, Navbar } from './components'

const workflowStore = useWorkflowStore()
const route = useRoute()
const { canShowNavbar } = useAppConfig()
const isEditorRoute = computed(() => route.name === 'workflow-editor')
const workflowId = computed(() => route.params.id)
const workflowTitle = computed(() => route.query.title)
onMounted(() => {
  // console.log('object route', route.params, route.query)
})
</script>
<style lang="scss" scoped>
.workflows {
  display: flex;
  flex-direction: column;
}
</style>
