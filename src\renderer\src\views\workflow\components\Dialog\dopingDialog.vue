<template>
  <Dialog :open="visiable" @update:open="closeSturcture">
    <DialogContent class="sm:max-w-[800px]">
      <DialogHeader>
        <DialogTitle>掺杂</DialogTitle>
      </DialogHeader>

      <div class="overflow-auto">
        <div class="pt-1">
          <div class="text-base mb-2 font-bold">待替换元素</div>
          <div class="px-1">
            <Select
              v-model="hostElement"
              :default-value="hostElement"
              @update:model-value="setHostElement"
            >
              <SelectTrigger>
                <SelectValue placeholder="请选择待替换元素" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem v-for="(opt, i) in hostElementList" :key="i" :value="opt.value">
                    {{ opt.label }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div class="w-full mt-2">
          <div class="text-base font-bold">替换成分</div>
          <div class="flex justify-start">
            <Button class="text-blue-500 gap-1 pl-0" variant="link" @click="addElement">
              <Plus class="w-4 h-4" />
              <span>添加成分</span>
            </Button>
            <Button class="text-red-500 gap-1 pl-0 ml-2" variant="link" @click="removeElement()">
              <Trash2 class="w-4 h-4" />
              <span>删除成分</span>
            </Button>
          </div>
          <div class="w-full min-h-[300px] max-h-[400px] overflow-auto">
            <TableDataVue
              ref="tableRef"
              :is-bordered="true"
              :columns="threeColumns"
              :data-sources="threeDataSources"
            />
          </div>
        </div>
        <div class="w-full mt-2">
          <div class="text-base mb-2 font-bold">最终化学式</div>
          <div
            class="flex justify-start items-center text-sm mb-2 p-2 border border-gray-200 rounded-md"
          >
            <Loader2 v-if="formulaLoading" class="w-3 h-3 animate-spin mr-2" />
            <span>{{ formula }}</span>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeSturcture">取消</Button>
        <Button @click="confirmSturcture">
          <span>确定</span>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { createMaterialService } from '@renderer/config/api/grpc/materialService'
import { Loader2, Plus, Trash2 } from 'lucide-vue-next'
import { h, nextTick, onMounted, ref, watch } from 'vue'
import { periodicTable } from './periodicTable'
import TableDataVue from './tableData.vue'
const props = defineProps({
  visiable: Boolean,
  dataParam: {
    type: Object,
  },
})
// 定义emit
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'ok', data?: any): void
  (e: 'update:visiable', visiable: boolean): void
}>()

const hostElement = ref(null)
const hostElementList: any = ref([])
// 设置待替换元素
const setHostElement = () => {
  threeDataSources.value = []
  setFinalFormula()
}
watch(
  () => props.dataParam,
  (val: any) => {
    setHostElementList(val)
  },
)
const formula: any = ref('-- -- --')
const setHostElementList = (value: any) => {
  if (value) {
    const list: any = value.elements.map((m: any) => {
      return { label: m, value: m }
    })
    formula.value = value.formula
    hostElementList.value = list
    hostElement.value = list[0].value
  }
}

const closeSturcture = () => {
  emit('close')
  emit('update:visiable', false)
}
const confirmSturcture = async () => {
  if (formulaLoading.value) {
    return
  }
  emit('close')
  emit('ok', structureParams)
  emit('update:visiable', false)
}
const threeColumns: any = [
  {
    title: '元素',
    key: 'element',
    isEdit: true,
    type: 'select',
    options: {
      selectOptions: [],
      emits: {
        update: () => {
          updateElement()
        },
      },
    },
    width: '30%',
  },
  {
    title: '比例',
    key: 'ratio',
    isEdit: true,
    type: 'number',
    width: '30%',
    options: {
      props: {
        min: 0,
        step: 1,
      },
      emits: {
        update: () => {
          updateElement()
        },
      },
    },
  },
  {
    title: '%',
    key: 'percent',
    width: '30%',
  },
  {
    title: '操作',
    width: '80px',
    isRender: true,
    options: {
      customRender: (value: any) => {
        return h(
          'div',
          {
            class: 'flex justify-start items-center cursor-pointer text-red-500',
            onClick: () => {
              removeElement(value.rowIndex)
            },
          },
          [h(Trash2, { class: 'w-4 h-4' }), h('span', { class: 'text-sm ml-1' }, '删除')],
        )
      },
    },
  },
]
const tableRef: any = ref(null)
const threeDataSources: any = ref([])
const updateElement = () => {
  const newTable = countElementRatio()
  threeDataSources.value = newTable
  setFinalFormula()
}
const addElement = () => {
  if (tableRef.value) {
    const dataSource = { element: hostElement.value, ratio: 1, percent: '100%' }
    tableRef.value.addRow(dataSource)
    const newTable = countElementRatio()
    threeDataSources.value = newTable
    setFinalFormula()
  }
}
const removeElement = (i?: number) => {
  if (tableRef.value) {
    let index = i
    if (!index) {
      index = tableRef.value.getData().length - 1
    }
    if (index >= 0) {
      tableRef.value.removeRow(index)
    }
    setFinalFormula()
  }
}
const countElementRatio = () => {
  if (tableRef.value) {
    const tableData: any = tableRef.value.getData()
    let total: any = 0
    tableData.forEach((item: any) => {
      total += item.ratio
    })
    tableData.map((m: any) => {
      if (total === 0) {
        m.percent = '0.00%'
      } else {
        m.percent = ((m.ratio / total) * 100).toFixed(2) + '%'
      }
    })
    return tableData
  }
}
onMounted(() => {
  initSelect()
  setHostElementList(props.dataParam)
})
const initSelect = () => {
  // 修改元素下拉列表
  const selectOptions: any = []
  const list: any = periodicTable.slice(0, 103)
  list.forEach((item: any) => {
    selectOptions.push({ label: item, value: item })
  })
  nextTick(() => {
    tableRef.value?.updateColumn(0, {
      options: {
        selectOptions,
      },
    })
  })
}
const materialService = createMaterialService()
const formulaLoading = ref(false)
// 结构式掺杂
let structureParams: any = null
const setFinalFormula = async () => {
  if (tableRef.value) {
    formula.value = '-- -- --'
    const data: any = tableRef.value.getData()
    if (data.length === 0 || threeDataSources.value.length === 0) {
      return
    }
    const comObj: any = {}
    data.forEach((item: any) => {
      if (item.element in comObj) {
        comObj[item.element] += item.ratio
      } else {
        comObj[item.element] = item.ratio
      }
    })
    const comStr: any = JSON.stringify(comObj)
    const params: any = {
      com: comStr,
      host: hostElement.value,
      str: props.dataParam?.structureData,
    }
    structureParams = params
    formulaLoading.value = true
    const result = await materialService.getDopedStructureFormula(
      params.com,
      params.host,
      params.str,
    )
    if (Number(result.statusCode) === 200) {
      formula.value = result.keyValuePairs.formula
    }
    formulaLoading.value = false
  }
}
</script>
