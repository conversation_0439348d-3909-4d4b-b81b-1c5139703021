<template>
  <div>
    <!-- 可拖动的 AI 图标按钮 -->
    <div
      v-if="!isChatOpenInWorkflow"
      ref="aiChatButtonContainerRef"
      class="fixed z-[500] cursor-grab ai-chat-button"
      :style="{
        left: `${aiChatButtonPosition.x}px`,
        top: `${aiChatButtonPosition.y}px`,
      }"
    >
      <button
        class="p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:bg-primary/90 transition-colors flex items-center justify-center"
        title="打开/关闭 AI 助手"
        @click="toggleAIChat"
      >
        <LucideIcon name="Bot" :width="28" :height="28" />
      </button>
    </div>

    <!-- AI 聊天窗口 -->
    <div
      v-if="isChatOpenInWorkflow"
      ref="aiChatWindowRef"
      class="ai-chat-workflow-window fixed bg-card border border-border shadow-xl rounded-lg overflow-hidden flex flex-col"
      :style="{
        left: `${chatPositionInWorkflow.x}px`,
        top: `${chatPositionInWorkflow.y}px`,
        width: `${chatSizeInWorkflow.width}px`,
        height: `${chatSizeInWorkflow.height}px`,
        zIndex: 10, // 确保在其他元素之上
      }"
    >
      <!-- 聊天窗口头部，用于拖动和关闭 -->
      <div
        ref="aiChatHeaderRef"
        class="chat-header p-2 bg-muted border-b border-border flex justify-between items-center cursor-move"
      >
        <div class="flex items-center space-x-2">
          <span class="font-semibold text-sm">Matt AI 助手</span>
          <!-- 添加"更多"按钮 -->
          <button
            class="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded hover:bg-primary/20 transition-colors"
            title="查看完整聊天"
            @click="navigateToFullChat"
          >
            更多
          </button>
        </div>
        <div class="flex items-center space-x-1">
          <button
            class="p-1 hover:bg-muted-foreground/20 rounded"
            title="关闭AI助手"
            @click="toggleAIChat"
          >
            <LucideIcon name="X" :width="16" :height="16" />
          </button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content flex-1 overflow-hidden flex flex-col">
        <!-- 消息列表 -->
        <ChatMessageList :messages="currentMessages" />

        <!-- 模式切换 -->
        <div class="mt-2 mx-6 flex items-center justify-between">
          <ToggleGroup
            type="single"
            :model-value="chatMode"
            variant="outline"
            class="toggle-success"
            size="sm"
            @update:model-value="updateChatMode"
          >
            <ToggleGroupItem
              value="chat"
              class="flex items-center space-x-1 data-[state=on]:bg-green-500 data-[state=on]:text-white"
            >
              <LucideIcon name="MessageSquare" class="w-3.5 h-3.5" />
              <span class="hidden sm:inline-block">聊天</span>
            </ToggleGroupItem>
            <ToggleGroupItem
              value="work"
              class="flex items-center space-x-1 data-[state=on]:bg-blue-500 data-[state=on]:text-white"
            >
              <LucideIcon name="Network" class="w-3.5 h-3.5" />
              <span class="hidden sm:inline-block">工作</span>
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
        <!-- 输入区域 -->
        <div class="border-t border-border">
          <ChatInput
            :is-workflow-mode="true"
            :sending="sending"
            :model-temperature="temperature"
            :stream-enabled="streamEnabled"
            :welcome-mode="false"
            :is-floating="true"
            :selected-server-id="selectedServerId"
            :selected-model="selectedModel"
            :running-servers="runningServers"
            :model-groups="modelGroups"
            :is-loading-models="isLoadingModels"
            @send-message="submitUserMessage"
            @update:temperature="updateTemperature"
            @update:stream-enabled="updateStreamEnabled"
            @update:selected-server-id="updateSelectedServerId"
            @update:selected-model="updateSelectedModel"
            @server-change="handleServerChange"
          />
        </div>
      </div>

      <!-- 添加调整大小的句柄 -->
      <div
        ref="resizeHandleRef"
        class="resize-handle absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'
import { createAIChatService } from '@renderer/config/api/grpc/aiChatService'
import {
  addWorkflowDataToCanvas,
  extractLifePredictionNodes,
  registerLifePredictionWorkflow,
} from '@renderer/services/grpc'
import {
  useAIChatStore,
  useNodeModulesStore,
  useServerStore,
  useWorkflowStore,
} from '@renderer/store'
import {
  ChatInput,
  ChatMessageList,
} from '@renderer/views/layout/ai/chat/components/ChatMain/components'
import { useDraggable } from '@vueuse/core'
import { nanoid } from 'nanoid'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'

// 获取 AI 聊天相关的 store
const aiChatStore = useAIChatStore()
const aiChatService = createAIChatService()
const workflowStore = useWorkflowStore()
const nodeModulesStore = useNodeModulesStore()
const serverStore = useServerStore()
const router = useRouter()

const {
  isChatOpenInWorkflow,
  chatPositionInWorkflow,
  chatSizeInWorkflow,
  currentSession,
  currentMessages,
} = storeToRefs(aiChatStore)

// 当前工作流ID
const currentWorkflowId = computed(() => workflowStore.currentWfId)

// 引用和状态
const aiChatWindowRef = ref<HTMLElement | null>(null)
const aiChatHeaderRef = ref<HTMLElement | null>(null)
const aiChatButtonContainerRef = ref<HTMLElement | null>(null)
const resizeHandleRef = ref<HTMLElement | null>(null)

const isDragging = ref(false)
const dragStartPos = ref({ x: 0, y: 0 })

// 初始化 AI 聊天按钮的位置
const aiChatButtonPosition = ref({
  x: typeof window !== 'undefined' ? 80 : 80,
  y: typeof window !== 'undefined' ? window.innerHeight - 180 : 500,
})

// 聊天相关状态
const sending = ref(false)
const temperature = ref([0.7])
const streamEnabled = ref(true)
const selectedServerId = ref('')
const selectedModel = ref('')
const modelGroups = ref({})
const isLoadingModels = ref(false)

// 聊天模式
const chatMode = computed({
  get: () => aiChatStore.chatMode,
  set: (value) => aiChatStore.updateChatMode(value),
})

// 只显示运行中的服务器
const runningServers = computed(() => {
  return serverStore.servers.filter(
    (server) => server.serverStatus === 'Running' && server.serverType === 'agentServer',
  )
})

// 更新聊天模式
const updateChatMode = (mode) => {
  chatMode.value = mode
  toast.success(`已切换到${mode === 'chat' ? '聊天' : '工作'}模式`)
}

// 使 AI 聊天按钮可拖动
useDraggable(aiChatButtonContainerRef, {
  initialValue: aiChatButtonPosition,
  onStart: (position) => {
    // 记录拖动开始位置
    isDragging.value = false
    dragStartPos.value = { x: position.x, y: position.y }
  },
  onMove: (position) => {
    // 判断是否移动超过阈值，如果是则标记为拖动
    const dx = Math.abs(position.x - dragStartPos.value.x)
    const dy = Math.abs(position.y - dragStartPos.value.y)
    if (dx > 5 || dy > 5) {
      isDragging.value = true
    }

    // 更新按钮位置
    aiChatButtonPosition.value = position
    // 如果聊天窗口已打开，则更新聊天窗口位置
    if (isChatOpenInWorkflow.value) {
      updateChatWindowPosition()
    }
  },
  onEnd: () => {
    // 拖动结束后短暂延迟重置状态，避免立即触发点击
    setTimeout(() => {
      isDragging.value = false
    }, 100)
  },
})

// 计算并更新聊天窗口位置
const updateChatWindowPosition = () => {
  if (!aiChatButtonContainerRef.value) return

  const buttonRect = aiChatButtonContainerRef.value.getBoundingClientRect()
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  // 默认在按钮右侧显示
  let x = buttonRect.right + 10
  let y = buttonRect.top

  // 检查是否超出右侧边界
  if (x + chatSizeInWorkflow.value.width > windowWidth - 20) {
    // 如果超出右侧边界，则显示在按钮左侧
    x = buttonRect.left - chatSizeInWorkflow.value.width - 10
  }

  // 检查是否超出底部边界
  if (y + chatSizeInWorkflow.value.height > windowHeight - 20) {
    // 调整垂直位置，确保不超出底部
    y = windowHeight - chatSizeInWorkflow.value.height - 20
  }

  // 确保不超出顶部边界
  y = Math.max(20, y)

  // 更新聊天窗口位置
  chatPositionInWorkflow.value = { x, y }
}

// 使 AI 聊天窗口可拖动 (通过其头部)
useDraggable(aiChatWindowRef, {
  handle: aiChatHeaderRef,
  initialValue: chatPositionInWorkflow,
  onMove: (newPos) => {
    // 更新 Pinia store 中的位置
    chatPositionInWorkflow.value = { x: newPos.x, y: newPos.y }
  },
})
// 模拟生成寿命预测流程
const handleUserInput = (content: string) => {
  // 转换为小写进行检查
  const lowerContent = content.toLowerCase()
  window.logger.info('用户输入:', lowerContent)

  // 检查是否包含"生成寿命预测流程"关键词
  if (lowerContent.includes('生成寿命预测流程') || lowerContent.includes('创建寿命预测流程')) {
    // 获取当前会话ID
    const sessionId = aiChatStore.currentSession?.id

    if (sessionId && currentWorkflowId.value) {
      try {
        window.logger.info('检测到生成寿命预测流程命令，开始创建流程...')

        // 获取节点模块
        const lifePredictionModule = nodeModulesStore.nodeModules['豪鹏电池寿命预测']

        if (lifePredictionModule) {
          // 直接使用导入的函数，不进行深拷贝
          const { nodes } = extractLifePredictionNodes(lifePredictionModule)

          // 调用服务层方法添加到画布
          addWorkflowDataToCanvas(currentWorkflowId.value, nodes, [])

          // 同时也调用API注册
          registerLifePredictionWorkflow(lifePredictionModule, sessionId)
            .then((response) => {
              window.logger.info('寿命预测流程API注册成功:', response)
            })
            .catch((error) => {
              window.logger.error('寿命预测流程API注册失败:', error)
            })
        } else {
          window.logger.error('未找到豪鹏电池寿命预测模块')
        }
      } catch (error) {
        window.logger.error('处理生成寿命预测流程命令失败:', error)
      }
    } else {
      window.logger.warn('无法注册寿命预测流程：未找到当前会话ID或工作流ID')
    }
  }

  // 返回原始内容，不做修改
  return content
}
// 提交用户消息
const submitUserMessage = async (content) => {
  if (!content || sending.value) return

  // 检查是否选择了服务器
  if (!selectedServerId.value) {
    toast.error('发送失败', {
      description: '请先选择服务器',
    })
    return
  }
  // 检查是否选择了模型
  if (!selectedModel.value) {
    toast.error('发送失败', {
      description: '请先选择模型',
    })
    return
  }

  sending.value = true
  handleUserInput(content)

  try {
    await aiChatService.sendUserMessage(content)
  } catch (error) {
    console.error('Error sending message:', error)
  } finally {
    sending.value = false
  }
}

// 更新温度设置
const updateTemperature = (value) => {
  temperature.value = value
  aiChatStore.updateTemperature(value)
}

// 更新流式输出设置
const updateStreamEnabled = (value) => {
  streamEnabled.value = value
}

// 更新服务器ID
const updateSelectedServerId = (value) => {
  selectedServerId.value = value
  aiChatStore.updateSelectedServer(value)
}

// 更新模型
const updateSelectedModel = (value) => {
  selectedModel.value = value
  aiChatStore.updateSelectedModel(value)
}

// 处理服务器选择变化
const handleServerChange = async (serverId) => {
  selectedServerId.value = serverId
  aiChatStore.updateSelectedServer(serverId)

  // 获取该服务器支持的模型列表
  isLoadingModels.value = true
  try {
    // 获取模型分组
    modelGroups.value = await aiChatService.getAvailableModels(
      serverId,
      aiChatStore.currentSessionId,
    )

    // 检查是否有可用模型
    if (Object.keys(modelGroups.value).length > 0) {
      // 获取第一个提供商
      const firstProvider = Object.keys(modelGroups.value)[0]
      const firstModelGroup = modelGroups.value[firstProvider]

      if (firstModelGroup && firstModelGroup.length > 0) {
        // 获取第一个模型
        const firstModel = firstModelGroup[0]
        const modelKey = Object.keys(firstModel)[0]
        const modelValue = Object.values(firstModel)[0]

        // 构建模型ID
        const firstModelId = `${firstProvider}/${modelValue}`

        // 选择第一个可用模型
        selectedModel.value = firstModelId
        aiChatStore.updateSelectedModel(firstModelId)
        window.logger.info(`自动选择模型: ${modelKey} (${firstModelId})`)
      }
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
  } finally {
    isLoadingModels.value = false
  }
}

// 导航到完整聊天页面
const navigateToFullChat = () => {
  // 如果有工作流ID，传递给聊天页面
  if (currentSession.value?.workflowId) {
    router.push({
      path: '/ai/chat',
      query: { workflowId: currentSession.value.workflowId },
    })
  } else {
    router.push({ path: '/ai/chat' })
  }

  // 关闭浮动窗口
  isChatOpenInWorkflow.value = false
}

// 切换 AI 聊天窗口的显示/隐藏
const toggleAIChat = async () => {
  // 如果是拖动状态，不触发打开/关闭
  if (isDragging.value) return

  // 先切换窗口状态
  isChatOpenInWorkflow.value = !isChatOpenInWorkflow.value

  // 如果打开聊天窗口，计算合适的位置
  if (isChatOpenInWorkflow.value) {
    updateChatWindowPosition()

    // 生成会话ID
    const expectedSessionId = `0::${currentWorkflowId.value}`
    window.logger.info(`打开聊天窗口，尝试获取工作流会话 ${expectedSessionId}`)

    try {
      // 尝试从服务器获取会话
      const session = await aiChatService.fetchSession(expectedSessionId)

      if (session) {
        // 如果找到会话，选择它
        window.logger.info(`找到会话 ${expectedSessionId}，设置为当前会话`)
        aiChatStore.setCurrentSessionId(expectedSessionId)
        aiChatStore.setCurrentSessionData(session)
      } else {
        // 如果没有找到会话，创建新会话
        window.logger.info(`未找到会话 ${expectedSessionId}，创建新会话`)

        // 获取工作流信息
        const workflow = workflowStore.workflows.find((wf) => wf.id === currentWorkflowId.value)
        const title = workflow ? `${workflow.title} 对话` : '工作流对话'

        // 创建新会话
        const newSession = await aiChatService.createNewSession(title, currentWorkflowId.value)

        if (newSession) {
          window.logger.info(`成功创建新会话 ${newSession.id}`)
        } else {
          window.logger.error(`创建新会话失败`)
        }
      }
    } catch (error) {
      window.logger.error('获取或创建会话失败:', error)

      // 出错时，创建一个临时会话数据
      const workflow = workflowStore.workflows.find((wf) => wf.id === currentWorkflowId.value)
      const title = workflow ? `${workflow.title} 对话` : '工作流对话'

      // 构建会话数据
      const sessionData = {
        id: expectedSessionId,
        title: title,
        messages: [
          {
            id: nanoid(),
            sessionId: expectedSessionId,
            content: '你好！这是一个新的对话。',
            role: 'assistant',
            timestamp: Date.now(),
          },
        ],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        workflowId: currentWorkflowId.value,
      }

      // 直接更新当前会话状态
      aiChatStore.setCurrentSessionId(expectedSessionId)
      aiChatStore.setCurrentSessionData(sessionData)
    }

    // 加载服务器和模型信息
    await getServerList()

    // 如果已有服务器ID但没有模型信息，重新获取模型列表
    if (
      selectedServerId.value &&
      (!selectedModel.value || Object.keys(modelGroups.value).length === 0)
    ) {
      window.logger.info(`已有服务器ID ${selectedServerId.value}，但没有模型信息，重新获取模型列表`)
      await handleServerChange(selectedServerId.value)
    }
  }
}

// 获取服务器列表
const getServerList = async () => {
  try {
    if (serverStore.servers.length === 0) {
      await serverStore.updateServerList()
    }

    let serverIdToUse = ''

    // 优先使用存储中的选择
    if (aiChatStore.selectedServerId) {
      serverIdToUse = aiChatStore.selectedServerId
    }
    // 否则使用第一个可用服务器
    else if (runningServers.value.length > 0) {
      serverIdToUse = runningServers.value[0].serverId
    }

    if (serverIdToUse) {
      selectedServerId.value = serverIdToUse
      aiChatStore.updateSelectedServer(serverIdToUse)

      // 获取该服务器支持的模型列表
      isLoadingModels.value = true
      try {
        // 获取模型分组
        modelGroups.value = await aiChatService.getAvailableModels(
          serverIdToUse,
          aiChatStore.currentSessionId,
        )

        // 检查是否有可用模型
        if (Object.keys(modelGroups.value).length > 0) {
          // 获取第一个提供商
          const firstProvider = Object.keys(modelGroups.value)[0]
          const firstModelGroup = modelGroups.value[firstProvider]

          if (firstModelGroup && firstModelGroup.length > 0) {
            // 获取第一个模型
            const firstModel = firstModelGroup[0]
            const modelKey = Object.keys(firstModel)[0]
            const modelValue = Object.values(firstModel)[0]

            // 构建模型ID
            const firstModelId = `${firstProvider}/${modelValue}`

            // 选择第一个可用模型
            selectedModel.value = firstModelId
            aiChatStore.updateSelectedModel(firstModelId)
            window.logger.info(`自动选择模型: ${modelKey} (${firstModelId})`)
          }
        }
      } catch (error) {
        console.error('获取模型列表失败:', error)
      } finally {
        isLoadingModels.value = false
      }
    }
  } catch (error) {
    console.error('加载服务器列表失败:', error)
  }
}

// 监听 AI 聊天窗口的打开状态
watch(isChatOpenInWorkflow, async (newValue) => {
  if (newValue) {
    window.logger.info('AI 聊天窗口被打开')

    // 确保当前工作流ID存在
    if (!currentWorkflowId.value) {
      window.logger.warn('当前工作流ID不存在，无法获取或创建会话')
      return
    }

    // 生成会话ID
    const expectedSessionId = `0::${currentWorkflowId.value}`

    // 检查当前会话是否与当前工作流匹配
    if (!aiChatStore.currentSessionId || aiChatStore.currentSessionId !== expectedSessionId) {
      window.logger.info(
        `当前会话 ${aiChatStore.currentSessionId || '无'} 与工作流 ${currentWorkflowId.value} 不匹配，需要切换会话`,
      )

      try {
        // 尝试从服务器获取会话
        const session = await aiChatService.fetchSession(expectedSessionId)

        if (session) {
          // 如果找到会话，设置为当前会话
          window.logger.info(`找到会话 ${expectedSessionId}，设置为当前会话`)
          aiChatStore.setCurrentSessionId(expectedSessionId)
          aiChatStore.setCurrentSessionData(session)
        } else {
          // 如果没有找到会话，创建新会话
          window.logger.info(`未找到会话 ${expectedSessionId}，创建新会话`)
          const workflow = workflowStore.workflows.find((wf) => wf.id === currentWorkflowId.value)
          if (workflow) {
            const newSession = await aiChatService.createNewSession(
              `${workflow.title} 对话`,
              currentWorkflowId.value,
            )
            if (newSession) {
              window.logger.info(`成功创建新会话 ${newSession.id}`)
            }
          }
        }
      } catch (error) {
        window.logger.error('无法获取或创建工作流会话:', error)
      }
    } else {
      // 即使会话ID匹配，也重新从服务器获取会话数据以确保数据最新
      window.logger.info(
        `当前会话 ${aiChatStore.currentSessionId} 已经与工作流 ${currentWorkflowId.value} 关联，刷新会话数据`,
      )
      try {
        const session = await aiChatService.fetchSession(expectedSessionId)
        if (session) {
          aiChatStore.setCurrentSessionData(session)
        }
      } catch (error) {
        window.logger.error('刷新会话数据失败:', error)
      }
    }
  } else {
    window.logger.info('AI 聊天窗口被关闭')
  }
})

// 监听工作流ID变化
watch(currentWorkflowId, async (newWorkflowId, oldWorkflowId) => {
  if (newWorkflowId && isChatOpenInWorkflow.value && newWorkflowId !== oldWorkflowId) {
    // 工作流ID变化时，只有在聊天窗口打开时才切换对话
    window.logger.info(`工作流ID变化: ${oldWorkflowId} -> ${newWorkflowId}，需要切换对话`)
    const expectedSessionId = `0::${newWorkflowId}`

    try {
      // 先检查当前会话是否已经是期望的会话
      if (aiChatStore.currentSessionId === expectedSessionId) {
        window.logger.info(`当前会话 ${expectedSessionId} 已经是期望的会话，不需要切换`)
        return
      }

      // 尝试从服务器获取会话
      const session = await aiChatService.fetchSession(expectedSessionId)

      if (session) {
        // 如果找到会话，选择它
        window.logger.info(`找到会话 ${expectedSessionId}，直接选择`)
        await aiChatService.selectSession(expectedSessionId)
      } else {
        // 如果没有找到会话，创建新会话
        window.logger.info(`未找到会话 ${expectedSessionId}，创建新会话`)
        const workflow = workflowStore.workflows.find((wf) => wf.id === newWorkflowId)
        if (workflow) {
          await aiChatService.createNewSession(`${workflow.title} 对话`, newWorkflowId)
        }
      }
    } catch (error) {
      console.error('无法获取或创建工作流会话:', error)
    }
  }
})

// 监听 selectedServerId 变化
watch(selectedServerId, (newServerId, oldServerId) => {
  if (newServerId && newServerId !== oldServerId) {
    window.logger.info(`服务器ID变化: ${oldServerId || '无'} -> ${newServerId}，更新模型列表`)
    handleServerChange(newServerId)
  }
})

// 监听 selectedModel 变化
watch(selectedModel, (newModel, oldModel) => {
  if (newModel && newModel !== oldModel) {
    window.logger.info(`选择的模型变化: ${oldModel || '无'} -> ${newModel}`)
    aiChatStore.updateSelectedModel(newModel)
  }
})

// 监听 aiChatStore 中的 selectedServerId 和 selectedModel
watch(
  () => aiChatStore.selectedServerId,
  (newServerId) => {
    if (newServerId && newServerId !== selectedServerId.value) {
      window.logger.info(
        `aiChatStore 中的服务器ID变化: ${selectedServerId.value || '无'} -> ${newServerId}，同步到本地`,
      )
      selectedServerId.value = newServerId
    }
  },
)

watch(
  () => aiChatStore.selectedModel,
  (newModel) => {
    if (newModel && newModel !== selectedModel.value) {
      window.logger.info(
        `aiChatStore 中的模型变化: ${selectedModel.value || '无'} -> ${newModel}，同步到本地`,
      )
      selectedModel.value = newModel
    }
  },
)

// 在组件挂载时设置事件监听
onMounted(async () => {
  // 监听窗口大小变化，更新 AI 按钮位置
  const handleWindowResize = () => {
    // 确保按钮不会超出窗口边界
    aiChatButtonPosition.value = {
      x: Math.min(aiChatButtonPosition.value.x, window.innerWidth - 80),
      y: Math.min(aiChatButtonPosition.value.y, window.innerHeight - 80),
    }

    // 如果聊天窗口已打开，更新其位置
    if (isChatOpenInWorkflow.value) {
      updateChatWindowPosition()
    }
  }

  window.addEventListener('resize', handleWindowResize)

  // 初始化服务器列表
  if (serverStore.servers.length === 0) {
    await serverStore.updateServerList()
  }

  // 如果聊天窗口已打开，初始化会话数据
  if (isChatOpenInWorkflow.value && currentWorkflowId.value) {
    window.logger.info('组件挂载时，聊天窗口已打开，初始化会话数据')

    // 生成会话ID
    const expectedSessionId = `0::${currentWorkflowId.value}`

    try {
      // 尝试从服务器获取会话
      const session = await aiChatService.fetchSession(expectedSessionId)

      if (session) {
        // 如果找到会话，设置为当前会话
        window.logger.info(`找到会话 ${expectedSessionId}，设置为当前会话`)
        aiChatStore.setCurrentSessionId(expectedSessionId)
        aiChatStore.setCurrentSessionData(session)
      } else {
        // 如果没有找到会话，创建新会话
        window.logger.info(`未找到会话 ${expectedSessionId}，创建新会话`)
        const workflow = workflowStore.workflows.find((wf) => wf.id === currentWorkflowId.value)
        if (workflow) {
          await aiChatService.createNewSession(`${workflow.title} 对话`, currentWorkflowId.value)
        }
      }

      // 初始化服务器和模型信息
      await getServerList()

      // 如果已有服务器ID但没有模型信息，重新获取模型列表
      if (
        selectedServerId.value &&
        (!selectedModel.value || Object.keys(modelGroups.value).length === 0)
      ) {
        window.logger.info(
          `已有服务器ID ${selectedServerId.value}，但没有模型信息，重新获取模型列表`,
        )
        await handleServerChange(selectedServerId.value)
      }
    } catch (error) {
      console.error('初始化会话数据失败:', error)
    }
  } else {
    // 预加载服务器列表，以便打开聊天窗口时可以立即使用
    serverStore.updateServerList()
  }

  // 清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleWindowResize)
  })
})
</script>

<style scoped>
/* AI 聊天按钮样式 */
.ai-chat-button {
  position: fixed;
  z-index: 500;
  cursor: grab;
  user-select: none;
}

/* AI 聊天窗口样式 */
.ai-chat-workflow-window {
  display: flex;
  flex-direction: column;
  min-width: 350px;
  min-height: 500px;
  max-width: 90vw;
  max-height: 80vh;
}

.chat-header {
  flex-shrink: 0;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 14px;
  height: 14px;
  cursor: se-resize;
  background-image: linear-gradient(135deg, transparent 50%, var(--primary) 50%);
  opacity: 0.6;
  transition: opacity 0.2s;
}

.resize-handle:hover {
  opacity: 1;
}
/* 为弹出菜单添加样式 */
:deep(.v-popper__popper) {
  z-index: 1100 !important; /* 确保下拉菜单在最上层 */
}

/* 确保下拉菜单能够显示 */
:deep(.dropdown-menu-content) {
  z-index: 1100 !important;
}
</style>
