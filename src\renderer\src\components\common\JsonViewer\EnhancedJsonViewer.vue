<template>
  <Dialog :open="isOpen" @update:open="updateOpen">
    <DialogContent class="enhanced-json-viewer max-w-6xl h-[80vh] p-4">
      <div class="flex flex-col h-full overflow-auto">
        <div class="flex items-center justify-between mb-2 h-[5vh]">
          <div>
            <h3 class="text-base font-medium">详情</h3>
            <p class="text-sm text-muted-foreground">详细 JSON 数据</p>
          </div>
        </div>

        <ResizablePanelGroup direction="horizontal" class="flex-1">
          <!-- 左侧 JSON 数据编辑器 -->
          <ResizablePanel :default-size="30" :min-size="20" class="h-full">
            <div class="h-full flex flex-col">
              <div
                class="p-1 border-b bg-gray-50 dark:bg-gray-800 flex items-center justify-between"
              >
                <h3 class="text-xs font-medium">JSON 数据</h3>
                <div class="flex items-center gap-0.5">
                  <Button
                    v-tooltip="{ content: '格式化' }"
                    size="icon"
                    variant="ghost"
                    class="h-6 w-6"
                    @click="formatJson"
                  >
                    <FileText class="h-3 w-3" />
                  </Button>
                  <Button
                    v-tooltip="{ content: '删除空格' }"
                    size="icon"
                    variant="ghost"
                    class="h-6 w-6"
                    @click="compactJson"
                  >
                    <FileMinusIcon class="h-3 w-3" />
                  </Button>
                  <Button
                    v-tooltip="{ content: '删除空格并转义' }"
                    size="icon"
                    variant="ghost"
                    class="h-6 w-6"
                    @click="compactAndEscapeJson"
                  >
                    <FileCode class="h-3 w-3" />
                  </Button>
                  <Button
                    v-tooltip="{ content: '去除转义' }"
                    size="icon"
                    variant="ghost"
                    class="h-6 w-6"
                    @click="unescapeJson"
                  >
                    <FileText class="h-3 w-3" />
                  </Button>
                  <Button
                    v-tooltip="{ content: '复制' }"
                    size="icon"
                    variant="ghost"
                    class="h-6 w-6"
                    @click="copyJson"
                  >
                    <Copy class="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <div class="flex-1 overflow-auto">
                <ScrollArea class="h-full">
                  <div
                    ref="codeEditorRef"
                    class="font-mono p-2 code-editor min-w-full rounded-md cursor-pointer"
                    contenteditable="true"
                    @input="handleEditorInput"
                    @blur="formatOnBlur"
                    v-html="highlightedJson"
                  ></div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>

          <!-- 拖动句柄 -->
          <ResizableHandle class="w-1 bg-gray-300 dark:bg-gray-600" />

          <!-- 中间 JSON 树形视图 -->
          <ResizablePanel :default-size="40" :min-size="30" class="h-full">
            <div class="h-full flex flex-col">
              <div
                class="p-1 border-b bg-gray-50 dark:bg-gray-800 flex items-center justify-between"
              >
                <h3 class="text-xs font-medium">JSON 视图</h3>
                <div class="flex items-center gap-1">
                  <Button size="sm" variant="outline" class="h-6 px-2 text-xs" @click="expandAll">
                    展开全部
                  </Button>
                  <Button size="sm" variant="outline" class="h-6 px-2 text-xs" @click="collapseAll">
                    折叠全部
                  </Button>
                  <div class="relative">
                    <Input v-model="searchText" placeholder="搜索..." class="w-32 h-6 text-xs" />
                    <Button
                      v-if="searchText"
                      :variant="'ghost'"
                      :size="'icon'"
                      class="absolute right-1 top-1/2 -translate-y-1/2 h-4 w-4"
                      @click="searchText = ''"
                    >
                      <X class="h-2 w-2" />
                    </Button>
                  </div>
                </div>
              </div>
              <div class="flex-1 overflow-auto">
                <ScrollArea class="h-full">
                  <div class="p-2">
                    <div v-if="error" class="text-red-500 p-2 rounded bg-red-50 dark:bg-red-900/20">
                      {{ error }}
                    </div>
                    <JsonTreeNode
                      v-else-if="parsedJson"
                      :data="parsedJson"
                      :level="0"
                      :path="'root'"
                      :expanded-paths="expandedPaths"
                      :search-text="searchText"
                      :selected-path="selectedPath"
                      @toggle="toggleNode"
                      @select="selectNode"
                    />
                    <div v-else class="text-gray-500 p-4 text-center">
                      请在左侧输入有效的 JSON 数据
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>

          <!-- 拖动句柄 -->
          <ResizableHandle class="w-1 bg-gray-300 dark:bg-gray-600" />

          <!-- 右侧属性详情面板 -->
          <ResizablePanel :default-size="30" :min-size="20" class="h-full">
            <div class="h-full flex flex-col">
              <div class="p-1 border-b bg-gray-50 dark:bg-gray-800">
                <h3 class="text-xs font-medium p-1">属性详情</h3>
              </div>
              <div class="flex-1 overflow-auto">
                <ScrollArea class="h-full">
                  <div class="p-2">
                    <div v-if="!selectedNodeData" class="text-gray-500 p-4 text-center">
                      请在 JSON 视图中选择一个节点查看详情
                    </div>
                    <div v-else>
                      <div class="mb-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <div class="text-xs font-medium">选中路径</div>
                        <div class="text-xs font-mono mt-1">{{ selectedPath }}</div>
                      </div>
                      <div class="space-y-1">
                        <div
                          v-for="(value, key) in selectedNodeProperties"
                          :key="key"
                          class="p-2 border rounded hover:bg-gray-50 dark:hover:bg-gray-800/50"
                        >
                          <div class="flex items-start">
                            <div class="text-blue-600 dark:text-blue-400 font-medium text-xs mr-2">
                              {{ key }}:
                            </div>
                            <div
                              class="text-xs font-mono break-all max-w-full string-wrap"
                              :class="getValueClass(selectedNodeProperties[key])"
                            >
                              {{ getFormattedValue(selectedNodeProperties[key]) }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { toast } from 'vue-sonner'

import { Copy, X, FileText, FileMinusIcon, FileCode } from 'lucide-vue-next'
import JsonTreeNode from './JsonTreeNode.vue'
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.css'
import 'prismjs/components/prism-json'

interface Props {
  initialData?: any
  isOpen?: boolean
  onSave?: (data: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  initialData: null,
  isOpen: false,
  onSave: undefined,
})

const emit = defineEmits<{
  (e: 'update:isOpen', value: boolean): void
  (e: 'save', data: any): void
}>()

// 状态
const jsonInput = ref('')
const parsedJson = ref<any>(null)
const error = ref('')
const expandedPaths = ref<Set<string>>(new Set())
const searchText = ref('')
const selectedPath = ref('')
const selectedNodeData = ref<any>(null)
const codeEditorRef = ref<HTMLElement | null>(null)
const isOpen = computed(() => props.isOpen)
const preventParseUpdate = ref(false)

// 高亮显示的 JSON
const highlightedJson = computed(() => {
  if (!jsonInput.value.trim()) return ''

  try {
    // 尝试格式化 JSON 以便更好地显示
    const formatted = JSON.stringify(JSON.parse(jsonInput.value), null, 2)
    // 使用 Prism 高亮显示
    return Prism.highlight(formatted, Prism.languages.json, 'json')
  } catch (e) {
    // 如果解析失败，仍然尝试高亮显示原始文本
    return Prism.highlight(jsonInput.value, Prism.languages.json, 'json')
  }
})

// 处理编辑器输入
const handleEditorInput = (event: Event) => {
  const target = event.target as HTMLElement
  const content = target.innerText || ''

  // 更新 jsonInput，但不立即解析，避免在输入过程中出现错误提示
  jsonInput.value = content
}

// 失去焦点时格式化
const formatOnBlur = () => {
  try {
    parseJson()
    // 如果解析成功，更新编辑器内容为高亮格式
    nextTick(() => {
      if (codeEditorRef.value) {
        // 保存当前选择位置
        const selection = window.getSelection()
        const range = selection?.getRangeAt(0)

        // 更新内容
        codeEditorRef.value.innerHTML = highlightedJson.value

        // 尝试恢复选择位置
        if (range && selection) {
          selection.removeAllRanges()
          selection.addRange(range)
        }
      }
    })
  } catch (e) {
    // 解析失败时不做处理，保留用户输入
  }
}

// 初始化数据
const initData = () => {
  if (props.initialData) {
    try {
      const formattedJson = JSON.stringify(props.initialData, null, 2)
      jsonInput.value = formattedJson
      parsedJson.value = props.initialData
      expandedPaths.value.add('root')

      // 更新编辑器内容
      nextTick(() => {
        if (codeEditorRef.value) {
          codeEditorRef.value.innerHTML = highlightedJson.value
        }
      })
    } catch (e) {
      error.value = `初始化数据失败: ${(e as Error).message}`
    }
  }
}

// 解析 JSON
const parseJson = () => {
  if (!jsonInput.value.trim()) {
    parsedJson.value = null
    error.value = ''
    return
  }

  try {
    parsedJson.value = JSON.parse(jsonInput.value)
    error.value = ''
    // 如果之前没有展开任何节点，默认展开根节点
    if (expandedPaths.value.size === 0) {
      expandedPaths.value.add('root')
    }
  } catch (e) {
    error.value = `JSON 解析错误: ${(e as Error).message}`
    parsedJson.value = null
  }
}

// 使用防抖处理输入
const parseJsonDebounced = useDebounceFn(parseJson, 300)

// 格式化 JSON
const formatJson = () => {
  try {
    if (!jsonInput.value.trim()) return
    const obj = JSON.parse(jsonInput.value)
    jsonInput.value = JSON.stringify(obj, null, 2)
    parseJson()

    // 更新编辑器内容
    nextTick(() => {
      if (codeEditorRef.value) {
        codeEditorRef.value.innerHTML = highlightedJson.value
      }
    })

    // 显示成功消息
    toast.success('格式化成功')
  } catch (e) {
    error.value = `格式化失败: ${(e as Error).message}`
  }
}

// 压缩 JSON（删除空格）
const compactJson = () => {
  try {
    if (!jsonInput.value.trim()) return
    const obj = JSON.parse(jsonInput.value)
    jsonInput.value = JSON.stringify(obj)
    parseJson()

    // 更新编辑器内容
    nextTick(() => {
      if (codeEditorRef.value) {
        codeEditorRef.value.innerHTML = highlightedJson.value
      }
    })
    toast.success('压缩成功')
  } catch (e) {
    error.value = `压缩失败: ${(e as Error).message}`
  }
}

// 压缩并转义 JSON
const compactAndEscapeJson = () => {
  try {
    if (!jsonInput.value.trim()) return

    // 设置标志位，防止更新 parsedJson
    preventParseUpdate.value = true

    // 解析并压缩 JSON
    const obj = JSON.parse(jsonInput.value)
    const compacted = JSON.stringify(obj)

    // 转义 JSON 字符串（只更新编辑器显示，不影响树视图）
    jsonInput.value = JSON.stringify(compacted)

    // 更新编辑器内容，确保自动换行
    nextTick(() => {
      if (codeEditorRef.value) {
        const formattedJson = JSON.stringify(JSON.parse(jsonInput.value), null, 2)
        codeEditorRef.value.innerHTML = Prism.highlight(formattedJson, Prism.languages.json, 'json')
        // 强制换行
        codeEditorRef.value.style.whiteSpace = 'pre-wrap'

        // 恢复标志位
        preventParseUpdate.value = false
      }
    })
    toast.success('压缩并转义成功')
  } catch (e) {
    // 出错时也要恢复标志位
    preventParseUpdate.value = false
    error.value = `压缩并转义失败: ${(e as Error).message}`
  }
}

// 去除转义
const unescapeJson = () => {
  try {
    if (!jsonInput.value.trim()) return
    // 检查是否是转义的 JSON 字符串
    if (jsonInput.value.startsWith('"') && jsonInput.value.endsWith('"')) {
      const unescaped = JSON.parse(jsonInput.value)
      jsonInput.value = unescaped
      parseJson()

      // 更新编辑器内容
      nextTick(() => {
        if (codeEditorRef.value) {
          codeEditorRef.value.innerHTML = highlightedJson.value
        }
      })

      toast.success('去除转义成功')
    } else {
      toast.error('当前 JSON 不是转义格式')
    }
  } catch (e) {
    error.value = `去除转义失败: ${(e as Error).message}`
  }
}

// 复制 JSON
const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(jsonInput.value)
    toast.success('复制成功')
  } catch (e) {
    toast.error('复制失败')
  }
}

// 展开所有节点
const expandAll = () => {
  const paths = new Set<string>()

  const traverse = (obj: any, path: string) => {
    if (obj !== null && typeof obj === 'object') {
      paths.add(path)

      if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          traverse(item, `${path}[${index}]`)
        })
      } else {
        Object.keys(obj).forEach((key) => {
          traverse(obj[key], `${path}.${key}`)
        })
      }
    }
  }

  traverse(parsedJson.value, 'root')
  expandedPaths.value = paths
}

// 折叠所有节点
const collapseAll = () => {
  expandedPaths.value.clear()
  // 保持根节点展开
  expandedPaths.value.add('root')
}

// 切换节点展开/折叠状态
const toggleNode = (path: string) => {
  if (expandedPaths.value.has(path)) {
    expandedPaths.value.delete(path)
  } else {
    expandedPaths.value.add(path)
  }
}

// 选择节点
const selectNode = (path: string, data: any) => {
  selectedPath.value = path
  selectedNodeData.value = data
}

// 获取选中节点的属性
const selectedNodeProperties = computed(() => {
  if (!selectedNodeData.value) return {}

  // 如果是对象或数组，返回其所有属性
  if (typeof selectedNodeData.value === 'object' && selectedNodeData.value !== null) {
    return selectedNodeData.value
  }

  // 如果是基本类型，返回包含值的对象
  return { value: selectedNodeData.value }
})

// 获取值的类型样式
const getValueClass = (value: any) => {
  if (value === null) return 'text-gray-500 dark:text-gray-400'
  if (typeof value === 'string') return 'text-green-600 dark:text-green-400'
  if (typeof value === 'number') return 'text-orange-600 dark:text-orange-400'
  if (typeof value === 'boolean') return 'text-purple-600 dark:text-purple-400'
  if (Array.isArray(value)) return 'text-gray-600 dark:text-gray-300'
  if (typeof value === 'object') return 'text-gray-600 dark:text-gray-300'
  return ''
}

// 获取格式化的值
const getFormattedValue = (value: any) => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'string') return `"${value}"`
  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return `Array(${value.length})`
    }
    return `Object{${Object.keys(value).length}}`
  }
  return String(value)
}

// 更新弹窗状态
const updateOpen = (value: boolean) => {
  emit('update:isOpen', value)
}

// 关闭弹窗
const closeDialog = () => {
  emit('update:isOpen', false)
}

// 保存数据
const handleSave = () => {
  if (parsedJson.value) {
    if (props.onSave) {
      props.onSave(parsedJson.value)
    }
    emit('save', parsedJson.value)
    closeDialog()
  } else {
    toast.warning('无效的 JSON 数据')
  }
}

// 在组件挂载后初始化 Prism
onMounted(() => {
  // 确保 Prism 正确加载
  nextTick(() => {
    if (codeEditorRef.value && jsonInput.value) {
      codeEditorRef.value.innerHTML = highlightedJson.value
    }
  })
})

// 监听数据变化
watch(() => props.initialData, initData, { immediate: true })

// 监听 jsonInput 变化，延迟解析
watch(jsonInput, (newValue) => {
  if (!preventParseUpdate.value) {
    parseJsonDebounced(newValue)
  }
})
</script>

<style scoped>
.enhanced-json-viewer {
  @apply w-full h-full;
}

.code-editor {
  @apply outline-none whitespace-pre-wrap min-w-full break-all;
}

/* 确保 ResizablePanel 不被内容撑开 */
:deep(.resizable-panel) {
  @apply max-w-[50%] min-w-[20%];
}

/* 确保 ResizableHandle 可见 */
:deep(.resizable-handle) {
  @apply bg-gray-300 dark:bg-gray-600 transition-colors cursor-col-resize;
}

:deep(.resizable-handle:hover) {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 优化 ScrollArea 样式 */
:deep(.scroll-area) {
  @apply h-full w-full;
}

/* 强制长字符串换行 */
.string-wrap {
  @apply whitespace-pre-wrap break-all;
}

/* 添加自定义 Prism 样式 */
:deep(.token.property) {
  @apply text-blue-600 dark:text-blue-400;
}

:deep(.token.string) {
  @apply text-green-600 dark:text-green-400 whitespace-pre-wrap break-all;
}

:deep(.token.number) {
  @apply text-orange-600 dark:text-orange-400;
}

:deep(.token.boolean) {
  @apply text-purple-600 dark:text-purple-400;
}

:deep(.token.null) {
  @apply text-gray-500 dark:text-gray-400;
}

:deep(.token.punctuation) {
  @apply text-gray-600 dark:text-gray-300;
}
</style>
