import fs from 'fs'
import path from 'path'
import logger from '../utils/logger'
import { getConfigPath, getFallbackConfigPath } from '../utils/paths'
/**
 * 配置服务类
 * 处理应用程序配置相关操作
 */
export class ConfigService {
  /**
   * 从配置文件读取 Linker API
   */
  getLinkerApiFromConfig(): string | null {
    try {
      const configPath = getConfigPath()
      if (fs.existsSync(configPath)) {
        const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'))
        return configData.linkerApi || null
      }
    } catch (error) {
      logger.error('读取配置文件失败:', error)
    }
    return null
  }

  /**
   * 更新 Linker API 配置
   */
  updateLinkerApiConfig(newLinkerApi: string): { success: boolean; message?: string } {
    try {
      const configPath = getConfigPath()

      // 读取现有配置或创建新配置
      let configData = {}
      if (fs.existsSync(configPath)) {
        try {
          configData = JSON.parse(fs.readFileSync(configPath, 'utf8'))
        } catch (error) {
          logger.error('解析配置文件失败:', error)
        }
      }

      // 更新配置
      configData = {
        ...configData,
        linkerApi: newLinkerApi,
        lastUpdated: new Date().toISOString(),
      }

      try {
        // 检查目录是否存在，如果不存在则创建
        const configDir = path.dirname(configPath)
        if (!fs.existsSync(configDir)) {
          fs.mkdirSync(configDir, { recursive: true })
        }

        // 写入配置文件
        fs.writeFileSync(configPath, JSON.stringify(configData, null, 2), 'utf8')
        logger.info(`已更新配置文件: ${configPath}，新的linkerApi: ${newLinkerApi}`)

        return { success: true }
      } catch (error) {
        logger.error('写入配置文件失败:', error)

        // 如果写入失败，尝试使用用户数据目录作为备选
        const fallbackPath = getFallbackConfigPath()

        // 确保父目录存在
        const fallbackDir = path.dirname(fallbackPath)
        if (!fs.existsSync(fallbackDir)) {
          fs.mkdirSync(fallbackDir, { recursive: true })
        }

        fs.writeFileSync(fallbackPath, JSON.stringify(configData, null, 2), 'utf8')
        logger.info(`已更新备选配置文件: ${fallbackPath}`)

        return {
          success: true,
          message: `无法写入应用程序目录，已保存到: ${fallbackPath}`,
        }
      }
    } catch (error) {
      logger.error('更新Linker API失败:', error)
      return { success: false, message: (error as Error).message || '未知错误' }
    }
  }

  /**
   * 获取环境变量
   */
  getEnvVars() {
    // 尝试从配置文件读取
    try {
      const configPath = getConfigPath()
      if (fs.existsSync(configPath)) {
        const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'))
        return {
          VITE_APP_LINKER_API: configData.linkerApi || process.env.VITE_APP_LINKER_API || '',
          VITE_APP_BASE_API: configData.baseApi || process.env.VITE_APP_BASE_API || '',
        }
      }
    } catch (error) {
      logger.error('读取配置文件失败:', error)
    }

    return {
      VITE_APP_LINKER_API: process.env.VITE_APP_LINKER_API || '',
      VITE_APP_BASE_API: process.env.VITE_APP_BASE_API || '',
    }
  }
}

// 导出单例实例
export const configService = new ConfigService()

// 为兼容性保留的函数
export function getLinkerApiFromConfig(): string | null {
  return configService.getLinkerApiFromConfig()
}

export function updateLinkerApiConfig(newLinkerApi: string): {
  success: boolean
  message?: string
} {
  return configService.updateLinkerApiConfig(newLinkerApi)
}

export function getEnvVars() {
  return configService.getEnvVars()
}
