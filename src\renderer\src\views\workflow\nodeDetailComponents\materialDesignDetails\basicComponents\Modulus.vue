<template>
  <div class="w-full h-full">
    <CardVue>
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center justify-center gap-2 text-2xl font-bold">
            <SvgIcon :name="svg.spring" />
            <span>模量</span>
          </div>
          <div class="flex gap-2">
            <Button size="sm" @click="selectStructure()">
              <LucideIcon name="Search" class="h-4 w-4" />
              <span>选择结构</span>
            </Button>
          </div>
        </div>
      </template>
      <form @submit="onSubmit">
        <FormField v-slot="{ componentField }" name="materialName">
          <FormItem class="flex justify-start items-center">
            <FormLabel class="w-[80px] truncate line-height-1 mt-[5px]">
              <span class="text-red-500 mr-1">*</span>
              <span class="text-sm">材料名称</span>
            </FormLabel>
            <FormControl class="w-[calc(100%-80px)]">
              <Input type="text" placeholder="材料名称" v-bind="componentField" disabled />
            </FormControl>
          </FormItem>
        </FormField>
        <FormField v-slot="{ componentField }" name="formula">
          <FormItem class="flex justify-start items-center">
            <FormLabel class="w-[80px] truncate line-height-1 mt-[5px]">
              <span class="text-red-500 mr-1">*</span>
              <span class="text-sm">化学式</span>
            </FormLabel>
            <FormControl class="w-[calc(100%-80px)]">
              <Input type="text" placeholder="化学式" v-bind="componentField" disabled />
            </FormControl>
          </FormItem>
        </FormField>
        <FormField v-slot="{ componentField }" name="computationModel">
          <FormItem class="flex justify-start items-center">
            <FormLabel class="w-[80px] truncate line-height-1 mt-[5px]">
              <span class="text-red-500 mr-1">*</span>
              <span class="text-sm">计算模型</span>
            </FormLabel>
            <FormControl class="w-[calc(100%-80px)] flex justify-start items-center">
              <div class="flex justify-start items-center">
                <div class="w-[calc(100%-80px)]">
                  <Select v-bind="componentField">
                    <SelectTrigger>
                      <SelectValue placeholder="请选择计算模型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectItem
                          v-for="(opt, i) in computationModelList"
                          :key="i"
                          :value="opt.value"
                        >
                          {{ opt.label }}
                        </SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
                <div class="w-[60px] pl-2">
                  <LucideIcon
                    v-tooltip="{ content: tooltip }"
                    name="CircleHelp"
                    :width="28"
                    :height="28"
                  />
                </div>
              </div>
            </FormControl>
          </FormItem>
        </FormField>
        <FormField v-slot="{ componentField }" name="optimizeMode">
          <FormItem class="flex justify-start items-center">
            <FormLabel class="w-[80px] truncate line-height-1 mt-[5px]">
              <span class="text-red-500 mr-1">*</span>
              <span class="text-sm">优化模式</span>
            </FormLabel>
            <FormControl class="w-[calc(100%-80px)]">
              <div class="flex justify-start items-center">
                <Select v-bind="componentField">
                  <SelectTrigger>
                    <SelectValue placeholder="请选择优化模式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <template v-for="(opt, i) in optimizerSchemeListsChgnet" :key="i">
                        <SelectItem v-if="values.computationModel === 'ml'" :value="opt">
                          {{ opt }}
                        </SelectItem>
                      </template>
                      <template v-for="(opt, o) in optimizerSchemeListsReaxnet" :key="o">
                        <SelectItem v-if="values.computationModel === 'rn'" :value="opt">
                          {{ opt }}
                        </SelectItem>
                      </template>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </FormControl>
          </FormItem>
        </FormField>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="resetFormValues()">重置</Button>
          <Button type="submit">
            <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
            <span>提交</span>
          </Button>
        </div>
      </form>
    </CardVue>
    <CardVue class="mt-6">
      <CardVue :title="'结果'" class="mt-6 shadow-md rounded-xl">
        <div class="min-h-[100px] flex justify-center items-center flex-col p-2 box-border">
          <div class="flex justify-center items-center">
            <span class="text-sm">模量</span>
            <Button
              variant="outline"
              size="icon"
              class="w-[36px] h-[24px] flex justify-center items-center ml-2"
              @click="copyResult"
            >
              <LucideIcon class="cursor-pointer" name="Copy" :width="8" :height="8" />
            </Button>
          </div>
          <div class="text-xl">
            <span>{{ resultVal }}Gpa</span>
          </div>
        </div>
      </CardVue>
    </CardVue>
    <SelectStructureDialog
      v-if="structureVisiable"
      v-model:visiable="structureVisiable"
      :form-data="selectForm"
      @ok="okStructure"
    />
  </div>
</template>
<script setup lang="ts">
import { LucideIcon, SvgIcon } from '@renderer/components'
import { createMaterialService } from '@renderer/config/api/grpc/materialService'
import svg from '@renderer/config/constants/svg'
import { toTypedSchema } from '@vee-validate/zod'
import { Loader2 } from 'lucide-vue-next'
import { useForm } from 'vee-validate'
import { onMounted, ref, Ref } from 'vue'
import { toast } from 'vue-sonner'
import * as z from 'zod'
import { CardVue, SelectStructureDialog } from '../../../components'
const structureVisiable: Ref<boolean> = ref(false)
const selectForm = ref({})
const selectStructure = () => {
  structureVisiable.value = true
}
let selectColumn: any = null
const okStructure = (ev: any) => {
  selectColumn = ev
  const fromData: any = selectColumn.selectItem
  setValues({
    materialName: fromData.name,
    formula: fromData.formula,
  })
}
// 表单内容
const computationModelList = [
  {
    label: '模型1',
    value: 'v1',
  },
  {
    label: '模型2',
    value: 'v2',
  },
]

const optimizerSchemeListsChgnet = ref([
  'BFGS',
  'BFGSLineSearch',
  'LBFGS',
  'LBFGSLineSearch',
  'GPMin',
  'MDMin',
  'FIRE',
])
const optimizerSchemeListsReaxnet = ref(['BFGS'])

const tooltip: string = '模型2经过预编译,可加快分子动力学计算'
const formSchema = toTypedSchema(
  z.object({
    materialName: z.string().optional(),
    formula: z.string().optional(),
    computationModel: z.string().optional(),
    optimizeMode: z.string().optional(),
  }),
)

const { handleSubmit, resetForm, setValues, values } = useForm({
  validationSchema: formSchema,
})
const resetFormValues = () => {
  resetForm({
    values: {
      materialName: '',
      formula: '',
      computationModel: '',
      optimizeMode: '',
    },
  })
}
const materialService = createMaterialService()
const resultVal: Ref<string> = ref('0.00')
const loading: Ref<boolean> = ref(false)
const onSubmit = handleSubmit(async (values: any) => {
  loading.value = true
  const res: any = await materialService.predictModulus(
    values.materialName,
    values.formula,
    values.optimizeMode,
  )
  if (res.statusCode === 200) {
    resultVal.value = res.keyValuePairs.progress
  }
  loading.value = false
})
// 复制结果
const copyResult = async () => {
  try {
    const text: string = `Bulk_modulus \n ${resultVal.value} Gpa`
    await navigator.clipboard.writeText(text)
    toast.success('模量', {
      duration: 1000,
      description: '复制成功，请到粘贴板中粘贴',
    })
  } catch (error) {
    console.error(error)
  }
}
const initValues = () => {
  setValues({
    materialName: '',
    formula: '',
    computationModel: 'v1',
    optimizeMode: 'BFGS',
  })
}
onMounted(() => {
  initValues()
})
</script>
<style scoped lang="scss"></style>
