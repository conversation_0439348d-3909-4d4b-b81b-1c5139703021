import handleCsvFile from '../csvFileWorker/handleCsvFile.mjs'
import handleUploadFile from '../csvFileWorker/handleUploadFile.mjs'
import readCsvFile from '../csvFileWorker/readCsvFile.mjs'

const conver1DArrayToCSV = (array) => {
  return Array.from(array)
    .map((value, index) => `${index + 1},${value}`)
    .join('\n')
}
export function analysisUploadFile(data) {
  const filePath = data.filePath
  const chunkSize = 1024 * 1024 // 1MB
  const port = data.port
  readCsvFile(filePath, (file) => {
    // 读取csv文件
    if (file.type === 'data') {
      const fileData = file.data
      handleCsvFile(fileData, (cvsData) => {
        // 由于业务问题，这里需要上传两次，所以在给主线程发送信息的时候，需要做区分
        if (cvsData.type === 'data') {
          // 处理csv数据
          const newData = cvsData.data
          if (newData.type === 'default' || newData.type === 'other') {
            // 需要上传两部分数据
            if (newData.type === 'default') {
              // 默认符合格式文件
              handleUploadFile(filePath, 'file', chunkSize, (chunk) => {
                port.postMessage({
                  type: 'data',
                  chunk,
                })
              })
            } else if (newData.type === 'other') {
              // 新威，蓝电文件
              const upCvsData = newData.csv
              handleUploadFile(upCvsData, 'data', chunkSize, (chunk) => {
                port.postMessage({
                  type: 'data',
                  chunk,
                })
              })
            }
            // 统一上传圈数文件
            const capacityData = newData.capacity
            const capacityCsvData = 'cycle,capacity\n' + conver1DArrayToCSV(capacityData)
            handleUploadFile(capacityCsvData, 'data', chunkSize, (chunk) => {
              port.postMessage({
                type: 'capacity',
                chunk,
              })
            })
          } else if (newData.type === 'SOC-OCV') {
            // 需要上传SOC-OCV数据
            handleUploadFile(filePath, 'file', chunkSize, (chunk) => {
              port.postMessage({
                type: 'data',
                chunk,
              })
            })
          }
        }
      })
    }
  })
}
