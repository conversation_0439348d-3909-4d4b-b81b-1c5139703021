import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useToolsStore = defineStore(
  'tools',
  () => {
    // 工具状态 - 改为空对象，动态添加
    const toolsStatus = ref({})

    // 添加工具
    const addTool = (toolName: string, status: boolean = true) => {
      toolsStatus.value[toolName] = status
    }

    // 移除工具
    const removeTool = (toolName: string) => {
      if (toolsStatus.value[toolName] !== undefined) {
        delete toolsStatus.value[toolName]
      }
    }

    // 更新工具状态
    const updateToolStatus = (toolName: string, status: boolean) => {
      toolsStatus.value[toolName] = status
    }

    // 批量更新工具状态
    const updateToolsStatus = (newStatus: Record<string, boolean>) => {
      toolsStatus.value = { ...toolsStatus.value, ...newStatus }
    }

    // 获取工具状态
    const getToolStatus = (toolName: string) => {
      return toolsStatus.value[toolName] || false
    }

    return {
      toolsStatus,
      addTool,
      removeTool,
      updateToolStatus,
      updateToolsStatus,
      getToolStatus,
    }
  },
  {
    persist: true, // 启用持久化存储
  },
)
