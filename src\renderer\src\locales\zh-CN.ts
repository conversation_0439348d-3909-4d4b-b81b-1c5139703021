export default {
  //工作流
  workflows: {
    title: '工作流',
    tooltip: '工作流',
  },
  //计算任务
  tasks: {
    title: '计算任务',
    tooltip: '计算任务',
  },
  //服务器状态
  server: {
    title: '服务器状态',
    tooltip: '服务器状态',
  },
  //日志
  logger: {
    title: '日志',
    tooltip: '日志',
  },
  //工具
  tools: {
    title: '工具管理',
    tooltip: '工具管理',
  },
  //设置
  settings: {
    title: '设置',
    tooltip: '这是设置页面',
    //基本设置
    baseSetting: {
      title: '基本设置',
      theme: {
        title: '主题设置',
        description: '自定义应用的外观和主题',
        mode: '主题模式',
        light: '浅色',
        dark: '深色',
        system: '跟随系统',
        colors: {
          title: '主题色调',
          'city-light': '城市-浅色',
          'forest-light': '森林-浅色',
          'lake-light': '湖水-浅色',
          'desert-light': '沙漠-浅色',
          'farm-light': '农场-浅色',
          'garden-light': '花园-浅色',
          'city-dark': '城市-深色',
          'forest-dark': '森林-深色',
          'lake-dark': '湖水-深色',
          'desert-dark': '沙漠-深色',
          'farm-dark': '农场-深色',
          'garden-dark': '花园-深色',
        },
      },
      language: {
        title: '语言设置',
        description: '选择应用界面语言',
        select: '界面语言',
        placeholder: '选择语言',
      },
      // 字体设置
      font: {
        title: '字体设置',
        description: '选择应用界面字体',
        select: '界面字体',
        placeholder: '选择字体',
        preview: '预览效果',
        fonts: {
          AlibabaPuHuiTi: '阿里巴巴普惠体',
          AlibabaSansHK: '阿里巴巴香港字体',
          'NotoSansSC-Black': 'Noto Sans SC 黑体',
          'NotoSansSC-ExtraBold': 'Noto Sans SC 特粗体',
          'NotoSansSC-Light': 'Noto Sans SC 轻体',
          'NotoSansSC-Medium': 'Noto Sans SC 中等体',
          SourceHanSans: '思源黑体',
          SourceHanSansHC: '思源黑体 HC',
          SourceHanSansHK: '思源黑体 HK',
          SourceHanSansK: '思源黑体 K',
          SourceHanSansSC: '思源黑体 SC',
          SourceHanSansTC: '思源黑体 TC',
        },
      },
      fontSize: {
        title: '字号设置',
        description: '调整应用的文字大小',
        select: '选择字号',
        placeholder: '请选择字号',
        preview: '预览效果',
        sizes: {
          'extra-small': '小',
          small: '中等',
          medium: '大',
          large: '特大',
          'extra-large': '最大',
        },
      },
      // 缓存设置
      cache: {
        title: '缓存设置',
        description: '管理应用缓存数据，清除不需要的缓存',
        select: '选择要清除的缓存',
        selectAll: '全选',
        deselectAll: '取消全选',
        clearSelected: '清除选中',
        clearAll: '清除所有',
        confirmClearSelected: '确认清除选中缓存',
        confirmClearSelectedDesc: '您确定要清除以下选中的缓存吗？这将重置相关设置。',
        confirmClearAll: '确认清除所有缓存',
        confirmClearAllDesc: '您确定要清除所有缓存吗？这将重置所有应用设置和数据。',
        warning: '警告',
        warningDesc: '清除所有缓存将重置应用到初始状态，包括所有工作流、设置和偏好。',
        cancel: '取消',
        confirm: '确认清除',
        clearSuccess: '清除成功',
        clearSuccessDesc: '已成功清除选中的缓存，页面将自动刷新。',
        clearAllSuccess: '清除成功',
        clearAllSuccessDesc: '已成功清除所有缓存，页面将自动刷新。',
        clearPartial: '部分清除成功',
        clearSuccessCount: '项清除成功',
        clearFailCount: '项清除失败',
        types: {
          title: '存储类型',
          localStorage: '本地存储',
          sessionStorage: '会话存储',
          cookies: 'Cookie',
          indexedDB: 'IndexedDB 数据库',
          applicationCache: '应用缓存',
          serviceWorkers: 'Service Workers',
        },
        typesDesc: {
          localStorage: '存储长期保存的数据，如用户偏好设置',
          sessionStorage: '存储临时会话数据，浏览器关闭后清除',
          cookies: '网站存储在浏览器中的小型数据',
          indexedDB: '客户端数据库，用于存储大量结构化数据',
          applicationCache: '离线访问应用的缓存机制',
          serviceWorkers: '后台脚本，用于缓存和拦截网络请求',
        },
        items: {
          title: '缓存项目',
          theme: '主题设置',
          themeDesc: '应用主题和颜色偏好',
          language: '语言设置',
          languageDesc: '界面语言偏好',
          font: '字体设置',
          fontDesc: '应用字体偏好',
          fontSize: '字号设置',
          fontSizeDesc: '应用字体大小偏好',
          workflow: '工作流',
          workflowDesc: '工作流文件夹和基本信息',
          flows: '流程节点',
          flowsDesc: '流程图节点和连线数据',
          navbar: '导航栏',
          navbarDesc: '工作流导航标签',
          tools: '工具设置',
          toolsDesc: '工具状态和配置',
        },
      },
      // 文件加密设置
      fileEncrypt: {
        title: '文件加密处理',
        description: '上传JSON文件并进行加密处理，导出为模型或插件文件',
        uploadTitle: '点击或拖拽上传JSON文件',
        uploadDescription: '支持 .json 格式文件',
        uploadedFiles: '已上传文件',
        uploadSuccess: '文件上传成功',
        uploadFailed: '文件上传失败',
        invalidJson: '无效的JSON文件',
        fileRemoved: '文件已删除',
        exportTitle: '选择导出类型',
        exportDescription: '请选择要导出的文件类型',
        modelFile: '模型文件',
        modelFileDesc: '导出为.model格式',
        pluginFile: '工具文件',
        pluginFileDesc: '导出为.plugin格式',
        cancel: '取消',
        confirm: '确认',
        saveFileTitle: '保存文件',
        exportSuccess: '导出成功',
        exportFailed: '导出失败',
      },
    },
    //基本设置
    middlePlatformSetting: {
      title: '中台设置',
      address: {
        title: '中台服务地址',
        description: '设置连接到中台服务的地址，修改后需要重启应用',
        input: '服务地址',
        placeholder: '请输入中台服务地址，例如: 127.0.0.1:9999',
        save: '保存',
        saveSuccess: '保存成功',
        saveFailed: '保存失败',
        invalidUrl: '无效的地址',
        pleaseEnterValidUrl: '请输入有效的服务器地址和端口',
        unknownError: '发生未知错误',
        currentStatus: '当前连接状态',
        connected: '已连接',
        disconnected: '未连接',
        currentAddress: '当前服务地址',
        confirmTitle: '确认修改服务地址',
        confirmDescription: '您确定要将服务地址修改为:',
        restartWarning: '修改后应用将自动重启以应用新设置',
        restartNow: '应用将在2秒后重启',
        cancel: '取消',
        confirm: '确认修改',
      },
      server: {
        version: '中台版本',
      },
    },
    //流程图设置
    flowSetting: {
      title: '流程图设置',
      viewport: {
        title: '视图设置',
        description: '配置流程图的视图和缩放相关选项',
        defaultViewport: '默认视口',
        xCoordinate: 'X 坐标',
        yCoordinate: 'Y 坐标',
        zoomRatio: '缩放比例',
        zoomRange: '缩放范围',
        minZoom: '最小缩放',
        maxZoom: '最大缩放',
      },
      edge: {
        title: '连线设置',
        description: '配置流程图连线的样式和行为',
        type: '线条类型',
        typePlaceholder: '选择线条类型',
        types: {
          default: '默认',
          straight: '直线',
          step: '步进',
          smoothstep: '平滑步进',
          bezier: '贝塞尔曲线',
        },
        animation: '启用线条动画',
        arrow: '显示连线箭头',
        color: '线条颜色',
        width: '线条宽度',
      },
      alignment: {
        title: '对齐设置',
        description: '配置节点对齐和网格相关选项',
        snapToLines: '显示对齐辅助线',
        snapToGrid: '启用对齐网格',
        gridSpacingX: 'X轴间距',
        gridSpacingY: 'Y轴间距',
      },
      interface: {
        title: '界面元素',
        description: '配置流程图界面元素的显示',
        fitView: '自动适应视图大小',
        showMiniMap: '显示小地图',
        showLeftControls: '显示左侧控制器',
        showRightControls: '显示右侧控制器',
      },
      miniMap: {
        title: '小地图样式',
        description: '配置小地图的显示样式',
        backgroundColor: '背景颜色',
        nodeStrokeColor: '节点边框颜色',
        nodeStrokeWidth: '节点边框宽度',
      },
      background: {
        title: '背景样式',
        description: '配置流程图背景的显示样式',
        patternColor: '图案颜色',
        gap: '间距',
        size: '大小',
        variant: '图案类型',
        variantPlaceholder: '选择图案类型',
        variants: {
          dots: '点状',
          lines: '线状',
          cross: '网格',
        },
      },
    },
    //关于
    about: {
      title: '关于',
    },
  },
}
