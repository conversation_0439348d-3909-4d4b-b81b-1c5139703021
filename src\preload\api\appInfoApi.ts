import os from 'os'
import logger from '../utils/logger'
/**
 * 应用信息相关API
 * 提供应用类型、默认路径等信息
 */
export const appInfoApi = {
  /**
   * 获取应用类型
   */
  isMatt: process.env.VITE_APP_IS_MATT === '1',

  /**
   * 获取默认工作流ID
   */
  defaultWorkflowId: 'workflow-default',

  /**
   * 获取默认路由路径
   */
  getDefaultRoutePath: () => {
    const isMatt = process.env.VITE_APP_IS_MATT === '1'
    return isMatt ? '/' : `/workflow/editor/workflow-default`
  },
  /**
   * 获取本机IP地址
   * @returns {string} 本机IP地址
   */
  getLocalIp: (): string => {
    try {
      const networkInterfaces = os.networkInterfaces()
      // 查找非内部IP地址
      for (const name of Object.keys(networkInterfaces)) {
        for (const net of networkInterfaces[name] || []) {
          // 跳过内部IP和非IPv4地址
          if (!net.internal && net.family === 'IPv4') {
            return net.address
          }
        }
      }
      // 如果没有找到非内部IPv4地址，返回本地回环地址
      return '127.0.0.1'
    } catch (error) {
      logger.error('获取本机IP失败:', error)
      return '127.0.0.1'
    }
  },
}
