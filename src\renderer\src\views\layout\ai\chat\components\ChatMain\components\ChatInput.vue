<template>
  <div :class="[welcomeMode ? 'px-0' : 'px-6 py-4 flex-shrink-0']">
    <div
      class="relative flex items-center rounded-md border border-gray-200 bg-white"
      :class="welcomeMode ? 'shadow-md' : ''"
    >
      <Textarea
        id="chat-input"
        ref="chatInputRef"
        v-model="inputMessage"
        class="flex-1 py-8 resize-none border-none focus-visible:ring-0 focus-visible:outline-none min-h-[64px] placeholder:text-gray-400 text-gray-800"
        :placeholder="
          welcomeMode ? '请输入您的问题，我将为您提供科研支持...' : '在这里输入您的问题...'
        "
        @input="adjustTextareaHeight"
        @keydown="handleKeydown"
      />

      <!-- 左侧工具按钮 - 移至左下角 -->
      <div class="absolute left-3 bottom-3 flex items-center space-x-1">
        <!-- 上传文件按钮 -->
        <Button
          variant="ghost"
          size="sm"
          class="text-gray-500 hover:text-gray-700 rounded-full p-1"
        >
          <LucideIcon name="Paperclip" class="w-5 h-5" />
        </Button>

        <!-- 语音输入按钮 -->
        <Button
          variant="ghost"
          size="sm"
          class="text-gray-500 hover:text-gray-700 rounded-full p-1"
        >
          <LucideIcon name="Mic" class="w-5 h-5" />
        </Button>
      </div>

      <!-- 右侧：发送按钮和其他功能按钮 -->
      <div class="absolute right-3 bottom-4 flex items-center space-x-2">
        <!-- 知识库按钮 - 在非浮动模式下显示 -->
        <Button
          v-if="!isFloating"
          variant="ghost"
          size="sm"
          class="text-gray-500 hover:text-gray-700 rounded-full p-1"
          @click="openKnowledgeReference"
        >
          <LucideIcon name="BookOpen" class="w-5 h-5" />
        </Button>

        <!-- 设置按钮 - ChatSettings组件 -->
        <ChatSettings
          :model-temperature="modelTemperature"
          :stream-enabled="streamEnabled"
          :is-floating="isFloating"
          :selected-server-id="selectedServerId"
          :selected-model="selectedModel"
          :running-servers="runningServers"
          :model-groups="modelGroups"
          :is-loading-models="isLoadingModels"
          @update:temperature="updateTemperature"
          @update:stream-enabled="updateStreamEnabled"
          @update:selected-server-id="updateSelectedServerId"
          @update:selected-model="updateSelectedModel"
          @server-change="handleServerChange"
        />

        <!-- 发送按钮 -->
        <Button
          variant="default"
          class="bg-black hover:bg-gray-800 text-white rounded-md px-4 py-1.5 text-sm font-medium flex items-center"
          :disabled="!inputMessage.trim() && !aiChatStore.isGenerating"
          @click="handleButtonClick"
        >
          {{
            isFloating
              ? aiChatStore.isGenerating
                ? '停止'
                : '发送'
              : aiChatStore.isGenerating
                ? '停止'
                : '发送消息'
          }}
          <LucideIcon
            :name="aiChatStore.isGenerating ? 'Square' : 'CornerDownLeft'"
            class="w-4 h-4 ml-1"
          />
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'
import { createAIChatService } from '@renderer/config/api/grpc/aiChatService'
import { useAIChatStore } from '@renderer/store'
import { nextTick, ref, watch } from 'vue'
import ChatSettings from './ChatSettings.vue'

const props = defineProps({
  isWorkflowMode: {
    type: Boolean,
    default: false,
  },
  sending: {
    type: Boolean,
    default: false,
  },
  welcomeMode: {
    type: Boolean,
    default: false,
  },
  // 浮动窗口模式
  isFloating: {
    type: Boolean,
    default: false,
  },
  // 浮动窗口模式下的额外属性
  selectedServerId: {
    type: String,
    default: '',
  },
  selectedModel: {
    type: String,
    default: '',
  },
  runningServers: {
    type: Array,
    default: () => [],
  },
  modelGroups: {
    type: Object,
    default: () => ({}),
  },
  modelTemperature: {
    type: Array,
    default: () => [0.7],
  },
  isLoadingModels: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'send-message',
  'open-knowledge-reference',
  'open-settings',
  'update:temperature',
  'update:stream-enabled',
  'update:selectedServerId',
  'update:selectedModel',
  'server-change',
])

const aiChatStore = useAIChatStore()
const aiChatService = createAIChatService()

const inputMessage = ref('')
const chatInputRef = ref(null)
const modelTemperature = ref(props.modelTemperature)
const streamEnabled = ref(true)

// 调整文本框高度
const adjustTextareaHeight = () => {
  nextTick(() => {
    const textarea = chatInputRef.value?.$el
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(Math.max(64, textarea.scrollHeight), 200)}px`
    }
  })
}
// 处理按钮点击
const handleButtonClick = () => {
  if (aiChatStore.isGenerating) {
    // 如果正在生成，则停止生成
    aiChatService.stopMessageGeneration()
  } else {
    // 否则发送消息
    if (inputMessage.value.trim()) {
      submitUserMessage()
    }
  }
}
// 提交用户消息
const submitUserMessage = async () => {
  const content = inputMessage.value.trim()
  if (!content || props.sending) return

  emit('send-message', content)
  inputMessage.value = ''
  adjustTextareaHeight()

  // 聚焦输入框
  await nextTick()
  chatInputRef.value?.$el.focus()
}

// 打开知识库引用
const openKnowledgeReference = () => {
  emit('open-knowledge-reference')
}

// 打开设置
const openSettings = () => {
  emit('open-settings')
}

// 更新温度设置
const updateTemperature = (value) => {
  modelTemperature.value = value
  emit('update:temperature', value)
}

// 更新流式输出设置
const updateStreamEnabled = (value) => {
  streamEnabled.value = value
  emit('update:stream-enabled', value)
}

// 浮动模式下的更新服务器ID
const updateSelectedServerId = (value) => {
  emit('update:selectedServerId', value)
}

// 浮动模式下的更新模型
const updateSelectedModel = (value) => {
  emit('update:selectedModel', value)
}

// 浮动模式下的服务器选择处理
const handleServerChange = (serverId) => {
  emit('server-change', serverId)
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    submitUserMessage()
  }
}

// 设置输入框内容
const setInputMessage = (message: string) => {
  inputMessage.value = message
  adjustTextareaHeight()

  // 聚焦输入框
  nextTick(() => {
    chatInputRef.value?.$el.focus()
  })
}

watch(
  () => props.modelTemperature,
  (newVal) => {
    modelTemperature.value = newVal
  },
  { deep: true },
)

// 暴露方法供父组件调用
defineExpose({
  setInputMessage,
})
</script>

<style lang="scss" scoped>
:deep(textarea) {
  box-shadow: none !important;
  background-color: transparent;
}
</style>
