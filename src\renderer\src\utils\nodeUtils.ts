import { useFlowsStore } from '@renderer/store'

// 类型定义
interface NodeData {
  id: string
  data?: {
    workflowId?: string
    type?: string
    params?: any
    [key: string]: any
  }
  [key: string]: any
}

interface NodeWithParams {
  node: NodeData
  params: any
  type?: string
}

/**
 * 获取节点参数
 * @param nodeData 节点数据
 * @returns Promise<节点参数>
 */
export async function getNodeParams(nodeData: NodeData): Promise<any> {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return {}

  try {
    const params = await flowsStore.getNodeParams(workflowId, nodeId)
    return params || {}
  } catch (error) {
    console.error('获取节点参数失败:', error)
    return {}
  }
}

/**
 * 保存节点参数
 * @param nodeData 节点数据
 * @param params 要保存的参数
 * @returns Promise<boolean> 是否保存成功
 */
export async function saveNodeParams(nodeData: NodeData, params: any): Promise<boolean> {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return false

  try {
    // 保存到数据库
    await flowsStore.saveNodeParams(workflowId, nodeId, params)

    // 更新节点数据中的缓存
    if (nodeData.data) {
      nodeData.data.params = params
    }

    return true
  } catch (error) {
    console.error('保存节点参数失败:', error)
    return false
  }
}

/**
 * 删除节点参数
 * @param nodeData 节点数据
 * @returns Promise<boolean> 是否成功删除
 */
export async function deleteNodeParams(nodeData: NodeData): Promise<boolean> {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return false

  try {
    // 从数据库中删除
    await flowsStore.deleteNodeParams(workflowId, nodeId)

    // 清空节点数据中的参数缓存
    if (nodeData.data) {
      nodeData.data.params = {}
    }

    return true
  } catch (error) {
    console.error('删除节点参数失败:', error)
    return false
  }
}

/**
 * 获取节点的输入节点
 * @param nodeData 节点数据
 * @returns Promise<输入节点数组>
 */
export async function getInputNodes(nodeData: NodeData): Promise<NodeWithParams[]> {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return []

  try {
    const workflow = flowsStore.getWorkflow(workflowId)
    if (!workflow || !workflow.edges || !workflow.nodes) return []

    const inputEdges = workflow.edges.filter((e: any) => e.target === nodeId)

    // 并行获取所有输入节点的参数
    const inputNodesPromises = inputEdges.map(async (edge: any) => {
      const sourceNodeId = edge.source
      const sourceNode = workflow.nodes.find((n: any) => n.id === sourceNodeId)

      if (!sourceNode) return null

      try {
        const params = await flowsStore.getNodeParams(workflowId, sourceNodeId)
        return {
          node: sourceNode,
          params: params || {},
          type: sourceNode.data?.type,
        }
      } catch (error) {
        console.error(`获取输入节点 ${sourceNodeId} 参数失败:`, error)
        return {
          node: sourceNode,
          params: {},
          type: sourceNode.data?.type,
        }
      }
    })

    const results = await Promise.all(inputNodesPromises)
    return results.filter(Boolean) as NodeWithParams[]
  } catch (error) {
    console.error('获取输入节点失败:', error)
    return []
  }
}

/**
 * 获取节点的输出节点
 * @param nodeData 节点数据
 * @returns Promise<输出节点数组>
 */
export async function getOutputNodes(nodeData: NodeData): Promise<NodeWithParams[]> {
  const flowsStore = useFlowsStore()
  const { id: nodeId, data } = nodeData
  const workflowId = data?.workflowId

  if (!workflowId || !nodeId) return []

  try {
    const workflow = flowsStore.getWorkflow(workflowId)
    if (!workflow || !workflow.edges || !workflow.nodes) return []

    const outputEdges = workflow.edges.filter((e: any) => e.source === nodeId)

    // 并行获取所有输出节点的参数
    const outputNodesPromises = outputEdges.map(async (edge: any) => {
      const targetNodeId = edge.target
      const targetNode = workflow.nodes.find((n: any) => n.id === targetNodeId)

      if (!targetNode) return null

      try {
        const params = await flowsStore.getNodeParams(workflowId, targetNodeId)
        return {
          node: targetNode,
          params: params || {},
          type: targetNode.data?.type,
        }
      } catch (error) {
        console.error(`获取输出节点 ${targetNodeId} 参数失败:`, error)
        return {
          node: targetNode,
          params: {},
          type: targetNode.data?.type,
        }
      }
    })

    const results = await Promise.all(outputNodesPromises)
    return results.filter(Boolean) as NodeWithParams[]
  } catch (error) {
    console.error('获取输出节点失败:', error)
    return []
  }
}

/**
 * 按类型获取输入节点
 * @param nodeData 节点数据
 * @param type 节点类型
 * @returns Promise<指定类型的输入节点>
 */
export async function getInputNodesByType(
  nodeData: NodeData,
  type: string,
): Promise<NodeWithParams[]> {
  const inputs = await getInputNodes(nodeData)
  return inputs.filter((input) => input.type === type)
}

/**
 * 按类型获取输出节点
 * @param nodeData 节点数据
 * @param type 节点类型
 * @returns Promise<指定类型的输出节点>
 */
export async function getOutputNodesByType(
  nodeData: NodeData,
  type: string,
): Promise<NodeWithParams[]> {
  const outputs = await getOutputNodes(nodeData)
  return outputs.filter((output) => output.type === type)
}

/**
 * 检查两个节点是否可以连接
 * @param sourceNode 源节点
 * @param targetNode 目标节点
 * @returns 是否可连接
 */
export function canNodesConnect(sourceNode: NodeData, targetNode: NodeData): boolean {
  if (!sourceNode?.data || !targetNode?.data) return false

  const sourceType = sourceNode.data.type
  const targetInputTypes = targetNode.data.inputType || []

  return targetInputTypes.includes(sourceType)
}

/**
 * 更新节点数据
 * @param nodeData 节点数据
 * @param newData 新数据
 */
export function updateNodeData(nodeData: NodeData, newData: any): boolean {
  if (!nodeData || !nodeData.data) return false

  // 合并数据
  Object.assign(nodeData.data, newData)

  // 获取工作流ID和节点ID
  const workflowId = nodeData.data.workflowId
  const nodeId = nodeData.id

  // 如果有工作流ID，将更改保存到flows存储
  if (workflowId) {
    const flowsStore = useFlowsStore()
    const workflow = flowsStore.getWorkflow(workflowId)

    if (workflow && workflow.nodes) {
      // 找到节点并更新其数据
      const nodeIndex = workflow.nodes.findIndex((node: any) => node.id === nodeId)
      if (nodeIndex !== -1) {
        // 更新节点数据
        workflow.nodes[nodeIndex].data = {
          ...workflow.nodes[nodeIndex].data,
          ...newData,
        }

        // 使用现有的saveWorkflow方法保存整个工作流
        flowsStore.saveWorkflow(workflowId, workflow)
      }
    }
  }

  return true
}

/**
 * 获取工作流中的所有节点
 * @param workflowId 工作流ID
 * @returns 节点数组
 */
export function getAllNodes(workflowId: string): NodeData[] {
  const flowsStore = useFlowsStore()
  const workflow = flowsStore.getWorkflow(workflowId)

  if (!workflow || !workflow.nodes) return []

  return workflow.nodes
}

/**
 * 获取工作流中的所有连接
 * @param workflowId 工作流ID
 * @returns 连接数组
 */
export function getAllEdges(workflowId: string): any[] {
  const flowsStore = useFlowsStore()
  const workflow = flowsStore.getWorkflow(workflowId)

  if (!workflow || !workflow.edges) return []

  return workflow.edges
}

/**
 * 添加节点监听函数
 * @param callback 回调函数
 */
export function addNodeListener(callback: (...args: any[]) => void): void {
  const flowsStore = useFlowsStore()
  flowsStore.listenNodeLink(callback)
}

export type { NodeData, NodeWithParams }
