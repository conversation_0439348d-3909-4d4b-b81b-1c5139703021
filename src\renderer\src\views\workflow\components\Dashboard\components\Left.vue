<template>
  <div class="h-full">
    <div class="flex items-center flex-col px-2">
      <div class="left-btn w-full text-center">
        <Button class="w-full text-white" @click="handleOpenWorkflow">新建工作流</Button>
      </div>
      <Separator class="my-4" />
      <div class="folders flex flex-col h-[calc(100vh-216px)] w-full cursor-pointer">
        <!-- 文件夹标题 -->
        <div class="folder-title flex justify-between text-muted-foreground">
          <div>Folders</div>
          <div
            class="flex items-center cursor-pointer hover:text-foreground transition-colors"
            @click="openCreateFolderModal"
          >
            <Icon icon="flowbite:plus-outline" :width="16" :height="16" />
            新建
          </div>
        </div>

        <!-- 文件夹列表 -->
        <div class="folder-item scrollbar flex-1 overflow-y-auto">
          <div
            v-for="folder in folders"
            :key="folder.id"
            class="folder-item-content flex flex-row items-center justify-between hover:bg-accent/50 rounded-md p-2 my-2 group max-w-full transition-colors"
            :class="{ 'bg-primary/10 dark:bg-primary/20': currentFolderId === folder.id }"
            @click="handleSelectFolder(folder.id)"
          >
            <div class="flex items-center min-w-0 flex-1">
              <div class="flex-shrink-0">
                <Icon
                  icon="flowbite:folder-outline"
                  :width="24"
                  :height="24"
                  class="text-foreground"
                />
              </div>
              <div class="ml-2 min-w-0 flex-1">
                <span class="truncate block w-full text-foreground">{{ folder.name }}</span>
              </div>
            </div>
            <!-- 操作按钮 -->
            <div
              v-if="folder.id !== 'all'"
              class="flex-shrink-0 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            >
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <button
                    class="focus:outline-none text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <Icon icon="flowbite:dots-horizontal-outline" :width="24" :height="24" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem @click="handelDropdown('download', folder)">
                    <FileDown class="mr-2 h-4 w-4" />
                    <span>导出</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="handelDropdown('edit', folder)">
                    <Edit2 class="mr-2 h-4 w-4" />
                    <span>编辑</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    class="text-destructive hover:text-destructive hover:bg-destructive/50"
                    @click="handelDropdown('delete', folder)"
                  >
                    <Trash class="mr-2 h-4 w-4" />
                    <span>删除</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </div>
    <FolderDialog ref="folderDialogRef" @confirm="handleFolderAction" />
    <WorkflowDialog ref="workflowDialogRef" width="40%" @confirm="handleWfAction" />

    <AlertDialog v-model:open="isDeleteDialogOpen">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除文件夹 "{{ folderToDelete?.name }}"
            吗？此操作将删除文件夹及其中的所有工作流，且无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            @click="confirmDeleteFolder"
          >
            删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue'

import { useWorkflowStore } from '@renderer/store'
import { storeToRefs } from 'pinia'

import { Icon } from '@iconify/vue'
import { FolderDialog, WorkflowDialog } from '@renderer/components'
import { useDebounceFn } from '@vueuse/core'
import { Edit2, FileDown, Trash } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
interface Folder {
  id: string
  name: string
}
const workflowDialogRef = ref<InstanceType<typeof WorkflowDialog>>()
const folderDialogRef = ref<InstanceType<typeof FolderDialog>>()
const workflowStore = useWorkflowStore()

const { folders, currentFolderId } = storeToRefs(workflowStore)
//删除确认对话框的状态
const isDeleteDialogOpen = ref(false)
const folderToDelete = ref<Folder | null>(null)

const openCreateFolderModal = () => {
  folderDialogRef.value?.open('add')
}
const handleOpenWorkflow = () => {
  workflowDialogRef.value?.open('add')
}
const handleFolderAction = useDebounceFn(async (folderName: string, type: string) => {
  console.log('folderName', folderName, type)

  try {
    if (folderName) {
      if (type === 'edit') {
        // 编辑文件夹
        await workflowStore.editFolder(currentFolderId.value, folderName)
      } else {
        // 新建文件夹
        await workflowStore.addFolder(folderName)
      }
    } else {
      toast.warning('请输入名称')
    }
  } catch {
    throw new Error('新建文件夹失败')
  }
}, 300)

const handleWfAction = useDebounceFn(async (form: { name: string; description: string }) => {
  try {
    if (form.name) {
      await workflowStore.addWorkflow({
        title: form.name,
        description: form.description,
        folderId: currentFolderId.value,
      })
    } else {
      toast.warning('请输入名称')
    }
  } catch (error) {
    throw new Error('新建工作流失败')
  }
}, 300)

const handleSelectFolder = (folderId: string) => {
  workflowStore.currentFolderId = folderId
}
const handleDeleteFolder = async (folderId: string) => {
  workflowStore.deleteFolder(folderId)
}
// 确认删除文件夹
const confirmDeleteFolder = () => {
  if (folderToDelete.value) {
    handleDeleteFolder(folderToDelete.value.id)
    isDeleteDialogOpen.value = false
    folderToDelete.value = null
  }
}
const handelDropdown = useDebounceFn((val, folder) => {
  console.log('folder', folder)
  switch (val) {
    case 'download':
      // 处理导出
      break
    case 'edit':
      nextTick(() => {
        folderDialogRef.value?.open('edit', folder.name)
      })
      break
    case 'delete':
      // handleDeleteFolder(folder.id)
      folderToDelete.value = folder
      isDeleteDialogOpen.value = true
      break
  }
}, 300)
</script>
<style lang="scss"></style>
