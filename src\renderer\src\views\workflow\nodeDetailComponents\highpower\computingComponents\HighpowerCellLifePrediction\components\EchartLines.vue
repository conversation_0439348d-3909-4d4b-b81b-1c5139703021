<template>
  <div class="w-full h-full relative">
    <div ref="chartContainer" class="w-full h-full"></div>
    <div class="absolute top-0 left-0 w-full flex justify-center items-center">
      <div
        v-for="(item, idx) in legendList"
        :key="idx"
        class="flex justify-center items-center mr-2"
      >
        <div
          v-if="item.type === 'line'"
          class="w-[20px] h-[2px] bg-gray-150 mr-1 rounded-sm"
          :style="{ backgroundColor: item.color }"
        ></div>
        <div v-if="item.type === 'point'" class="mr-1 line-clamp-1" :style="{ color: item.color }">
          *
        </div>
        <div class="text-xs">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onUnmounted, Ref, ref, watch } from 'vue'
const props = defineProps({
  // data: {
  //   type: Object,
  // },
  markPoint: {
    type: Object,
  },
  xMarkPoint: {
    type: Object,
  },
})
// watch(
//   () => props.data,
//   (newVal) => {
//     addValueToEchart(newVal)
//   },
// )
// watch(
//   () => props.markPoint,
//   (newVal) => {
//     addMarkPointToEchart(newVal)
//   },
// )
watch(
  () => props.xMarkPoint,
  (newVal) => {
    addXMarkLineToEchart(newVal)
  },
)
const setChartData = (data: any) => {
  if (chart) {
    addValueToEchart(data)
  }
}
const setChartMarkPoint = (point: any) => {
  if (chart) {
    addMarkPointToEchart(point)
  }
}
const visualMapRange: any = [0, 0]
const addValueToEchart = (data: any) => {
  if (chart) {
    const option: any = chart.getOption()
    option.series[0].data = data.value
    const yAixs = data.value.map((m) => m[1])
    let maxValue = Math.max(...yAixs)
    // const yMax = Math.ceil(maxValue / 100) * 100
    // option.yAxis[0].min = 0
    maxValue = Math.ceil(maxValue * 100) / 100
    option.yAxis[0].max = maxValue
    // option.yAxis[0].interval = yMax / 5
    // option.series[1].data = data.ratio
    // console.log(data, '----------->>>>>')
    // option.xAxis[0].data = data.xData
    // const maxXAixs = data.value[data.value.length - 1][0]
    // option.xAxis[0].axisLabel.formatter = function (value: any) {
    //   console.log(value, '------------', maxXAixs)
    //   if (value < maxXAixs) {
    //     return value
    //   } else {
    //     return ''
    //   }
    // }
    if (data.value.length > 0) {
      const minX = data.value[0][0]
      const maxX = data.value[data.value.length - 1][0]
      visualMapRange[0] = minX
      visualMapRange[1] = maxX
    }
    chart.setOption(option)
    addXMarkLineToEchart(props.xMarkPoint)
  }
}
const pointSeries: any = {
  // 独立点系列
  yAxisIndex: 0,
  data: [],
  type: 'scatter',
  symbol: 'circle',
  symbolSize: 0,
  symbolOffset: [0, 0],
  label: {
    show: true,
    formatter: '*',
    color: '#ff0000',
    fontSize: 24,
    position: [-6, -5],
    lineHeight: 24,
  },
  markPoint: {
    data: [
      {
        coord: [],
        symbolSize: 0,
        symbolOffset: [0, -12],
        label: {
          formatter: '',
        },
      },
    ],
  },
  // markLine: {
  //   silent: true, // 不触发事件
  //   symbol: 'none', // 无箭头
  //   lineStyle: { width: 1, type: 'dashed', color: '#ff0000' },
  //   data: [],
  // },
  markLine: {
    symbol: 'none', // 不显示箭头
    lineStyle: {
      type: 'dashed', // 虚线样式
      color: '#ff0000', // 虚线颜色
    },
    data: [],
  },
}
const addMarkPointToEchart = (point: any) => {
  if (point && point.length > 0 && chart) {
    const option: any = chart.getOption()
    pointSeries.data = [point]
    // console.log(pointSeries.data)
    pointSeries.markLine.data = [{ yAxis: point[1], label: { formatter: `80%`, show: false } }]
    pointSeries.markPoint.data[0].coord = point
    pointSeries.markPoint.data[0].label.formatter = `${point[0]}`
    // const minX = option.xAxis[0].min
    option.series.push(pointSeries)
    chart.setOption(option)
  }
}
const addXMarkLineToEchart = (xdata: any) => {
  if (xdata && xdata.length > 0 && chart) {
    const option: any = chart.getOption()
    // const markListData: any = []
    // // const xData = option.xAxis[0].data
    // xdata.forEach((element: any) => {
    //   // const index = xData.indexOf(element)
    //   markListData.push({ xAxis: element, label: { formatter: `${element}` } })
    // })
    if (visualMapRange[1] > visualMapRange[0]) {
      option.visualMap[0].min = visualMapRange[0]
      option.visualMap[0].max = visualMapRange[1]
    }
    option.visualMap[0].range = xdata
    // option.series[0].markLine.data = markListData
    chart.setOption(option)
  }
}
const chartContainer = ref<HTMLDivElement>()
onMounted(() => {
  initChart()
})
const legendList: Ref<any> = ref([
  { name: '实验数据', color: '#5571C6', type: 'line' },
  { name: '使用数据', color: '#ff0000', type: 'line' },
  { name: '预测终点', color: '#ff0000', type: 'point' },
])
const option = {
  title: {
    text: '电池循环容量',
    left: 'center',
    top: '10%',
    textStyle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#333',
    },
  },
  tooltip: {
    trigger: 'axis',
    formatter: function (params) {
      let str: any = ''
      const valNammes: any = ['循环圈数', '容量(Ah)', '容量(%)']
      const data = params[0]?.data
      for (let i = 0; i < data.length; i++) {
        const item = Math.round(data[i] * 10000000) / 10000000
        str += `${valNammes[i]}: ${item}<br>`
      }
      return str
    },
  },
  grid: {
    left: '7%',
    right: '7%',
    bottom: '10%',
    top: '28%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    name: '循环圈数',
    nameLocation: 'middle',
    // data: [],
    nameGap: 30,
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      show: true,
      align: 'center',
      interval: 0,
      // showMinLabel: true, // 强制显示第一个标签
      // showMaxLabel: true,
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '容量(Ah)',
      nameLocation: 'end',
      nameGap: 22,
      nameTextStyle: {
        padding: [0, 0, 0, 0],
        align: 'center',
        fontSize: 14,
        color: '#666',
      },
    },
    {
      type: 'value',
      name: '容量(%)',
      min: 0,
      max: 100,
      nameLocation: 'end',
      nameGap: 22,
      nameTextStyle: {
        padding: [0, 0, 0, 0],
        align: 'center',
        fontSize: 14,
        color: '#666',
      },
    },
  ],
  visualMap: {
    type: 'continuous', // 分段型 visualMap
    dimension: 0,
    range: [0, 0],
    show: false,
    inRange: {
      color: '#ff0000',
    },
    outOfRange: {
      // 不在指定区间(4-5)的部分使用固定颜色
      color: '#5571C6',
    },
    // orient: 'horizontal',
    // left: 'center',
    // top: 10,
    // textStyle: {
    //   fontSize: 12,
    // },
  },
  series: [
    {
      name: '容量(Ah)',
      type: 'line',
      smooth: true,
      data: [],
      symbol: 'none',
      yAxisIndex: 0,
      // itemStyle: { color: colors[index % colors.length] },
      sampling: 'lttb',
      markPoint: {},
      markLine: {
        silent: true, // 不触发事件
        symbol: 'none', // 无箭头
        lineStyle: { width: 1, type: 'dashed' },
        data: [],
      },
    },
    // {
    //   name: '容量(%)',
    //   type: 'line',
    //   smooth: true,
    //   data: [],
    //   symbol: 'none',
    //   yAxisIndex: 1,
    //   // itemStyle: { color: colors[index % colors.length] },
    //   sampling: 'lttb',
    //   markPoint: {},
    //   markLine: {
    //     silent: true, // 不触发事件
    //     symbol: 'none', // 无箭头
    //     lineStyle: { width: 1, type: 'dashed' },
    //     data: [],
    //   },
    // },
  ],
  dataZoom: [
    {
      type: 'inside',
      start: 0,
      end: 100,
      zoomLock: false,
      zoomOnMouseWheel: true,
      moveOnMouseMove: true,
      moveOnMouseWheel: false,
      preventDefaultMouseMove: true,
    },
    {
      type: 'slider',
      show: false,
      start: 0,
      end: 100,
      height: 12,
      bottom: 1,
    },
  ],
  animation: false,
}

let chart: any = null
const observer: any = new ResizeObserver(() => {
  echartResize()
})
const initChart = () => {
  if (chartContainer.value) {
    chart = echarts.init(chartContainer.value)
    chart.setOption(option)
    // addValueToEchart(props.data)
    observer.observe(chartContainer.value)
  }
}
const echartResize = () => {
  if (chart) {
    chart.resize()
  }
}
defineExpose({
  setChartData,
  setChartMarkPoint,
})
onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
  observer.disconnect()
})
</script>
