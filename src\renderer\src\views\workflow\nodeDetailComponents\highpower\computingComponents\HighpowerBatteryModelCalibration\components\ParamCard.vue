<template>
  <div
    class="flex flex-col p-2 hover:bg-gray-50 rounded-lg border border-gray-200 shadow-sm transition-colors h-full"
    :class="{ 'border-blue-500 bg-blue-50': param.is_recommended, 'opacity-75': isProcessing }"
  >
    <!-- Skeleton 加载状态 -->
    <template v-if="isProcessing">
      <!-- 头部区域 Skeleton -->
      <!-- <div class="flex items-start justify-between mb-1.5 animate-pulse">
        <div class="flex items-start">
          <div class="w-4 h-4 bg-gray-200 rounded-sm"></div>
          <div class="ml-2 h-5 bg-gray-200 rounded w-24"></div>
        </div>
        <div v-if="param.is_recommended" class="w-10 h-4 bg-blue-100 rounded-full"></div>
      </div> -->

      <!-- 参数值和范围 Skeleton -->
      <!-- <div class="mt-1 space-y-1 animate-pulse">
        <div class="flex justify-between items-center">
          <div class="w-12 h-3 bg-gray-200 rounded"></div>
          <div class="w-16 h-3 bg-gray-200 rounded ml-1"></div>
        </div>
        <div class="flex justify-between items-center">
          <div class="w-8 h-3 bg-gray-200 rounded"></div>
          <div class="w-20 h-3 bg-gray-200 rounded ml-1"></div>
        </div>
      </div> -->
    </template>
    <!-- 头部区域：复选框、名称和推荐标签 -->
    <div class="flex items-start justify-between mb-1.5">
      <div class="flex items-start">
        <Checkbox
          :model-value="param.selected"
          class="flex-shrink-0 mt-0.5"
          :disabled="isProcessing"
          @update:model-value="(value) => updateSelection(value)"
        />
        <p
          class="ml-2 text-sm font-medium text-gray-700 line-clamp-1 break-words"
          :title="param.param_name"
        >
          {{ param.param_name || '--' }}
        </p>
      </div>
      <div class="flex items-center gap-1">
        <Badge
          v-if="param.is_recommended"
          variant="secondary"
          class="bg-blue-100 text-blue-800 text-[10px] px-1 py-0"
        >
          推荐
        </Badge>
        <Button
          v-if="!isProcessing"
          variant="ghost"
          size="icon"
          class="h-5 w-5 p-0.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
          @click="openEditDialog"
        >
          <LucideIcon name="Edit2" class="h-3 w-3" />
        </Button>
      </div>
    </div>

    <!-- 参数值和范围 -->
    <div class="mt-1 space-y-1 text-xs">
      <div class="flex justify-between items-center">
        <span class="text-gray-500 whitespace-nowrap">初始值:</span>
        <span
          class="text-orange-500 font-medium ml-1 truncate max-w-[120px]"
          :title="param.init_value || '--'"
        >
          {{ param.init_value || '--' }}
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-gray-500 whitespace-nowrap">范围:</span>
        <span
          class="text-blue-500 ml-1 truncate max-w-[120px]"
          :title="`${param.min || '0'} - ${param.max || '1'}`"
        >
          {{ param.min || '0' }} - {{ param.max || '1' }}
        </span>
      </div>
    </div>

    <!-- 编辑参数对话框 -->
    <Dialog :open="isEditDialogOpen" @update:open="isEditDialogOpen = $event">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>编辑参数</DialogTitle>
          <DialogDescription>
            修改 "{{ param.param_name || '--' }}" 的初始值和范围
          </DialogDescription>
        </DialogHeader>
        <form class="space-y-4 py-2" @submit="onSubmit">
          <div class="space-y-4">
            <FormField v-slot="{ componentField }" name="init_value">
              <FormItem>
                <FormLabel>初始值</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="请输入初始值" v-bind="componentField" />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>

            <div class="grid grid-cols-2 gap-4">
              <FormField v-slot="{ componentField }" name="min">
                <FormItem>
                  <FormLabel>最小值</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="请输入最小值" v-bind="componentField" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField v-slot="{ componentField }" name="max">
                <FormItem>
                  <FormLabel>最大值</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="请输入最大值" v-bind="componentField" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <DialogFooter class="mt-4">
            <Button variant="outline" type="button" @click="isEditDialogOpen = false">取消</Button>
            <Button type="submit">保存更改</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import { toFormValidator } from '@vee-validate/zod'

import { LucideIcon } from '@renderer/components'
const props = defineProps({
  param: {
    type: Object,
    required: true,
  },
  paramType: {
    type: String,
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  paramIndex: {
    type: Number,
    required: true,
  },
  isProcessing: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update-selection', 'update-param'])
const isEditDialogOpen = ref(false)
const updateSelection = (value) => {
  emit('update-selection', {
    paramType: props.paramType,
    category: props.category,
    paramIndex: props.paramIndex,
    value,
  })
}

// 编辑参数
const editedParam = ref({
  init_value: '',
  min: '',
  max: '',
})
// 使用 Zod 创建校验规则
const validationSchema = computed(() => {
  return toFormValidator(
    z.object({
      min: z
        .string()
        .nonempty({ message: '最小值不能为空' })
        .transform((val) => String(val))
        .refine((val) => !isNaN(Number(val)), { message: '请输入有效的数字' })
        .refine(
          (val) => {
            const max = Number(editedParam.value.max)
            return isNaN(max) || Number(val) < max
          },
          { message: '最小值必须小于最大值' },
        ),
      max: z
        .string()
        .nonempty({ message: '最大值不能为空' })
        .transform((val) => String(val))
        .refine((val) => !isNaN(Number(val)), { message: '请输入有效的数字' })
        .refine(
          (val) => {
            const min = Number(editedParam.value.min)
            return isNaN(min) || Number(val) > min
          },
          { message: '最大值必须大于最小值' },
        ),
      init_value: z
        .string()
        .transform((val) => (val === '' ? '' : String(val)))
        .refine((val) => val === '' || !isNaN(Number(val)), { message: '请输入有效的数字' })
        .refine(
          (val) => {
            const min = Number(editedParam.value.min)
            const max = Number(editedParam.value.max)
            return (
              val === '' ||
              ((isNaN(min) || Number(val) >= min) && (isNaN(max) || Number(val) <= max))
            )
          },
          { message: '初始值必须在最小值和最大值之间' },
        ),
    }),
  )
})

const { handleSubmit, resetForm } = useForm({
  validationSchema,
})

const openEditDialog = () => {
  // 复制当前参数值到编辑表单
  const values = {
    init_value: props.param.init_value !== undefined ? String(props.param.init_value) : '',
    min: props.param.min !== undefined ? String(props.param.min) : '',
    max: props.param.max !== undefined ? String(props.param.max) : '',
  }

  // 更新 editedParam 引用值
  editedParam.value = { ...values }

  // 打开对话框
  isEditDialogOpen.value = true

  // 确保在下一个渲染周期重置表单值
  nextTick(() => {
    resetForm({ values })
  })
}
// 关闭编辑对话框
const closeEditDialog = () => {
  isEditDialogOpen.value = false
  // 清空临时数据
  editedParam.value = {
    init_value: '',
    min: '',
    max: '',
  }
}
const onSubmit = handleSubmit((values) => {
  // 发送更新事件
  emit('update-param', {
    paramType: props.paramType,
    category: props.category,
    paramIndex: props.paramIndex,
    param: {
      ...props.param,
      init_value: values.init_value !== '' ? Number(values.init_value) : '',
      min: values.min !== '' ? Number(values.min) : '',
      max: values.max !== '' ? Number(values.max) : '',
    },
  })

  // 关闭对话框
  isEditDialogOpen.value = false
})
</script>
