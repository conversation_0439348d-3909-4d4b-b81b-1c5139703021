<template>
  <div class="h-full">
    <div class="flex flex-col">
      <HeaderTitle
        :title="t('workflows.title')"
        :tooltip="t('workflows.tooltip')"
        :show-icon="true"
      />
      <div class="main flex flex-row mt-4" style="height: calc(100vh - 8rem)">
        <Left class="w-[25%]" />
        <Right class="flex-1" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { HeaderTitle } from '@renderer/components'
import { Left, Right } from './components'
import { useLanguage } from '@renderer/config/hooks'

const { t } = useLanguage()
</script>

<style lang="scss" scoped></style>
