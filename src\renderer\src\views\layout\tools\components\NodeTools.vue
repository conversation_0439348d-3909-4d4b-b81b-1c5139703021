<template>
  <div class="w-full">
    <!-- 节点工具模块卡片列表 -->
    <div v-if="Object.keys(nodeModulesStore.nodeModules).length === 0" class="text-center py-10">
      <EmptyState
        title="暂无工具模块"
        description='请点击右上角的"导入工具"按钮导入工具模块'
        icon="cib:node-red"
        class="transition-all duration-500 hover:scale-105"
      />
    </div>

    <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
      <!-- 动态生成工具模块卡片 -->
      <Card
        v-for="(module, name) in filteredModules"
        :key="name"
        class="group hover:shadow-lg transition-all duration-300 relative overflow-hidden border border-border/40 bg-gradient-to-br from-card to-card/95"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
        ></div>

        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle
            class="text-lg font-medium group-hover:text-primary transition-colors duration-300"
          >
            {{ name }}
          </CardTitle>
          <div
            class="rounded-full bg-primary/10 p-2.5 transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/20"
          >
            <Icon
              v-if="module.icon.type === 'icon'"
              :icon="module.icon.value"
              class="w-5 h-5 text-primary"
            />
            <img v-else :src="module.icon.value" :width="24" :height="24" class="object-contain" />
          </div>
        </CardHeader>

        <CardContent>
          <p
            class="text-sm text-muted-foreground line-clamp-2 h-10 group-hover:text-foreground/90 transition-colors duration-300"
          >
            {{ module.description || '暂无描述' }}
          </p>
          <div class="flex items-center justify-between mt-4">
            <span
              class="text-sm font-medium text-gray-500 group-hover:text-gray-700 transition-colors duration-300"
            >
              是否启用
            </span>
            <Switch
              :checked="module.enabled"
              class="transition-all duration-300"
              :disabled="module.isBuiltin"
              @update:checked="(val) => updateModuleStatus(name, val)"
            />
          </div>
        </CardContent>

        <!-- 删除按钮 -->
        <Button
          v-if="!module.isBuiltin"
          variant="destructive"
          size="icon"
          class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 scale-90 group-hover:scale-100 hover:bg-destructive/90"
          @click="openDeleteDialog(name)"
        >
          <Trash class="h-4 w-4" />
        </Button>

        <!-- 内置模块标记 -->
        <Badge
          v-if="module.isBuiltin"
          class="absolute top-1 left-2 bg-gray-400/80 backdrop-blur-sm transition-all duration-300 group-hover:bg-gray-500"
        >
          内置
        </Badge>
      </Card>
    </div>

    <!-- 导入对话框 -->
    <Dialog :open="isImportDialogOpen" @update:open="isImportDialogOpen = $event">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>导入工具模块</DialogTitle>
          <DialogDescription>请选择一个工具模块文件 (.plugin) 进行导入</DialogDescription>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <FileDropUpload
            :accept-types="['.plugin']"
            :max-size="5"
            @file-selected="handleFileSelected"
            @error="handleUploadError"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" @click="isImportDialogOpen = false">取消</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 删除确认对话框 -->
    <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除"{{ moduleToDelete }}"工具模块吗？此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction @click="confirmDelete">确认删除</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { EmptyState, FileDropUpload } from '@renderer/components'
import { Trash } from 'lucide-vue-next'
import { Icon } from '@iconify/vue'
import { toast } from 'vue-sonner'
import { useNodeModulesStore } from '@renderer/store'
const props = defineProps<{ searchQuery?: string }>()
const nodeModulesStore = useNodeModulesStore()

// 导入对话框状态
const isImportDialogOpen = ref(false)
const isDeleteDialogOpen = ref(false)
const moduleToDelete = ref('')

const filteredModules = computed(() => {
  const q = props.searchQuery?.trim().toLowerCase() || ''
  if (!q) return nodeModulesStore.nodeModules
  // 只保留名称或描述中包含搜索词的模块
  return Object.fromEntries(
    Object.entries(nodeModulesStore.nodeModules).filter(
      ([name, module]) =>
        name.toLowerCase().includes(q) ||
        (module.description && module.description.toLowerCase().includes(q)),
    ),
  )
})

// 打开导入对话框
const openImportDialog = () => {
  isImportDialogOpen.value = true
}

// 处理文件选择
const handleFileSelected = async (file: File) => {
  if (!file.name.endsWith('.plugin')) {
    toast.error('文件格式错误', {
      description: '请选择.plugin格式的文件',
    })
    return
  }

  const result = await nodeModulesStore.importNodeModule(file)

  if (result.success) {
    toast.success('导入成功', {
      description: result.message,
    })
    isImportDialogOpen.value = false
  } else {
    toast.error('导入失败', {
      description: result.message,
    })
  }
}

// 处理上传错误
const handleUploadError = (error: string) => {
  toast.error('上传错误', {
    description: error,
  })
}

// 更新模块状态
const updateModuleStatus = (moduleName: string, enabled: boolean) => {
  nodeModulesStore.updateNodeModuleStatus(moduleName, enabled)
  toast.info(enabled ? '已启用' : '已禁用', {
    description: `${moduleName} 已${enabled ? '启用' : '禁用'}`,
  })
}

// 打开删除对话框
const openDeleteDialog = (moduleName: string) => {
  moduleToDelete.value = moduleName
  isDeleteDialogOpen.value = true
}

// 确认删除
const confirmDelete = () => {
  const success = nodeModulesStore.deleteNodeModule(moduleToDelete.value)

  if (success) {
    toast.success('删除成功', {
      description: `成功删除工具模块 ${moduleToDelete.value}`,
    })
  } else {
    toast.error('删除失败', {
      description: '无法删除该工具模块',
    })
  }

  isDeleteDialogOpen.value = false
  moduleToDelete.value = ''
}

defineExpose({
  openImportDialog,
})
</script>
<style lang="scss" scoped>
.card {
  @apply backdrop-blur-sm;

  &:hover {
    @apply shadow-md shadow-primary/5;
  }
}

.badge {
  @apply text-xs font-medium px-2 py-0.5 rounded-full;
}
</style>
