import { BaseService, BaseResponse } from './baseService'
import { Server, ServerUsage } from '../../types/api/server'

export interface ServerResult {
  serverId: string
  serverList: Server[]
}

export interface ServerResponse extends BaseResponse {
  serverResult: ServerResult
}

export interface ServerUsageResponse extends BaseResponse {
  serverUsage: ServerUsage
}

export class ServerService extends BaseService {
  /**
   * 获取服务器列表
   * @param serverId 可选的特定服务器ID
   */
  async getServerList(serverId?: string): Promise<ServerResponse> {
    return this.call<ServerResponse>('getServerList', {
      id: serverId,
    })
  }

  /**
   * 删除服务器信息
   * @param serverId 服务器ID
   */
  async deleteServerInfo(serverId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('deleteServerInfo', {
      id: serverId,
    })
  }

  /**
   * 获取服务器资源使用情况
   * @param serverId 服务器ID
   */
  async getServerUsage(serverId: string): Promise<ServerUsageResponse> {
    return this.call<ServerUsageResponse>('getServerUsage', {
      id: serverId,
    })
  }
}

/**
 * 创建 ServerService 实例
 */
export function createServerService(): ServerService {
  return new ServerService()
}
