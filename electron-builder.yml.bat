appId: ${APP_ID}
productName: ${PRODUCT_NAME}
directories:
  output: dist    # 添加：指定输出目录
  buildResources: resources
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
extraResources:
  - from: src/main/grpc/protos
    to: protos
    filter: '**/*.proto'
asarUnpack:
  - resources/**
win:
  icon: resources/matt.ico
  executableName: ${EXECUTABLE_NAME}
  target:
    - target: nsis
      arch:
        - x64
  sign: null                    # 添加：禁用签名
  signAndEditExecutable: false   # 添加：禁用可执行文件签名和编辑
  certificateFile: null
  certificatePassword: null
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  oneClick: false               # 添加：禁用一键安装
  allowToChangeInstallationDirectory: true  # 添加：允许更改安装目录
  perMachine: false            # 添加：是否为所有用户安装
  deleteAppDataOnUninstall: true # 添加：卸载时删除应用数据
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish: null
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
