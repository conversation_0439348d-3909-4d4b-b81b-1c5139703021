import vue from '@vitejs/plugin-vue'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import { resolve } from 'path'

import autoprefixer from 'autoprefixer'
import fs, { cpSync } from 'fs'
import tailwindcss from 'tailwindcss'
import svgLoader from 'vite-svg-loader'
// import dotenv from 'dotenv'

// 定义环境变量的类型
interface EnvVars {
  VITE_APP_LINKER_API?: string
  VITE_APP_USER_ID?: string
  VITE_APP_USER_TOKEN?: string
  VITE_APP_ROBOT_AGENT_API?: string
  VITE_APP_BASE_API?: string
  VITE_APP_IS_MATT?: string
}

// 加载环境变量的函数
function loadEnvFile(mode: string): EnvVars {
  const envPath =
    mode === 'production'
      ? resolve(__dirname, '.env.production')
      : resolve(__dirname, '.env.development')
  const env: EnvVars = {}
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    envContent.split('\n').forEach((line) => {
      if (line && !line.startsWith('#')) {
        const [key, ...value] = line.split('=')
        env[key.trim() as keyof EnvVars] = value.join('=').trim()
      }
    })
    console.log(`Loaded ${mode} env file:`, env)
  } else {
    console.warn(`Env file not found: ${envPath}`)
  }
  return env
}

// 用于 shadcn-vue 检测
const viteConfig = {
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve('src/renderer/src'),
      '~@': resolve('src/renderer/src'),
      '~components': resolve('src/renderer/src/components'),
      '~utils': resolve('src/renderer/src/utils/utils'),
    },
  },
}

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnvFile(mode)

  // 定义环境变量注入
  const envDefines = {
    'process.env.VITE_APP_LINKER_API': JSON.stringify(env.VITE_APP_LINKER_API || ''),
    'process.env.VITE_APP_USER_ID': JSON.stringify(env.VITE_APP_USER_ID || ''),
    'process.env.VITE_APP_USER_TOKEN': JSON.stringify(env.VITE_APP_USER_TOKEN || ''),
    'process.env.VITE_APP_ROBOT_AGENT_API': JSON.stringify(env.VITE_APP_ROBOT_AGENT_API || ''),
    'process.env.VITE_APP_BASE_API': JSON.stringify(env.VITE_APP_BASE_API || ''),
    'process.env.VITE_APP_IS_MATT': JSON.stringify(env.VITE_APP_IS_MATT || ''),
  }

  return {
    cacheDir: './.electron-vite',
    main: {
      plugins: [
        externalizeDepsPlugin(),
        {
          name: 'inject-env',
          transform(code, id) {
            if (id.includes('index.ts')) {
              console.log('Injecting environment variables into main process')
              let modifiedCode = code
              for (const [key, value] of Object.entries(envDefines)) {
                modifiedCode = modifiedCode.replace(new RegExp(key.replace('.', '\\.'), 'g'), value)
              }
              return {
                code: modifiedCode,
                map: null,
              }
            }
          },
        },
        {
          name: 'copy-proto-files',
          writeBundle() {
            const protoSrc = resolve(__dirname, 'src/main/grpc/protos')
            const protoDest = resolve(__dirname, 'out/main/protos')
            cpSync(protoSrc, protoDest, { recursive: true })
            console.log('已复制 protos 文件夹到 out/main/protos')
          },
        },
        {
          name: 'copy-worker-files',
          writeBundle() {
            const srcDir = resolve(__dirname, 'src/main/workers')
            const destDir = resolve(__dirname, 'out/main/workers')
            cpSync(srcDir, destDir, { recursive: true })
            console.log('已复制 workers 文件夹到 out/main/workers')
          },
        },
        {
          name: 'copy-service-files',
          writeBundle() {
            const srcDir = resolve(__dirname, 'src/main/services')
            const destDir = resolve(__dirname, 'out/main/services')
            cpSync(srcDir, destDir, { recursive: true })
            console.log('已复制 services 文件夹到 out/main/services')
          },
        },
        {
          name: 'copy-grpc-files',
          writeBundle() {
            const srcDir = resolve(__dirname, 'src/main/grpc')
            const destDir = resolve(__dirname, 'out/main/grpc')
            cpSync(srcDir, destDir, { recursive: true })
            console.log('已复制 grpc 文件夹到 out/main/grpc')
          },
        },
      ],
      build: {
        emptyOutDir: true,
        rollupOptions: {
          external: ['@grpc/grpc-js', '@grpc/proto-loader'],
          input: {
            index: resolve(__dirname, 'src/main/index.ts'),
            protocol: resolve(__dirname, 'src/main/grpc/protocol.ts'),
          },
          output: {
            dir: 'out/main',
            format: 'cjs',
            entryFileNames: '[name].js',
            compact: true,
            minifyInternalExports: true,
          },
        },
        minify: 'terser',
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
      },
      define: envDefines,
    },
    preload: {
      plugins: [externalizeDepsPlugin()],
      build: {
        emptyOutDir: true,
      },
      define: envDefines,
    },
    renderer: {
      ...viteConfig,
      build: {
        emptyOutDir: true,
        // 添加渲染进程的构建优化
        minify: 'terser',
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
        // 启用代码分割
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['vue', 'vue-router', 'pinia'],
              ui: ['radix-vue', 'echarts'],
            },
          },
        },
      },
      resolve: {
        alias: {
          '@renderer': resolve('src/renderer/src'),
          '@store': resolve('src/renderer/src/store'),
          '@utils': resolve('src/renderer/src/utils'),
          '@components': resolve('src/renderer/src/components'),
          '@router': resolve('src/renderer/src/router'),
          '@views': resolve('src/renderer/src/views'),
          '@services': '/src/main/services',
          '@assets': resolve('src/renderer/src/assets'),
          '@styles': resolve('src/renderer/src/config/styles'),
          '@api': resolve('src/renderer/src/config/api'),
          '@hooks': resolve('src/renderer/config/src/hooks'),
          '@lib': resolve('src/renderer/src/lib'),
          '~@': resolve('src/renderer/src'),
          '~components': resolve('src/renderer/src/components'),
          '~utils': resolve('src/renderer/src/utils/utils'),
        },
      },
      css: {
        postcss: {
          plugins: [tailwindcss(), autoprefixer()],
        },
      },
      plugins: [
        vue(),
        svgLoader({
          defaultImport: 'url',
        }),
      ],
      server: {
        headers: {
          'Content-Security-Policy': [
            "default-src 'self' https: http:",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https: http:",
            "connect-src 'self' https: http: ws: wss: grpc:",
            "font-src 'self' data:",
          ].join('; '),
        },
      },
    },
  }
})
