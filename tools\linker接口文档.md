# 🚀 gRPC API 文档

## **📜 目录**

### 🎯 **API 接口**

- [`submitService`](#submitservice) - 提交服务请求，执行特定任务。
- [`agentService`](#agentservice) - 智能体相关服务接口
- [`getServerList`](#getserverlist) - 获取服务器列表
- [`getTaskList`](#gettasklist) - 获取任务列表,不包含任务结果
- [`getTaskResult`](#gettaskresult) - 获取特定任务的执行结果
- [`deleteTask`](#deletetask) - 删除任务
- [`stopTask`](#stoptask) - 停止任务
- [`pauseTask`](#pausetask) - 暂停任务
- [`resumeTask`](#resumetask) - 恢复任务
- [`deleteServerInfo`](#deleteserverinfo) - 删除服务器信息
- [`getServerUsage`](#getserverusage) - 获得服务器资源使用情况
- [`getVersion`](#getversion) - 获得中台的版本号
- [`userSignIn`](#userSignIn) - 用户登录
- [`getUserToken`](#getUserToken) - 用户登录
- [`addSession`](#addsession) - 新增会话
- [`deleteSession`](#deletesession) - 删除会话
- [`getSession`](#getsession) - 获得指定会话
- [`getUserAllSessions`](#getuserallsessions) - 获得当前用户所有会话

### 📂 **数据结构**

- [`db.UserInfoResult`](#userinforesult) - 用户信息响应结果
- [`db.UserResult`](#userresult) - 用户响应结果
- [`db.ServerResult`](#serverresult) - 服务器响应结果
- [`db.ServiceResult`](#serviceresult) - 服务响应结果
- [`db.TaskResult`](#taskresult) - 任务响应结果
- [`db.UserInfo`](#userinfo) - 用户信息实体
- [`db.User`](#user) - 用户实体
- [`db.Server`](#server) - 服务器实体
- [`db.Service`](#service) - 服务实体
- [`db.Task`](#task) - 任务实体
- [`db.Session`](#session) - 会话实体
- [`db.SessionMessage`](#sessionmessage) - 会话消息实体
- [`db.SessionContent`](#sessioncontent) - 会话消息内容实体
- [`common.ServerUsage`](#serverusage) - 服务器资源使用情况
- [`common.MemoryStatus`](#memorystatus) - 内存使用情况
- [`common.GpuStatus`](#gpustatus) - 显卡使用情况

### 📌 **状态枚举**

- [`common.ResponseStatus`](#responsestatus) - 响应状态
- [`db.ServerStatus`](#serverstatus) - 服务器状态
- [`db.TaskStatus`](#taskstatus) - 任务状态
- [`db.UserStatus`](#userstatus) - 用户状态
- [`db.UserRole`](#userrole) - 用户角色
- [`common.ValueType`](#valuetype) - 值类型

---

## 🎯 **API 接口**

### **🎯 `submitService`** <a id="submitservice"></a>

#### **描述**

提交服务请求，执行特定任务。

#### **📤 请求 (`matt.GeneralRequest`)**

| 参数名            | 类型                     | 必填  | 描述                                        |
| ----------------- | ------------------------ | ----- | ------------------------------------------- |
| `task_id`         | `string`                 | ❌ 否 | 由服务器生成, 不需要填写                    |
| `server_id`       | `string`                 | ❌ 否 | 目标服务器 ID, 不填则由linker自动分配服务器 |
| `service_name`    | `string`                 | ✅ 是 | 目标服务名称                                |
| `user_id`         | `string`                 | ✅ 是 | 用户 ID                                     |
| `token`           | `string`                 | ✅ 是 | 认证令牌                                    |
| `is_save`         | `bool`                   | ✅ 是 | 是否保存结果                                |
| `key_type_pairs`  | `map<string, ValueType>` | ❌ 否 | 任务参数类型映射                            |
| `key_value_pairs` | `map<string, string>`    | ❌ 否 | 任务参数值映射                              |

#### **📥 响应 (`matt.SubmitResponse`)**

| 参数名        | 类型     | 描述          |
| ------------- | -------- | ------------- |
| `status_code` | `int32`  | 响应状态码    |
| `message`     | `string` | 响应信息      |
| `task_id`     | `string` | 生成的任务 ID |

---

### **🎯 `agentService`** <a id="submitservice"></a>

#### **描述**

智能体相关服务接口。

#### **📤 请求 (`matt.AgentRequest`)**

| 参数名              | 类型                  | 必填  | 描述          |
| ------------------- | --------------------- | ----- | ------------- |
| `user_id`           | `string`              | ✅ 是 | 用户 ID       |
| `token`             | `string`              | ✅ 是 | 认证令牌      |
| `server_id`         | `string`              | ✅ 是 | 目标服务器 ID |
| `service_name`      | `string`              | ✅ 是 | 目标服务名称  |
| `model_type`        | `string`              | ✅ 是 | 模型类型      |
| `is_stream_reponse` | `bool`                | ✅ 是 | 是否流式响应  |
| `messages`          | `map<string, string>` | ✅ 是 | 消息内容      |

#### **📥 响应 (`matt.AgentResponse`)**

| 参数名       | 类型                                       | 描述     |
| ------------ | ------------------------------------------ | -------- |
| `status`     | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message`    | `string`                                   | 响应信息 |
| `session_id` | `string`                                   | 会话ID   |
| `result`     | `string`                                   | 会话结果 |

---

### **🎯 `getServerList`** <a id="getserverlist"></a>

#### **描述**

获取已注册的服务器列表。

#### **📤 请求 (`common.GetRequest`)**

| 参数名    | 类型     | 必填  | 描述                               |
| --------- | -------- | ----- | ---------------------------------- |
| `user_id` | `string` | ✅ 是 | 用户ID                             |
| `token`   | `string` | ✅ 是 | 用户的校验令牌                     |
| `id`      | `string` | ❌ 否 | 要查询的服务器ID, 不填默认返回所有 |

#### **📥 响应 (`db.DbResponse`)**

| 参数名          | 类型                                       | 描述       |
| --------------- | ------------------------------------------ | ---------- |
| `status`        | [`common.ResponseStatus`](#responsestatus) | 响应状态   |
| `message`       | `string`                                   | 描述信息   |
| `server_result` | [`db.ServerResult`](#serverresult)         | 服务器列表 |

---

### **🎯 `getTaskList`** <a id="gettasklist"></a>

#### **描述**

获取任务列表, 不包含任务结果。

#### **📤 请求 (`common.GetRequest`)**

| 参数名    | 类型     | 必填  | 描述                                                                 |
| --------- | -------- | ----- | -------------------------------------------------------------------- |
| `user_id` | `string` | ✅ 是 | 用户ID                                                               |
| `token`   | `string` | ✅ 是 | 用户的校验令牌                                                       |
| `id`      | `string` | ❌ 否 | 要查询的任务ID, 查询多个时用逗号分隔，不填默认返回当前用户的所有任务 |

#### **📥 响应 (`db.DbResponse`)**

| 参数名        | 类型                                       | 描述     |
| ------------- | ------------------------------------------ | -------- |
| `status`      | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message`     | `string`                                   | 描述信息 |
| `task_result` | [`db.TaskResult`](#taskresult)             | 任务列表 |

---

### **🎯 `getTaskResult`** <a id="gettaskresult"></a>

#### **描述**

获取特定任务的执行结果。

#### **📤 请求 (`common.GetRequest`)**

| 参数名    | 类型     | 必填  | 描述                                      |
| --------- | -------- | ----- | ----------------------------------------- |
| `user_id` | `string` | ✅ 是 | 用户ID                                    |
| `token`   | `string` | ✅ 是 | 用户的校验令牌                            |
| `id`      | `string` | ✅ 是 | 要查询结果任务的ID, 可用','分隔多个任务ID |

#### **📥 响应 (`linker.TaskResultResponse`)**

| 参数名    | 类型                                       | 描述         |
| --------- | ------------------------------------------ | ------------ |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态     |
| `message` | `string`                                   | 描述信息     |
| `result`  | `string`                                   | 任务执行结果 |

---

### **🎯 `deleteTask`** <a id="deletetask"></a>

#### **描述**

删除指定的任务。

#### **📤 请求 (`common.OperateRequest`)**

| 参数名    | 类型     | 必填  | 描述           |
| --------- | -------- | ----- | -------------- |
| `user_id` | `string` | ✅ 是 | 用户ID         |
| `token`   | `string` | ✅ 是 | 用户的校验令牌 |
| `id`      | `string` | ✅ 是 | 要删除的任务ID |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `stopTask`** <a id="stoptask"></a>

#### **描述**

停止指定的任务。

#### **📤 请求 (`common.OperateRequest`)**

| 参数名    | 类型     | 必填  | 描述               |
| --------- | -------- | ----- | ------------------ |
| `user_id` | `string` | ✅ 是 | 用户ID             |
| `token`   | `string` | ✅ 是 | 用户的校验令牌     |
| `id`      | `string` | ✅ 是 | 要停止运行的任务ID |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `pauseTask`** <a id="pausetask"></a>

#### **描述**

暂停指定的任务。

#### **📤 请求 (`common.OperateRequest`)**

| 参数名    | 类型     | 必填  | 描述               |
| --------- | -------- | ----- | ------------------ |
| `user_id` | `string` | ✅ 是 | 用户ID             |
| `token`   | `string` | ✅ 是 | 用户的校验令牌     |
| `id`      | `string` | ✅ 是 | 要暂停运行的任务ID |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `resumeTask`** <a id="resumetask"></a>

#### **描述**

恢复指定的任务。

#### **📤 请求 (`common.OperateRequest`)**

| 参数名    | 类型     | 必填  | 描述               |
| --------- | -------- | ----- | ------------------ |
| `user_id` | `string` | ✅ 是 | 用户ID             |
| `token`   | `string` | ✅ 是 | 用户的校验令牌     |
| `id`      | `string` | ✅ 是 | 要恢复运行的任务ID |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `updateTaskProcess`** <a id="updateTaskProcess"></a>

#### **描述**

server更新任务的进度，包含任务状态的逻辑校验

#### **📤 请求 (`db.DbRequest`)**

| 参数名        | 类型          | 必填  | 描述                                       |
| ------------- | ------------- | ----- | ------------------------------------------ |
| `user_id`     | `string`      | ✅ 是 | 用户ID                                     |
| `token`       | `string`      | ✅ 是 | 用户的校验令牌                             |
| `taskRequest` | `TaskRequest` | ✅ 是 | 要更新的任务实体,必填task_id、task_process |

#### **📥 响应 (`db.DbResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `deleteServerInfo`** <a id="deteleserverinfo"></a>

#### **描述**

删除指定的服务器信息, 不能在服务器运行状态 [`db.serverstatus.Runing`](#serverstatus) 下使用

#### **📤 请求 (`common.OperateRequest`)**

| 参数名    | 类型     | 必填  | 描述             |
| --------- | -------- | ----- | ---------------- |
| `user_id` | `string` | ✅ 是 | 用户ID           |
| `token`   | `string` | ✅ 是 | 用户的校验令牌   |
| `id`      | `string` | ✅ 是 | 要删除的服务器ID |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `getServerUsage`** <a id="getserverusage"></a>

#### **描述**

获得服务器资源使用情况, 只能在服务器运行状态 [`db.serverstatus.Runing`](#serverstatus) 下使用

#### **📤 请求 (`common.GetRequest`)**

| 参数名    | 类型     | 必填  | 描述             |
| --------- | -------- | ----- | ---------------- |
| `user_id` | `string` | ✅ 是 | 用户ID           |
| `token`   | `string` | ✅ 是 | 用户的校验令牌   |
| `id`      | `string` | ✅ 是 | 要查询的服务器ID |

#### **📥 响应 (`common.ServerOperateResponse`)**

| 参数名         | 类型                                                     | 描述               |
| -------------- | -------------------------------------------------------- | ------------------ |
| `status`       | [`common.ResponseStatus`](#responsestatus)               | 响应状态           |
| `message`      | `string`                                                 | 详细信息           |
| `server_usage` | [`common.ServerOperateResponse`](#serveroperateresponse) | 服务器资源使用情况 |

---

### **🎯 `getVersion`** <a id="getversion"></a>

#### **描述**

获得中台的版本号，使用common.PingRequest，不需要其他参数

#### **📤 请求 (`common.PingRequest`)**

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
|        |      |      |      |

#### **📥 响应 (`common.GetResponse`)**

| 参数名        | 类型                                       | 描述                                                              |
| ------------- | ------------------------------------------ | ----------------------------------------------------------------- |
| `status`      | [`common.ResponseStatus`](#responsestatus) | 响应状态                                                          |
| `message`     | `string`                                   | 详细信息                                                          |
| `result`      | `string`                                   | 返回结果，字符串形式的版本号                                      |
| `result_type` | [`common.ValueType`](#valuetype)           | 返回结果类型，该请求固定为[`common.ValueType`](#valuetype).String |

---

### **🎯 `userSignIn`** <a id="userSignIn"></a>

#### **描述**

用户登录

#### **📤 请求 (`db.UserRequest`)**

| 参数名      | 类型     | 必填  | 描述     |
| ----------- | -------- | ----- | -------- |
| `user_name` | `string` | ✅ 是 | 用户名称 |
| `password`  | `string` | ✅ 是 | 用户密码 |
| `login_ip`  | `string` | ✅ 是 | 登录IP   |

#### **📥 响应 (`db.DbResponse`)**

| 参数名        | 类型                                       | 描述                                          |
| ------------- | ------------------------------------------ | --------------------------------------------- |
| `status`      | [`common.ResponseStatus`](#responsestatus) | 响应状态                                      |
| `message`     | `string`                                   | 详细信息                                      |
| `user_result` | `UserResult`                               | 返回结果，包含user_list、token，主要关注token |

---

### **🎯 `getUserToken`** <a id="getUserToken"></a>

#### **描述**

用户登录

#### **📤 请求 (`db.UserRequest`)**

| 参数名      | 类型     | 必填  | 描述     |
| ----------- | -------- | ----- | -------- |
| `user_name` | `string` | ✅ 是 | 用户名称 |
| `password`  | `string` | ✅ 是 | 用户密码 |

#### **📥 响应 (`db.DbResponse`)**

| 参数名        | 类型                                       | 描述                |
| ------------- | ------------------------------------------ | ------------------- |
| `status`      | [`common.ResponseStatus`](#responsestatus) | 响应状态            |
| `message`     | `string`                                   | 详细信息            |
| `user_result` | `UserResult`                               | 返回结果，只有token |

---

### **🎯 `addSession`** <a id="addsession"></a>

#### **描述**

新增会话

#### **📤 请求 (`db.SessionRequest`)**

| 参数名             | 类型     | 必填  | 描述                             |
| ------------------ | -------- | ----- | -------------------------------- |
| `user_id`          | `string` | ✅ 是 | 用户 ID                          |
| `token`            | `string` | ✅ 是 | 认证令牌                         |
| `session_id`       | `string` | ✅ 是 | 会话 ID                          |
| `session_user_id`  | `string` | ✅ 是 | 会话所属人ID, 后台调用接口时使用 |
| `session_messages` | `string` | ✅ 是 | 会话消息内容                     |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `deleteSession`** <a id="deletesession"></a>

#### **描述**

删除会话

#### **📤 请求 (`common.OperateRequest`)**

| 参数名    | 类型     | 必填  | 描述           |
| --------- | -------- | ----- | -------------- |
| `user_id` | `string` | ✅ 是 | 用户ID         |
| `token`   | `string` | ✅ 是 | 用户的校验令牌 |
| `id`      | `string` | ✅ 是 | 要删除的会话ID |

#### **📥 响应 (`common.OperateResponse`)**

| 参数名    | 类型                                       | 描述     |
| --------- | ------------------------------------------ | -------- |
| `status`  | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message` | `string`                                   | 详细信息 |

---

### **🎯 `getSession`** <a id="getUserToken"></a>

#### **描述**

获得会话内容

#### **📤 请求 (`common.GetRequest`)**

| 参数名    | 类型     | 必填  | 描述                 |
| --------- | -------- | ----- | -------------------- |
| `user_id` | `string` | ✅ 是 | 用户ID               |
| `token`   | `string` | ✅ 是 | 用户的校验令牌       |
| `id`      | `string` | ✅ 是 | 要查询的会话的会话ID |

#### **📥 响应 (`db.DbResponse`)**

| 参数名           | 类型                                       | 描述     |
| ---------------- | ------------------------------------------ | -------- |
| `status`         | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message`        | `string`                                   | 描述信息 |
| `session_result` | [`db.SessionResult`](#sessionresult)       | 会话结果 |

---

### **🎯 `getUserAllSessions`** <a id="getUserToken"></a>

#### **描述**

获得当前用户的全部会话信息

#### **📤 请求 (`common.GetRequest`)**

| 参数名    | 类型     | 必填  | 描述           |
| --------- | -------- | ----- | -------------- |
| `user_id` | `string` | ✅ 是 | 用户ID         |
| `token`   | `string` | ✅ 是 | 用户的校验令牌 |
| `id`      | `string` | ❌ 否 |                |

#### **📥 响应 (`db.DbResponse`)**

| 参数名           | 类型                                       | 描述     |
| ---------------- | ------------------------------------------ | -------- |
| `status`         | [`common.ResponseStatus`](#responsestatus) | 响应状态 |
| `message`        | `string`                                   | 描述信息 |
| `session_result` | [`db.SessionResult`](#sessionresult)       | 会话结果 |

## 📂 **数据结构**

### **📂 用户信息响应结果 (`db.UserInfoResult`)** <a id="userinforesult"></a>

| 参数名           | 类型                              | 描述         |
| ---------------- | --------------------------------- | ------------ |
| `user_info_id`   | `string`                          | 用户信息ID   |
| `user_info_list` | Array[[`db.UserInfo`](#userinfo)] | 用户信息列表 |

---

### **📂 用户响应结果 (`db.UserResult`)** <a id="userresult"></a>

| 参数名      | 类型                      | 描述     |
| ----------- | ------------------------- | -------- |
| `token`     | `string`                  | 访问令牌 |
| `user_list` | Array[[`db.User`](#user)] | 用户列表 |

---

### **📂 服务器响应结果 (`db.ServerResult`)** <a id="serverresult"></a>

| 参数名        | 类型                          | 描述               |
| ------------- | ----------------------------- | ------------------ |
| `server_id`   | `string`                      | 服务器ID, 默认为空 |
| `server_list` | Array[[`db.Server`](#server)] | 服务器列表         |

---

### **📂 服务响应结果 (`db.ServiceResult`)** <a id="serviceresult"></a>

| 参数名         | 类型                            | 描述     |
| -------------- | ------------------------------- | -------- |
| `service_id`   | `string`                        | 服务ID   |
| `service_list` | Array[[`db.Service`](#service)] | 服务列表 |

---

### **📂 任务响应结果 (`db.TaskResult`)** <a id="taskresult"></a>

| 参数名      | 类型                      | 描述     |
| ----------- | ------------------------- | -------- |
| `task_id`   | `string`                  | 任务ID   |
| `task_list` | Array[[`db.Task`](#task)] | 任务列表 |

---

### **📂 会话结果 (`db.SessionResult`)** <a id="sessionresult"></a>

| 参数名         | 类型                            | 描述     |
| -------------- | ------------------------------- | -------- |
| `session_id`   | `string`                        | 会话ID   |
| `session_list` | Array[[`db.session`](#session)] | 会话列表 |

---

### **📂 用户信息实体 (`db.UserInfo`)** <a id="userinfo"></a>

| 参数名         | 类型                           | 描述               |
| -------------- | ------------------------------ | ------------------ |
| `user_id`      | `string`                       | 用户ID             |
| `user_name`    | `string`                       | 用户名             |
| `password`     | `string`                       | 用户密码           |
| `access_level` | `int32`                        | 用户拥有的访问权限 |
| `user_status`  | [`db.UserStatus`](#userstatus) | 用户状态           |

---

### **📂 用户实体 (`db.User`)** <a id="user"></a>

| 参数名         | 类型     | 描述         |
| -------------- | -------- | ------------ |
| `user_id`      | `string` | 用户ID       |
| `token`        | `string` | 访问令牌     |
| `expired_time` | `int64`  | 令牌过期时间 |

---

### **📂 服务器实体 (`db.Server`)** <a id="server"></a>

| 参数名          | 类型                               | 描述           |
| --------------- | ---------------------------------- | -------------- |
| `server_id`     | `string`                           | 服务器ID       |
| `server_name`   | `string`                           | 服务器名称     |
| `url`           | `string`                           | 服务器地址     |
| `server_status` | [`db.ServerStatus`](#serverstatus) | 服务器状态     |
| `region`        | `string`                           | 服务器所在地区 |
| `version`       | `string`                           | 服务器的版本   |
| `access_level`  | `int32`                            | 服务器所需权限 |
| `create_time`   | `int64`                            | 服务器注册时间 |
| `update_time`   | `int64`                            | 服务器更新时间 |

---

### **📂 服务实体 (`db.Service`)** <a id="service"></a>

| 参数名          | 类型     | 描述             |
| --------------- | -------- | ---------------- |
| `service_id`    | `string` | 服务ID           |
| `service_name`  | `string` | 服务名称         |
| `server_id`     | `string` | 关联的服务器ID   |
| `version`       | `string` | 服务版本         |
| `protocol_type` | `string` | 协议类型         |
| `access_level`  | `int32`  | 服务所需访问权限 |

---

### **📂 任务实体 (`db.Task`)** <a id="task"></a>

| 参数名         | 类型                           | 描述             |
| -------------- | ------------------------------ | ---------------- |
| `task_id`      | `string`                       | 任务ID           |
| `user_id`      | `string`                       | 用户ID           |
| `service_id`   | `string`                       | 服务ID           |
| `start_time`   | `int64`                        | 任务开始时间     |
| `end_time`     | `int64`                        | 任务结束时间     |
| `task_log`     | `string`                       | 任务日志         |
| `task_status`  | [`db.TaskStatus`](#taskstatus) | 任务状态         |
| `task_process` | `float`                        | 任务进度         |
| `task_pid`     | `int32`                        | 任务进程号       |
| `result`       | `string`                       | 任务结果         |
| `file_path`    | `string`                       | 任务结果文件路径 |
| `create_time`  | `int64`                        | 任务创建时间     |
| `update_time`  | `int64`                        | 任务更新时间     |

---

### **📂 会话实体 (`db.Session`)** <a id="session"></a>

| 参数名             | 类型     | 描述           |
| ------------------ | -------- | -------------- |
| `session_id`       | `string` | 会话ID         |
| `user_id`          | `string` | 用户ID         |
| `session_messages` | `string` | 会话消息列表值 |

---

### **📂 会话消息实体 (`db.SessionMessage`)** <a id="sessionmessage"></a>

| 参数名             | 类型                                          | 描述           |
| ------------------ | --------------------------------------------- | -------------- |
| `message_id`       | `string`                                      | 会话ID         |
| `user_id`          | `string`                                      | 用户ID         |
| `session_contents` | Array[[`db.SessionContent`](#sessioncontent)] | 会话消息内容值 |

---

### **📂 会话消息内容实体 (`db.SessionContent`)** <a id="sessioncontent"></a>

| 参数名         | 类型     | 描述             |
| -------------- | -------- | ---------------- |
| `role`         | `string` | 会话角色         |
| `content_type` | `string` | 会话消息类型值   |
| `content`      | `string` | 会话消息内容值   |
| `create_time`  | `int64`  | 会话内容生成时间 |

---

### **📂 服务器资源使用情况 (`common.ServerUsage`)** <a id="serverusage"></a>

| 参数名            | 类型                                    | 描述             |
| ----------------- | --------------------------------------- | ---------------- |
| `cpu_usage`       | `float`                                 | cpu使用率        |
| `memory_status`   | [`common.MemoryStatus`](#memorystatus)  | 内存使用情况     |
| `gpu_status_list` | Array[[`common.GpuStatus`](#gpustatus)] | 显卡使用情况列表 |

---

### **📂 内存使用情况 (`common.MemoryStatus`)** <a id="memorystatus"></a>

| 参数名         | 类型     | 描述       |
| -------------- | -------- | ---------- |
| `total_memory` | `float`  | 总内存     |
| `free_memory`  | `float`  | 空闲内存   |
| `used_memory`  | `float`  | 已使用内存 |
| `unit`         | `string` | 内存单位   |

---

### **📂 显卡使用情况 (`common.GpuStatus`)** <a id="gpustatus"></a>

| 参数名         | 类型     | 描述       |
| -------------- | -------- | ---------- |
| `index`        | `int32`  | 设备序号   |
| `model`        | `string` | 显卡型号   |
| `usage`        | `float`  | 显卡使用率 |
| `used_memory`  | `float`  | 已使用显存 |
| `free_memory`  | `float`  | 空闲显存   |
| `total_memory` | `float`  | 总显存     |
| `memory_usage` | `float`  | 显存占用率 |

---

## 📌 **状态枚举**

### **📌 响应状态 (`common.ResponseStatus`)** <a id="responsestatus"></a>

| 枚举名称  | 枚举值 | 描述     |
| --------- | ------ | -------- |
| `Success` | `0`    | 响应成功 |
| `Failed`  | `1`    | 响应失败 |

---

### **📌 服务器状态 (`db.ServerStatus`)** <a id="serverstatus"></a>

| 枚举名称     | 枚举值 | 描述                                           |
| ------------ | ------ | ---------------------------------------------- |
| `Running`    | `0`    | 正常运行                                       |
| `Stopped`    | `1`    | 已停止运行                                     |
| `Expired`    | `2`    | 已过期, 无法运行任务                           |
| `Overloaded` | `3`    | 服务器超载, 无法运行任务                       |
| `Stay`       | `4`    | 仅用于传输, 保持原始状态, 不是真正的服务器状态 |
| `Unknown`    | `5`    | 未知状态                                       |

---

### **📌 任务状态 (`db.TaskStatus`)** <a id="taskstatus"></a>

| 枚举名称       | 枚举值 | 描述                                         |
| -------------- | ------ | -------------------------------------------- |
| `Initializing` | `0`    | 初始化中                                     |
| `Computing`    | `1`    | 计算中                                       |
| `Pending`      | `2`    | 等待调度                                     |
| `Paused`       | `3`    | 暂停                                         |
| `Finished`     | `4`    | 任务完成                                     |
| `Error`        | `5`    | 任务失败                                     |
| `TaskStay`     | `6`    | 仅用于传输，保持原始状态，不是真正的任务状态 |
| `Abort`        | `7`    | 任务中止                                     |

---

### **📌 用户状态 (`db.UserStatus`)** <a id="userstatus"></a>

| 枚举名称      | 枚举值 | 描述   |
| ------------- | ------ | ------ |
| `Activated`   | `0`    | 已激活 |
| `Deactivated` | `1`    | 已停用 |
| `Deleted`     | `2`    | 已删除 |

---

### **📌 用户角色 (`db.UserRole`)** <a id="userrole"></a>

| 枚举名称 | 枚举值 | 描述       |
| -------- | ------ | ---------- |
| `Super`  | `0`    | 超级管理员 |
| `Admin`  | `1`    | 管理员     |
| `Normal` | `2`    | 普通用户   |

---

### **📌 值类型 (`common.ValueType`)** <a id="valuetype"></a>

| 枚举名称    | 枚举值 | 描述             |
| ----------- | ------ | ---------------- |
| `Int32`     | `0`    | 32 位整数        |
| `Int64`     | `1`    | 64 位整数        |
| `Uint32`    | `2`    | 无符号 32 位整数 |
| `Uint64`    | `3`    | 无符号 64 位整数 |
| `Float`     | `4`    | 单精度浮点数     |
| `Double`    | `5`    | 双精度浮点数     |
| `String`    | `7`    | 字符串类型       |
| `Json`      | `8`    | JSON 对象        |
| `Jsonarray` | `9`    | JSON 数组        |
| `Bool`      | `10`   | 布尔值类型       |

---
