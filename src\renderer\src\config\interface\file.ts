/**
 * 保存文件选项接口
 */
export interface SaveFileOptions {
  /** 对话框标题 */
  title?: string
  /** 默认保存路径 */
  defaultPath?: string
  /** 文件过滤器 */
  filters?: Array<{ name: string; extensions: string[] }>
  /** 文件内容（字符串形式） */
  content?: string
  /** 文件数据（二进制或字符串） */
  data?: string | Buffer | Uint8Array | number[]
  /** 文件路径 */
  filePath?: string
  /** 编码方式 */
  encoding?: string
  /** 是否为二进制数据 */
  isBinary?: boolean
}

/**
 * 打开文件选项接口
 */
export interface OpenFileOptions {
  /** 对话框标题 */
  title?: string
  /** 默认打开路径 */
  defaultPath?: string
  /** 文件过滤器 */
  filters?: Array<{ name: string; extensions: string[] }>
  /** 对话框属性 */
  properties?: Array<string>
}

/**
 * 加密文件选项接口
 */
export interface EncryptFileOptions {
  /** JSON数据内容 */
  jsonData: string
  /** 文件类型 */
  fileType: string
  /** 默认保存路径 */
  defaultPath: string
  /** 对话框标题 */
  title?: string
  /** 文件过滤器 */
  filters?: Array<{ name: string; extensions: string[] }>
}

/**
 * 文件操作结果接口
 */
export interface FileOperationResult {
  /** 操作是否成功 */
  success: boolean
  /** 错误信息 */
  error?: string
  /** 文件路径 */
  filePath?: string
  /** 文件内容 */
  content?: string
  /** 是否取消操作 */
  canceled?: boolean
  /** 其他数据 */
  [key: string]: any
}
