import { BrowserWindow, ipcMain, screen } from 'electron'
import logger from '../utils/logger'
/**
 * 设置窗口控制事件监听
 */
export function setupWindowControls(mainWindow: BrowserWindow): void {
  // 登录后调整窗口大小
  ipcMain.on('adjust-window-after-login', () => {
    if (mainWindow) {
      mainWindow.maximize()
      logger.info('Window maximized after login')
    }
  })

  // 调整窗口大小到登录模式
  ipcMain.on('set-login-window-size', () => {
    if (mainWindow) {
      mainWindow.unmaximize() // 取消最大化状态

      const bounds = mainWindow.getBounds()
      const workAreaSize = screen.getPrimaryDisplay().workAreaSize
      const width = 1000
      const height = 650
      const x = Math.floor(workAreaSize.width / 2 - width / 2)
      const y = Math.floor(workAreaSize.height / 2 - height / 2)

      mainWindow.setBounds({ x, y, width, height })
      logger.info('Window resized for login')
    }
  })

  // 监听窗口最大化/取消最大化事件
  mainWindow.on('maximize', () => {
    if (mainWindow) {
      mainWindow.webContents.send('window-maximized-change', true)
    }
  })

  mainWindow.on('unmaximize', () => {
    if (mainWindow) {
      mainWindow.webContents.send('window-maximized-change', false)
    }
  })
}
