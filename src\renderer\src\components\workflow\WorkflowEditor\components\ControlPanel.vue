<template>
  <Panel v-if="showLeftControls" position="bottom-center" class="z-10">
    <div
      class="flex gap-3 bg-background/80 backdrop-blur-sm p-2 rounded-lg shadow-lg dark:bg-background/60 dark:shadow-none border border-border/30"
    >
      <!-- 撤销/重做组 -->
      <div class="flex gap-2 px-1">
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80 disabled:opacity-50 disabled:cursor-not-allowed"
          :class="{ 'opacity-50 cursor-not-allowed': !canUndo }"
          title="撤销"
          @click="undo"
        >
          <Icon icon="flowbite:undo-outline" :width="20" :height="20" />
        </button>
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80 disabled:opacity-50 disabled:cursor-not-allowed"
          :class="{ 'opacity-50 cursor-not-allowed': !canRedo }"
          title="重做"
          @click="redo"
        >
          <Icon icon="flowbite:redo-outline" :width="20" :height="20" />
        </button>
      </div>

      <!-- 对齐工具组 -->
      <div class="flex gap-1 px-1">
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80 disabled:opacity-50 disabled:cursor-not-allowed"
          :class="{ 'opacity-50 cursor-not-allowed': !hasMultipleNodesSelected }"
          title="水平对齐（需要选择至少两个节点）"
          @click="alignHorizontal"
        >
          <Icon icon="flowbite:adjustments-horizontal-outline" :width="20" :height="20" />
        </button>
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80 disabled:opacity-50 disabled:cursor-not-allowed"
          :class="{ 'opacity-50 cursor-not-allowed': !hasMultipleNodesSelected }"
          title="垂直对齐（需要选择至少两个节点）"
          @click="alignVertical"
        >
          <Icon icon="flowbite:adjustments-vertical-outline" :width="20" :height="20" />
        </button>
      </div>
    </div>
  </Panel>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { Panel } from '@vue-flow/core'

interface Props {
  showLeftControls: boolean
  canUndo: boolean
  canRedo: boolean
  hasMultipleNodesSelected: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  undo: []
  redo: []
  'align-horizontal': []
  'align-vertical': []
}>()

const undo = () => {
  emit('undo')
}

const redo = () => {
  emit('redo')
}

const alignHorizontal = () => {
  emit('align-horizontal')
}

const alignVertical = () => {
  emit('align-vertical')
}
</script>
