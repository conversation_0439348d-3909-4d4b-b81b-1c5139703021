<template>
  <div class="h-full">
    <div class="flex flex-col justify-between h-full">
      <!-- 搜索和导入区域 -->
      <div class="flex flex-row justify-between">
        <div class="relative w-full max-w-sm items-center">
          <Input
            id="search"
            v-model="search"
            type="text"
            placeholder="搜索..."
            class="pl-10"
            @input="handleSearch"
          />
          <span
            class="absolute start-0 inset-y-0 flex items-center justify-center px-2 text-muted-foreground"
          >
            <Icon icon="flowbite:search-outline" :width="24" :height="24" />
          </span>
        </div>

        <Button class="text-white" @click="handleImport">
          <FileUp class="mr-2 h-4 w-4" />
          导入
        </Button>
      </div>

      <!-- 工作流列表区域 -->
      <div class="flex-1 py-2 overflow-y-auto overflow-x-hidden scrollbar">
        <div class="h-full">
          <template v-if="currentWorkflows.length > 0">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
              <Card
                v-for="item in currentWorkflows"
                :key="item.id"
                class="overflow-hidden hover:shadow-md transition-shadow duration-300 border-border/50"
              >
                <CardHeader class="p-2 pb-2">
                  <div class="flex items-center justify-between">
                    <div class="bg-primary/10 p-2 rounded-md flex items-center">
                      <Icon
                        icon="flowbite:draw-square-outline"
                        :width="24"
                        :height="24"
                        class="text-primary"
                      />
                    </div>
                    <div class="cursor-pointer">
                      <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                          <button
                            class="focus:outline-none text-muted-foreground hover:text-foreground transition-colors"
                          >
                            <Icon
                              icon="flowbite:dots-horizontal-outline"
                              :width="24"
                              :height="24"
                            />
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem @click="handelDropdown('download', item)">
                            <FileDown class="mr-2 h-4 w-4" />
                            <span>导出</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem @click="handelDropdown('edit', item)">
                            <Edit2 class="mr-2 h-4 w-4" />
                            <span>重命名</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            class="text-destructive hover:text-destructive hover:bg-destructive/50"
                            @click="handelDropdown('delete', item)"
                          >
                            <Trash class="mr-2 h-4 w-4" />
                            <span>删除</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>
                <CardContent class="p-2 pt-2 cursor-pointer" @click="handleClickWorkflow(item)">
                  <h3 class="text-lg font-semibold text-foreground truncate mb-2">
                    {{ item.title }}
                  </h3>
                  <div class="overflow-hidden">
                    <p class="text-sm text-muted-foreground truncate">
                      {{ item.description || '暂无描述' }}
                    </p>
                  </div>
                </CardContent>
                <CardFooter class="p-2 pt-2 mt-auto border-t border-border/30">
                  <div class="w-full overflow-hidden">
                    <p class="text-xs text-muted-foreground flex items-center">
                      <Icon icon="flowbite:clock-outline" class="mr-1 flex-shrink-0 h-4 w-4" />
                      <span class="truncate">{{ item.createTime }}</span>
                    </p>
                  </div>
                </CardFooter>
              </Card>
            </div>
          </template>
          <!-- 无数据时显示空状态 -->
          <EmptyState
            v-else
            title="暂无工作流"
            description="当前文件夹中没有工作流，您可以创建一个新的工作流或导入已有的工作流"
            icon="flowbite:chart-pie-outline"
            :icon-size="96"
          >
            <template #action>
              <Button class="text-white" @click="handleCreateWorkflow">
                <Icon icon="flowbite:plus-outline" class="mr-2 h-4 w-4" />
                创建工作流
              </Button>
            </template>
          </EmptyState>
        </div>
      </div>
    </div>
    <WorkflowDialog ref="workflowDialogRef" width="40%" @confirm="handleWfAction" />
    <AlertDialog v-model:open="isDeleteDialogOpen">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除工作流 "{{ workflowToDelete?.title }}" 吗？此操作无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            @click="confirmDeleteWorkflow"
          >
            删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>
<script setup>
import { ref, computed, nextTick } from 'vue'
import { Icon } from '@iconify/vue'
import { FileUp, FileDown, Edit2, Trash } from 'lucide-vue-next'
import { WorkflowDialog, EmptyState } from '@renderer/components'
import { useWorkflowStore, useFlowsStore, useNavbarStore } from '@renderer/store'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { useDebounceFn } from '@vueuse/core'
import * as msgpack from '@msgpack/msgpack'

const router = useRouter()
const workflowStore = useWorkflowStore()
const flowsStore = useFlowsStore()
const navbarStore = useNavbarStore()

const { currentWfId } = storeToRefs(workflowStore)

const workflowDialogRef = ref(null)
const search = ref('')
const searchTimeout = ref(null) // 添加防抖处理
const filteredWorkflows = ref([])
const loading = ref(false)
// 删除确认对话框
const isDeleteDialogOpen = ref(false)
const workflowToDelete = ref(null)

const currentWorkflows = computed(() => {
  return search.value ? filteredWorkflows.value : workflowStore.getCurrentFolderWorkflows()
})

// 创建工作流的方法
const handleCreateWorkflow = () => {
  workflowDialogRef.value?.open('add')
}

const handleWfAction = useDebounceFn(async (formData, type) => {
  console.log('handleWfAction', formData, type)
  if (!formData.name) {
    toast.warning('请输入名称')
    return
  }

  loading.value = true
  try {
    if (type === 'add') {
      // 新建工作流
      const newWorkflow = workflowStore.addWorkflow({
        title: formData.name,
        description: formData.description,
        folderId: workflowStore.currentFolderId,
      })

      if (newWorkflow) {
        // 创建流程图数据
        flowsStore.createWorkflow(newWorkflow.id)

        // 延迟添加标签和导航
        await nextTick()
        navbarStore.addTag({
          id: newWorkflow.id,
          title: newWorkflow.title,
          path: `/workflow/editor/${newWorkflow.id}`,
        })

        router.push({
          name: 'workflow-editor',
          params: { id: newWorkflow.id },
          query: { title: newWorkflow.title },
        })

        toast.success('创建成功', {
          description: `工作流 "${newWorkflow.title}" 已创建`,
        })
      }
    } else {
      // 编辑工作流
      console.log('编辑---')
      await nextTick()
      workflowStore.editWorkflow({
        id: currentWfId.value,
        title: formData.name,
        description: formData.description,
      })

      toast.success('更新成功', {
        description: `工作流已更新`,
      })
    }
    // 关闭对话框
    workflowDialogRef.value?.close()
  } catch (error) {
    toast.error('操作失败', {
      description: `${type === 'add' ? '新建' : '修改'}工作流失败`,
    })
  } finally {
    loading.value = false
  }
}, 300)

// 删除工作流处理
const handleDeleteWorkFlow = useDebounceFn(async (workflowId) => {
  loading.value = true
  try {
    await workflowStore.deleteWorkflow(workflowId)
    toast.success('删除成功', {
      description: `工作流已删除`,
    })
  } catch (error) {
    toast.error('删除失败', {
      description: `删除工作流失败:${error.message}`,
    })
  } finally {
    loading.value = false
  }
}, 300)

// 下拉菜单处理
const handelDropdown = useDebounceFn(async (val, workflow) => {
  workflowStore.currentWfId = workflow.id
  switch (val) {
    case 'download':
      await handleDownload()
      break
    case 'edit':
      await nextTick()
      workflowDialogRef.value?.open('edit', workflow)
      break
    case 'delete':
      // await handleDeleteWorkFlow(workflow.id)
      workflowToDelete.value = workflow
      isDeleteDialogOpen.value = true
      break
  }
}, 300)

// 确认删除工作流
const confirmDeleteWorkflow = async () => {
  if (workflowToDelete.value) {
    await handleDeleteWorkFlow(workflowToDelete.value.id)
    // 关闭对话框
    isDeleteDialogOpen.value = false
    workflowToDelete.value = null
  }
}

//工作流导出
const handleDownload = async () => {
  try {
    // 检查 electronAPI 是否可用
    if (!window.electronAPI) {
      toast.warning('导出失败', {
        description: `导出功能在当前环境不可用`,
      })
      return
    }
    // 获取当前工作流的基本信息
    const currentWorkflow = workflowStore.workflows.find(
      (wf) => wf.id === workflowStore.currentWfId,
    )

    // 获取流程图数据
    const flowData = flowsStore.getWorkflow(workflowStore.currentWfId)

    if (!currentWorkflow) {
      toast.warning('提示', {
        description: `当前没有可导出的工作流`,
      })
      return
    }

    // 组合工作流信息和流程图数据
    const workflowData = {
      id: currentWorkflow.id,
      title: currentWorkflow.title,
      description: currentWorkflow.description,
      createTime: currentWorkflow.createTime,
      flowData: {
        nodes: flowData.nodes || [],
        edges: flowData.edges || [],
      },
    }

    // 获取当前日期时间作为文件名的一部分
    const dateStr = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19)
    const defaultFileName = `${currentWorkflow.title || 'workflow'}_${dateStr}.workflow`
    console.log('workflowData', workflowData)
    // 使用 msgpack 编码数据
    const encodedData = msgpack.encode(workflowData)

    // 使用 electronAPI.saveFile 保存文件
    const result = await window.electronAPI.saveFile({
      title: '保存工作流配置',
      defaultPath: defaultFileName,
      filters: [
        { name: '工作流文件', extensions: ['workflow'] },
        { name: '所有文件', extensions: ['*'] },
      ],
      content: encodedData,
      isBinary: true, // 标记为二进制数据
    })

    if (result.success) {
      toast.success('导出成功', {
        description: `文件已保存至: ${result.filePath}`,
      })
    } else if (!result.canceled) {
      toast.error('导出失败', {
        description: result.error || '未知错误',
      })
    }
  } catch (error) {
    toast.error('导出失败', {
      description: error.message || '未知错误',
    })
  }
}

// 工作流导入
const handleImport = async () => {
  try {
    // 检查 electronAPI 是否可用
    if (!window.electronAPI) {
      toast.warning('提示', {
        description: '导入功能在当前环境不可用',
      })
      return
    }

    // 调用打开文件对话框
    const result = await window.electronAPI.openFile({
      title: '导入工作流配置',
      filters: [
        { name: '工作流文件', extensions: ['workflow'] },
        { name: 'JSON文件', extensions: ['json'] },
        { name: '所有文件', extensions: ['*'] },
      ],
      properties: ['openFile'],
    })

    if (!result.filePaths || result.filePaths.length === 0 || result.canceled) {
      return
    }

    const filePath = result.filePaths[0]
    const fileExt = filePath.substring(filePath.lastIndexOf('.')).toLowerCase()

    // 获取文件名（不包含扩展名）
    const fileName = filePath
      .split('\\')
      .pop()
      .split('/')
      .pop()
      .replace(/\.(json|workflow)$/, '')

    let workflowData

    if (fileExt === '.workflow') {
      // 读取二进制文件内容
      const fileBuffer = await window.electronAPI.readFile({
        filePath: filePath,
        encoding: null, // 指定为null以获取Buffer
        isBinary: true,
      })

      if (!fileBuffer) {
        toast.error('导入失败', {
          description: '无法读取文件内容',
        })
        return
      }

      // 使用 msgpack 解码数据
      workflowData = msgpack.decode(new Uint8Array(fileBuffer))
    } else {
      // 读取JSON文件
      const fileContent = await window.electronAPI.readFile({
        filePath: filePath,
        encoding: 'utf-8',
      })

      if (!fileContent) {
        toast.error('导入失败', {
          description: '无法读取文件内容',
        })
        return
      }

      // 解析 JSON 数据
      workflowData = JSON.parse(fileContent)
    }

    // 验证数据结构
    if (!workflowData.id || !workflowData.title || !workflowData.flowData) {
      toast.warning('提示', {
        description: '无效的工作流配置文件',
      })
      return
    }

    // 检查工作流是否已存在
    const existingWorkflow = workflowStore.workflows.find((wf) => wf.id === workflowData.id)
    const newWorkflowId = existingWorkflow ? `${workflowData.id}-${Date.now()}` : workflowData.id

    // 确保 flowData 包含必要的属性
    const flowData = {
      nodes: workflowData.flowData.nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          label: node.data.label || '未命名节点',
          description: node.data.description || '',
          icon: node.data.icon || {
            type: 'icon',
            value: 'flowbite:draw-square-outline',
          },
        },
      })),
      edges: workflowData.flowData.edges.map((edge) => ({
        ...edge,
        type: edge.type || 'smoothstep',
        animated: edge.animated !== undefined ? edge.animated : true,
      })),
    }

    // 先保存流程图数据
    flowsStore.saveWorkflow(newWorkflowId, flowData)

    // 保存工作流基本信息
    // workflowStore.addWorkflow({
    //   id: newWorkflowId,
    //   title: workflowData.title || fileName,
    //   description: workflowData.description || '',
    //   createTime: workflowData.createTime || new Date().toLocaleString(),
    //   folderId: workflowStore.currentFolderId,
    // })

    // 添加标签
    navbarStore.addTag({
      id: newWorkflowId,
      title: fileName || workflowData.title,
      path: `/workflow/editor/${newWorkflowId}`,
    })

    // 导航到编辑器页面
    router.push({
      name: 'workflow-editor',
      params: { id: newWorkflowId },
      query: {
        title: fileName || workflowData.title,
      },
    })

    // console.log('Import successful:', {
    //   workflowId: newWorkflowId,
    //   flowData,
    //   fileName,
    // })

    toast.success('导入成功', {
      description: `工作流 "${fileName || workflowData.title}" 已导入`,
    })
  } catch (error) {
    toast.error('导入失败', {
      description: error.message || '未知错误',
    })
  }
}

// 搜索处理
const handleSearch = useDebounceFn((val) => {
  if (searchTimeout.value) clearTimeout(searchTimeout.value)
  searchTimeout.value = setTimeout(() => {
    filteredWorkflows.value = workflowStore.searchWorkflow(search.value)
  }, 300)
}, 300)

const handleClickWorkflow = useDebounceFn(async (workflow) => {
  loading.value = true
  try {
    await nextTick()
    navbarStore.addTag({
      id: workflow.id,
      title: workflow.title,
      path: `/workflow/editor/${workflow.id}`,
    })

    await router.push({
      name: 'workflow-editor',
      params: { id: workflow.id },
      query: { title: workflow.title },
    })
  } catch (error) {
    toast.error('跳转失败', {
      description: '无法打开工作流',
    })
  } finally {
    loading.value = false
  }
}, 300)
</script>
<style lang="scss"></style>
