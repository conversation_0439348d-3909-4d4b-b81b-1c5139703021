import * as THREE from 'three'
class CreateMesh {
  // 创建球体
  createSphere(x: number, y: number, z: number, radius: number, color: string) {
    const geometry = new THREE.SphereGeometry(radius, 32, 32)
    const material = new THREE.MeshPhysicalMaterial({
      color: new THREE.Color(color),
      metalness: 0.8,
      roughness: 0.5,
    })
    const sphere = new THREE.Mesh(geometry, material)
    sphere.position.set(x, y, z)
    return sphere
  }
  // 创建圆柱体
  createCylinderLine(start, end, radius, color) {
    const s = new THREE.Vector3().copy(start)
    const e = new THREE.Vector3().copy(end)
    const length = s.distanceTo(e)
    const geometry = new THREE.CylinderGeometry(radius, radius, length, 8, 1)
    const material = new THREE.MeshPhysicalMaterial({
      color: new THREE.Color(color),
      metalness: 0.95,
      roughness: 0.3,
    })
    const cylinder = new THREE.Mesh(geometry, material)

    const center = new THREE.Vector3().addVectors(s, e).multiplyScalar(0.5)
    cylinder.position.copy(center)

    const direction = new THREE.Vector3().subVectors(e, s).normalize()
    cylinder.quaternion.setFromUnitVectors(new THREE.Vector3(0, 1, 0), direction)

    return cylinder
  }
  // 创建线
  createLine(start, end, color) {
    const s = new THREE.Vector3().copy(start)
    const e = new THREE.Vector3().copy(end)
    const geometry = new THREE.BufferGeometry().setFromPoints([s, e])
    const material = new THREE.LineBasicMaterial({ color: new THREE.Color(color), linewidth: 1 })
    const line = new THREE.Line(geometry, material)
    return line
  }
  // 创建group
  createGroup(meshes: any) {
    const group = new THREE.Group()
    meshes.forEach((mesh) => {
      group.add(mesh)
    })
    return group
  }
  // 获取不规则模型中心点
  getCenter(group: any) {
    const box = new THREE.Box3().setFromObject(group)
    const center = new THREE.Vector3()
    box.getCenter(center)
    return center
  }
  // 根据模型设置相机的位置，让相机始终在看向模型中心，并且保持模型在视野内
  setCamera(group: any, camera: any, controls: any) {
    const box = new THREE.Box3().setFromObject(group)
    const size = box.getSize(new THREE.Vector3()) // 模型尺寸
    const center = box.getCenter(new THREE.Vector3()) // 模型中心点
    // 计算相机距离
    const maxDim = Math.max(size.x, size.y, size.z)
    const fov = camera.fov * (Math.PI / 180)
    let cameraZ = Math.abs(maxDim / Math.sin(fov / 2))
    // 防止模型过近或者过远（可选约束）
    cameraZ = Math.max(cameraZ, maxDim * 1.5)
    camera.position.set(center.x, center.y, cameraZ)
    camera.lookAt(center)
    controls.target.copy(center) // 关键步骤
    controls.update() // 立即生效
  }
}
export default CreateMesh
