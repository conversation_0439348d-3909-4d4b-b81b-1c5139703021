<template>
  <ScrollArea class="h-full w-full">
    <div class="base-setting">
      <!-- 主题设置 -->
      <ThemeSetting />

      <!-- 缓存设置 -->
      <CacheSetting />

      <!-- 文件加密设置
      <FileEncryptSetting /> -->

      <!-- 语言设置 -->
      <LanguageSetting />

      <!-- 字体设置 -->
      <FontSetting />

      <!-- 字号设置 -->
      <FontSizeSetting />
    </div>
  </ScrollArea>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@renderer/store'
import { onMounted } from 'vue'
import {
  CacheSetting,
  FileEncryptSetting,
  FontSetting,
  FontSizeSetting,
  LanguageSetting,
  ThemeSetting,
} from './components'

const settingsStore = useSettingsStore()

// 初始化时应用保存的设置
onMounted(() => {
  settingsStore.initializeSettings()
})
</script>

<style scoped>
.base-setting {
  @apply max-w-3xl mx-auto;
}
</style>
