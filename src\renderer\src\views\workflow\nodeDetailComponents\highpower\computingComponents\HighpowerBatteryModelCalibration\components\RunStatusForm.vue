<template>
  <div class="w-full">
    <div class="mb-2">
      <div class="text-sm font-bold mb-2">
        <span>充电工况</span>
      </div>
      <div class="flex justify-between flex-wrap">
        <div class="w-[49%] mb-2">
          <div class="text-sm font-medium mb-2">
            <span>运行工况</span>
          </div>
          <div>
            <Select :value="chargeForm.runType" @update:model-value="onChargeRunType">
              <SelectTrigger>
                <SelectValue placeholder="请选择运行工况" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem v-for="item in runTypeList" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div class="w-[49%] flex justify-start mb-2">
          <div class="w-[100%]">
            <div class="text-sm font-medium mb-2">
              <span>{{ powerChargeLabel[0].label }}</span>
            </div>
            <div>
              <Input v-model="chargeForm.power" class="w-full" type="text" @change="inputChange" />
            </div>
          </div>
          <div v-if="chargeForm.runType === 2" class="w-[100%] ml-2">
            <div class="text-sm font-medium mb-2">
              <span>{{ powerChargeLabel[1].label }}</span>
            </div>
            <div>
              <Input
                v-model="chargeForm.voltageVal"
                type="text"
                class="w-full"
                @change="inputChange"
              />
            </div>
          </div>
        </div>
        <div class="w-[49%] mb-2">
          <div class="text-sm font-medium mb-2">
            <span>充电完后静置时间</span>
          </div>
          <div class="relative">
            <div class="w-full box-border">
              <Input v-model="chargeForm.holdTime" type="text" @change="inputChange" />
            </div>
            <div class="absolute top-[50%] right-5 translate-y-[-50%]">
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <span class="h-full text-blue-500">{{ selectChargeLabel }}</span>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    v-for="item in timeUnitList"
                    :key="item.value"
                    :value="item.value"
                    @click="selectChargeMenu(item.value)"
                  >
                    <span>{{ item.label }}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mb-2">
      <div class="text-sm font-bold mb-2">
        <span>放电工况</span>
      </div>
      <div class="flex justify-between flex-wrap">
        <div class="w-[49%] mb-2">
          <div class="text-sm font-medium mb-2">
            <span>运行工况</span>
          </div>
          <div>
            <Select :value="dischargeForm.runType" @update:model-value="onDischargeRunType">
              <SelectTrigger>
                <SelectValue placeholder="请选择运行工况" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem v-for="item in runTypeList" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div class="w-[49%] flex justify-start mb-2">
          <div class="w-[100%]">
            <div class="text-sm font-medium mb-2">
              <span>{{ powerDischargeLabel[0].label }}</span>
            </div>
            <div>
              <Input
                v-model="dischargeForm.power"
                class="w-full"
                type="text"
                @change="inputChange"
              />
            </div>
          </div>
          <div v-if="dischargeForm.runType === '2'" class="w-[100%] ml-2">
            <div class="text-sm font-medium mb-2">
              <span>{{ powerDischargeLabel[1].label }}</span>
            </div>
            <div>
              <Input
                v-model="dischargeForm.voltageVal"
                type="text"
                class="w-full"
                @change="inputChange"
              />
            </div>
          </div>
        </div>
        <div class="w-[49%] mb-2">
          <div class="text-sm font-medium mb-2">
            <span>放电完后静置时间</span>
          </div>
          <div class="relative">
            <div class="w-full box-border">
              <Input v-model="dischargeForm.holdTime" type="text" @change="inputChange" />
            </div>
            <div class="absolute top-[50%] right-5 translate-y-[-50%]">
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <span class="h-full text-blue-500">{{ selectDischargeLabel }}</span>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    v-for="item in timeUnitList"
                    :key="item.value"
                    :value="item.value"
                    @click="selectDischargeMenu(item.value)"
                  >
                    <span>{{ item.label }}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
const emits = defineEmits(['change'])
// 放电工况
const runTypeList: any = [
  {
    label: '恒功率充电(CP)',
    value: '0',
  },
  {
    label: '恒流充电(CC)',
    value: '1',
  },
  {
    label: '恒流恒压充电(CCCV)',
    value: '2',
  },
]
// 时间单位
const timeUnitList: any = [
  {
    label: '时',
    value: 0,
  },
  {
    label: '分',
    value: 1,
  },
  {
    label: '秒',
    value: 2,
  },
]
// 充电工况
const chargeForm: any = ref({
  runType: 0, // 运行工况
  power: null, // 充电功率
  voltageVal: null, // 充电电压
  holdTime: null, // 充电完后静置时间
  timeUnit: 0, // 时间单位
})
// 通过运行工况来动态充电功率
const chargeTypeMap: any = {
  '0': [
    {
      label: '充电功率(W)',
    },
  ],
  '1': [
    {
      label: '充电电流(A)',
    },
  ],
  '2': [
    {
      label: '充电电流(A)',
    },
    {
      label: '充电电压(V)',
    },
  ],
}
const onChargeRunType = (value: number | string) => {
  chargeForm.value.runType = value
  sendData()
}
const powerChargeLabel = computed(() => chargeTypeMap[chargeForm.value.runType]) // 放电工况
const selectChargeLabel = computed(() => {
  const unit = timeUnitList.find((item) => item.value === Number(chargeForm.value.timeUnit))
  return unit?.label || ''
})
const selectChargeMenu = (value: number | string) => {
  chargeForm.value.timeUnit = value
}
// 放电工况
const dischargeForm: any = ref({
  runType: 0, // 运行工况
  power: null, // 充电功率
  voltageVal: null, // 充电电压
  holdTime: null, // 充电完后静置时间
  timeUnit: 0, // 时间单位
})
// 通过运行工况来动态充电功率
const dischargeTypeMap = {
  '0': [
    {
      label: '充电功率(W)',
    },
  ],
  '1': [
    {
      label: '充电电流(A)',
    },
  ],
  '2': [
    {
      label: '充电电流(A)',
    },
    {
      label: '充电电压(V)',
    },
  ],
}
const onDischargeRunType = (value: number | string) => {
  dischargeForm.value.runType = value
  sendData()
}
const selectDischargeMenu = (value: number | string) => {
  dischargeForm.value.timeUnit = value
}
const powerDischargeLabel = computed(() => dischargeTypeMap[dischargeForm.value.runType]) // 放电工况
const selectDischargeLabel = computed(() => {
  const unit = timeUnitList.find((item) => item.value === dischargeForm.value.timeUnit)
  return unit?.label || ''
})
const inputChange = () => {
  sendData()
}
// 发送数据
const sendData = () => {
  emits('change', {
    chargeForm: {
      runType: chargeForm.value.runType,
      power: chargeForm.value.power,
      voltageVal: chargeForm.value.voltageVal,
      holdTime: chargeForm.value.holdTime,
      timeUnit: chargeForm.value.timeUnit,
    },
    dischargeForm: {
      runType: dischargeForm.value.runType,
      power: dischargeForm.value.power,
      voltageVal: chargeForm.value.voltageVal,
      holdTime: dischargeForm.value.holdTime,
      timeUnit: dischargeForm.value.timeUnit,
    },
  })
}
</script>
