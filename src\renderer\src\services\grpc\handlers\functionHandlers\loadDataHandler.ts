/**
 * 加载数据处理器
 */
import { useWorkflowStore } from '@renderer/store'
import { addWorkflowDataToCanvas } from '../../workflow/canvas'

/**
 * 处理加载数据功能
 * @param data 加载数据功能调用数据
 */
export function handleLoadDataFunction(data: any): void {
  try {
    window.logger.info('收到数据加载请求:', data)

    // 确保有有效的参数
    if (!data.key_value_pairs || !data.key_value_pairs.call_params) {
      window.logger.warn('数据加载请求缺少必要参数')
      return
    }

    // 解析数据
    let loadData
    try {
      // 如果是字符串，则解析为JSON
      if (typeof data.key_value_pairs.call_params === 'string') {
        loadData = JSON.parse(data.key_value_pairs.call_params)
      } else {
        // 如果已经是对象，直接使用
        loadData = data.key_value_pairs.call_params
      }
    } catch (error) {
      window.logger.error('解析数据加载数据失败:', error)
      return
    }

    // 确保有工作流名称、节点和边
    if (!loadData.workflow_name || !loadData.nodes) {
      window.logger.warn('数据加载数据格式不正确:', loadData)
      return
    }

    // 获取当前工作流ID
    const workflowStore = useWorkflowStore()
    const currentWorkflowId = workflowStore.currentWfId

    if (!currentWorkflowId) {
      window.logger.warn('未找到当前工作流ID，无法添加节点')
      return
    }

    // 处理节点数据，确保节点类型正确
    const processedNodes = loadData.nodes.map((node) => {
      // 确保节点类型与 data.type 保持一致
      return {
        ...node,
        type: node.data.type || 'custom',
        data: {
          ...node.data,
          // workflowId: workflowId, // 添加工作流ID
        },
      }
    })

    // 添加到画布
    window.logger.info(`开始将数据添加到画布...`)
    addWorkflowDataToCanvas(currentWorkflowId, processedNodes, loadData.edges || [])
  } catch (error) {
    window.logger.error('处理数据加载失败:', error)
  }
}
