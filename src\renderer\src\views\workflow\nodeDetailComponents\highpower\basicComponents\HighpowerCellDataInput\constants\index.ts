// 材料参数结构
export const EXPECTED_MATERIAL_PARAMETERS = {
  正极: [
    { parameter: '克容量', variableName: 'positive_electrode_capacity' },
    { parameter: '密度', variableName: 'positive_electrode_density' },
    { parameter: '平衡电位', variableName: 'positive_electrode_equilibrium_potential' },
    { parameter: '温熵系数', variableName: 'positive_electrode_entropy_coefficient' },
    { parameter: '粒径分布均值', variableName: 'positive_electrode_mean_particle_size' },
    { parameter: '粒径分布方差', variableName: 'positive_electrode_particle_size_variance' },
    { parameter: '粒径D50', variableName: 'positive_electrode_particle_size_d50' },
    { parameter: '孔隙率', variableName: 'positive_electrode_porosity' },
    { parameter: '电导率', variableName: 'positive_electrode_conductivity' },
    {
      parameter: '活性物质体积分数',
      variableName: 'positive_electrode_active_material_volume_fraction',
    },
    {
      parameter: '导电剂体积分数',
      variableName: 'positive_electrode_conductive_agent_volume_fraction',
    },
    { parameter: '粘结剂体积分数', variableName: 'positive_electrode_binder_volume_fraction' },
    { parameter: '比热容', variableName: 'positive_electrode_specific_heat_capacity' },
    { parameter: '导热系数', variableName: 'positive_electrode_thermal_conductivity' },
    { parameter: '面密度', variableName: 'positive_electrode_surface_density' },
  ],
  负极: [
    { parameter: '克容量', variableName: 'negative_electrode_capacity' },
    { parameter: '密度', variableName: 'negative_electrode_density' },
    { parameter: '平衡电位', variableName: 'negative_electrode_equilibrium_potential' },
    { parameter: '温熵系数', variableName: 'negative_electrode_entropy_coefficient' },
    { parameter: '粒径分布均值', variableName: 'negative_electrode_mean_particle_size' },
    { parameter: '粒径分布方差', variableName: 'negative_electrode_particle_size_variance' },
    { parameter: '粒径D50', variableName: 'negative_electrode_particle_size_d50' },
    { parameter: '孔隙率', variableName: 'negative_electrode_porosity' },
    { parameter: '电导率', variableName: 'negative_electrode_conductivity' },
    {
      parameter: '活性物质体积分数',
      variableName: 'negative_electrode_active_material_volume_fraction',
    },
    {
      parameter: '导电剂体积分数',
      variableName: 'negative_electrode_conductive_agent_volume_fraction',
    },
    { parameter: '粘结剂体积分数', variableName: 'negative_electrode_binder_volume_fraction' },
    { parameter: '比热容', variableName: 'negative_electrode_specific_heat_capacity' },
    { parameter: '导热系数', variableName: 'negative_electrode_thermal_conductivity' },
    { parameter: '面密度', variableName: 'negative_electrode_surface_density' },
  ],
  隔膜: [
    { parameter: '孔隙率', variableName: 'separator_porosity' },
    { parameter: '密度', variableName: 'separator_density' },
    { parameter: '导热系数', variableName: 'separator_thermal_conductivity' },
    { parameter: '比热容', variableName: 'separator_specific_heat_capacity' },
  ],
  电解液: [
    { parameter: '密度', variableName: 'electrolyte_density' },
    { parameter: '导热系数', variableName: 'electrolyte_thermal_conductivity' },
    { parameter: '比热容', variableName: 'electrolyte_specific_heat_capacity' },
    { parameter: '电解液组分', variableName: 'electrolyte_electrolyte_composition' },
    { parameter: '电解质盐浓度', variableName: 'electrolyte_electrolyte_salt_concentration' },
  ],
  电芯: [
    { parameter: '容量', variableName: 'battery_cell_capacity' },
    { parameter: '充电截止电压', variableName: 'battery_cell_charge_cutoff_voltage' },
    { parameter: '放电截止电压', variableName: 'battery_cell_discharge_cutoff_voltage' },
  ],
}

// 几何参数结构
export const EXPECTED_GEOMETRY_PARAMETERS = {
  正极: [
    { parameter: '极片厚度', variableName: 'positive_electrode_thickness' },
    { parameter: '极片宽度', variableName: 'positive_electrode_width' },
    { parameter: '单芯极片总长度', variableName: 'positive_electrode_single_core_length' },
    { parameter: '卷芯数', variableName: 'positive_electrode_core_count' },
    { parameter: '铝箔厚度', variableName: 'positive_electrode_aluminum_foil_thickness' },
    { parameter: '极片层数', variableName: 'positive_electrode_layer_count' },
  ],
  负极: [
    { parameter: '极片厚度', variableName: 'negative_electrode_thickness' },
    { parameter: '极片宽度', variableName: 'negative_electrode_width' },
    { parameter: '单芯极片总长度', variableName: 'negative_electrode_single_core_length' },
    { parameter: '卷芯数', variableName: 'negative_electrode_core_count' },
    { parameter: '铜箔厚度', variableName: 'negative_electrode_copper_foil_thickness' },
    { parameter: '极片层数', variableName: 'negative_electrode_layer_count' },
  ],
  隔膜: [{ parameter: '厚度', variableName: 'separator_thickness' }],
  电芯: [{ parameter: '电池类型', variableName: 'battery_cell_battery_type' }],
}
