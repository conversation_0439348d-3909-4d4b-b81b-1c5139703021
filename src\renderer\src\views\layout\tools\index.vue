<template>
  <Container>
    <div class="flex flex-col space-y-6">
      <!-- 标题和操作区 -->
      <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <HeaderTitle :title="t('tools.title')" :tooltip="t('tools.tooltip')" :show-icon="true" />
        <div class="flex flex-wrap gap-2">
          <Button
            size="sm"
            variant="outline"
            class="flex items-center gap-1 hover:bg-primary/10 transition-colors duration-300"
            @click="openImportDialog('node')"
          >
            <FileUp class="w-4 h-4" />
            导入工具
          </Button>
          <!-- <Button
            size="sm"
            variant="outline"
            class="flex items-center gap-1 hover:bg-blue-500/10 transition-colors duration-300"
            @click="openImportDialog('robot')"
          >
            <Icon icon="mdi:robot-outline" class="w-4 h-4" />
            导入机器人
          </Button> -->
          <Button v-if="!isMatt" size="sm" @click="goBack">
            <ArrowLeft class="w-4 h-4 mr-2" />
            返回
          </Button>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="relative w-full max-w-md">
        <Search
          class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"
        />
        <Input v-model="searchQuery" placeholder="搜索工具和模块..." class="pl-10 pr-4 py-2" />
      </div>

      <!-- 工具类别网格布局 -->
      <ScrollArea class="w-full h-[80vh]">
        <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
          <!-- 工具模块分组 -->
          <Collapsible
            :open="nodeToolsOpen"
            class="border border-border/50 rounded-lg overflow-hidden bg-card shadow-sm transition-all duration-300 hover:shadow-md"
          >
            <CollapsibleTrigger
              class="flex items-center justify-between w-full p-4 hover:bg-muted/30 transition-colors"
              @click="nodeToolsOpen = !nodeToolsOpen"
            >
              <div class="flex items-center gap-2">
                <div class="bg-primary/10 p-2 rounded-full">
                  <Icon icon="cib:node-red" class="w-5 h-5 text-primary" />
                </div>
                <h3 class="text-lg font-medium">工具模块</h3>
              </div>
              <div class="flex items-center gap-2">
                <HoverCard>
                  <HoverCardTrigger as-child>
                    <Button
                      variant="ghost"
                      size="icon"
                      class="rounded-full hover:bg-primary/5"
                      @click.stop
                    >
                      <HelpCircle class="h-4 w-4" />
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent class="w-80" @click.stop>
                    <div class="flex justify-between space-x-4">
                      <div>
                        <h4 class="text-sm font-semibold">工具模块说明</h4>
                        <p class="text-sm">
                          工具模块是系统的功能扩展单元，可以通过导入.plugin文件添加新的功能模块。
                        </p>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
                <ChevronDown
                  class="h-5 w-5 transition-transform duration-300"
                  :class="{ 'transform rotate-180': nodeToolsOpen }"
                />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent class="p-4 animate-in fade-in-50 slide-in-from-top-5 duration-300">
              <NodeTools ref="nodeToolsRef" :search-query="searchQuery" />
            </CollapsibleContent>
          </Collapsible>

          <!-- 机器人分组 -->
          <!-- <Collapsible
            :open="robotToolsOpen"
            class="border border-border/50 rounded-lg overflow-hidden bg-card shadow-sm transition-all duration-300 hover:shadow-md"
          >
            <CollapsibleTrigger
              class="flex items-center justify-between w-full p-4 hover:bg-muted/30 transition-colors"
              @click="robotToolsOpen = !robotToolsOpen"
            >
              <div class="flex items-center gap-2">
                <div class="bg-blue-500/10 p-2 rounded-full">
                  <Icon icon="mdi:robot-outline" class="w-5 h-5 text-blue-500" />
                </div>
                <h3 class="text-lg font-medium">机器人</h3>
              </div>
              <div class="flex items-center gap-2">
                <HoverCard as-child>
                  <HoverCardTrigger as-child>
                    <Button variant="ghost" size="icon" class="rounded-full hover:bg-blue-500/5">
                      <HelpCircle class="h-4 w-4" />
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent class="w-80" @click.stop>
                    <div class="flex justify-between space-x-4">
                      <div>
                        <h4 class="text-sm font-semibold">机器人说明</h4>
                        <p class="text-sm">
                          机器人是自动化助手，可以帮助您完成特定任务，通过导入.robot文件添加新的机器人。
                        </p>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
                <ChevronDown
                  class="h-5 w-5 transition-transform duration-300"
                  :class="{ 'transform rotate-180': robotToolsOpen }"
                />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent class="p-4 animate-in fade-in-50 slide-in-from-top-5 duration-300">
              <RobotTools ref="robotToolsRef" />
            </CollapsibleContent>
          </Collapsible> -->

          <!-- 其他配置分组 -->
          <Collapsible
            :open="otherToolsOpen"
            class="border border-border/50 rounded-lg overflow-hidden bg-card shadow-sm transition-all duration-300 hover:shadow-md"
          >
            <CollapsibleTrigger
              class="flex items-center justify-between w-full p-4 hover:bg-muted/30 transition-colors"
              @click="otherToolsOpen = !otherToolsOpen"
            >
              <div class="flex items-center gap-2">
                <div class="bg-gray-500/10 p-2 rounded-full">
                  <Settings class="w-5 h-5 text-gray-500" />
                </div>
                <h3 class="text-lg font-medium">其他配置</h3>
              </div>
              <div class="flex items-center gap-2">
                <HoverCard>
                  <HoverCardTrigger as-child>
                    <Button variant="ghost" size="icon" class="rounded-full hover:bg-gray-500/5">
                      <HelpCircle class="h-4 w-4" />
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent class="w-80" @click.stop>
                    <div class="flex justify-between space-x-4">
                      <div>
                        <h4 class="text-sm font-semibold">其他配置说明</h4>
                        <p class="text-sm">
                          这里可以配置系统的其他参数和功能，未来将添加更多配置选项。
                        </p>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
                <ChevronDown
                  class="h-5 w-5 transition-transform duration-300"
                  :class="{ 'transform rotate-180': otherToolsOpen }"
                />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent class="p-4 animate-in fade-in-50 slide-in-from-top-5 duration-300">
              <div class="text-center py-10">
                <EmptyState
                  title="暂无其他配置"
                  description="更多配置选项将在未来版本中添加"
                  icon="flowbite:cog-outline"
                  class="transition-all duration-500 hover:scale-105"
                />
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </ScrollArea>
    </div>
  </Container>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useLanguage } from '@renderer/config/hooks'
import { Container, HeaderTitle, EmptyState } from '@renderer/components'

import { Search, FileUp, ArrowLeft, HelpCircle, Settings, ChevronDown } from 'lucide-vue-next'
import { Icon } from '@iconify/vue'
import { useRouter } from 'vue-router'

import { NodeTools, RobotTools } from './components'

const { t } = useLanguage()
const router = useRouter()
const isMatt = ref(import.meta.env.VITE_APP_IS_MATT === '1')
const searchQuery = ref('')

const nodeToolsRef = ref<InstanceType<typeof NodeTools> | null>(null)
const robotToolsRef = ref<InstanceType<typeof RobotTools> | null>(null)

// 分组展开状态
const nodeToolsOpen = ref(true)
const robotToolsOpen = ref(false)
const otherToolsOpen = ref(false)

// 返回上一级路由
const goBack = () => {
  router.back()
}

// 打开导入对话框
const openImportDialog = (type: 'node' | 'robot') => {
  if (type === 'node') {
    nodeToolsRef.value?.openImportDialog()
  } else if (type === 'robot') {
    robotToolsRef.value?.openImportDialog()
  }
}
</script>

<style lang="scss" scoped>
/* 添加卡片悬浮效果 */
:deep(.card) {
  @apply transition-all duration-300 hover:translate-y-[-2px] hover:shadow-md;
}

/* 美化空状态 */
:deep(.empty-state) {
  @apply transition-all duration-300 hover:scale-105;
}

/* 分组动画 */
.collapsible-content {
  overflow: hidden;
}

.collapsible-content[data-state='open'] {
  animation: slideDown 300ms ease-out;
}

.collapsible-content[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}
</style>
