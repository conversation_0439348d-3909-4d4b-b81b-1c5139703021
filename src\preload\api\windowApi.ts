import { ipc<PERSON><PERSON><PERSON> } from 'electron'

/**
 * 窗口控制相关API
 */
export const windowApi = {
  /**
   * 最小化窗口
   */
  minimize: () => {
    try {
      ipcRenderer.send('window-minimize')
    } catch (error) {
      console.error('Failed to minimize window:', error)
    }
  },

  /**
   * 最大化/还原窗口
   */
  maximize: () => {
    try {
      ipcRenderer.send('window-maximize')
    } catch (error) {
      console.error('Failed to maximize window:', error)
    }
  },

  /**
   * 关闭窗口
   */
  close: () => {
    try {
      ipcRenderer.send('window-close')
    } catch (error) {
      console.error('Failed to close window:', error)
    }
  },

  /**
   * 检查窗口是否最大化
   */
  isMaximized: () => {
    try {
      return ipcRenderer.invoke('is-window-maximized')
    } catch (error) {
      console.error('Failed to check if window is maximized:', error)
      return Promise.resolve(false)
    }
  },

  /**
   * 监听窗口最大化状态变化
   */
  onMaximizedChange: (callback: (maximized: boolean) => void) => {
    try {
      ipcRenderer.on('window-maximized-change', (_event, maximized) => callback(maximized))
    } catch (error) {
      console.error('Failed to add maximized change listener:', error)
    }
  },

  /**
   * 移除窗口最大化状态变化监听
   */
  removeMaximizedChangeListener: (callback: (maximized: boolean) => void) => {
    try {
      ipcRenderer.removeListener('window-maximized-change', callback)
    } catch (error) {
      console.error('Failed to remove maximized change listener:', error)
    }
  },

  /**
   * 设置登录窗口大小
   */
  setLoginSize: () => {
    try {
      ipcRenderer.send('set-login-window-size')
    } catch (error) {
      console.error('Failed to set login window size:', error)
    }
  },

  /**
   * 登录后最大化窗口
   */
  maximizeAfterLogin: () => {
    try {
      ipcRenderer.send('adjust-window-after-login')
    } catch (error) {
      console.error('Failed to maximize window after login:', error)
    }
  },
}
