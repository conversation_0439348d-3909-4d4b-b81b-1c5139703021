<template>
  <div class="p-4 space-y-6">
    <h1 class="text-2xl font-semibold">AI 设置</h1>

    <section>
      <h2 class="text-lg font-medium mb-2">API 密钥管理</h2>
      <div class="space-y-4">
        <div>
          <label for="openai-api-key" class="block text-sm font-medium text-gray-700">
            OpenAI API 密钥
          </label>
          <Input
            id="openai-api-key"
            v-model="apiKeyOpenAI"
            type="password"
            placeholder="请输入您的 OpenAI API 密钥"
            class="mt-1"
          />
        </div>
        <div>
          <label for="ollama-api-key" class="block text-sm font-medium text-gray-700">
            Ollama API 密钥 (可选)
          </label>
          <Input
            id="ollama-api-key"
            v-model="apiKeyOllama"
            type="password"
            placeholder="请输入您的 Ollama API 密钥"
            class="mt-1"
          />
        </div>
        <Button @click="saveApiKeys">保存密钥</Button>
      </div>
    </section>

    <section>
      <h2 class="text-lg font-medium mb-2">模型选择</h2>
      <div class="space-y-4">
        <div>
          <label for="default-model" class="block text-sm font-medium text-gray-700">
            默认模型
          </label>
          <Select v-model="selectedModel">
            <SelectTrigger id="default-model" class="w-full">
              <SelectValue placeholder="选择一个模型" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>可用模型</SelectLabel>
                <SelectItem v-for="model in availableModels" :key="model.id" :value="model.id">
                  {{ model.name }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <Button @click="saveModelSelection">保存模型选择</Button>
      </div>
    </section>

    <section>
      <h2 class="text-lg font-medium mb-2">其他设置</h2>
      <div class="flex items-center space-x-2">
        <Switch id="enable-streaming" v-model="enableStreaming" />
        <Label for="enable-streaming">启用流式响应</Label>
      </div>
      <div class="flex items-center space-x-2 mt-4">
        <Switch id="enable-history" v-model="enableHistory" />
        <Label for="enable-history">保存对话历史</Label>
      </div>
      <Button class="mt-4" @click="saveOtherSettings">保存其他设置</Button>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import { toast } from 'vue-sonner'

// API 密钥
const apiKeyOpenAI = ref('')
const apiKeyOllama = ref('')

const saveApiKeys = () => {
  // 在实际应用中，这里会将密钥保存到安全的地方，例如 Electron store 或后端
  console.log('OpenAI API Key:', apiKeyOpenAI.value)
  console.log('Ollama API Key:', apiKeyOllama.value)
  toast({
    title: 'API 密钥已保存',
    description: '您的 API 密钥已成功更新。',
  })
}

// 模型选择
interface Model {
  id: string
  name: string
}

const selectedModel = ref<string | undefined>(undefined)
const availableModels = ref<Model[]>([
  { id: 'gpt-4', name: 'GPT-4 (OpenAI)' },
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo (OpenAI)' },
  { id: 'claude-2', name: 'Claude 2 (Anthropic)' },
  { id: 'llama-2-7b', name: 'Llama 2 7B (Ollama)' },
  { id: 'custom-model', name: '自定义模型' },
])

const saveModelSelection = () => {
  console.log('Selected Model:', selectedModel.value)
  toast({
    title: '模型选择已保存',
    description: `默认模型已设置为 ${selectedModel.value}.`,
  })
}

// 其他设置
const enableStreaming = ref(true)
const enableHistory = ref(true)

const saveOtherSettings = () => {
  console.log('Enable Streaming:', enableStreaming.value)
  console.log('Enable History:', enableHistory.value)
  toast({
    title: '其他设置已保存',
    description: '相关设置已更新。',
  })
}

// 初始化加载设置 (示例)
// 在实际应用中，您会从持久化存储中加载这些设置
// onMounted(() => {
//   apiKeyOpenAI.value = localStorage.getItem('openaiApiKey') || ''
//   apiKeyOllama.value = localStorage.getItem('ollamaApiKey') || ''
//   selectedModel.value = localStorage.getItem('selectedAiModel') || 'gpt-3.5-turbo'
//   enableStreaming.value = localStorage.getItem('enableAiStreaming') === 'true'
//   enableHistory.value = localStorage.getItem('enableAiHistory') !== 'false' // 默认为 true
// })
</script>

<style lang="scss" scoped>
/* 可以在这里添加特定的组件样式 */
</style>
