<template>
  <div class="flex-1 flex flex-col overflow-hidden bg-background max-h-[calc(100vh-10vh)]">
    <!-- 欢迎界面 - 当没有工作流时显示 -->
    <div
      v-if="!currentSession || (workflowStore.workflows.length === 0 && !props.isWorkflowMode)"
      class="flex-1 flex flex-col items-center justify-center px-6 py-8 overflow-auto"
    >
      <div class="flex flex-col items-center justify-center max-w-3xl w-full space-y-6">
        <!-- AI 图标和标题 -->
        <div class="flex items-center space-x-4 mb-2">
          <div class="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
            <Bot class="w-7 h-7 text-primary" />
          </div>
          <h1 class="text-3xl font-bold text-foreground">我是AI科研助手，有什么需要帮忙的吗？</h1>
        </div>

        <!-- 副标题 -->
        <h3 class="text-lg text-muted-foreground text-center">
          选择聊天模式或者工作模式我可以为您处理不同的AI工作任务
        </h3>

        <!-- 快速选择对话标签 -->
        <div class="grid grid-cols-3 gap-4 w-full mt-4">
          <Button
            v-for="(item, index) in quickStartOptions"
            :key="index"
            variant="outline"
            class="h-auto p-4 text-left flex flex-col items-start"
            @click="fillInputWithPrompt(item.prompt)"
          >
            <div class="flex items-center space-x-2 mb-1">
              <component :is="item.icon" class="w-5 h-5 text-primary" />
              <span class="font-medium">{{ item.title }}</span>
            </div>
            <span class="text-xs text-muted-foreground">{{ item.description }}</span>
          </Button>
        </div>

        <!-- 消息输入 -->
        <div class="w-full mt-6">
          <ChatInput
            ref="welcomeChatInputRef"
            :is-workflow-mode="props.isWorkflowMode"
            :sending="sending"
            :model-temperature="temperature"
            :stream-enabled="streamEnabled"
            :welcome-mode="true"
            @send-message="handleWelcomePageMessage"
            @open-knowledge-reference="openKnowledgeModal"
            @update:temperature="(value) => (temperature = value)"
            @update:stream-enabled="(value) => (streamEnabled = value)"
          />
        </div>
      </div>
    </div>

    <!-- 正常聊天界面 - 当有工作流时显示 -->
    <template v-else>
      <!-- 头部 -->
      <ChatHeader
        :is-workflow-mode="props.isWorkflowMode"
        :current-session="currentSession"
        :messages-count="messages.length"
        @edit-session="handleEditSession"
        @delete-session="handleDeleteSession"
        @navigate-to-full-chat="navigateToFullChat"
      />

      <!-- 消息列表 -->
      <ChatMessageList :messages="messages" />

      <!-- AI 模型选择 & 高级设置 (仅在非工作流模式下显示) -->
      <ChatModelSettings
        v-if="!props.isWorkflowMode"
        v-model:selected-server-id="selectedServerId"
        v-model:selected-model="selectedModel"
        v-model:chat-mode="chatMode"
        :running-servers="runningServers"
        :model-groups="modelGroups"
        :is-loading-models="isLoadingModels"
        @server-change="handleServerChange"
      />

      <!-- 消息输入 -->
      <ChatInput
        :is-workflow-mode="props.isWorkflowMode"
        :sending="sending"
        :model-temperature="temperature"
        :stream-enabled="streamEnabled"
        :welcome-mode="false"
        @send-message="submitUserMessage"
        @open-knowledge-reference="openKnowledgeModal"
        @update:temperature="(value) => (temperature = value)"
        @update:stream-enabled="(value) => (streamEnabled = value)"
      />
    </template>
  </div>

  <!-- 工作流对话框 -->
  <WorkflowDialog ref="workflowDialogRef" @confirm="handleWorkflowConfirm" />

  <!-- 删除确认对话框 -->
  <AlertDialog v-model:open="showDeleteConfirm">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>确认删除对话</AlertDialogTitle>
        <AlertDialogDescription>
          您确定要删除对话 "{{ currentSessionTitle }}" 吗？此操作不可撤销。
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="showDeleteConfirm = false">取消</AlertDialogCancel>
        <AlertDialogAction
          class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          @click="confirmDelete"
        >
          删除
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>

  <!-- 创建工作流提示对话框 -->
  <AlertDialog v-model:open="showCreateWorkflowPrompt">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>创建工作流</AlertDialogTitle>
        <AlertDialogDescription>
          当前暂无关联工作流会话，需要创建一个工作流才能继续对话。是否创建新的工作流？
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="cancelCreateWorkflow">取消</AlertDialogCancel>
        <AlertDialogAction @click="confirmCreateWorkflow">创建工作流</AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { WorkflowDialog } from '@renderer/components'

import { createAIChatService } from '@renderer/config/api/grpc/aiChatService'
import { useAIChatStore, useNavbarStore, useServerStore, useWorkflowStore } from '@renderer/store'
import {
  BookOpen,
  Bot,
  BrainCircuit,
  FlaskConical,
  GraduationCap,
  Lightbulb,
} from 'lucide-vue-next'
import { nanoid } from 'nanoid'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'

// 导入子组件
import { ChatHeader, ChatInput, ChatMessageList, ChatModelSettings } from './components'

const props = defineProps({
  isWorkflowMode: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['open-knowledge-reference'])

const router = useRouter()
const route = useRoute()
const aiChatStore = useAIChatStore()
const aiChatService = createAIChatService()
const workflowStore = useWorkflowStore()
const navbarStore = useNavbarStore()
const serverStore = useServerStore()

// 快速选择对话选项
const quickStartOptions = [
  {
    title: '电池性能分析',
    description: '分析电池容量、循环寿命等性能指标',
    prompt: '请帮我分析一组锂电池的循环性能数据，重点关注容量衰减趋势和库伦效率变化。',
    icon: BrainCircuit,
  },
  {
    title: '电池材料研究',
    description: '探讨电池正负极、电解质等材料',
    prompt: '我正在研究硅碳负极材料，请介绍其优缺点以及最新的改性方法。',
    icon: FlaskConical,
  },
  {
    title: '电池安全性',
    description: '分析电池热失控、安全机制等问题',
    prompt: '请解释电池热失控的机理，以及目前主流的电池安全防护技术有哪些？',
    icon: BookOpen,
  },
  {
    title: '电池管理系统',
    description: '讨论BMS设计、SOC估算等技术',
    prompt: '我需要了解电池管理系统中SOC估算的主流算法，以及它们各自的优缺点。',
    icon: GraduationCap,
  },
  {
    title: '电池测试方法',
    description: '介绍电池性能测试和表征技术',
    prompt: '请介绍电化学阻抗谱(EIS)测试方法的原理和如何解读测试结果。',
    icon: Lightbulb,
  },
  {
    title: '电池回收与再利用',
    description: '探讨废旧电池回收技术与方案',
    prompt: '请分析目前锂电池回收的主要技术路线和经济可行性。',
    icon: Lightbulb,
  },
]

const workflowDialogRef = ref(null)
const welcomeChatInputRef = ref(null)
const showDeleteConfirm = ref(false)
const sending = ref(false)

// 创建工作流相关状态
const showCreateWorkflowPrompt = ref(false)
const pendingMessage = ref('')

const modelGroups = ref({})
const isLoadingModels = ref(false)
const selectedServerId = ref<string>('')
const streamEnabled = ref(true)

const selectedServerName = computed(() => {
  const server = serverStore.servers.find((s) => s.serverId === selectedServerId.value)
  return server ? server.serverName : ''
})

// 只显示运行中的服务器
const runningServers = computed(() => {
  return serverStore.servers.filter(
    (server) => server.serverStatus === 'Running' && server.serverType === 'agentServer',
  )
})

// 使用状态库中的聊天模式
const chatMode = computed({
  get: () => aiChatStore.chatMode,
  set: (value) => aiChatStore.updateChatMode(value),
})

const selectedModel = computed({
  get: () => aiChatStore.selectedModel,
  set: (value) => aiChatStore.updateSelectedModel(value),
})

const temperature = computed({
  get: () => {
    return aiChatStore.temperature || [0.7]
  },
  set: (value) => aiChatStore.updateTemperature(value),
})

const messages = computed(() => aiChatStore.currentMessages)
const currentSession = computed(() => aiChatStore.currentSession)

const currentSessionTitle = computed(() => {
  return aiChatStore.currentSession?.title || (props.isWorkflowMode ? 'AI 对话' : '临时对话')
})

// 填充输入框内容
const fillInputWithPrompt = (prompt) => {
  if (welcomeChatInputRef.value) {
    welcomeChatInputRef.value.setInputMessage(prompt)
  }
}

// 处理欢迎页面消息发送
const handleWelcomePageMessage = (content) => {
  if (!content) return

  // 保存消息内容，等待创建工作流后使用
  pendingMessage.value = content

  // 显示创建工作流提示
  showCreateWorkflowPrompt.value = true
}

// 取消创建工作流
const cancelCreateWorkflow = () => {
  showCreateWorkflowPrompt.value = false
  pendingMessage.value = ''
}

// 确认创建工作流
const confirmCreateWorkflow = () => {
  showCreateWorkflowPrompt.value = false

  // 打开工作流创建对话框
  workflowDialogRef.value?.open('add', {
    initialMessage: pendingMessage.value,
  })
}

// 处理服务器选择变化
const handleServerChange = async (serverId: string) => {
  selectedServerId.value = serverId
  aiChatStore.updateSelectedServer(serverId)

  // 获取该服务器支持的模型列表
  isLoadingModels.value = true
  try {
    // 获取模型分组
    modelGroups.value = await aiChatService.getAvailableModels(
      serverId,
      aiChatStore.currentSessionId,
    )

    // 检查是否有可用模型
    if (Object.keys(modelGroups.value).length > 0) {
      // 获取第一个提供商
      const firstProvider = Object.keys(modelGroups.value)[0]
      const firstModelGroup = modelGroups.value[firstProvider]

      if (firstModelGroup && firstModelGroup.length > 0) {
        // 获取第一个模型
        const firstModel = firstModelGroup[0]
        const modelKey = Object.keys(firstModel)[0]
        const modelValue = Object.values(firstModel)[0]

        // 构建模型ID
        const firstModelId = `${firstProvider}/${modelValue}`

        // 如果没有选择模型或者选择的模型不在可用列表中，选择第一个
        if (
          !selectedModel.value ||
          !Object.entries(modelGroups.value).some(([provider, models]) =>
            models.some((model) => {
              const value = Object.values(model)[0]
              return `${provider}/${value}` === selectedModel.value
            }),
          )
        ) {
          selectedModel.value = firstModelId
          aiChatStore.updateSelectedModel(firstModelId)
          window.logger.info(`自动选择模型: ${modelKey} (${firstModelId})`)
        }
      }
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
  } finally {
    isLoadingModels.value = false
  }
}

// 处理编辑对话
const handleEditSession = () => {
  if (!currentSession.value) return

  // 打开编辑对话框
  workflowDialogRef.value?.open('edit', {
    id: currentSession.value.workflowId,
    title: currentSession.value.title,
    description: currentSession.value.description || '',
  })
}

// 处理删除对话
const handleDeleteSession = () => {
  if (!currentSession.value) return
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!currentSession.value) return

  try {
    const sessionId = currentSession.value.id
    const workflowId = currentSession.value.workflowId

    // 删除对话
    await aiChatService.removeSession(sessionId)

    // 如果有关联的工作流，也删除工作流
    if (workflowId) {
      await workflowStore.deleteWorkflow(workflowId)
    }

    toast.success('删除成功', {
      description: '对话已删除',
    })

    // 关闭确认对话框
    showDeleteConfirm.value = false

    // 检查是否还有工作流
    if (workflowStore.workflows.length === 0) {
      // 没有工作流了，清除当前工作流ID，让主界面显示欢迎页面
      workflowStore.currentWfId = ''

      // 如果在聊天页面，更新URL参数
      if (route.path.includes('/ai/chat')) {
        router.replace({
          query: { ...route.query, workflowId: undefined },
        })
      }
    } else {
      // 如果有其他工作流，选择第一个
      workflowStore.currentWfId = workflowStore.workflows[0].id

      // 获取与该工作流关联的会话ID
      const expectedSessionId = `0::${workflowStore.workflows[0].id}`

      // 尝试从服务器获取会话
      const session = await aiChatService.fetchSession(expectedSessionId)

      if (session) {
        // 选择关联的会话
        await aiChatService.selectSession(expectedSessionId)
      } else {
        // 如果没有找到会话，创建新会话
        await aiChatService.createNewSession('新对话')
      }
    }
  } catch (error) {
    toast.error('删除失败', {
      description: error.message || '未知错误',
    })
  }
}

// 处理工作流对话框确认
const handleWorkflowConfirm = async (formData, type) => {
  if (type === 'edit' && currentSession.value) {
    try {
      // 更新对话信息
      await aiChatService.updateSession(currentSession.value.id, {
        title: formData.name,
        description: formData.description,
      })

      // 如果有关联的工作流，也更新工作流信息
      if (currentSession.value.workflowId) {
        await workflowStore.editWorkflow({
          id: currentSession.value.workflowId,
          title: formData.name,
          description: formData.description,
        })
      }

      toast.success('更新成功', {
        description: '对话已更新',
      })
    } catch (error) {
      toast.error('更新失败', {
        description: error.message || '未知错误',
      })
    }
  } else if (type === 'add') {
    try {
      // 添加新工作流
      await workflowStore.addWorkflow({
        title: formData.name,
        description: formData.description,
        folderId: workflowStore.currentFolderId,
      })

      // 获取新创建的工作流
      const newWorkflow = workflowStore.workflows[workflowStore.workflows.length - 1]

      if (newWorkflow) {
        // 选择新创建的工作流
        workflowStore.currentWfId = newWorkflow.id

        // 创建关联的对话
        const newSession = await aiChatService.createNewSession(
          `${newWorkflow.title} 对话`,
          newWorkflow.id,
        )

        // 如果有待发送的消息，发送它
        if (pendingMessage.value && newSession) {
          setTimeout(() => {
            submitUserMessage(pendingMessage.value)
            pendingMessage.value = ''
          }, 300)
        }

        toast.success('创建成功', {
          description: '工作流已创建',
        })
      }
    } catch (error) {
      toast.error('创建失败', {
        description: error.message || '未知错误',
      })
    }
  }
}

const submitUserMessage = async (content: string) => {
  if (!content || sending.value) return

  // 检查是否选择了服务器
  if (!selectedServerId.value) {
    toast.error('发送失败', {
      description: '请先选择服务器',
    })
    return
  }

  // 检查是否选择了模型
  if (!selectedModel.value) {
    toast.error('发送失败', {
      description: '请先选择模型',
    })
    return
  }

  sending.value = true

  try {
    await aiChatService.sendUserMessage(content)
  } catch (error) {
    console.error('Error sending message:', error)
  } finally {
    sending.value = false
  }
}

const openKnowledgeModal = () => {
  emit('open-knowledge-reference')
}

// 导航到完整聊天页面（从工作流模式）
const navigateToFullChat = () => {
  // 如果有工作流ID，传递给聊天页面
  if (currentSession.value?.workflowId) {
    router.push({
      path: '/ai/chat',
      query: { workflowId: currentSession.value.workflowId },
    })
  } else {
    router.push({ path: '/ai/chat' })
  }
}

// 返回工作流页面
const navigateToWorkflow = () => {
  // 如果有关联的工作流，返回到该工作流
  if (currentSession.value?.workflowId) {
    const workflowId = currentSession.value.workflowId
    const tagId = `${workflowId}`

    // 检查导航栏中是否已存在此工作流的标签
    const existingTag = navbarStore.tags.find((tag) => tag.id === tagId)

    if (existingTag) {
      // 如果标签已存在，直接激活它
      navbarStore.setActiveTag(tagId)
      // 导航到工作流编辑器，不重新创建标签
      router.push(existingTag.path)
      return
    }

    // 查找工作流信息，用于创建导航标签
    const workflow = workflowStore.workflows.find((wf) => wf.id === workflowId)

    if (workflow) {
      // 标签不存在，添加新标签
      navbarStore.addTag({
        id: tagId,
        title: workflow.title || '工作流',
        path: `/workflow/editor/${workflowId}`,
      })

      // 导航到工作流编辑器
      router.push(`/workflow/editor/${workflowId}`)
    } else {
      // 工作流不存在，直接导航
      router.push(`/workflow/editor/${workflowId}`)
    }
  } else {
    // 否则返回工作流列表
    router.push('/workflow')
  }
}

const getServerList = async () => {
  try {
    if (serverStore.servers.length === 0) {
      await serverStore.updateServerList()
    }

    let serverIdToUse = ''

    // 优先使用存储中的选择
    if (aiChatStore.selectedServerId) {
      serverIdToUse = aiChatStore.selectedServerId
    }
    // 否则使用第一个可用服务器
    else if (runningServers.value.length > 0) {
      serverIdToUse = runningServers.value[0].serverId
    }

    if (serverIdToUse) {
      selectedServerId.value = serverIdToUse
      aiChatStore.updateSelectedServer(serverIdToUse)

      // 获取该服务器支持的模型列表
      isLoadingModels.value = true
      try {
        // 获取模型分组
        modelGroups.value = await aiChatService.getAvailableModels(
          serverIdToUse,
          aiChatStore.currentSessionId,
        )
        // 获取所有可用模型的扁平列表
        const allAvailableModels = Object.entries(modelGroups.value).flatMap(([provider, models]) =>
          models.map((model) => {
            const modelKey = Object.keys(model)[0]
            return `${provider}/${modelKey}`
          }),
        )
        // 使用第一个可用模型
        if (!selectedModel.value || !allAvailableModels.includes(selectedModel.value)) {
          if (allAvailableModels.length > 0) {
            selectedModel.value = allAvailableModels[0]
          }
        }
      } catch (error) {
        console.error('获取模型列表失败:', error)
      } finally {
        isLoadingModels.value = false
      }
    }
  } catch (error) {
    console.error('加载服务器列表失败:', error)
    toast.error('加载服务器列表失败')
  }
}

// 监听路由查询参数
watch(
  () => route.query.workflowId,
  async (newWorkflowId) => {
    if (newWorkflowId && typeof newWorkflowId === 'string') {
      window.logger.info(`路由工作流ID变化为 ${newWorkflowId}，更新工作流和会话`)

      // 更新工作流 Store 的当前工作流
      workflowStore.currentWfId = newWorkflowId

      // 生成会话ID
      const expectedSessionId = `0::${newWorkflowId}`

      // 检查当前会话是否已经是目标会话
      if (aiChatStore.currentSessionId === expectedSessionId && aiChatStore.currentSession) {
        window.logger.info(`当前会话 ${expectedSessionId} 已经是目标会话，不需要切换`)
        return
      }

      // 直接构建会话数据，不查询服务器
      try {
        const workflow = workflowStore.workflows.find((wf) => wf.id === newWorkflowId)
        const title = workflow ? `${workflow.title} 对话` : '工作流对话'

        // 构建会话数据
        const sessionData = {
          id: expectedSessionId,
          title: title,
          messages: [
            {
              id: nanoid(),
              sessionId: expectedSessionId,
              content: '你好！这是一个新的对话。',
              role: 'assistant',
              timestamp: Date.now(),
            },
          ],
          createdAt: Date.now(),
          updatedAt: Date.now(),
          workflowId: newWorkflowId,
        }

        // 直接更新当前会话状态
        aiChatStore.setCurrentSessionId(expectedSessionId)
        aiChatStore.setCurrentSessionData(sessionData)
        window.logger.info(`已切换到工作流会话 ${expectedSessionId}`)
      } catch (error) {
        console.error('构建工作流会话失败:', error)
      }
    }
  },
  { immediate: true },
)

onMounted(async () => {
  setTimeout(() => {
    getServerList()
  }, 1000)
})
</script>

<style lang="scss" scoped></style>
