<template>
  <Dialog v-model:open="isOpen" @update:open="handleModalToggle">
    <DialogContent class="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
      <DialogHeader>
        <DialogTitle>{{ editing ? '编辑模板' : '创建新模板' }}</DialogTitle>
        <DialogDescription>设计您的流程模板，添加并连接节点。</DialogDescription>
      </DialogHeader>

      <div class="grid gap-4 py-2">
        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="template-name" class="text-right">名称</Label>
          <Input
            id="template-name"
            v-model="templateData.name"
            class="col-span-3"
            placeholder="请输入模板名称"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="template-description" class="text-right">描述</Label>
          <Textarea
            id="template-description"
            v-model="templateData.description"
            class="col-span-3 resize-none max-h-[80px] overflow-auto"
            placeholder="请输入模板描述"
            rows="2"
          />
        </div>
      </div>

      <div class="flex-1 flex border rounded-md overflow-hidden mt-2 min-h-[400px] max-h-[500px]">
        <!-- 左侧节点面板 -->
        <div class="w-1/4 border-r bg-muted/20 p-2 overflow-y-auto scrollbar">
          <div class="font-medium mb-2">可用节点</div>
          <div class="space-y-1">
            <Accordion type="single" collapsible>
              <AccordionItem
                v-for="(module, moduleName) in enabledModules"
                :key="moduleName"
                :value="moduleName"
              >
                <AccordionTrigger class="text-sm py-1 px-2">
                  {{ module.name }}
                </AccordionTrigger>
                <AccordionContent>
                  <div v-for="category in module.categories" :key="category.name" class="mb-2">
                    <div class="text-xs font-medium text-muted-foreground mb-1 px-2">
                      {{ category.name }}
                    </div>
                    <div class="space-y-1">
                      <div
                        v-for="node in category.nodes"
                        :key="node.id"
                        class="flex items-center px-2 py-2 rounded-md bg-muted/80 text-xs hover:bg-accent cursor-grab border border-transparent hover:border-border"
                        draggable="true"
                        @dragstart="onDragStart($event, node)"
                      >
                        <div
                          class="w-8 h-8 mr-2 flex-shrink-0 flex items-center justify-center rounded-md bg-muted/40"
                        >
                          <img
                            v-if="node.data?.icon?.type === 'svg' && node.data?.icon?.value"
                            :src="node.data.icon.value"
                            class="w-4 h-4"
                            alt="节点图标"
                          />
                          <LucideIcon v-else :name="getIconName(node)" class="w-5 h-5" />
                        </div>
                        <div class="flex flex-col">
                          <span class="font-medium">{{ node.data?.label || node.type }}</span>
                          <!-- <span class="text-xs text-muted-foreground truncate max-w-[120px]">
                            {{ node.data?.nodeType || '基础节点' }}
                          </span> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>

        <!-- 右侧流程图编辑区 -->
        <div ref="flowWrapper" class="w-3/4 relative">
          <VueFlow
            v-model:nodes="nodes"
            v-model:edges="edges"
            :fit-view-on-init="true"
            :default-edge-options="defaultEdgeOptions"
            :connect-on-click="true"
            class="bg-background"
            @drop="onDrop"
            @dragover="onDragOver"
            @connect="onConnect"
            @node-drag-stop="onNodeDragStop"
            @edge-update="onEdgeUpdate"
          >
            <Background :pattern-color="'#aaa'" :gap="20" />
            <MiniMap
              :pannable="true"
              :zoomable="true"
              :position="'bottom-right'"
              :width="50"
              :height="40"
            />
            <Controls />
            <Panel position="top-right" class="bg-background p-2 rounded-md shadow-sm border">
              <div class="flex space-x-2">
                <Button size="sm" variant="outline" @click="deleteSelection">
                  <LucideIcon name="Trash2" class="w-4 h-4 mr-1" />
                  删除选中
                </Button>
                <Button size="sm" variant="outline" @click="fitView">
                  <LucideIcon name="Maximize" class="w-4 h-4 mr-1" />
                  适应视图
                </Button>
              </div>
            </Panel>
            <template #node-custom="nodeProps">
              <div :class="['custom-node', nodeProps.selected ? 'selected' : '']">
                <div class="custom-node-header">
                  <div class="node-icon">
                    <img
                      v-if="nodeProps.data?.icon?.type === 'svg' && nodeProps.data?.icon?.value"
                      :src="nodeProps.data.icon.value"
                      class="w-4 h-4"
                      alt="节点图标"
                    />
                    <LucideIcon v-else :name="getIconName(nodeProps)" class="w-4 h-4" />
                  </div>
                  <div class="node-label">{{ nodeProps.data?.label || nodeProps.type }}</div>
                </div>
                <!-- <div class="custom-node-body" v-if="nodeProps.data?.nodeType">
                  <div class="node-type">{{ nodeProps.data.nodeType }}</div>
                </div> -->

                <!-- 添加连接点 -->
                <Handle type="source" :position="Position.Right" class="nodrag source-handle" />
                <Handle type="target" :position="Position.Left" class="nodrag target-handle" />
              </div>
            </template>
          </VueFlow>
        </div>
      </div>

      <DialogFooter class="mt-2">
        <Button variant="outline" @click="closeModal">取消</Button>
        <Button @click="handleSubmit">{{ editing ? '保存更改' : '创建模板' }}</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { nanoid } from 'nanoid'
import {
  VueFlow,
  Panel,
  useVueFlow,
  Position,
  MarkerType,
  NodeDragEvent,
  Edge,
  Node,
  Handle,
  Connection,
  EdgeChange,
  EdgeUpdateEvent,
} from '@vue-flow/core'
import { MiniMap } from '@vue-flow/minimap'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { LucideIcon } from '@renderer/components'
import { useNodeModulesStore } from '@renderer/store'
import { toast } from 'vue-sonner'
import '@vue-flow/core/dist/style.css'
import '@vue-flow/minimap/dist/style.css'
import '@vue-flow/controls/dist/style.css'

interface TemplateProps {
  open: boolean
  template?: any
}

const props = defineProps<TemplateProps>()
const emit = defineEmits(['update:open', 'submit'])

const nodeModulesStore = useNodeModulesStore()
const enabledModules = computed(() => nodeModulesStore.enabledNodeModules)

const isOpen = ref(props.open)
const editing = computed(() => !!props.template)
const flowWrapper = ref<HTMLElement | null>(null)

// 默认边配置
const defaultEdgeOptions = {
  animated: true,
  markerEnd: MarkerType.ArrowClosed,
  style: { stroke: '#4f46e5', strokeWidth: 2 },
  type: 'default',
}

const {
  findNode,
  onConnect: vfOnConnect,
  addNodes,
  addEdges,
  project,
  getSelectedNodes,
  getSelectedEdges,
  fitView,
  toObject,
} = useVueFlow({
  defaultEdgeOptions,
})

const templateData = ref({
  id: '',
  name: '',
  description: '',
})

const nodes = ref<any[]>([])
const edges = ref<any[]>([])

// 初始化数据
watch(
  () => props.open,
  (newVal) => {
    isOpen.value = newVal
    if (newVal) {
      if (props.template) {
        templateData.value = {
          id: props.template.id,
          name: props.template.name,
          description: props.template.description,
        }
        nodes.value = JSON.parse(JSON.stringify(props.template.nodes || []))
        edges.value = JSON.parse(JSON.stringify(props.template.edges || []))

        // 延迟调用 fitView 确保节点已渲染
        setTimeout(() => {
          fitView()
        }, 200)
      } else {
        templateData.value = {
          id: nanoid(),
          name: '',
          description: '',
        }
        nodes.value = []
        edges.value = []
      }
    }
  },
  { immediate: true },
)

const handleModalToggle = (openState: boolean) => {
  emit('update:open', openState)
}

const closeModal = () => {
  emit('update:open', false)
}

// 处理节点拖拽开始
const onDragStart = (event: DragEvent, node: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', JSON.stringify(node))
    event.dataTransfer.effectAllowed = 'move'
  }
}

// 处理拖拽结束
const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

// 处理节点放置
const onDrop = (event: DragEvent) => {
  if (!event.dataTransfer) return

  event.preventDefault()

  const data = event.dataTransfer.getData('application/vueflow')
  if (!data) return

  try {
    const nodeData = JSON.parse(data)
    const position = project({
      x: event.clientX - (flowWrapper.value?.getBoundingClientRect().left || 0),
      y: event.clientY - (flowWrapper.value?.getBoundingClientRect().top || 0),
    })

    const newNode = {
      ...nodeData,
      id: `node-${nanoid(6)}`,
      position,
      selected: false,
      // 确保节点类型为 custom，这样会使用我们的自定义节点模板
      type: 'custom',
      // 添加连接点配置
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
      connectable: true,
      // 确保数据字段存在
      data: {
        ...nodeData.data,
        // 确保有标签
        label: nodeData.data?.label || '节点',
      },
    }

    addNodes([newNode])
  } catch (error) {
    console.error('Failed to process dropped node:', error)
  }
}

// 处理节点连接
const onConnect = (connection: Connection) => {
  // 确保连接有源和目标
  if (!connection.source || !connection.target) return

  const newEdge = {
    ...connection,
    id: `edge-${nanoid(6)}`,
    animated: true,
    markerEnd: MarkerType.ArrowClosed,
    style: { stroke: '#4f46e5', strokeWidth: 2 },
    type: 'default', // 使用默认边类型
  }

  // 添加新的边
  addEdges([newEdge])
}

// 处理边更新
const onEdgeUpdate = (event: EdgeUpdateEvent) => {
  const { edge: oldEdge, connection: newConnection } = event

  if (!newConnection.source || !newConnection.target) return

  // 删除旧的边
  edges.value = edges.value.filter((e) => e.id !== oldEdge.id)

  // 添加新的边
  const newEdge = {
    ...newConnection,
    id: oldEdge.id,
    animated: true,
    markerEnd: MarkerType.ArrowClosed,
    style: { stroke: '#4f46e5', strokeWidth: 2 },
  }

  addEdges([newEdge])
}

// 处理节点拖动停止
const onNodeDragStop = (e: NodeDragEvent) => {
  // 可以在这里添加节点位置更新的逻辑
}

// 删除选中的元素
const deleteSelection = () => {
  const selectedNodes = getSelectedNodes.value
  const selectedEdges = getSelectedEdges.value

  if (selectedNodes.length > 0 || selectedEdges.length > 0) {
    // 删除选中的边
    if (selectedEdges.length > 0) {
      const edgeIds = selectedEdges.map((edge) => edge.id)
      edges.value = edges.value.filter((edge) => !edgeIds.includes(edge.id))
    }

    // 删除选中的节点
    if (selectedNodes.length > 0) {
      const nodeIds = selectedNodes.map((node) => node.id)
      nodes.value = nodes.value.filter((node) => !nodeIds.includes(node.id))
    }
  }
}

// 获取图标名称
const getIconName = (node: any): string => {
  if (node.data?.icon) {
    return typeof node.data.icon === 'string' ? node.data.icon : 'Box'
  }
  return 'Box'
}

// 提交表单
const handleSubmit = () => {
  if (!templateData.value.name.trim()) {
    // 可以添加表单验证
    toast.warning('提示', {
      description: '请输入模板名称',
    })
    return
  }

  const template = {
    ...templateData.value,
    nodes: JSON.parse(JSON.stringify(nodes.value)),
    edges: JSON.parse(JSON.stringify(edges.value)),
  }

  emit('submit', template)
  closeModal()
}
</script>

<style scoped>
:deep(.vue-flow__node) {
  padding: 0;
  border-radius: 8px;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  min-width: 120px;
  overflow: visible; /* 改为 visible 以便连接点可以显示在节点外部 */
}

:deep(.vue-flow__node.selected) {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.custom-node {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

.custom-node-header {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e5e5e5;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

.node-label {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-node-body {
  padding: 6px 8px;
}

.node-type {
  font-size: 10px;
  color: #666;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

/* 连接点样式 */
:deep(.source-handle),
:deep(.target-handle) {
  width: 12px;
  height: 12px;
  background: #4f46e5;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
  z-index: 10;
}

:deep(.vue-flow__edge-path) {
  stroke: #4f46e5;
  stroke-width: 1.5;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #4f46e5;
  stroke-width: 2.5;
}

:deep(.vue-flow__connection-path) {
  stroke: #4f46e5;
  stroke-width: 2;
}

:deep(.vue-flow__minimap) {
  border: 1px solid #ddd;
  border-radius: 4px;
}

:deep(.vue-flow__controls) {
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
