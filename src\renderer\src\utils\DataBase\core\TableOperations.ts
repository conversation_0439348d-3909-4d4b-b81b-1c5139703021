import Dexie from 'dexie'
/**
 * 二次封装Dexie对于表的操作，扩展表的操作修改这个文件
 */
export class TableOperations {
  private table: any // 将Dexie的table对象传入
  constructor(table) {
    this.table = table
  }
  // 添加数据，参数为键值对对象
  public add(item: any) {
    return this.table.add(item)
  }
  // 更新数据，参数为键值对对象
  public update(item: any, by: string) {
    return this.table.update(by, item)
  }
  // 删除数据，参数为主键值
  public delete(id: number) {
    return this.table.delete(id)
  }
  /**
   * 创建Where查询
   * @param index 索引字段名
   * @returns WhereClause实例
   */
  public where(index: string): WhereClause {
    return new WhereClause(this.table.where(index as string))
  }
  // 批量操作
  // 批量添加
  public bulkAdd(items: any[]) {
    return this.table.bulkAdd(items)
  }
  // 批量更新
  public bulkUpdate(items: any[]) {
    return this.table.bulkPut(items)
  }
  // 查询构建器
  public query(): QueryBuilder {
    return new QueryBuilder(this.table)
  }
}
/**
 * 查询构建器，主要是实现复杂的查询条件
 */
class QueryBuilder {
  private collection: Dexie.Collection
  private conditions: ((item: any) => boolean)[] = []

  constructor(table: any) {
    this.collection = table.toCollection()
  }

  where(condition: (item: any) => boolean): this {
    this.conditions.push(condition)
    return this
  }
  // 排序
  orderBy(field: any, descending = false): this {
    this.collection = this.collection
      .sortBy(field as string)
      .then((items) => (descending ? items.reverse() : items)) as any
    return this
  }
  // 现在结果的数量
  limit(count: number): this {
    this.collection = this.collection.limit(count)
    return this
  }
  // 需要跳过的条数
  offset(count: number): this {
    this.collection = this.collection.offset(count)
    return this
  }
  // 执行查询
  async execute(): Promise<any[]> {
    let result = await this.collection.toArray()

    // 应用条件过滤
    if (this.conditions.length > 0) {
      result = result.filter((item) => this.conditions.every((condition) => condition(item)))
    }

    return result
  }
  // 执行查询，返回数据总数
  async count(): Promise<number> {
    const items = await this.execute()
    return items.length
  }
  // 执行查询，返回第一条数据
  async first(): Promise<any> {
    const items = await this.limit(1).execute()
    return items[0]
  }
}
/**
 * 对于table的条件查询进行封装，这个不是脱裤子放屁，我们可以进行自己的业务封装
 */
class WhereClause {
  /**
   * 构造函数
   * @param collection Dexie集合
   * @param config 表配置
   */
  constructor(private collection: Dexie.Collection | any) {}

  // ---------- 条件方法 ----------
  // 等于
  equals(value: any): this {
    this.collection = this.collection.equals(value)
    return this
  }
  // 大于
  above(value: any): this {
    this.collection = this.collection.above(value)
    return this
  }
  // 小于
  below(value: any): this {
    this.collection = this.collection.below(value)
    return this
  }
  // 大于等于
  between(lower: any, upper: any): this {
    this.collection = this.collection.between(lower, upper)
    return this
  }

  // ---------- 执行方法 ----------
  // 执行查询
  async toArray(): Promise<any[]> {
    return this.collection.toArray()
  }
  // 执行查询，返回第一条数据
  async first(): Promise<any> {
    return this.collection.first()
  }
  // 执行查询，返回数据总数
  async count(): Promise<number> {
    return this.collection.count()
  }
  // 删除数据
  async delete(): Promise<number> {
    return this.collection.delete()
  }
}
