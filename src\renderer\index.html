<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title id="app-title">应用加载中...</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' https: http:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: http:; connect-src 'self' https: http: ws: wss:; font-src 'self' data:"
    />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: http:; connect-src 'self' https://api.iconify.design https://api.simplesvg.com https://api.unisvg.com ws: wss:; font-src 'self' data:"
    />
    <style>
      /* 添加跳动动画 */
      @keyframes bounce {
        0%,
        100% {
          transform: translateY(0);
        }

        50% {
          transform: translateY(-15px);
        }
      }

      /* 添加文字渐变动画 */
      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }

        50% {
          background-position: 100% 50%;
        }

        100% {
          background-position: 0% 50%;
        }
      }

      .gradient-text {
        background: linear-gradient(90deg, #3b82f6, #10b981, #3b82f6);
        background-size: 200% auto;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation:
          gradientShift 3s ease infinite,
          bounce 1s ease infinite;
      }

      .dark .gradient-text {
        background: linear-gradient(90deg, #60a5fa, #34d399, #60a5fa);
        background-size: 200% auto;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    </style>
    <script>
      console.log('index.html loaded')
      // 初始化主题 - 需要在 Vue 应用启动前执行，避免主题闪烁
      // 注意：这里使用 VueUse 的 localStorage key，与 settings store 保持同步
      ;(function () {
        const theme = localStorage.getItem('vueuse-color-scheme') || 'auto'
        if (
          theme === 'dark' ||
          (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)
        ) {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      })()

      // 设置应用标题
      ;(function () {
        try {
          const isMatt = window.__IS_MATT__ || false
          const title = isMatt ? 'MattVerse 电池设计自动化平台' : '豪鹏电池寿命预测软件'
          document.getElementById('app-title').textContent = title
        } catch (e) {
          document.getElementById('app-title').textContent = 'MattVerse'
        }
      })()
    </script>
  </head>

  <body>
    <div id="app">
      <h1
        style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 3rem;
          font-weight: bold;
          z-index: 9999;
          background-color: rgba(255, 255, 255, 0.8);
        "
        class="dark:bg-opacity-80 dark:bg-gray-900"
      >
        <span class="gradient-text">MattVerse</span>
      </h1>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
