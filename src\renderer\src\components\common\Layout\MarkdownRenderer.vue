<template>
  <div class="markdown-body">
    <component :is="markdownComponent" />
  </div>
</template>

<script setup lang="ts">
import hljs from 'highlight.js'
import MarkdownIt from 'markdown-it'
import { computed, defineProps, h } from 'vue'

const props = defineProps({
  content: {
    type: String,
    required: true,
  },
})

// 配置markdown-it实例
const md = new MarkdownIt({
  html: false, // 禁用HTML标签，提高安全性
  breaks: true,
  linkify: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {
        // 忽略错误
        return ''
      }
    }
    return '' // 使用默认编码转义
  },
})

// 创建渲染后的组件
const markdownComponent = computed(() => {
  const html = md.render(props.content || '')

  return {
    render() {
      return h('div', {
        innerHTML: html,
        class: 'prose prose-sm max-w-none leading-relaxed break-words whitespace-normal',
      })
    },
  }
})
</script>

<style lang="scss" scoped>
.markdown-body {
  word-break: break-word;
  overflow-wrap: break-word;
}

.markdown-body pre {
  max-width: 100%;
  overflow-x: auto;
}

.markdown-body table {
  display: block;
  max-width: 100%;
  overflow-x: auto;
}

.markdown-body img {
  max-width: 100%;
  height: auto;
}

.markdown-body code {
  white-space: pre-wrap;
}
</style>
