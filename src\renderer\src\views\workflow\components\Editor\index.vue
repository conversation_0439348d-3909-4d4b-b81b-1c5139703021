<template>
  <div ref="editorContainerRef" class="workflow-editor w-full h-full">
    <div class="flex flex-row h-[calc(100vh-6vh)]">
      <ResizablePanelGroup direction="horizontal">
        <!-- 工具栏面板 -->
        <ResizablePanel
          v-if="showToolbar"
          id="toolbar-panel"
          :default-size="15"
          :min-size="0"
          :max-size="25"
          :collapsible="false"
        >
          <div class="h-full p-2 bg-background">
            <WorkflowToolbar :workflow-title="workflowTitle" />
          </div>
        </ResizablePanel>

        <!-- 编辑器面板分隔符 -->
        <ResizableHandle v-if="showToolbar" with-handle />

        <ResizablePanel
          id="editor-panel"
          :default-size="nodeNavbarStore.showNodeSettings ? 55 : 85"
          :min-size="30"
        >
          <div ref="workflowEditorRef" class="workflow h-full">
            <WorkflowEditor :workflow-id="workflowId" @toggle-toolbar="toggleToolbar" />
          </div>
        </ResizablePanel>

        <!-- 节点设置面板分隔符 -->
        <ResizableHandle v-if="nodeNavbarStore.showNodeSettings" with-handle />

        <!-- 节点设置面板 -->
        <ResizablePanel
          v-if="nodeNavbarStore.showNodeSettings"
          id="settings-panel"
          :default-size="30"
          :min-size="0"
          :max-size="50"
          :collapsible="false"
        >
          <NodeSettingTabs @update="handleNodeUpdate" @save="handleNodeSave" />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NodeSettingTabs, WorkflowEditor, WorkflowToolbar } from '@renderer/components/workflow'
import { useFlowsStore, useNodeNavbarStore } from '@renderer/store'
import { ref } from 'vue'

const props = defineProps({
  workflowId: {
    type: String,
    required: true,
  },
  workflowTitle: {
    type: String,
    required: true,
    default: '',
  },
})

const nodeNavbarStore = useNodeNavbarStore()
const flowsStore = useFlowsStore()

const showToolbar = ref(true)
const workflowEditorRef = ref(null)
const editorContainerRef = ref(null)

const toggleToolbar = () => {
  showToolbar.value = !showToolbar.value
}

// 处理节点更新
const handleNodeUpdate = (nodeId: string, newData: any) => {
  // 这里可以添加额外的处理逻辑
  window.logger.info('Node updated:', nodeId, newData)
}

// 处理节点保存
const handleNodeSave = (nodeId: string, data: any) => {
  // 获取当前工作流的所有元素
  const workflow = flowsStore.getWorkflow(props.workflowId)
  if (!workflow) return

  // 更新节点
  const nodes = workflow.nodes.map((node: any) => (node.id === nodeId ? data : node))

  // 保存到 store
  flowsStore.saveWorkflow(props.workflowId, {
    nodes,
    edges: workflow.edges || [],
  })
}
</script>

<style lang="scss" scoped>
.workflow-editor {
  width: 100%;
  height: 100%;
}
</style>
