<template>
  <div>
    <!-- 工作流模式下的简化头部 -->
    <div v-if="props.isWorkflowMode" class="p-4 border-b flex justify-between items-center">
      <h2 class="text-lg font-semibold">AI Chat</h2>
      <Button variant="ghost" size="sm" @click="navigateToFullChat">更多</Button>
    </div>

    <!-- 完整模式下的详细头部 -->
    <div v-else class="p-4 border-b border-border bg-card shadow-sm">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <h1 class="text-xl font-semibold text-foreground">
            {{ currentSessionTitle }}
          </h1>
          <div
            v-if="currentSession"
            class="flex items-center space-x-1.5 text-xs text-muted-foreground"
          >
            <span>创建于 {{ currentSessionCreatedAt }}</span>
            <span>•</span>
            <span>{{ messagesCount }} 条消息</span>
          </div>
        </div>
        <div class="flex items-center space-x-1">
          <Button variant="ghost" size="icon" title="编辑对话" @click="handleEditSession">
            <LucideIcon name="Edit" class="w-4 h-4 text-muted-foreground" />
          </Button>
          <Button variant="ghost" size="icon" title="删除对话" @click="handleDeleteSession">
            <LucideIcon name="Trash2" class="w-4 h-4 text-muted-foreground" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'
import { useWorkflowStore } from '@renderer/store'
import { computed } from 'vue'

const props = defineProps({
  isWorkflowMode: {
    type: Boolean,
    default: false,
  },
  currentSession: {
    type: Object,
    default: null,
  },
  messagesCount: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['edit-session', 'delete-session', 'navigate-to-full-chat'])

const workflowStore = useWorkflowStore()

const currentSessionTitle = computed(() => {
  return props.currentSession?.title || (props.isWorkflowMode ? 'AI 对话' : '临时对话')
})

const currentSessionCreatedAt = computed(() => {
  if (props.currentSession?.createdAt) {
    return new Date(props.currentSession.createdAt).toLocaleDateString()
  }
  return ''
})

const handleEditSession = () => {
  emit('edit-session')
}

const handleDeleteSession = () => {
  // 如果当前会话关联了工作流，需要同时删除工作流
  if (props.currentSession?.workflowId) {
    // 检查工作流是否存在
    const workflow = workflowStore.workflows.find((wf) => wf.id === props.currentSession.workflowId)
    if (workflow) {
      // 存在关联工作流，通知父组件删除会话和工作流
      emit('delete-session')
    } else {
      // 不存在关联工作流（可能已被删除），只删除会话
      emit('delete-session')
    }
  } else {
    // 普通会话，直接删除
    emit('delete-session')
  }
}

const navigateToFullChat = () => {
  emit('navigate-to-full-chat')
}
</script>
