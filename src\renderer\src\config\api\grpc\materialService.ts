import { BaseService } from './baseService'

// 同步服务请求响应
export interface DefaultServiceResponse {
  status: string
  message: string
  statusCode: string
  keyTypePairs: Record<string, string>
  keyValuePairs: Record<string, string>
}
export interface MaterialResponse {
  statusCode: string
  message: string
  keyTypePairs: Record<string, any>
  keyValuePairs: Record<string, any>
}

export interface SubmitResponse {
  statusCode: string
  message: string
  taskId: string
}

class MaterialService extends BaseService {
  /**
   * 获取选择结构
   */
  async getDatabaseList(): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('getDatabaseList', '', false, {})
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 获取详情
   */
  async getStructureInfoByName(name: string, file: string): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>(
      'getStructureInfoByName',
      '',
      false,
      {
        key_type_pairs: {
          structure_file_name: file,
        },
        key_value_pairs: {
          structure_file_name: name,
        },
      },
    )
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 提交模量
   */
  async predictModulus(model: string, scheme: string, str: string): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('predictModulus', '', false, {
      key_type_pairs: {
        model: 'String',
        optimizer_scheme: 'String',
        structure_cif_str: 'String',
      },
      key_value_pairs: {
        model: model,
        optimizer_scheme: scheme,
        structure_cif_str: str,
      },
    })
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 提交电导率
   */
  async predictConductivity(
    model: string,
    temperature: string,
    str: string,
  ): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('predictConductivity', '', false, {
      key_type_pairs: {
        model: 'String',
        ref_T: 'String',
        structure_cif_str: 'String',
      },
      key_value_pairs: {
        model: model,
        ref_T: temperature,
        structure_cif_str: str,
      },
    })
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 提交相图
   */
  async getPhaseDiagram(formula: string): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('getPhaseDiagram', '', false, {
      key_type_pairs: {
        formula: 'String',
      },
      key_value_pairs: {
        formula: formula,
      },
    })
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 提交能量
   */
  async predictEnergy(model: string, str: string): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('predictEnergy', '', false, {
      key_type_pairs: {
        model: 'String',
        structure_cif_str: 'String',
      },
      key_value_pairs: {
        model: model,
        structure_cif_str: str,
      },
    })
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * predictElecDiffusion 提交扩散系数
   */
  async predictElecDiffusion(
    boxSize: string,
    concentration: string,
    model: string,
    refT: string,
    workdir: string,
  ): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('predictElecDiffusion', '', false, {
      key_type_pairs: {
        box_size: 'String',
        concentration: 'String',
        model: 'String',
        ref_T: 'String',
        workdir: 'String',
      },
      key_value_pairs: {
        box_size: boxSize,
        concentration: concentration,
        model: model,
        ref_T: refT,
        workdir: workdir,
      },
    })
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 获取 getStructurePlotData
   */
  async getStructurePlotData(fileName: string): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>('getStructurePlotData', '', false, {
      key_type_pairs: {
        structure_file_name: 'String',
      },
      key_value_pairs: {
        structure_file_name: fileName,
      },
    })
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 根据比率计算结构
   */
  async getDopedStructureFormula(
    com: string,
    host: string,
    str: string,
  ): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>(
      'getDopedStructureFormula',
      '',
      false,
      {
        key_type_pairs: {
          composition: 'String',
          host_element: 'String',
          structure_cif_str: 'String',
        },
        key_value_pairs: {
          composition: com,
          host_element: host,
          structure_cif_str: str,
        },
      },
    )
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
  /**
   * 设置掺杂结构
   */
  async getDopingStructure(com: string, host: string, str: string): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>('getDopingStructure', '', true, {
      key_type_pairs: {
        composition: 'String',
        host_element: 'String',
        structure_cif_str: 'String',
      },
      key_value_pairs: {
        composition: com,
        host_element: host,
        structure_cif_str: str,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }
  /**
   * 设置结构优化
   */
  async getRelaxedStructure(scheme: string, str: string): Promise<SubmitResponse> {
    const response = await this.submit<SubmitResponse>('getRelaxedStructure', '', true, {
      key_type_pairs: {
        optimizer_scheme: 'String',
        structure_cif_str: 'String',
      },
      key_value_pairs: {
        optimizer_scheme: scheme,
        structure_cif_str: str,
      },
    })
    return {
      statusCode: response.statusCode,
      message: response.message,
      taskId: response.taskId,
    }
  }
  /**
   * 根据上传的结构文件获取结构信息
   */
  async getStructureInfoByFile(fileName: string, str: string): Promise<MaterialResponse> {
    const response = await this.default<DefaultServiceResponse>(
      'getStructureInfoByFile',
      '',
      false,
      {
        key_type_pairs: {
          format: 'String',
          structure_file_str: 'String',
        },
        key_value_pairs: {
          format: fileName,
          structure_file_str: str,
        },
      },
    )
    return {
      statusCode: response.statusCode,
      keyTypePairs: response.keyTypePairs,
      message: response.message,
      keyValuePairs: response.keyValuePairs,
    }
  }
}
/**
 * 创建 TaskService 实例
 */
export function createMaterialService(): MaterialService {
  return new MaterialService()
}
