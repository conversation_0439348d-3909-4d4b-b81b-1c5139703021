// ... 现有代码 ...
export interface LogEntry {
  id?: number
  taskId?: string // 任务ID
  workflow: string //工作流名称
  workflowId: string //工作流ID
  service_name: string // 服务名称
  server_id: string // 服务ID
  createdAt: number // 提交时间戳
  computationTime?: number // 计算时间（毫秒）
  taskStatus: string // 运行状态
  taskProgress: number // 任务进度 0-100
  input_data?: string // 存储提交参数的JSON字符串
  result?: string // 任务结果
  nodeData: any // 整个节点数据
  message?: string // 成功或错误信息
  nodeId: string // 节点ID
  lastUpdated: number // 最后更新时间Z
}

export enum TaskStatus {
  INITIALIZING = 'Initializing',
  COMPUTING = 'Computing',
  COMPLETED = 'Completed',
  PENDING = 'Pending',
  PAUSED = 'Paused',
  FINISHED = 'Finished',
  ERROR = 'Error',
  FAILED = 'Failed',
  CANCELED = 'Canceled',
  TASK_STAY = 'TaskStay',
  RUNNING = 'Running',
  ABORT = 'Abort',
}

export type TimeFilter = '1d' | '7d' | '30d' | 'all'
