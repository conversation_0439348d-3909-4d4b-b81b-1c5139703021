<template>
  <div class="space-y-4">
    <!-- 应用基本信息 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
          >
            <Info class="w-3.5 h-3.5" />
          </div>
          应用信息
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <div class="grid grid-cols-2 gap-3 text-sm">
          <div class="flex items-center justify-between">
            <span class="text-muted-foreground">类型</span>
            <Badge variant="outline" class="font-mono text-xs">{{ appType }}</Badge>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-muted-foreground">版本</span>
            <Badge variant="secondary" class="font-mono text-xs">{{ appMeta.version }}</Badge>
          </div>
        </div>

        <Separator />

        <div class="space-y-2">
          <div>
            <Label class="text-xs text-muted-foreground">应用名称</Label>
            <p class="text-sm font-medium">{{ appMeta.name }}</p>
          </div>
          <div>
            <Label class="text-xs text-muted-foreground">应用标题</Label>
            <p class="text-sm font-medium">{{ appMeta.title }}</p>
          </div>
          <div>
            <Label class="text-xs text-muted-foreground">应用描述</Label>
            <p class="text-sm text-muted-foreground">{{ appMeta.description }}</p>
          </div>
          <div>
            <Label class="text-xs text-muted-foreground">开发者</Label>
            <p class="text-sm font-medium">{{ appMeta.author }}</p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 应用Logo和图标 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400"
          >
            <Image class="w-3.5 h-3.5" />
          </div>
          图标资源
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex items-start space-x-4">
          <!-- 应用图标 -->
          <div class="text-center">
            <div
              class="w-12 h-12 bg-background rounded-lg border border-muted flex items-center justify-center mb-2 shadow-sm"
            >
              <img
                :src="getIconUrl"
                :alt="`${appMeta.name} icon`"
                class="w-8 h-8 object-contain"
                @error="handleImageError"
              />
            </div>
            <Label class="text-xs text-muted-foreground">图标</Label>
          </div>

          <!-- 应用Logo -->
          <div class="text-center">
            <div
              class="w-12 h-12 bg-background rounded-lg border border-muted flex items-center justify-center mb-2 shadow-sm"
            >
              <img
                :src="getLogoUrl"
                :alt="`${appMeta.name} logo`"
                class="w-10 h-8 object-contain"
                @error="handleImageError"
              />
            </div>
            <Label class="text-xs text-muted-foreground">Logo</Label>
          </div>

          <!-- 文件路径信息 -->
          <div class="flex-1 space-y-1">
            <div class="flex items-center justify-between">
              <Label class="text-xs text-muted-foreground">图标路径</Label>
              <code class="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                {{ appMeta.icon }}
              </code>
            </div>
            <div class="flex items-center justify-between">
              <Label class="text-xs text-muted-foreground">Logo路径</Label>
              <code class="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                {{ appMeta.logo }}
              </code>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 环境信息 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400"
          >
            <Monitor class="w-3.5 h-3.5" />
          </div>
          环境信息
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <div class="grid grid-cols-2 gap-3">
          <div class="flex items-center justify-between">
            <Label class="text-xs text-muted-foreground">应用类型</Label>
            <Badge variant="outline" class="font-mono text-xs">{{ envIsMatt }}</Badge>
          </div>
          <div class="flex items-center justify-between">
            <Label class="text-xs text-muted-foreground">环境</Label>
            <Badge :variant="isDev ? 'default' : 'secondary'" class="font-mono text-xs">
              {{ nodeEnv }}
            </Badge>
          </div>
        </div>

        <Separator />

        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <Label class="text-xs text-muted-foreground">开发模式</Label>
            <Badge :variant="isDev ? 'default' : 'destructive'" class="text-xs">
              {{ isDev ? '是' : '否' }}
            </Badge>
          </div>
          <div class="flex items-center justify-between">
            <Label class="text-xs text-muted-foreground">构建时间</Label>
            <code class="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">{{ buildTime }}</code>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 应用统计 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400"
          >
            <BarChart3 class="w-3.5 h-3.5" />
          </div>
          功能统计
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center space-y-1">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ enabledFeaturesCount }}
            </div>
            <Label class="text-xs text-muted-foreground">已启用功能</Label>
          </div>
          <div class="text-center space-y-1">
            <div class="text-2xl font-bold text-muted-foreground">
              {{ totalFeaturesCount }}
            </div>
            <Label class="text-xs text-muted-foreground">总功能数</Label>
          </div>
          <div class="text-center space-y-1">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ allowedNodeCategories.length }}
            </div>
            <Label class="text-xs text-muted-foreground">节点分类</Label>
          </div>
          <div class="text-center space-y-1">
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {{ allowedThemes.length }}
            </div>
            <Label class="text-xs text-muted-foreground">可用主题</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { Label } from '@renderer/components/ui/label'
import { Separator } from '@renderer/components/ui/separator'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { BarChart3, Image, Info, Monitor } from 'lucide-vue-next'
import { computed } from 'vue'

const {
  appType,
  appMeta,
  getIconUrl,
  getLogoUrl,
  getAllowedNodeCategories,
  getAllowedThemes,
  features,
} = useAppConfig()

// 环境变量
const envIsMatt = import.meta.env.VITE_APP_IS_MATT
const nodeEnv = import.meta.env.NODE_ENV
const isDev = import.meta.env.DEV
const buildTime = new Date().toLocaleString()

// 功能统计
const enabledFeaturesCount = computed(() => {
  let count = 0
  const featuresObj = features.value

  // 递归计算启用的功能数量
  const countFeatures = (obj: any): void => {
    for (const key in obj) {
      if (typeof obj[key] === 'boolean' && obj[key]) {
        count++
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        countFeatures(obj[key])
      }
    }
  }

  countFeatures(featuresObj)
  return count
})

const totalFeaturesCount = computed(() => {
  let count = 0
  const featuresObj = features.value

  // 递归计算总功能数量
  const countFeatures = (obj: any): void => {
    for (const key in obj) {
      if (typeof obj[key] === 'boolean') {
        count++
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        countFeatures(obj[key])
      }
    }
  }

  countFeatures(featuresObj)
  return count
})

const allowedNodeCategories = computed(() => getAllowedNodeCategories.value)
const allowedThemes = computed(() => getAllowedThemes.value)

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
}
</script>
