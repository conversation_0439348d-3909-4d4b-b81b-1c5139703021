// composables/useConfirmDialog.ts
import { h, reactive, render } from 'vue'
import ConfirmDialog from '@renderer/components/dialog/confirmDialog.vue'
interface ConfirmOptions {
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  onConfirm?: () => Promise<void> | void
}

export function useConfirmDialog() {
  const state = reactive({
    visible: false,
    options: {} as ConfirmOptions,
  })

  const container = document.createElement('div')
  document.body.appendChild(container)

  const openConfirm = (options: ConfirmOptions) => {
    state.options = options
    state.visible = true
    renderConfirm()
  }

  const closeConfirm = () => {
    state.visible = false
  }

  const renderConfirm = () => {
    render(
      h(ConfirmDialog, {
        ...state.options,
        visible: state.visible,
        'onUpdate:visible': (v: boolean) => {
          state.visible = v
          if (!v) render(null, container) // 清除 DOM
        },
        onConfirmed: () => state.options.onConfirm?.(),
      }),
      container,
    )
  }

  return {
    openConfirm,
    closeConfirm,
  }
}
