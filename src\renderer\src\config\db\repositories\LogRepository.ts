import { LogEntry } from '../models/LogEntry'
import { db } from '../index'

export class LogRepository {
  // 添加日志记录
  static async add(logEntry: Partial<LogEntry>): Promise<number> {
    return await db.logEntries.add(logEntry)
  }

  // 更新日志记录
  static async update(id: number, data: Partial<LogEntry>): Promise<void> {
    await db.logEntries.update(id, data)
  }

  // 获取单条日志记录
  static async get(id: number): Promise<LogEntry | undefined> {
    return await db.logEntries.get(id)
  }

  // 根据任务ID查找日志
  static async findByTaskId(taskId: string): Promise<LogEntry | undefined> {
    return await db.logEntries.where('taskId').equals(taskId).first()
  }

  // 根据节点ID查找日志
  static async findByNodeId(nodeId: string): Promise<LogEntry[]> {
    return await db.logEntries.where('nodeId').equals(nodeId).toArray()
  }

  // 获取所有日志记录，按最后更新时间排序
  static async getAll(): Promise<LogEntry[]> {
    return await db.logEntries.orderBy('lastUpdated').reverse().toArray()
  }

  // 根据工作流获取日志
  static async findByWorkflow(workflow: string): Promise<LogEntry[]> {
    return await db.logEntries.where('workflow').equals(workflow).toArray()
  }

  // 根据时间范围查询日志
  static async findByTimeRange(startTime: number, endTime: number): Promise<LogEntry[]> {
    return await db.logEntries.where('createdAt').between(startTime, endTime).toArray()
  }

  // 删除日志记录
  static async delete(id: number): Promise<void> {
    await db.logEntries.delete(id)
  }

  // 清空所有日志
  static async clear(): Promise<void> {
    await db.logEntries.clear()
  }
}
