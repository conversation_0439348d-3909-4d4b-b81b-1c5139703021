/**
 * 工作流注册模块
 */
import { useAuthStore, useNodeModulesStore } from '@renderer/store'
import { createAuthService } from '@renderer/config/api/grpc/authService'
import { createLifePredictionEdges, extractLifePredictionNodes } from './generation'

// 注册状态标志
let hasRegistered = false

/**
 * 注册节点模块处理函数
 * @param workflowName 可选的工作流名称
 */
export function registerNodeModules(workflowName?: string): void {
  if (hasRegistered) return

  try {
    // 获取节点模块存储
    const nodeModulesStore = useNodeModulesStore()

    // 如果提供了工作流名称，则注册对应的工作流
    if (workflowName && nodeModulesStore.nodeModules[workflowName]) {
      const workflowModule = nodeModulesStore.nodeModules[workflowName]

      // 根据工作流类型注册不同的处理函数
      if (workflowName === '豪鹏电池寿命预测') {
        registerLifePredictionWorkflow(workflowModule)
        window.logger?.info(`已注册 ${workflowName} 工作流处理函数`)
      } else {
        // 对于其他类型的工作流，可以在这里添加处理逻辑
        window.logger?.info(`识别到 ${workflowName} 工作流，但暂无对应处理函数`)
      }
    } else {
      // 如果没有提供工作流名称，则尝试注册所有已知工作流
      if (nodeModulesStore.nodeModules['豪鹏电池寿命预测']) {
        registerLifePredictionWorkflow(nodeModulesStore.nodeModules['豪鹏电池寿命预测'])
        window.logger?.info('已注册豪鹏电池寿命预测工作流处理函数')
      }
    }

    hasRegistered = true
  } catch (error) {
    window.logger?.error('注册节点模块处理函数失败:', error)
  }
}

/**
 * 注册寿命预测流程
 * @param nodeModule 节点模块对象
 * @param sessionId 会话ID
 * @returns Promise<any>
 */
export async function registerLifePredictionWorkflow(
  nodeModule: any,
  sessionId = 'abcdefg',
): Promise<any> {
  const authStore = useAuthStore()
  const authService = createAuthService()

  try {
    // 提取节点信息
    const { nodes, nodeMap } = extractLifePredictionNodes(nodeModule)
    const edges = createLifePredictionEdges(nodeMap)

    // 构建注册参数
    const registrationParams = {
      workflow_name: '豪鹏电池寿命预测',
      nodes: nodes,
      edges: edges,
    }

    window.logger.info('寿命预测流程注册参数:', registrationParams)

    // 调用callClientApi注册节点
    const response = await window.grpcApi.call('callClientApi', {
      task_id: '',
      server_id: '',
      service_name: 'callClientApi',
      user_id: authStore.userId || '0',
      token: authStore.token || 'abcdefghijklmn',
      is_save: false,
      key_type_pairs: {
        session_id: 'String',
        function_name: 'String',
        call_params: 'Json',
      },
      key_value_pairs: {
        session_id: sessionId,
        function_name: 'generate_workflow',
        call_params: JSON.stringify(registrationParams),
      },
    })

    window.logger.info('寿命预测流程注册成功:', response)
    return response
  } catch (error) {
    window.logger.error('寿命预测流程注册失败:', error)
    throw error
  }
}
