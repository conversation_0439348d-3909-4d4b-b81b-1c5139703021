<template>
  <div
    class="bg-background rounded-xl shadow-sm p-6 border border-gray-200 dark:bg-muted-foreground"
  >
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold text-foreground">{{ title }}</h2>
      <Button
        v-if="hasData"
        :disabled="isDisabled"
        variant="destructive"
        size="sm"
        @click="$emit('reset')"
      >
        重新上传
      </Button>
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    required: true,
  },
  // 是否有数据
  hasData: {
    type: Boolean,
    required: true,
  },
  //参数是否提取
  isDisabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['reset'])
</script>

<style scoped>
.chart-container {
  transition: all 0.3s ease;
}
</style>
