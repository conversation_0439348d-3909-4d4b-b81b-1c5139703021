import { ipcMain } from 'electron'
import { fileService } from '../services/fileService'
import logger from '../utils/logger'

// 跟踪处理程序是否已设置
let handlersSetup = false
/**
 * 设置文件操作相关的IPC处理程序
 */
export function setupFileHandlers(): void {
  // 如果处理程序已设置，则直接返回
  if (handlersSetup) {
    logger.info('文件操作IPC处理程序已设置，跳过重复设置')
    return
  }

  // 显示保存对话框
  ipcMain.handle('show-save-dialog', async (_event, options) => {
    try {
      return await fileService.showSaveDialog(options)
    } catch (error) {
      logger.error('显示保存对话框失败:', error)
      throw error
    }
  })

  // 保存文件
  ipcMain.handle('save-file', async (_, options) => {
    try {
      return await fileService.saveFile(options)
    } catch (error) {
      logger.error('保存文件失败:', error)
      return { success: false, error: (error as Error).message || String(error) }
    }
  })

  // 加密并保存文件
  ipcMain.handle('encrypt-and-save-file', async (_, options) => {
    try {
      return await fileService.encryptAndSaveFile(options)
    } catch (error) {
      logger.error('文件保存失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  })

  // 显示打开文件对话框
  ipcMain.handle('show-open-dialog', async (_, options) => {
    try {
      return await fileService.showOpenDialog(options)
    } catch (error) {
      logger.error('打开文件对话框失败:', error)
      return { success: false, error: (error as Error).message || String(error) }
    }
  })

  // 读取文件
  ipcMain.handle('read-file', async (_, options) => {
    try {
      return await fileService.readFile(options)
    } catch (error) {
      logger.error('读取文件失败:', error)
      return { success: false, error: (error as Error).message || String(error) }
    }
  })
  // 标记处理程序已设置
  handlersSetup = true
  logger.info('文件操作IPC处理程序已设置')
}
