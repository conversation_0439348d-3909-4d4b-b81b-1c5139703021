import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { useAuthStore } from '@renderer/store'
import { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'

// 身份验证守卫
export const authGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const authStore = useAuthStore()
  const { isMattVerse, navigationConfig } = useAppConfig()

  // 如果是非MattVerse应用且访问根路径，直接重定向到默认路由
  if (!isMattVerse.value && to.path === '/') {
    console.log('非MattVerse应用访问根路径，重定向到默认路由')
    const defaultPath = navigationConfig.value.defaultRoute
    next({ path: defaultPath })
    return
  }

  // 如果目标路由是登录页，允许直接访问
  if (to.name === 'auth') {
    // 如果已登录并尝试访问登录页，重定向到首页并设置窗口为全屏
    if (authStore.isLoggedIn) {
      // 调整窗口为全屏模式
      if (window.windowControl?.maximizeAfterLogin) {
        window.windowControl.maximizeAfterLogin()
      }
      // 根据应用类型重定向到不同的首页
      const defaultPath = navigationConfig.value.defaultRoute
      next({ path: defaultPath })
      return
    }

    // 未登录，确保窗口为登录窗口大小
    if (window.windowControl && window.windowControl.setLoginSize) {
      window.windowControl.setLoginSize()
    }
    next()
    return
  }

  // 检查用户是否已登录
  if (!authStore.isLoggedIn) {
    // 先尝试从本地存储恢复登录状态
    const loginStatus = authStore.checkLoginStatus()

    if (!loginStatus) {
      // 未登录，重定向到登录页并调整窗口大小
      if (window.windowControl && window.windowControl.setLoginSize) {
        window.windowControl.setLoginSize()
      }
      next({ name: 'auth' })
      return
    } else {
      // 恢复了登录状态，确保窗口为全屏
      if (window.windowControl?.maximizeAfterLogin) {
        window.windowControl.maximizeAfterLogin()
      }
    }
  } else {
    // 已登录，确保窗口为全屏
    if (from.name === 'auth' || from.path === '/') {
      if (window.windowControl?.maximizeAfterLogin) {
        window.windowControl.maximizeAfterLogin()
      }
    }
  }

  // 已登录，继续导航
  next()
}
