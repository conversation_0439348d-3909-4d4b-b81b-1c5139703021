/**
 * 应用配置类型定义
 */

// 应用类型枚举
export const APP_TYPES = {
  /** MattVerse 电池设计自动化平台 */
  MATTVERSE: 'mattverse',
  /** 豪鹏电池寿命预测软件 */
  HIGHPOWER: 'highpower',
} as const

export type AppType = (typeof APP_TYPES)[keyof typeof APP_TYPES]

// 应用元信息配置接口
export interface AppMetaConfig {
  /** 应用名称 */
  name: string
  /** 应用标题 */
  title: string
  /** 应用描述 */
  description: string
  /** 应用版本 */
  version: string
  /** 应用图标 */
  icon: string
  /** 应用Logo */
  logo: string
  /** 开发者 */
  author: string
  /** 官网地址 */
  homepage: string
}

// 布局配置接口
export interface LayoutConfig {
  /** 是否显示侧边栏 */
  showSidebar: boolean
  /** 是否显示标题栏 */
  showTitleBar: boolean
  /** 是否显示状态栏 */
  showStatusBar: boolean
}

// 工作流编辑器配置接口
export interface WorkflowEditorConfig {
  /** 是否显示工作流导航栏 */
  showNavbar: boolean
  /** 是否显示AI浮动框 */
  showAIFloatingBox: boolean
  /** 是否显示工具栏 */
  showToolbar: boolean
  /** 是否显示小地图 */
  showMiniMap: boolean
  /** 是否显示左侧控制面板 */
  showLeftControls: boolean
  /** 是否显示右侧控制面板 */
  showRightControls: boolean
  /** 是否启用节点拖拽 */
  enableNodeDrag: boolean
  /** 是否启用节点调整大小 */
  enableNodeResize: boolean
  /** 是否显示背景 */
  showBackground: boolean
  /** 背景类型 */
  backgroundType: 'dots' | 'lines' | 'cross' | 'none'
}

// 导航配置接口
export interface NavigationConfig {
  /** 是否显示主导航 */
  showMainNavigation: boolean
  /** 是否显示面包屑 */
  showBreadcrumb: boolean
  /** 是否启用路由守卫 */
  enableRouteGuard: boolean
  /** 默认路由 */
  defaultRoute: string
}

// 节点工具栏配置接口
export interface NodeToolbarConfig {
  /** 是否显示节点工具栏 */
  showNodeToolbar: boolean
  /** 允许的节点分类 */
  enableNodeCategories: string[]
  /** 是否启用自定义节点 */
  enableCustomNodes: boolean
}

// 认证配置接口
export interface AuthConfig {
  /** 是否启用认证 */
  enableAuth: boolean
  /** 是否显示登录表单 */
  showLoginForm: boolean
  /** 是否显示注册表单 */
  showRegisterForm: boolean
  /** 是否启用访客模式 */
  enableGuestMode: boolean
}

// 主题配置接口
export interface ThemeConfig {
  /** 是否启用主题切换 */
  enableThemeSwitch: boolean
  /** 是否启用暗色模式 */
  enableDarkMode: boolean
  /** 默认主题 */
  defaultTheme: string
  /** 允许的主题列表 */
  allowedThemes: string[]
}

// 工具配置接口
export interface ToolsConfig {
  /** 是否显示工具面板 */
  showToolsPanel: boolean
  /** 启用的工具列表 */
  enabledTools: string[]
}

// 侧边栏菜单项配置接口
export interface SidebarMenuItems {
  /** 工作流菜单 */
  workflow: boolean
  /** 任务菜单 */
  task: boolean
  /** 服务器菜单 */
  server: boolean
  /** 日志菜单 */
  logger: boolean
  /** 工具菜单 */
  tools: boolean
  /** 设置菜单 */
  setting: boolean
  /** AI菜单 */
  ai: boolean
  /** 用户菜单 */
  user: boolean
  /** 关于菜单 */
  about: boolean
}

// 侧边栏配置接口
export interface SidebarConfig {
  /** 是否显示侧边栏 */
  showSidebar: boolean
  /** 菜单项配置 */
  menuItems: SidebarMenuItems
}

// 其他功能配置接口
export interface FeaturesConfig {
  /** 是否启用国际化 */
  enableI18n: boolean
  /** 是否启用通知 */
  enableNotifications: boolean
  /** 是否启用快捷键 */
  enableHotkeys: boolean
  /** 是否启用自动保存 */
  enableAutoSave: boolean
  /** 是否启用导出功能 */
  enableExport: boolean
  /** 是否启用导入功能 */
  enableImport: boolean
}

// 功能模块配置接口
export interface FeatureConfig {
  /** 布局相关配置 */
  layout: LayoutConfig
  /** 侧边栏相关配置 */
  sidebar: SidebarConfig
  /** 工作流编辑器相关配置 */
  workflowEditor: WorkflowEditorConfig
  /** 导航相关配置 */
  navigation: NavigationConfig
  /** 节点工具栏相关配置 */
  nodeToolbar: NodeToolbarConfig
  /** 认证相关配置 */
  auth: AuthConfig
  /** 主题相关配置 */
  theme: ThemeConfig
  /** 工具相关配置 */
  tools: ToolsConfig
  /** 其他功能配置 */
  features: FeaturesConfig
}

// 完整的应用配置接口
export interface AppConfig {
  /** 应用类型 */
  type: AppType
  /** 应用元信息 */
  meta: AppMetaConfig
  /** 功能配置 */
  features: FeatureConfig
}
