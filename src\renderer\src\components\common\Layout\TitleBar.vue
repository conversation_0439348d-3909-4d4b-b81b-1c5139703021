<template>
  <div class="title-bar">
    <div class="drag-region">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- <img src="@assets/images/logo-icon.png" alt="Logo" class="logo" /> -->
      </div>

      <!-- 中间标题 -->
      <div class="title">{{ title }}</div>

      <!-- 右侧窗口控制按钮 -->
      <div class="window-controls">
        <button class="window-control minimize" @click="minimize">
          <Icon icon="mdi:window-minimize" />
        </button>
        <button class="window-control maximize" @click="maximize">
          <Icon v-if="isMaximized" icon="mdi:window-restore" />
          <Icon v-else icon="mdi:window-maximize" />
        </button>
        <button class="window-control close" @click="close">
          <Icon icon="mdi:close" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'

const props = defineProps({
  title: {
    type: String,
    default: 'MattVerse',
  },
})

const isMaximized = ref(false)

// 窗口控制函数
const minimize = () => {
  console.log('Minimize button clicked')
  try {
    if (window.windowControl) {
      window.windowControl.minimize()
      // console.log('Minimize command sent')
    } else {
      console.warn('window.electron.ipcRenderer 不可用')
    }
  } catch (error) {
    console.error('Failed to send minimize command:', error)
  }
}

const maximize = () => {
  console.log('Maximize button clicked')
  try {
    if (window.windowControl) {
      window.windowControl.maximize()
      // console.log('Maximize command sent')
    } else {
      console.warn('window.electron.ipcRenderer 不可用')
    }
  } catch (error) {
    console.error('Failed to send maximize command:', error)
  }
}

const close = () => {
  console.log('Close button clicked')
  try {
    if (window.windowControl) {
      window.windowControl.close()
      // console.log('Close command sent')
    } else {
      console.warn('window.electron.ipcRenderer 不可用')
    }
  } catch (error) {
    console.error('Failed to send close command:', error)
  }
}

// 监听窗口状态变化
const handleMaximizeChange = (maximized) => {
  // console.log('Window maximized state changed:', maximized)
  isMaximized.value = maximized
}

onMounted(async () => {
  console.log('TitleBar component mounted')

  // 检查 window.electron 是否可用
  if (!window.windowControl) {
    console.warn('window.windowControl 不可用，窗口控制功能将不可用')
    return
  }

  // 检查初始窗口状态
  try {
    const maximized = await window.windowControl.isMaximized()
    // console.log('Initial window maximized state:', maximized)
    isMaximized.value = maximized
  } catch (error) {
    console.error('Failed to get initial window state:', error)
  }

  // 添加事件监听
  try {
    window.windowControl.onMaximizedChange(handleMaximizeChange)
    // console.log('Added window-maximized-change listener')
  } catch (error) {
    console.error('Failed to add window-maximized-change listener:', error)
  }
})

onUnmounted(() => {
  console.log('TitleBar component unmounted')

  // 检查 window.electron 是否可用
  if (!window.windowControl) {
    console.warn('window.windowControl 不可用，无法移除事件监听')
    return
  }

  // 移除事件监听
  try {
    window.windowControl.removeMaximizedChangeListener(handleMaximizeChange)
    // console.log('Removed window-maximized-change listener')
  } catch (error) {
    console.error('Failed to remove window-maximized-change listener:', error)
  }
})
</script>

<style lang="scss" scoped>
.title-bar {
  @apply h-8 bg-[#3C3C3C] text-white select-none flex items-center;
  -webkit-app-region: drag;
}

.drag-region {
  @apply flex items-center w-full h-full;
  align-self: xaA;
}

.left-section {
  @apply pl-2.5 flex items-center;
}

.logo {
  @apply w-5 h-5 mr-2;
}

.title {
  @apply flex-1 text-center text-sm font-medium;
}

.window-controls {
  @apply flex;
  -webkit-app-region: no-drag;
}

.window-control {
  @apply w-[46px] h-8 flex justify-center items-center bg-transparent border-none text-white cursor-pointer;

  &:hover {
    @apply bg-white bg-opacity-10;
  }

  &.close:hover {
    @apply bg-[#e81123];
  }
}
</style>
