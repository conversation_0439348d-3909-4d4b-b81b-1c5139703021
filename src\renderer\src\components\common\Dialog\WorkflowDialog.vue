<template>
  <div class="workflow-dialog">
    <Dialog v-model:open="visible">
      <DialogContent :class="width">
        <DialogHeader>
          <DialogTitle class="text-lg font-semibold">{{ title }}</DialogTitle>
        </DialogHeader>

        <div class="py-4">
          <form @submit.prevent="onConfirm">
            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="name">名称</Label>
                <Input id="name" v-model="formData.name" placeholder="请输入名称" class="w-full" />
              </div>

              <div class="space-y-2">
                <Label for="description">描述</Label>
                <Textarea
                  id="description"
                  v-model="formData.description"
                  placeholder="请输入描述"
                  class="w-full resize-none"
                  :rows="5"
                  :maxlength="350"
                />
                <div class="text-xs text-muted-foreground text-right">
                  {{ formData.description.length }}/350
                </div>
              </div>
            </div>
          </form>
        </div>

        <DialogFooter class="flex space-x-2">
          <Button variant="outline" class="w-full" @click="handleCancel">
            {{ cancelText }}
          </Button>
          <Button class="w-full text-white" @click="onConfirm">
            {{ confirmText }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const emit = defineEmits(['confirm'])

interface Props {
  width?: string
  cancelText?: string
}

withDefaults(defineProps<Props>(), {
  width: 'max-w-md',
  cancelText: '取消',
})

const visible = ref(false)
const typeOption = ref('')
const title = ref('添加工作流')
const confirmText = ref('添加')

// 使用 reactive 创建表单数据对象
const formData = reactive({
  name: '',
  description: '',
  id: '', // 添加 id 字段用于编辑时
})

const onConfirm = () => {
  if (!formData.name.trim()) return

  // 发送表单数据的副本
  const submitData = {
    name: formData.name.trim(),
    description: formData.description.trim(),
    id: formData.id,
  }

  emit('confirm', submitData, typeOption.value)
  close()
}

const handleCancel = () => {
  close()
}

const open = (type: 'add' | 'edit', item = { id: '', title: '', description: '' }) => {
  visible.value = true
  typeOption.value = type

  if (type === 'add') {
    title.value = '添加工作流'
    confirmText.value = '添加'
    // 重置表单
    formData.name = ''
    formData.description = ''
    formData.id = ''
  } else {
    title.value = '编辑工作流'
    confirmText.value = '更新'
    // 填充表单数据
    formData.name = item.title
    formData.description = item.description
    formData.id = item.id
  }
}

const close = () => {
  visible.value = false
  // 重置表单数据
  formData.name = ''
  formData.description = ''
  formData.id = ''
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.workflow-dialog {
  :deep(.dialog-overlay) {
    @apply bg-black/50;
  }
}
</style>
