<template>
  <Card class="my-6">
    <CardHeader>
      <CardTitle>{{ t('settings.baseSetting.fontSize.title') }}</CardTitle>
      <CardDescription>{{ t('settings.baseSetting.fontSize.description') }}</CardDescription>
    </CardHeader>
    <CardContent>
      <div class="space-y-2">
        <Label>{{ t('settings.baseSetting.fontSize.select') }}</Label>
        <Select v-model="currentFontSize" @update:model-value="setFontSize">
          <SelectTrigger class="w-full sm:w-[240px]">
            <SelectValue :placeholder="t('settings.baseSetting.fontSize.placeholder')" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>{{ t('settings.baseSetting.fontSize.select') }}</SelectLabel>
              <SelectItem v-for="size in fontSizes" :key="size.value" :value="size.value">
                <div class="flex items-center gap-2">
                  <span :style="{ fontSize: size.sizeValue }">Aa</span>
                  <span>{{ t(`settings.baseSetting.fontSize.sizes.${size.value}`) }}</span>
                </div>
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        <!-- 字号预览 -->
        <div class="mt-4 p-4 border rounded-md">
          <p class="text-sm text-muted-foreground mb-2">
            {{ t('settings.baseSetting.fontSize.preview') }}:
          </p>
          <p :style="{ fontSize: getFontSizeValue(currentFontSize) }" class="text-base">
            The quick brown fox jumps over the lazy dog.
            <br />
            中文示例：永和九年，岁在癸丑，暮春之初，会于会稽山阴之兰亭。
          </p>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { useLanguage } from '@renderer/config/hooks'
import { useSettingsStore } from '@renderer/store'
import { computed } from 'vue'

const { t } = useLanguage()
const settingsStore = useSettingsStore()

// 使用 store 中的状态
const currentFontSize = computed(() => settingsStore.fontSize)
const fontSizes = computed(() => settingsStore.fontSizeConfigs)

const getFontSizeValue = (sizeKey: string) => {
  const size = fontSizes.value.find((s) => s.value === sizeKey)
  return size ? size.sizeValue : '16px'
}

const setFontSize = (size: string) => {
  settingsStore.setFontSize(size)
}
</script>
