const grpc = require('@grpc/grpc-js')
const { getProtocol } = require('./protocol')
import logger from '../utils/logger'

export class ClientFactory {
  // 默认的连接选项
  private defaultOptions: Record<string, any>
  private clientMap: Record<string, any>

  constructor() {
    this.defaultOptions = {
      'grpc.max_send_message_length': 10 * 1024 * 1024, // 最大发送消息大小 10MB
      'grpc.max_receive_message_length': 10 * 1024 * 1024, // 最大接收消息大小 10MB
    }
    // 支持的client种类
    this.clientMap = {
      linker: LinkerClient,
    }
  }

  /**
   * 创建并连接 gRPC 客户端
   * @param {string} serverUrl 服务器地址
   * @param {string} clientType 客户端种类
   * @param {object} options 连接选项
   * @returns {object} { client: grpc.Client, isConnected: boolean }
   */
  create(serverUrl, clientType, options = {}) {
    // 合并默认选项和传入选项，传入选项会覆盖默认值
    const finalOptions = {
      ...this.defaultOptions, // 默认配置
      ...options, // 用户自定义配置，覆盖默认配置
    }

    if (!serverUrl || serverUrl.trim() === '') {
      logger.error(`无效的服务器地址: "${serverUrl}"`)
      return { client: null, isConnected: false }
    }

    const protocol = getProtocol(clientType)
    logger.info(protocol)
    const serviceName = this.getServiceNameByProtocol(clientType)

    const service = protocol[serviceName]

    if (!service) {
      logger.error(`协议中不存在服务 ${serviceName}`)
      return { client: null, isConnected: false }
    }

    try {
      logger.info(`尝试连接 gRPC 服务器: ${serverUrl}`)
      const client = this.clientMap[clientType](serverUrl, service, finalOptions)
      // 检查连接状态
      const state = client.getState()

      // 使用 isConnected 代替 connected
      const isConnected = state === grpc.connectivityState.READY

      if (isConnected) {
        logger.info(`成功连接到 ${serverUrl}`)
      } else {
        logger.warn(`连接未就绪，状态: ${state}`)
      }

      return { client, isConnected }
    } catch (error) {
      logger.error(`连接 ${serverUrl} 失败:`, error)
      return { client: null, isConnected: false }
    }
  }

  getServiceNameByProtocol(protocolName) {
    const serviceMap = {
      linker: 'LinkerService',
      matt: 'MattService',
      db: 'DbService',
      common: 'CommonService',
    }
    return (
      serviceMap[protocolName] ||
      `${protocolName.charAt(0).toUpperCase() + protocolName.slice(1)}Service`
    )
  }
}

class GrpcClient {
  public grpcClient: any
  constructor(serverUrl, service, options) {
    this.grpcClient = new service(serverUrl, grpc.credentials.createInsecure(), options)
  }
  getState() {
    const channel = this.grpcClient.getChannel()
    const state = channel.getConnectivityState(true)
    logger.info(
      `连接状态: ${state} (0=IDLE, 1=CONNECTING, 2=READY, 3=TRANSIENT_FAILURE, 4=SHUTDOWN)`,
    )
  }

  // 单次响应单次应答
  singleCall(apiName, params, metadata, callBack) {
    try {
      if (typeof this.grpcClient[apiName] !== 'function') {
        logger.error(`接口 ${apiName} 不存在，请检查 proto 文件`)
        callBack(new Error(`接口 ${apiName} 不存在`), null)
        return
      }

      // 普通的单次请求响应
      this.grpcClient[apiName](params, metadata, (error, response) => {
        if (error) {
          logger.error(`调用 ${apiName} 时发生错误:`, error)
        }
        callBack(error, response)
      })
    } catch (error) {
      logger.error(`调用 ${apiName} 时发生异常:`, error)
      callBack(error, null)
    }
  }

  // 客户端单次请求服务端流式响应
  serverStreamCall(apiName, params, metadata, callBack) {
    try {
      if (typeof this.grpcClient[apiName] !== 'function') {
        logger.error(`接口 ${apiName} 不存在，请检查 proto 文件`)
        callBack(new Error(`接口 ${apiName} 不存在`), null)
        return
      }

      const service = this.grpcClient[apiName](params, metadata)
      service.on('data', function (response) {
        logger.info(`服务端响应数据:`, response)
        callBack(response, 'data')
      })

      service.on('end', function () {
        logger.info(`服务端流式响应结束`)
        callBack(undefined, 'end')
      })

      service.on('error', function (error) {
        logger.error(`服务端流式响应错误:`, error)
        callBack(error, 'error')
      })
    } catch (error) {
      logger.error(`调用 ${apiName} 时发生异常:`, error)
      callBack(error, null)
    }
  }

  // 客户端流式请求服务端单次响应
  clientStreamCall(apiName, metadata, callBack) {
    try {
      if (typeof this.grpcClient[apiName] !== 'function') {
        logger.error(`接口 ${apiName} 不存在，请检查 proto 文件`)
        callBack(new Error(`接口 ${apiName} 不存在`), null)
        return
      }
      const stream = this.grpcClient[apiName]((error, response) => {
        if (error) {
          callBack(error, 'error')
        }
        if (response) {
          if (callBack) {
            callBack(response, 'data')
          }
        }
        //logger.info('返回结果:', response)
      }, metadata)

      stream.on('end', () => {
        callBack(null, 'end')
      })

      stream.on('error', (error) => {
        callBack(error, 'error')
      })
      return stream
    } catch (error) {
      //logger.error(`客户端流式调用时发生异常:`, error)
      callBack(error, null)
    }
  }
}

export class LinkerClient extends GrpcClient {
  call(apiName, params, metadata, callBack, callType = 'default') {
    switch (callType) {
      case 'default':
        this.singleCall(apiName, params, metadata, callBack) // 单次响应
        break
      case 'serverStream':
        this.serverStreamCall(apiName, params, metadata, callBack) // 服务端流式响应
        break
      case 'clientStream':
        this.clientStreamCall(apiName, metadata, callBack) // 客户端流式响应
        break
      default:
        logger.error(`不支持的调用类型: ${callType}`)
        callBack(new Error(`不支持的调用类型: ${callType}`), null)
        break
    }
  }
}
