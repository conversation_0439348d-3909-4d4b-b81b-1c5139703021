<template>
  <Card class="mb-6">
    <CardHeader>
      <CardTitle>{{ t('settings.baseSetting.cache.title') }}</CardTitle>
      <CardDescription>{{ t('settings.baseSetting.cache.description') }}</CardDescription>
    </CardHeader>
    <CardContent>
      <div class="space-y-6">
        <!-- 存储类型选择 -->
        <div class="flex flex-col space-y-2">
          <div class="flex items-center justify-between">
            <Label>{{ t('settings.baseSetting.cache.types.title') }}</Label>
            <div class="flex items-center space-x-2">
              <Button variant="outline" size="sm" @click="selectAllStorageTypes">
                {{ t('settings.baseSetting.cache.selectAll') }}
              </Button>
              <Button variant="outline" size="sm" @click="deselectAllStorageTypes">
                {{ t('settings.baseSetting.cache.deselectAll') }}
              </Button>
            </div>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2">
            <div
              v-for="storage in storageTypes"
              :key="storage.key"
              class="flex items-center space-x-2 p-3 rounded-md border hover:bg-muted/50 transition-colors"
            >
              <Checkbox :id="`storage-${storage.key}`" v-model="storage.selected" />
              <div class="flex flex-col">
                <Label :for="`storage-${storage.key}`" class="font-medium">
                  {{ storage.label }}
                </Label>
                <span class="text-xs text-muted-foreground">{{ storage.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 缓存子项选择 -->
        <div v-if="hasLocalStorageSelected" class="flex flex-col space-y-2">
          <div class="flex items-center justify-between">
            <Label>{{ t('settings.baseSetting.cache.select') }}</Label>
            <div class="flex items-center space-x-2">
              <Button variant="outline" size="sm" @click="selectAllCacheItems">
                {{ t('settings.baseSetting.cache.selectAll') }}
              </Button>
              <Button variant="outline" size="sm" @click="deselectAllCacheItems">
                {{ t('settings.baseSetting.cache.deselectAll') }}
              </Button>
            </div>
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2">
            <div
              v-for="cache in cacheItems"
              :key="cache.key"
              class="flex items-center space-x-2 p-3 rounded-md border hover:bg-muted/50 transition-colors"
            >
              <Checkbox :id="`cache-${cache.key}`" v-model="cache.selected" />
              <div class="flex flex-col">
                <Label :for="`cache-${cache.key}`" class="font-medium">{{ cache.label }}</Label>
                <span class="text-xs text-muted-foreground">{{ cache.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between mt-4">
          <Button
            variant="outline"
            :disabled="!hasSelectedItems"
            class="flex items-center"
            @click="openClearSelectedDialog"
          >
            <Icon icon="solar:trash-bin-trash-bold" class="mr-2 h-4 w-4" />
            {{ t('settings.baseSetting.cache.clearSelected') }}
          </Button>
          <Button variant="destructive" class="flex items-center" @click="openClearAllDialog">
            <Icon icon="solar:trash-bin-trash-bold-duotone" class="mr-2 h-4 w-4" />
            {{ t('settings.baseSetting.cache.clearAll') }}
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 清除选中缓存确认对话框 -->
  <Dialog :open="showClearSelectedDialog" @update:open="showClearSelectedDialog = $event">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ t('settings.baseSetting.cache.confirmClearSelected') }}</DialogTitle>
        <DialogDescription>
          {{ t('settings.baseSetting.cache.confirmClearSelectedDesc') }}
        </DialogDescription>
      </DialogHeader>
      <div class="py-4">
        <div class="max-h-[200px] overflow-y-auto space-y-2">
          <!-- 显示选中的存储类型 -->
          <div v-if="selectedStorageTypes.length > 0" class="mb-2">
            <div class="font-medium mb-1">{{ t('settings.baseSetting.cache.types.title') }}:</div>
            <div
              v-for="storage in selectedStorageTypes"
              :key="`selected-${storage.key}`"
              class="flex items-center space-x-2 ml-2"
            >
              <Icon icon="solar:check-circle-bold" class="h-4 w-4 text-primary" />
              <span>{{ storage.label }}</span>
            </div>
          </div>

          <!-- 显示选中的缓存子项 -->
          <div v-if="selectedCacheItems.length > 0" class="mb-2">
            <div class="font-medium mb-1">{{ t('settings.baseSetting.cache.items.title') }}:</div>
            <div
              v-for="cache in selectedCacheItems"
              :key="`selected-${cache.key}`"
              class="flex items-center space-x-2 ml-2"
            >
              <Icon icon="solar:check-circle-bold" class="h-4 w-4 text-primary" />
              <span>{{ cache.label }}</span>
            </div>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="showClearSelectedDialog = false">
          {{ t('settings.baseSetting.cache.cancel') }}
        </Button>
        <Button variant="destructive" @click="clearSelectedCaches">
          {{ t('settings.baseSetting.cache.confirm') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- 清除所有缓存确认对话框 -->
  <Dialog :open="showClearAllDialog" @update:open="showClearAllDialog = $event">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ t('settings.baseSetting.cache.confirmClearAll') }}</DialogTitle>
        <DialogDescription>
          {{ t('settings.baseSetting.cache.confirmClearAllDesc') }}
        </DialogDescription>
      </DialogHeader>
      <div class="py-4">
        <Alert variant="destructive">
          <AlertCircle class="h-4 w-4" />
          <AlertTitle>{{ t('settings.baseSetting.cache.warning') }}</AlertTitle>
          <AlertDescription>
            {{ t('settings.baseSetting.cache.warningDesc') }}
          </AlertDescription>
        </Alert>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="showClearAllDialog = false">
          {{ t('settings.baseSetting.cache.cancel') }}
        </Button>
        <Button variant="destructive" @click="clearAllCaches">
          {{ t('settings.baseSetting.cache.confirm') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { toast } from 'vue-sonner'
import { useLanguage } from '@renderer/config/hooks'
import { AlertCircle } from 'lucide-vue-next'
import { Icon } from '@iconify/vue'

// 使用语言钩子
const { t } = useLanguage()

// 定义存储类型接口
interface StorageType {
  key: string
  label: string
  description: string
  selected: boolean
  clearFunction: () => Promise<boolean>
}

// 定义缓存项接口
interface CacheItem {
  key: string
  label: string
  description: string
  selected: boolean
  storageKey: string
}

// 存储类型列表
const storageTypes = ref<StorageType[]>([
  {
    key: 'localStorage',
    label: t('settings.baseSetting.cache.types.localStorage'),
    description: t('settings.baseSetting.cache.typesDesc.localStorage'),
    selected: false,
    clearFunction: async () => {
      try {
        localStorage.clear()
        return true
      } catch (error) {
        console.error('清除 localStorage 失败:', error)
        return false
      }
    },
  },
  {
    key: 'sessionStorage',
    label: t('settings.baseSetting.cache.types.sessionStorage'),
    description: t('settings.baseSetting.cache.typesDesc.sessionStorage'),
    selected: false,
    clearFunction: async () => {
      try {
        sessionStorage.clear()
        return true
      } catch (error) {
        console.error('清除 sessionStorage 失败:', error)
        return false
      }
    },
  },
  {
    key: 'cookies',
    label: t('settings.baseSetting.cache.types.cookies'),
    description: t('settings.baseSetting.cache.typesDesc.cookies'),
    selected: false,
    clearFunction: async () => {
      try {
        const cookies = document.cookie.split(';')
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i]
          const eqPos = cookie.indexOf('=')
          const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim()
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
        }
        return true
      } catch (error) {
        console.error('清除 cookies 失败:', error)
        return false
      }
    },
  },
  {
    key: 'indexedDB',
    label: t('settings.baseSetting.cache.types.indexedDB'),
    description: t('settings.baseSetting.cache.typesDesc.indexedDB'),
    selected: false,
    clearFunction: async () => {
      try {
        const databases = await window.indexedDB.databases()
        for (const db of databases) {
          if (db.name) {
            window.indexedDB.deleteDatabase(db.name)
          }
        }
        return true
      } catch (error) {
        console.error('清除 indexedDB 失败:', error)
        return false
      }
    },
  },
  {
    key: 'applicationCache',
    label: t('settings.baseSetting.cache.types.applicationCache'),
    description: t('settings.baseSetting.cache.typesDesc.applicationCache'),
    selected: false,
    clearFunction: async () => {
      try {
        if ('caches' in window) {
          const cacheNames = await window.caches.keys()
          await Promise.all(cacheNames.map((cacheName) => window.caches.delete(cacheName)))
        }
        return true
      } catch (error) {
        console.error('清除应用缓存失败:', error)
        return false
      }
    },
  },
  {
    key: 'serviceWorkers',
    label: t('settings.baseSetting.cache.types.serviceWorkers'),
    description: t('settings.baseSetting.cache.typesDesc.serviceWorkers'),
    selected: false,
    clearFunction: async () => {
      try {
        if ('serviceWorker' in navigator) {
          const registrations = await navigator.serviceWorker.getRegistrations()
          for (const registration of registrations) {
            await registration.unregister()
          }
        }
        return true
      } catch (error) {
        console.error('清除 Service Workers 失败:', error)
        return false
      }
    },
  },
])

// 缓存子项列表
const cacheItems = ref<CacheItem[]>([
  {
    key: 'theme',
    label: t('settings.baseSetting.cache.items.theme'),
    description: t('settings.baseSetting.cache.items.themeDesc'),
    selected: false,
    storageKey: 'theme',
  },
  {
    key: 'language',
    label: t('settings.baseSetting.cache.items.language'),
    description: t('settings.baseSetting.cache.items.languageDesc'),
    selected: false,
    storageKey: 'language',
  },
  {
    key: 'font',
    label: t('settings.baseSetting.cache.items.font'),
    description: t('settings.baseSetting.cache.items.fontDesc'),
    selected: false,
    storageKey: 'font',
  },
  {
    key: 'fontSize',
    label: t('settings.baseSetting.cache.items.fontSize'),
    description: t('settings.baseSetting.cache.items.fontSizeDesc'),
    selected: false,
    storageKey: 'fontSize',
  },
  {
    key: 'workflow',
    label: t('settings.baseSetting.cache.items.workflow'),
    description: t('settings.baseSetting.cache.items.workflowDesc'),
    selected: false,
    storageKey: 'workflow',
  },
  {
    key: 'flows',
    label: t('settings.baseSetting.cache.items.flows'),
    description: t('settings.baseSetting.cache.items.flowsDesc'),
    selected: false,
    storageKey: 'flows',
  },
  {
    key: 'navbar',
    label: t('settings.baseSetting.cache.items.navbar'),
    description: t('settings.baseSetting.cache.items.navbarDesc'),
    selected: false,
    storageKey: 'navbar',
  },
  {
    key: 'tools',
    label: t('settings.baseSetting.cache.items.tools'),
    description: t('settings.baseSetting.cache.items.toolsDesc'),
    selected: false,
    storageKey: 'nodeModules',
  },
])

// 对话框状态
const showClearSelectedDialog = ref(false)
const showClearAllDialog = ref(false)

// 计算属性：是否有选中的本地存储
const hasLocalStorageSelected = computed(() => {
  return storageTypes.value.find((storage) => storage.key === 'localStorage')?.selected || false
})

// 计算属性：是否有选中的项目（存储类型或缓存子项）
const hasSelectedItems = computed(() => {
  const hasSelectedStorage = storageTypes.value.some((storage) => storage.selected)
  const hasSelectedCache =
    hasLocalStorageSelected.value && cacheItems.value.some((cache) => cache.selected)
  return hasSelectedStorage || hasSelectedCache
})

// 计算属性：选中的存储类型
const selectedStorageTypes = computed(() => {
  return storageTypes.value.filter((storage) => storage.selected)
})

// 计算属性：选中的缓存子项
const selectedCacheItems = computed(() => {
  return hasLocalStorageSelected.value ? cacheItems.value.filter((cache) => cache.selected) : []
})

// 全选存储类型
const selectAllStorageTypes = () => {
  storageTypes.value.forEach((storage) => {
    storage.selected = true
  })
}

// 取消全选存储类型
const deselectAllStorageTypes = () => {
  storageTypes.value.forEach((storage) => {
    storage.selected = false
  })
}

// 全选缓存子项
const selectAllCacheItems = () => {
  cacheItems.value.forEach((cache) => {
    cache.selected = true
  })
}

// 取消全选缓存子项
const deselectAllCacheItems = () => {
  cacheItems.value.forEach((cache) => {
    cache.selected = false
  })
}

// 打开清除选中缓存对话框
const openClearSelectedDialog = () => {
  if (hasSelectedItems.value) {
    showClearSelectedDialog.value = true
  }
}

// 打开清除所有缓存对话框
const openClearAllDialog = () => {
  showClearAllDialog.value = true
}

// 清除选中的缓存
const clearSelectedCaches = async () => {
  showClearSelectedDialog.value = false

  const results = {
    success: 0,
    fail: 0,
  }

  // 创建一个额外需要清除的项目集合
  const additionalItemsToRemove = new Set<string>()

  // 检查关联项
  for (const item of selectedCacheItems.value) {
    // 如果选择了工作流，同时添加flows到清除列表
    if (item.key === 'workflow') {
      additionalItemsToRemove.add('flows')
    }
    // 如果选择了nodeModules，同时添加tools到清除列表
    if (item.storageKey === 'nodeModules') {
      additionalItemsToRemove.add('tools')
    }
  }

  // 清除选中的存储类型
  for (const storage of selectedStorageTypes.value) {
    try {
      // 如果是localStorage且有选中的缓存子项，则只清除选中的子项
      if (storage.key === 'localStorage' && selectedCacheItems.value.length > 0) {
        // 处理选中的缓存项
        for (const item of selectedCacheItems.value) {
          try {
            localStorage.removeItem(item.storageKey)
            results.success++
          } catch (error) {
            console.error(`清除缓存项 ${item.key} 失败:`, error)
            results.fail++
          }
        }

        // 处理关联的额外项
        for (const additionalKey of additionalItemsToRemove) {
          // 查找对应的缓存项
          const additionalItem = cacheItems.value.find((item) => item.key === additionalKey)
          if (
            additionalItem &&
            !selectedCacheItems.value.some((item) => item.key === additionalKey)
          ) {
            try {
              localStorage.removeItem(additionalItem.storageKey)
              results.success++
              console.log(`已自动清除关联项: ${additionalItem.label}`)
            } catch (error) {
              console.error(`清除关联缓存项 ${additionalItem.key} 失败:`, error)
              results.fail++
            }
          }
        }

        // 特殊处理：如果清除nodeModules，同时清除key为tools的项
        if (selectedCacheItems.value.some((item) => item.storageKey === 'nodeModules')) {
          try {
            localStorage.removeItem('tools')
            results.success++
            console.log('已自动清除关联项: tools')
          } catch (error) {
            console.error('清除关联缓存项 tools 失败:', error)
            results.fail++
          }
        }
      } else {
        // 其他存储类型或没有选中缓存子项时，清除整个存储
        const success = await storage.clearFunction()
        if (success) {
          results.success++
        } else {
          results.fail++
        }
      }
    } catch (error) {
      console.error(`清除 ${storage.key} 失败:`, error)
      results.fail++
    }
  }

  // 显示结果通知
  if (results.fail === 0) {
    toast.success(t('settings.baseSetting.cache.clearSuccess'), {
      description: t('settings.baseSetting.cache.clearSuccessDesc'),
    })
  } else {
    toast.warning(t('settings.baseSetting.cache.clearPartial'), {
      description: `${results.success} ${t('settings.baseSetting.cache.clearSuccessCount')}, ${
        results.fail
      } ${t('settings.baseSetting.cache.clearFailCount')}`,
    })
  }

  // 延迟刷新页面
  setTimeout(() => {
    window.location.reload()
  }, 1500)
}

// 清除所有缓存
const clearAllCaches = async () => {
  let successCount = 0
  let failCount = 0

  // 清除所有存储类型
  for (const storage of storageTypes.value) {
    const success = await storage.clearFunction()
    if (success) {
      successCount++
    } else {
      failCount++
    }
  }

  showClearAllDialog.value = false

  if (failCount === 0) {
    toast.success(t('settings.baseSetting.cache.clearAllSuccess'), {
      description: t('settings.baseSetting.cache.clearAllSuccessDesc'),
    })
    // 清除成功后延迟1.5秒重新加载窗口，让用户看到成功提示
    setTimeout(() => {
      window.location.reload()
    }, 1500)
  } else {
    toast.warning(t('settings.baseSetting.cache.clearPartial'), {
      description: `${successCount} ${t('settings.baseSetting.cache.clearSuccessCount')}, ${failCount} ${t('settings.baseSetting.cache.clearFailCount')}`,
    })
  }
}
</script>
