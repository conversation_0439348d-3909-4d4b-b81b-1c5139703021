/**
 * 应用配置 Composable
 * 提供响应式的应用配置管理
 */

import { computed, ref } from 'vue'
import {
  APP_TYPES,
  getAppConfig,
  getAppIconUrl,
  getAppLogoUrl,
  getCurrentAppConfig,
  isFeatureEnabled,
  type AppConfig,
  type AppType,
} from '../app'

// 全局应用配置状态
const currentConfig = ref<AppConfig>(getCurrentAppConfig())

export function useAppConfig() {
  // 当前应用配置
  const appConfig = computed(() => currentConfig.value)

  // 应用类型
  const appType = computed(() => appConfig.value.type)

  // 应用元信息
  const appMeta = computed(() => appConfig.value.meta)

  // 功能配置
  const features = computed(() => appConfig.value.features)

  // 是否为 MattVerse
  const isMattVerse = computed(() => appType.value === APP_TYPES.MATTVERSE)

  // 是否为 Highpower
  const isHighpower = computed(() => appType.value === APP_TYPES.HIGHPOWER)

  // 布局配置
  const layoutConfig = computed(() => features.value.layout)

  // 侧边栏配置
  const sidebarConfig = computed(() => features.value.sidebar)

  // 工作流编辑器配置
  const workflowEditorConfig = computed(() => features.value.workflowEditor)

  // 导航配置
  const navigationConfig = computed(() => features.value.navigation)

  // 节点工具栏配置
  const nodeToolbarConfig = computed(() => features.value.nodeToolbar)

  // 认证配置
  const authConfig = computed(() => features.value.auth)

  // 主题配置
  const themeConfig = computed(() => features.value.theme)

  // 工具配置
  const toolsConfig = computed(() => features.value.tools)

  // 其他功能配置
  const featuresConfig = computed(() => features.value.features)

  // 检查功能是否启用
  const checkFeature = (featurePath: string): boolean => {
    return isFeatureEnabled(featurePath, appConfig.value)
  }

  // 切换应用配置（用于开发和测试）
  const switchAppConfig = (type: AppType) => {
    currentConfig.value = getAppConfig(type)
  }

  // 更新配置（用于动态配置）
  const updateConfig = (newConfig: Partial<AppConfig>) => {
    currentConfig.value = {
      ...currentConfig.value,
      ...newConfig,
    }
  }

  // 更新特定配置项
  const updateConfigValue = (path: string, value: any) => {
    const pathArray = path.split('.')
    const newConfig = JSON.parse(JSON.stringify(currentConfig.value))

    // 深度设置值
    let current = newConfig
    for (let i = 0; i < pathArray.length - 1; i++) {
      if (!current[pathArray[i]]) {
        current[pathArray[i]] = {}
      }
      current = current[pathArray[i]]
    }
    current[pathArray[pathArray.length - 1]] = value

    currentConfig.value = newConfig
  }

  // 重置为默认配置
  const resetConfig = () => {
    currentConfig.value = getCurrentAppConfig()
  }

  // 常用的功能检查方法
  const canShowSidebar = computed(() => checkFeature('layout.showSidebar'))
  const canShowNavbar = computed(() => checkFeature('workflowEditor.showNavbar'))
  const canShowAIFloatingBox = computed(() => checkFeature('workflowEditor.showAIFloatingBox'))
  const canShowToolbar = computed(() => checkFeature('workflowEditor.showToolbar'))
  const canShowMiniMap = computed(() => checkFeature('workflowEditor.showMiniMap'))
  const canShowMainNavigation = computed(() => checkFeature('navigation.showMainNavigation'))
  const canShowNodeToolbar = computed(() => checkFeature('nodeToolbar.showNodeToolbar'))
  const canEnableAuth = computed(() => checkFeature('auth.enableAuth'))
  const canSwitchTheme = computed(() => checkFeature('theme.enableThemeSwitch'))
  const canShowToolsPanel = computed(() => checkFeature('tools.showToolsPanel'))
  const canEnableI18n = computed(() => checkFeature('features.enableI18n'))
  const canEnableNotifications = computed(() => checkFeature('features.enableNotifications'))
  const canEnableAutoSave = computed(() => checkFeature('features.enableAutoSave'))
  const canEnableExport = computed(() => checkFeature('features.enableExport'))
  const canEnableImport = computed(() => checkFeature('features.enableImport'))

  // 获取允许的节点分类
  const getAllowedNodeCategories = computed(() => {
    return nodeToolbarConfig.value.enableNodeCategories
  })

  // 检查节点分类是否允许
  const isNodeCategoryAllowed = (category: string): boolean => {
    return getAllowedNodeCategories.value.includes(category)
  }

  // 获取允许的主题
  const getAllowedThemes = computed(() => {
    return themeConfig.value.allowedThemes
  })

  // 检查主题是否允许
  const isThemeAllowed = (theme: string): boolean => {
    return getAllowedThemes.value.includes(theme)
  }

  // 获取启用的工具
  const getEnabledTools = computed(() => {
    return toolsConfig.value.enabledTools
  })

  // 检查工具是否启用
  const isToolEnabled = (tool: string): boolean => {
    return getEnabledTools.value.includes(tool)
  }

  // 获取应用图标URL
  const getIconUrl = computed(() => {
    return getAppIconUrl(appConfig.value)
  })

  // 获取应用Logo URL
  const getLogoUrl = computed(() => {
    return getAppLogoUrl(appConfig.value)
  })

  return {
    // 基础配置
    appConfig,
    appType,
    appMeta,
    features,

    // 应用类型判断
    isMattVerse,
    isHighpower,

    // 分类配置
    layoutConfig,
    sidebarConfig,
    workflowEditorConfig,
    navigationConfig,
    nodeToolbarConfig,
    authConfig,
    themeConfig,
    toolsConfig,
    featuresConfig,

    // 方法
    checkFeature,
    switchAppConfig,
    updateConfig,
    updateConfigValue,
    resetConfig,

    // 常用功能检查
    canShowSidebar,
    canShowNavbar,
    canShowAIFloatingBox,
    canShowToolbar,
    canShowMiniMap,
    canShowMainNavigation,
    canShowNodeToolbar,
    canEnableAuth,
    canSwitchTheme,
    canShowToolsPanel,
    canEnableI18n,
    canEnableNotifications,
    canEnableAutoSave,
    canEnableExport,
    canEnableImport,

    // 特定检查方法
    getAllowedNodeCategories,
    isNodeCategoryAllowed,
    getAllowedThemes,
    isThemeAllowed,
    getEnabledTools,
    isToolEnabled,

    // 图标和Logo相关
    getIconUrl,
    getLogoUrl,
  }
}
