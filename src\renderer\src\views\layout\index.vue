<template>
  <section class="flex w-full h-full overflow-hidden">
    <!--sidebar-->
    <Sidebar v-if="canShowSidebar" />
    <!--main-->
    <div class="flex-1 bg-background">
      <RouterView />
    </div>
  </section>
</template>

<script setup lang="ts">
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { onMounted } from 'vue'
import { Sidebar } from './components'

const { canShowSidebar, appMeta } = useAppConfig()

onMounted(() => {
  console.log(`${appMeta.value.name} Layout component mounted`)
})
</script>

<style scoped lang="scss"></style>
