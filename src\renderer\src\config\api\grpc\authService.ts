import { grpcConnectionService } from '@renderer/services/grpc'
import { UserRole, UserStatus } from '@renderer/store/modules/auth'
import { BaseResponse, BaseService } from './baseService'

// 用户信息接口
export interface UserInfo {
  userId: string
  userName: string
  accessLevel: number
  userStatus: UserStatus
  userRole: UserRole
}

// 用户接口
export interface User {
  userId: string
  token: string
  expiredTime: number
}

// 服务器接口
export interface Server {
  serverId: string
  serverName: string
  url: string
  serverStatus: number
  region: string
  version: string
  accessLevel: number
  createTime: number
  updateTime: number
}

// 服务接口
export interface Service {
  serviceId: string
  serviceName: string
  serverId: string
  version: string
  protocolType: string
  accessLevel: number
}

// 任务接口
export interface Task {
  taskId: string
  userId: string
  serviceId: string
  startTime: number
  endTime: number
  taskLog: string
  taskStatus: number
  taskProcess: number
  taskPid: number
  result: string
  filePath: string
  createTime: number
  updateTime: number
}

// 会话接口
export interface Session {
  sessionId: string
  userId: string
  sessionContents: string
}

// 登录请求参数接口
export interface LoginParams {
  userName: string
  password: string
  lastIp: string
  userId?: string
  expiredTime?: number
}

// 登录响应接口
export interface LoginResponse extends BaseResponse {
  serverResult?: {
    serverId: string
    serverList: Server[]
  }
  serviceResult?: {
    serviceId: string
    serviceList: Service[]
  }
  userInfoResult?: {
    userInfoId: string
    userInfoList: UserInfo[]
  }
  userResult?: {
    token: string
    userList: User[]
  }
  taskResult?: {
    taskId: string
    taskList: Task[]
  }
  sessionResult?: {
    sessionId: string
    sessionList: Session[]
  }
}

// 注册响应接口
export interface RegisterResponse extends BaseResponse {
  userInfoId?: string
}

export class AuthService extends BaseService {
  /**
   * 用户登录
   * @param params 登录参数
   */
  async login(params: LoginParams): Promise<LoginResponse> {
    const loginParams = {
      user_name: params.userName,
      password: params.password,
      last_ip: params.lastIp,
      user_id: params.userId || '0',
      expired_time: params.expiredTime || -7338421516918927,
    }

    return this.call<LoginResponse>('getUserToken', loginParams)
  }

  /**
   * 用户注册
   * @param username 用户名
   * @param password 密码哈希
   * @param role 用户角色
   */
  async register(username: string, password: string, role: UserRole): Promise<RegisterResponse> {
    return this.call<RegisterResponse>('userRegister', {
      user_name: username,
      password,
      user_role: role,
    })
  }

  /**
   * 将认证信息同步到主进程
   * @param userId 用户ID
   * @param token 认证令牌
   */
  async updateAuthInfo(
    userId: string,
    token: string,
  ): Promise<{ success: boolean; message?: string }> {
    if (window.grpcApi?.updateAuthInfo) {
      return window.grpcApi.updateAuthInfo(userId, token)
    }
    return { success: false, message: '更新认证信息方法不存在' }
  }

  /**
   * 建立gRPC长连接
   * @param username 用户名
   * @param lastIp IP地址
   * @param password 密码哈希
   * @param expiredTime 过期时间
   */
  async connect(
    username: string,
    lastIp: string,
    password: string,
    expiredTime: number = 0,
  ): Promise<void> {
    return grpcConnectionService.connect(username, lastIp, password, expiredTime)
  }

  /**
   * 断开gRPC连接
   */
  async disconnect(): Promise<void> {
    return grpcConnectionService.disconnect()
  }
}

/**
 * 创建 AuthService 实例
 */
export function createAuthService(): AuthService {
  return new AuthService()
}
