<template>
  <div class="setting-right h-full">
    <component :is="currentComponent" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { BaseSetting, FlowSetting, AboutSetting, MiddlePlatformSetting } from './index'

const props = defineProps({
  activeComponent: {
    type: String,
    default: 'BaseSetting',
  },
})
const currentComponent = computed(() => {
  const components = {
    BaseSetting,
    FlowSetting,
    AboutSetting,
    MiddlePlatformSetting,
  }
  return components[props.activeComponent]
})
</script>

<style lang="scss" scoped></style>
