const animate = require('tailwindcss-animate')

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class', 'class'],
  safelist: ['dark'],
  prefix: '',

  content: [
    './pages/**/*.{ts,tsx,vue}',
    './components/**/*.{ts,tsx,vue}',
    './app/**/*.{ts,tsx,vue}',
    './src/**/*.{ts,tsx,vue}',
  ],

  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontFamily: {
        system: ['system-ui', 'sans-serif'],
        alibaba: ['Alibaba PuHuiTi', 'sans-serif'],
        'source-han-sans': ['Source Han Sans CN', 'sans-serif'],
        'source-han-serif': ['Source Han Serif CN', 'serif'],
        'noto-sans': ['Noto Sans SC', 'sans-serif'],
        'noto-serif': ['Noto Serif SC', 'serif'],
        arial: ['Arial', 'sans-serif'],
      },
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        themes: {
          zinc: {
            primary: 'hsl(240 5.9% 10%)',
            secondary: 'hsl(240 4.8% 95.9%)',
          },
          slate: {
            primary: 'hsl(222.2 47.4% 11.2%)',
            secondary: 'hsl(215 20.2% 65.1%)',
          },
          blue: {
            primary: 'hsl(221.2 83.2% 53.3%)',
            secondary: 'hsl(210 40% 96.1%)',
          },
          green: {
            primary: 'hsl(142.1 76.2% 36.3%)',
            secondary: 'hsl(142.1 76.2% 96.1%)',
          },
          violet: {
            primary: 'hsl(262.1 83.3% 57.8%)',
            secondary: 'hsl(262.1 83.3% 96.1%)',
          },
          rose: {
            primary: 'hsl(346.8 77.2% 49.8%)',
            secondary: 'hsl(346.8 77.2% 96.1%)',
          },
        },
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
      },
      borderRadius: {
        xl: 'calc(var(--radius) + 4px)',
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--reka-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--reka-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        'collapsible-down': {
          from: {
            height: 0,
          },
          to: {
            height: 'var(--radix-collapsible-content-height)',
          },
        },
        'collapsible-up': {
          from: {
            height: 'var(--radix-collapsible-content-height)',
          },
          to: {
            height: 0,
          },
        },
        ellipsis: {
          '0%': { width: '0' },
          '50%': { width: '0.75rem' },
          '100%': { width: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'collapsible-down': 'collapsible-down 0.2s ease-in-out',
        'collapsible-up': 'collapsible-up 0.2s ease-in-out',
        ellipsis: 'ellipsis 1.2s infinite',
      },
    },
  },
  plugins: [animate, require('tailwindcss-animate')],
}
