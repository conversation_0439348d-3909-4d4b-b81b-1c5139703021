import { ipc<PERSON>ender<PERSON> } from 'electron'

/**
 * 文件操作相关API
 */
export const fileApi = {
  /**
   * 保存文件
   */
  saveFile: (options: any) => ipcRenderer.invoke('save-file', options),

  /**
   * 打开文件选择对话框
   */
  openFile: (options: any) => ipcRenderer.invoke('show-open-dialog', options),

  /**
   * 读取文件
   */
  readFile: (filePath: string | any) => ipcRenderer.invoke('read-file', filePath),

  /**
   * 加密并保存文件
   */
  encryptAndSaveFile: (options: any) => ipcRenderer.invoke('encrypt-and-save-file', options),
  /**
   * 打开保存文件对话框
   */
  openSaveFileDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
}
