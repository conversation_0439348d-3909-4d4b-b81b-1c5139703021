/**
 * Flows 相关的数据库操作
 * 使用 Dexie 管理工作流节点参数的大数据存储
 */

import Dexie, { Table } from 'dexie'

// 节点参数数据接口
interface NodeParamsRecord {
  workflowId: string
  nodeId: string
  params: any
  timestamp: number
  version?: number
}

// 工作流元数据接口（可扩展）
interface WorkflowMetadata {
  id?: number
  workflowId: string
  name?: string
  description?: string
  createdAt: number
  updatedAt: number
}

/**
 * Flows 数据库类
 */
class FlowsDatabase extends Dexie {
  // 节点参数表
  nodeParams!: Table<NodeParamsRecord>

  // 工作流元数据表
  workflowMetadata!: Table<WorkflowMetadata>

  constructor() {
    super('MattverseFlowsDB')

    // 定义数据库结构，使用复合主键避免重复数据
    this.version(1).stores({
      // 节点参数表：使用复合主键 [workflowId+nodeId] 确保唯一性
      nodeParams: '[workflowId+nodeId], workflowId, nodeId, timestamp',
      // 工作流元数据表
      workflowMetadata: '++id, workflowId, createdAt, updatedAt',
    })
  }

  /**
   * 保存节点参数
   */
  async saveNodeParams(workflowId: string, nodeId: string, params: any): Promise<boolean> {
    try {
      // 使用复合主键，如果记录存在则更新，不存在则创建
      await this.nodeParams.put({
        workflowId,
        nodeId,
        params,
        timestamp: Date.now(),
        version: 1,
      })

      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 获取节点参数
   */
  async getNodeParams(workflowId: string, nodeId: string): Promise<any> {
    try {
      const record = await this.nodeParams
        .where('[workflowId+nodeId]')
        .equals([workflowId, nodeId])
        .first()

      if (record) {
        return record.params || {}
      } else {
        return {}
      }
    } catch (error) {
      return {}
    }
  }

  /**
   * 删除节点参数
   */
  async deleteNodeParams(workflowId: string, nodeId: string): Promise<boolean> {
    try {
      // 使用复合主键直接删除
      await this.nodeParams.delete([workflowId, nodeId])
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 删除工作流的所有节点参数
   */
  async deleteWorkflowParams(workflowId: string): Promise<number> {
    try {
      const deleted = await this.nodeParams.where('workflowId').equals(workflowId).delete()
      return deleted
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取工作流的所有节点参数
   */
  async getWorkflowAllParams(workflowId: string): Promise<Record<string, any>> {
    try {
      const records = await this.nodeParams.where('workflowId').equals(workflowId).toArray()

      const result: Record<string, any> = {}
      records.forEach((record) => {
        result[record.nodeId] = record.params
      })

      return result
    } catch (error) {
      return {}
    }
  }

  /**
   * 检查节点是否有参数
   */
  async hasNodeParams(workflowId: string, nodeId: string): Promise<boolean> {
    try {
      const count = await this.nodeParams
        .where('[workflowId+nodeId]')
        .equals([workflowId, nodeId])
        .count()

      return count > 0
    } catch (error) {
      console.error('❌ 检查节点参数失败:', error)
      return false
    }
  }

  /**
   * 获取节点参数的最后更新时间
   */
  async getNodeParamsTimestamp(workflowId: string, nodeId: string): Promise<number | null> {
    try {
      const record = await this.nodeParams
        .where('[workflowId+nodeId]')
        .equals([workflowId, nodeId])
        .first()

      return record?.timestamp || null
    } catch (error) {
      return null
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupOldParams(daysOld: number = 30): Promise<number> {
    try {
      const cutoffTime = Date.now() - daysOld * 24 * 60 * 60 * 1000

      const deleted = await this.nodeParams.where('timestamp').below(cutoffTime).delete()

      return deleted
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    totalParams: number
    totalWorkflows: number
    oldestTimestamp: number | null
    newestTimestamp: number | null
  }> {
    try {
      const totalParams = await this.nodeParams.count()
      const totalWorkflows = await this.nodeParams
        .orderBy('workflowId')
        .uniqueKeys()
        .then((keys) => keys.length)

      const oldestRecord = await this.nodeParams.orderBy('timestamp').first()
      const newestRecord = await this.nodeParams.orderBy('timestamp').reverse().first()

      return {
        totalParams,
        totalWorkflows,
        oldestTimestamp: oldestRecord?.timestamp || null,
        newestTimestamp: newestRecord?.timestamp || null,
      }
    } catch (error) {
      return {
        totalParams: 0,
        totalWorkflows: 0,
        oldestTimestamp: null,
        newestTimestamp: null,
      }
    }
  }
}

// 创建数据库实例
export const flowsDB = new FlowsDatabase()

// 数据库初始化函数
export const initFlowsDatabase = async (): Promise<void> => {
  try {
    // 打开数据库连接
    await flowsDB.open()
    console.log('✅ Flows 数据库初始化成功')

    // 可选：打印统计信息
    const stats = await flowsDB.getStats()
    console.log('📊 数据库统计:', stats)
  } catch (error) {
    console.error('❌ Flows 数据库初始化失败:', error)
    throw error
  }
}

// 导出类型（供其他文件使用）
export type { NodeParamsRecord, WorkflowMetadata }
