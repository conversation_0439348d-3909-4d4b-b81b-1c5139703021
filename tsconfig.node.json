{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*"],
  "compilerOptions": {
    "types": ["electron-vite/node", "unocss/vite","@iconify/vue","vite/client"],
    "baseUrl": ".",
    "paths": {
      "@renderer/*": ["src/renderer/src/*"],
      "@store/*": ["src/renderer/src/store/*"],
      "@utils/*": ["src/renderer/src/utils/*"],
      "@components/*": ["src/renderer/src/components/*"],
      "@router/*": ["src/renderer/src/router/*"],
      "@views/*": ["src/renderer/src/views/*"],
      "@assets/*": ["src/renderer/src/assets/*"],
      "@styles/*": ["src/renderer/src/config/styles/*"],
      "@api/*": ["src/renderer/src/config/api/*"],
      "@hooks/*": ["src/renderer/src/config/hooks/*"],
      "@lib/*": ["src/renderer/src/lib/*"],
    }
  }
}
