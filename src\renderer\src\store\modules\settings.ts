import { svg } from '@renderer/config/constants'
import { MarkerType } from '@vue-flow/core'
import { useColorMode, usePreferredDark } from '@vueuse/core'
import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'

// 主题色调配置
export interface ThemeColor {
  value: string
  label: string
  previewColor: string
  primary: string
  type: 'light' | 'dark'
}

// 字体配置
export interface FontConfig {
  value: string
  label: string
}

// 字号配置
export interface FontSizeConfig {
  value: string
  label: string
  sizeValue: string
}

// 语言配置
export interface LanguageConfig {
  value: string
  label: string
  flag: string
}

// 流程图配置接口
export interface FlowConfig {
  edgeConfig: {
    type: string
    animated: boolean
    showArrow: boolean
    style: {
      strokeWidth: number
      stroke: string
    }
    markerEnd: {
      type: any
      color: string
      width: number
      height: number
      strokeWidth: number
      markerUnits: string
    }
  }
  snapToLines: boolean
  snapToGrid: boolean
  snapGrid: {
    x: number
    y: number
  }
  minZoom: number
  maxZoom: number
  fitViewOnInit: boolean
  defaultViewport: {
    x: number
    y: number
    zoom: number
  }
  showMiniMap: boolean
  showLeftControls: boolean
  showRightControls: boolean
  miniMapConfig: {
    backgroundColor: string
    nodeStrokeColor: string
    nodeStrokeWidth: number
  }
  backgroundConfig: {
    patternColor: string
    gap: number
    size: number
    variant: string
  }
}

// 设置状态接口
export interface SettingsState {
  theme: string
  themeMode: 'light' | 'dark' | 'auto'
  language: string
  fontSize: string
  font: string
  flowConfig: FlowConfig
}

export const useSettingsStore = defineStore(
  'settings',
  () => {
    // 状态
    const theme = ref<string>('')
    const language = ref<string>('zh-CN')
    const fontSize = ref<string>('medium')
    const font = ref<string>('AlibabaPuHuiTi')

    // 流程图配置
    const flowConfig = ref<FlowConfig>({
      // 连线配置
      edgeConfig: {
        type: 'bezier', // 'default' | 'straight' | 'step' | 'smoothstep' | 'bezier'
        animated: false,
        showArrow: true, //是否显示连线箭头
        style: {
          strokeWidth: 5,
          stroke: '#000000',
        },
        markerEnd: {
          type: MarkerType.ArrowClosed, //'arrow', 'arrowclosed'
          color: '#000000',
          width: 10, // 添加箭头宽度
          height: 10, // 添加箭头高度
          strokeWidth: 0, // 设置边框宽度为0，使其成为实心
          markerUnits: 'userSpaceOnUse',
        },
      },

      // 对齐配置
      snapToLines: true,
      snapToGrid: true,
      snapGrid: {
        x: 16,
        y: 16,
      },
      // zoom 配置
      minZoom: 0.5,
      maxZoom: 2,
      // 画布在初始化时会自动调整缩放和位置以适应所有节点
      fitViewOnInit: false,
      defaultViewport: {
        x: 0,
        y: 0,
        zoom: 0.5,
      },

      // 控制器配置
      showMiniMap: true,
      showLeftControls: true,
      showRightControls: true,

      // 小地图配置
      miniMapConfig: {
        backgroundColor: '#fff',
        nodeStrokeColor: '#aaa',
        nodeStrokeWidth: 2,
      },

      // 背景配置
      backgroundConfig: {
        patternColor: '#aaa',
        gap: 20,
        size: 1,
        variant: 'dots', // 'dots' | 'lines' | 'cross'
      },
    })

    // 主题模式管理
    const themeMode = useColorMode({
      attribute: 'class',
      modes: {
        light: 'light',
        dark: 'dark',
        auto: 'auto',
      },
    })

    // 系统暗色模式偏好
    const prefersDark = usePreferredDark()

    // 主题色调配置
    const allThemeColors: ThemeColor[] = [
      // 浅色主题
      {
        value: 'city-light',
        label: '城市-浅色',
        previewColor: '#d8e8e7',
        primary: '187 25.9% 87.5%',
        type: 'light',
      },
      {
        value: 'forest-light',
        label: '森林-浅色',
        previewColor: '#e0d6af',
        primary: '49 47.4% 78.2%',
        type: 'light',
      },
      {
        value: 'lake-light',
        label: '湖水-浅色',
        previewColor: '#cbcad9',
        primary: '243 17.3% 82.2%',
        type: 'light',
      },
      {
        value: 'desert-light',
        label: '沙漠-浅色',
        previewColor: '#fdcdb0',
        primary: '26 96.2% 84.1%',
        type: 'light',
      },
      {
        value: 'farm-light',
        label: '农场-浅色',
        previewColor: '#c8d3ec',
        primary: '222 47.4% 85.3%',
        type: 'light',
      },
      {
        value: 'garden-light',
        label: '花园-浅色',
        previewColor: '#fee1b2',
        primary: '37 98.2% 85.1%',
        type: 'light',
      },
      // 深色主题
      {
        value: 'city-dark',
        label: '城市-深色',
        previewColor: '#2a3b42',
        primary: '197 22.9% 21.2%',
        type: 'dark',
      },
      {
        value: 'forest-dark',
        label: '森林-深色',
        previewColor: '#2c3320',
        primary: '79 22.4% 16.2%',
        type: 'dark',
      },
      {
        value: 'lake-dark',
        label: '湖水-深色',
        previewColor: '#1e2a4a',
        primary: '220 42.4% 20.2%',
        type: 'dark',
      },
      {
        value: 'desert-dark',
        label: '沙漠-深色',
        previewColor: '#3d2b1f',
        primary: '26 32.2% 18.2%',
        type: 'dark',
      },
      {
        value: 'farm-dark',
        label: '农场-深色',
        previewColor: '#2a2d1e',
        primary: '69 22.4% 15.2%',
        type: 'dark',
      },
      {
        value: 'garden-dark',
        label: '花园-深色',
        previewColor: '#2d1f2a',
        primary: '315 22.4% 15.2%',
        type: 'dark',
      },
    ]

    // 字体配置
    const fontConfigs: FontConfig[] = [
      { value: 'AlibabaPuHuiTi', label: '阿里巴巴普惠体' },
      { value: 'AlibabaSansHK', label: '阿里巴巴香港字体' },
      { value: 'NotoSansSC-Black', label: 'Noto Sans SC 黑体' },
      { value: 'NotoSansSC-ExtraBold', label: 'Noto Sans SC 特粗体' },
      { value: 'NotoSansSC-Light', label: 'Noto Sans SC 轻体' },
      { value: 'NotoSansSC-Medium', label: 'Noto Sans SC 中等体' },
      { value: 'SourceHanSans', label: '思源黑体' },
      { value: 'SourceHanSansHC', label: '思源黑体 HC' },
      { value: 'SourceHanSansHK', label: '思源黑体 HK' },
      { value: 'SourceHanSansK', label: '思源黑体 K' },
      { value: 'SourceHanSansSC', label: '思源黑体 SC' },
      { value: 'SourceHanSansTC', label: '思源黑体 TC' },
    ]

    // 字号配置
    const fontSizeConfigs: FontSizeConfig[] = [
      { value: 'extra-small', label: '小', sizeValue: '12px' },
      { value: 'small', label: '中等', sizeValue: '14px' },
      { value: 'medium', label: '大', sizeValue: '16px' },
      { value: 'large', label: '特大', sizeValue: '18px' },
      { value: 'extra-large', label: '最大', sizeValue: '20px' },
    ]

    // 语言配置
    const languageConfigs: LanguageConfig[] = [
      { value: 'zh-CN', label: '简体中文', flag: svg.zhCn },
      { value: 'en-US', label: 'English', flag: svg.enUs },
    ]

    // 计算属性
    const currentActualMode = computed(() => {
      if (themeMode.value === 'auto') {
        return prefersDark.value ? 'dark' : 'light'
      }
      return themeMode.value
    })

    // 根据当前模式过滤主题色调
    const filteredThemeColors = computed(() => {
      return allThemeColors.filter((themeColor) => themeColor.type === currentActualMode.value)
    })

    // 获取当前字体配置
    const currentFontConfig = computed(() => {
      return fontConfigs.find((f) => f.value === font.value) || fontConfigs[0]
    })

    // 获取当前字号配置
    const currentFontSizeConfig = computed(() => {
      return fontSizeConfigs.find((f) => f.value === fontSize.value) || fontSizeConfigs[2]
    })

    // 获取当前语言配置
    const currentLanguageConfig = computed(() => {
      return languageConfigs.find((l) => l.value === language.value) || languageConfigs[0]
    })

    // Actions
    const setTheme = (newTheme: string) => {
      if (theme.value === newTheme) {
        // 如果点击的是当前已选中的主题，则取消选中
        theme.value = ''
        document.documentElement.removeAttribute('data-theme')
        document.documentElement.style.removeProperty('--primary')
        return
      }

      theme.value = newTheme
      const selectedTheme = allThemeColors.find((t) => t.value === newTheme)
      if (selectedTheme) {
        document.documentElement.setAttribute('data-theme', newTheme)
        document.documentElement.style.setProperty('--primary', selectedTheme.primary)
      }
    }

    const setThemeMode = (mode: 'light' | 'dark' | 'auto') => {
      themeMode.value = mode

      // 如果是自动模式，立即应用系统主题
      if (mode === 'auto') {
        if (prefersDark.value) {
          document.documentElement.classList.add('dark')
          document.documentElement.classList.remove('light')
        } else {
          document.documentElement.classList.add('light')
          document.documentElement.classList.remove('dark')
        }
      }

      // 检查主题兼容性
      checkThemeCompatibility(mode)
    }

    const setLanguage = (newLanguage: string) => {
      language.value = newLanguage
    }

    const setFontSize = (newFontSize: string) => {
      fontSize.value = newFontSize
      const sizeConfig = fontSizeConfigs.find((s) => s.value === newFontSize)
      if (sizeConfig) {
        document.documentElement.style.fontSize = sizeConfig.sizeValue
      }
    }

    const setFont = (newFont: string) => {
      font.value = newFont
      document.documentElement.style.fontFamily = newFont
    }

    // 更新流程图配置
    const updateFlowConfig = (config: Partial<FlowConfig>) => {
      flowConfig.value = {
        ...flowConfig.value,
        ...config,
      }
    }

    // 检查主题与模式的兼容性
    const checkThemeCompatibility = (newMode: 'light' | 'dark' | 'auto') => {
      if (!theme.value) return

      let actualMode = newMode
      if (newMode === 'auto') {
        actualMode = prefersDark.value ? 'dark' : 'light'
      }

      const currentThemeObj = allThemeColors.find((t) => t.value === theme.value)
      if (currentThemeObj && currentThemeObj.type !== actualMode) {
        const currentThemeBase = theme.value.split('-')[0]
        const correspondingTheme = allThemeColors.find(
          (t) => t.value.startsWith(currentThemeBase) && t.type === actualMode,
        )

        if (correspondingTheme) {
          setTheme(correspondingTheme.value)
        } else {
          setTheme('')
        }
      }
    }

    // 初始化设置
    const initializeSettings = () => {
      // 应用字体设置
      if (font.value) {
        document.documentElement.style.fontFamily = font.value
      }

      // 应用字号设置
      const sizeConfig = fontSizeConfigs.find((s) => s.value === fontSize.value)
      if (sizeConfig) {
        document.documentElement.style.fontSize = sizeConfig.sizeValue
      }

      // 应用主题设置
      if (theme.value) {
        const selectedTheme = allThemeColors.find((t) => t.value === theme.value)
        if (selectedTheme) {
          document.documentElement.setAttribute('data-theme', theme.value)
          document.documentElement.style.setProperty('--primary', selectedTheme.primary)
        }
      }

      // 语言设置将在组件层面处理
    }

    // 监听模式变化，处理 auto 模式下的系统主题变化
    watch(
      [themeMode, prefersDark],
      ([newMode, newPrefersDark]) => {
        if (newMode === 'auto') {
          if (newPrefersDark) {
            document.documentElement.classList.add('dark')
            document.documentElement.classList.remove('light')
          } else {
            document.documentElement.classList.add('light')
            document.documentElement.classList.remove('dark')
          }
          checkThemeCompatibility('auto')
        }
      },
      { immediate: true },
    )

    return {
      // 状态
      theme,
      themeMode,
      language,
      fontSize,
      font,
      flowConfig,

      // 配置
      allThemeColors,
      fontConfigs,
      fontSizeConfigs,
      languageConfigs,

      // 计算属性
      currentActualMode,
      filteredThemeColors,
      currentFontConfig,
      currentFontSizeConfig,
      currentLanguageConfig,

      // Actions
      setTheme,
      setThemeMode,
      setLanguage,
      setFontSize,
      setFont,
      updateFlowConfig,
      initializeSettings,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: ['theme', 'themeMode', 'language', 'fontSize', 'font', 'flowConfig'],
    },
  },
)
