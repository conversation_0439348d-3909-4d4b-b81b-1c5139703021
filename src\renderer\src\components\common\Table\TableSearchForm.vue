<template>
  <div class="w-full max-w-2xl  space-y-6">
    <form @submit.prevent="onSubmit" class="space-y-4">
      <div class="grid gap-4" :class="gridClass">
        <div v-for="(field, index) in fields" :key="index" class="space-y-2">
          <Label :for="field.name">{{ field.label }}</Label>

          <!-- 输入框 -->
          <Input
            v-if="!field.type || field.type === 'text'"
            v-model="formData[field.name]"
            :id="field.name"
            :placeholder="field.placeholder || `请输入${field.label}`"
            class="w-full"
          />

          <!-- 下拉选择 -->
          <Select
            v-if="field.type === 'select'"
            v-model="formData[field.name]"
          >
            <SelectTrigger :id="field.name">
              <SelectValue :placeholder="field.placeholder || `请选择${field.label}`" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in field.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div class="flex gap-4 justify-end">
        <Button type="button" variant="outline" @click="onReset">
          重置
        </Button>
        <Button type="submit">
          搜索
        </Button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'


// 定义字段配置的类型
interface FieldConfig {
  name: string
  label: string
  type?: 'text' | 'select' // 默认text
  placeholder?: string
  options?: { value: string; label: string }[] // 当type为select时使用
}

// 定义props
const props = defineProps<{
  fields: FieldConfig[]
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'search', data: Record<string, any>): void
  (e: 'reset'): void
}>()

// 表单数据
const formData = ref<Record<string, any>>(
  Object.fromEntries(props.fields.map(field => [field.name, '']))
)

// 根据列数计算grid样式
const gridClass = computed(() => {
  const count = props.fields.length
  if (count === 1) return 'grid-cols-1'
  if (count === 2) return 'grid-cols-2'
  return 'grid-cols-3'
})

// 提交搜索
const onSubmit = () => {
  emit('search', { ...formData.value })
}

// 重置表单
const onReset = () => {
  for (const key in formData.value) {
    formData.value[key] = ''
  }
  emit('reset')
}
</script>
