import { BrowserWindow, Session } from 'electron'
import logger from '../utils/logger'
import { getMainWindow } from '../window/mainWindow'

/**
 * 缓存服务类
 * 封装与缓存管理相关的所有功能
 */
export class CacheService {
  /**
   * 获取当前会话
   * @returns Session | null
   */
  private getSession(): Session | null {
    const mainWindow = getMainWindow()
    return mainWindow ? mainWindow.webContents.session : null
  }

  /**
   * 清除应用缓存
   * @returns Promise<{success: boolean, message: string}>
   */
  async clearAppCache(): Promise<{ success: boolean; message: string }> {
    try {
      const session = this.getSession()
      if (!session) {
        return { success: false, message: '无法获取会话实例' }
      }

      await session.clearCache()
      await session.clearStorageData({
        storages: [
          'cookies',
          'filesystem',
          'indexdb',
          'localstorage',
          'shadercache',
          'websql',
          'serviceworkers',
          'cachestorage',
        ],
      })

      return { success: true, message: '应用缓存已清除' }
    } catch (error) {
      logger.error('清除应用缓存失败:', error)
      return { success: false, message: `清除失败: ${(error as Error).message}` }
    }
  }

  /**
   * 清除特定类型的存储数据
   * @param options 清除选项
   * @returns Promise<{success: boolean, message: string}>
   */
  async clearStorageData(
    options: Electron.ClearStorageDataOptions,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const session = this.getSession()
      if (!session) {
        return { success: false, message: '无法获取会话实例' }
      }

      await session.clearStorageData(options)
      return { success: true, message: '指定存储数据已清除' }
    } catch (error) {
      logger.error('清除存储数据失败:', error)
      return { success: false, message: `清除失败: ${(error as Error).message}` }
    }
  }

  /**
   * 清除Cookie
   * @param url 可选的URL过滤器
   * @returns Promise<{success: boolean, message: string}>
   */
  async clearCookies(url?: string): Promise<{ success: boolean; message: string }> {
    try {
      const session = this.getSession()
      if (!session) {
        return { success: false, message: '无法获取会话实例' }
      }

      if (url) {
        const cookies = await session.cookies.get({ url })
        for (const cookie of cookies) {
          await session.cookies.remove(url, cookie.name)
        }
      } else {
        await session.clearStorageData({ storages: ['cookies'] })
      }

      return { success: true, message: 'Cookie已清除' }
    } catch (error) {
      logger.error('清除Cookie失败:', error)
      return { success: false, message: `清除失败: ${(error as Error).message}` }
    }
  }

  /**
   * 设置内容安全策略
   * @param window 浏览器窗口
   */
  setupContentSecurityPolicy(window: BrowserWindow): void {
    window.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            "default-src 'self' https: http:",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https: http:",
            "connect-src 'self' https: http: ws: wss: grpc:",
            "font-src 'self' data:",
          ].join('; '),
        },
      })
    })
  }
}

// 导出单例实例
export const cacheService = new CacheService()
