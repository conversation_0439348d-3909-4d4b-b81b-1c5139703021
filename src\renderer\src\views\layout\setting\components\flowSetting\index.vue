<template>
  <ScrollArea class="h-full w-full">
    <div class="setting-right">
      <Card class="mb-6">
        <CardHeader>
          <CardTitle>{{ t('settings.flowSetting.viewport.title') }}</CardTitle>
          <CardDescription>{{ t('settings.flowSetting.viewport.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- 默认视口设置 -->
          <div class="space-y-2">
            <Label>{{ t('settings.flowSetting.viewport.defaultViewport') }}</Label>
            <div class="flex items-center gap-4">
              <div class="grid gap-2 flex-1">
                <Label>{{ t('settings.flowSetting.viewport.xCoordinate') }}</Label>
                <Input
                  v-model="config.defaultViewport.x"
                  type="number"
                  :step="50"
                  @change="handleConfigChange"
                />
              </div>
              <div class="grid gap-2 flex-1">
                <Label>{{ t('settings.flowSetting.viewport.yCoordinate') }}</Label>
                <Input
                  v-model="config.defaultViewport.y"
                  type="number"
                  :step="50"
                  @change="handleConfigChange"
                />
              </div>
            </div>
            <div class="grid gap-2">
              <Label>{{ t('settings.flowSetting.viewport.zoomRatio') }}</Label>
              <Input
                v-model="config.defaultViewport.zoom"
                type="number"
                :min="0.1"
                :max="2"
                :step="0.1"
                @change="handleConfigChange"
              />
            </div>
          </div>

          <!-- 缩放范围设置 -->
          <div class="space-y-2">
            <Label>{{ t('settings.flowSetting.viewport.zoomRange') }}</Label>
            <div class="flex items-center gap-4">
              <div class="grid gap-2 flex-1">
                <Label>{{ t('settings.flowSetting.viewport.minZoom') }}</Label>
                <Input
                  v-model="config.minZoom"
                  type="number"
                  :min="0.1"
                  :max="1"
                  :step="0.1"
                  @change="handleConfigChange"
                />
              </div>
              <div class="grid gap-2 flex-1">
                <Label>{{ t('settings.flowSetting.viewport.maxZoom') }}</Label>
                <Input
                  v-model="config.maxZoom"
                  type="number"
                  :min="1"
                  :max="10"
                  :step="0.5"
                  @change="handleConfigChange"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card class="mb-6">
        <CardHeader>
          <CardTitle>{{ t('settings.flowSetting.edge.title') }}</CardTitle>
          <CardDescription>{{ t('settings.flowSetting.edge.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- 线条类型设置 -->
          <div class="space-y-2">
            <Label>{{ t('settings.flowSetting.edge.type') }}</Label>
            <Select v-model="config.edgeConfig.type" @update:model-value="handleConfigChange">
              <SelectTrigger>
                <SelectValue :placeholder="t('settings.flowSetting.edge.typePlaceholder')" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">
                  {{ t('settings.flowSetting.edge.types.default') }}
                </SelectItem>
                <SelectItem value="straight">
                  {{ t('settings.flowSetting.edge.types.straight') }}
                </SelectItem>
                <SelectItem value="step">
                  {{ t('settings.flowSetting.edge.types.step') }}
                </SelectItem>
                <SelectItem value="smoothstep">
                  {{ t('settings.flowSetting.edge.types.smoothstep') }}
                </SelectItem>
                <SelectItem value="bezier">
                  {{ t('settings.flowSetting.edge.types.bezier') }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 线条动画设置 -->
          <div class="flex items-center space-x-2">
            <Switch
              id="animated"
              :checked="config.edgeConfig.animated"
              @update:checked="(value) => updateConfig('edgeConfig.animated', value)"
            />
            <Label for="animated">{{ t('settings.flowSetting.edge.animation') }}</Label>
          </div>

          <!-- 箭头显示设置 -->
          <div class="flex items-center space-x-2">
            <Switch
              id="show-arrow"
              :checked="config.edgeConfig.showArrow"
              @update:checked="(value) => updateConfig('edgeConfig.showArrow', value)"
            />
            <Label for="show-arrow">{{ t('settings.flowSetting.edge.arrow') }}</Label>
          </div>

          <!-- 线条样式设置 -->
          <div class="space-y-2">
            <Label>{{ t('settings.flowSetting.edge.color') }}</Label>
            <Input
              v-model="config.edgeConfig.style.stroke"
              type="color"
              @change="handleConfigChange"
            />
          </div>

          <div class="space-y-2">
            <Label>{{ t('settings.flowSetting.edge.width') }}</Label>
            <Input
              v-model="config.edgeConfig.style.strokeWidth"
              type="number"
              :min="1"
              :max="10"
              @change="handleConfigChange"
            />
          </div>
        </CardContent>
      </Card>

      <Card class="mb-6">
        <CardHeader>
          <CardTitle>{{ t('settings.flowSetting.alignment.title') }}</CardTitle>
          <CardDescription>{{ t('settings.flowSetting.alignment.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- 对齐辅助线设置 -->
          <div class="flex items-center space-x-2">
            <Switch
              id="snap-to-lines"
              :checked="config.snapToLines"
              @update:checked="(value) => updateConfig('snapToLines', value)"
            />
            <Label for="snap-to-lines">{{ t('settings.flowSetting.alignment.snapToLines') }}</Label>
          </div>

          <!-- 对齐网格设置 -->
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <Switch
                id="snap-to-grid"
                :checked="config.snapToLines"
                @update:checked="(value) => updateConfig('snapToLines', value)"
              />
              <Label for="snap-to-grid">{{ t('settings.flowSetting.alignment.snapToGrid') }}</Label>
            </div>

            <Collapsible v-if="config.snapToGrid">
              <CollapsibleContent>
                <div class="flex items-center gap-4 mt-2">
                  <div class="grid gap-2 flex-1">
                    <Label>{{ t('settings.flowSetting.alignment.gridSpacingX') }}</Label>
                    <Input
                      v-model="config.snapGrid.x"
                      type="number"
                      :min="1"
                      :max="50"
                      @change="handleConfigChange"
                    />
                  </div>
                  <div class="grid gap-2 flex-1">
                    <Label>{{ t('settings.flowSetting.alignment.gridSpacingY') }}</Label>
                    <Input
                      v-model="config.snapGrid.y"
                      type="number"
                      :min="1"
                      :max="50"
                      @change="handleConfigChange"
                    />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </CardContent>
      </Card>

      <Card class="mb-6">
        <CardHeader>
          <CardTitle>{{ t('settings.flowSetting.interface.title') }}</CardTitle>
          <CardDescription>{{ t('settings.flowSetting.interface.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- 初始化视图设置 -->
          <div class="flex items-center space-x-2">
            <Switch
              id="fit-view"
              :checked="config.fitViewOnInit"
              @update:checked="(value) => updateConfig('fitViewOnInit', value)"
            />
            <Label for="fit-view">{{ t('settings.flowSetting.interface.fitView') }}</Label>
          </div>

          <!-- 控制器显示设置 -->
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <Switch
                id="show-minimap"
                :checked="config.showMiniMap"
                @update:checked="(value) => updateConfig('showMiniMap', value)"
              />
              <Label for="show-minimap">
                {{ t('settings.flowSetting.interface.showMiniMap') }}
              </Label>
            </div>
            <div class="flex items-center space-x-2">
              <Switch
                id="show-left-controls"
                :checked="config.showLeftControls"
                @update:checked="(value) => updateConfig('showLeftControls', value)"
              />
              <Label for="show-left-controls">
                {{ t('settings.flowSetting.interface.showLeftControls') }}
              </Label>
            </div>
            <div class="flex items-center space-x-2">
              <Switch
                id="show-right-controls"
                :checked="config.showRightControls"
                @update:checked="(value) => updateConfig('showRightControls', value)"
              />
              <Label for="show-right-controls">
                {{ t('settings.flowSetting.interface.showRightControls') }}
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card class="mb-6">
        <CardHeader>
          <CardTitle>{{ t('settings.flowSetting.miniMap.title') }}</CardTitle>
          <CardDescription>{{ t('settings.flowSetting.miniMap.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid gap-4">
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.miniMap.backgroundColor') }}</Label>
              <Input
                v-model="config.miniMapConfig.backgroundColor"
                type="color"
                @change="handleConfigChange"
              />
            </div>
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.miniMap.nodeStrokeColor') }}</Label>
              <Input
                v-model="config.miniMapConfig.nodeStrokeColor"
                type="color"
                @change="handleConfigChange"
              />
            </div>
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.miniMap.nodeStrokeWidth') }}</Label>
              <Input
                v-model="config.miniMapConfig.nodeStrokeWidth"
                type="number"
                :min="1"
                :max="10"
                @change="handleConfigChange"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{{ t('settings.flowSetting.background.title') }}</CardTitle>
          <CardDescription>{{ t('settings.flowSetting.background.description') }}</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid gap-4">
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.background.patternColor') }}</Label>
              <Input
                v-model="config.backgroundConfig.patternColor"
                type="color"
                @change="handleConfigChange"
              />
            </div>
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.background.gap') }}</Label>
              <Input
                v-model="config.backgroundConfig.gap"
                type="number"
                :min="1"
                :max="50"
                @change="handleConfigChange"
              />
            </div>
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.background.size') }}</Label>
              <Input
                v-model="config.backgroundConfig.size"
                type="number"
                :min="0.1"
                :max="5"
                :step="0.1"
                @change="handleConfigChange"
              />
            </div>
            <div class="space-y-2">
              <Label>{{ t('settings.flowSetting.background.variant') }}</Label>
              <Select
                v-model="config.backgroundConfig.variant"
                @update:model-value="handleConfigChange"
              >
                <SelectTrigger>
                  <SelectValue
                    :placeholder="t('settings.flowSetting.background.variantPlaceholder')"
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dots">
                    {{ t('settings.flowSetting.background.variants.dots') }}
                  </SelectItem>
                  <SelectItem value="lines">
                    {{ t('settings.flowSetting.background.variants.lines') }}
                  </SelectItem>
                  <SelectItem value="cross">
                    {{ t('settings.flowSetting.background.variants.cross') }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </ScrollArea>
</template>

<script setup lang="ts">
import { useLanguage } from '@renderer/config/hooks'
import { useSettingsStore } from '@renderer/store'
import { computed } from 'vue'

const settingsStore = useSettingsStore()
const config = computed(() => settingsStore.flowConfig)
const { t } = useLanguage()

// 更新配置的辅助函数
const updateConfig = (key: string, value: any) => {
  console.log('更新配置的辅助函数', key, value)
  const pathArray = key.split('.')
  let current = config.value
  for (let i = 0; i < pathArray.length - 1; i++) {
    current = current[pathArray[i]]
  }
  current[pathArray[pathArray.length - 1]] = value
  handleConfigChange()
}

// 处理配置变更
const handleConfigChange = () => {
  settingsStore.updateFlowConfig(config.value)
}
</script>

<style scoped>
.setting-right {
  max-width: 800px;
  margin: 0 auto;
}
</style>
