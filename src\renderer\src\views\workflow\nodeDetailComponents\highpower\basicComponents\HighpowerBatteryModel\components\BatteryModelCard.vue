<template>
  <Card class="">
    <CardHeader>
      <CardTitle class="flex justify-between dark:text-white/80">
        <span class="text-2xl font-bold">{{ title }}</span>
        <div class="flex gap-2">
          <slot name="actions"></slot>
        </div>
      </CardTitle>
    </CardHeader>
    <CardContent>
      <slot></slot>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: '电池模型',
  },
})
</script>
