/**
 * 连接事件处理器
 */
import { dispatchGrpcEvent } from '../../connection/eventDispatcher'
import { resetReconnectAttempts, scheduleReconnect } from '../../connection/reconnect'
import { registerNodeModules } from '../../workflow/registration'
import { handleFunctionCall } from '../functionHandlers'

/**
 * 处理连接事件
 * @param data 事件数据
 * @param type 事件类型
 * @param connectionService 连接服务实例
 */
export function handleConnectionEvent(data: any, type: string, connectionService: any): void {
  if (type === 'data') {
    // 处理心跳消息
    if (data.message === 'Ping') {
      window.logger?.debug('收到心跳消息')
      resetReconnectAttempts()

      // 如果尚未注册节点模块，则进行注册
      if (!connectionService.getRegisteredState()) {
        registerNodeModules()
        connectionService.setRegisteredState(true)
      }

      // 分发心跳事件
      dispatchGrpcEvent('heartbeat', data)
    }
    // 处理连接建立消息
    else if (data.message === 'Channel established') {
      window.logger?.info('gRPC长连接已建立')
      connectionService.setConnectingState(false)
      resetReconnectAttempts()

      // 注册节点模块处理函数
      registerNodeModules()
      connectionService.setRegisteredState(true)

      // 分发连接建立事件
      dispatchGrpcEvent('connection-established', data)
    }
    // 处理功能调用消息
    else if (data.key_value_pairs && data.key_value_pairs.function_name) {
      const functionName = data.key_value_pairs.function_name
      window.logger?.info(`收到功能调用: ${functionName}`)

      // 处理功能调用
      handleFunctionCall(data)

      // 分发功能调用事件
      dispatchGrpcEvent('function-call', {
        functionName,
        data: data,
      })
    }
  } else if (type === 'end') {
    window.logger?.info('gRPC连接已结束')
    connectionService.setConnectingState(false)
    scheduleReconnect()

    // 分发连接结束事件
    dispatchGrpcEvent('connection-ended', null)
  } else if (type === 'error') {
    const errorMsg = typeof data === 'object' ? JSON.stringify(data) : String(data)
    window.logger?.error('gRPC连接错误:', errorMsg)
    connectionService.setConnectingState(false)
    scheduleReconnect()

    // 分发连接错误事件
    dispatchGrpcEvent('connection-error', errorMsg)
  }
}
