import { BrowserWindow } from 'electron'
import { RpcMgr } from '../grpc/rpcMgr'
import logger from '../utils/logger'

/**
 * 工作流服务
 * 处理工作流相关的操作和与中台的通信
 */
export class WorkflowService {
  private rpcMgr: RpcMgr | null = null

  /**
   * 构造函数
   * @param rpcMgr RpcMgr实例
   */
  constructor(rpcMgr: RpcMgr | null) {
    this.rpcMgr = rpcMgr
  }

  /**
   * 处理工作流操作
   * @param data 操作数据
   */
  async handleWorkflowOperation(data: any): Promise<any> {
    try {
      logger.info('工作流操作:', data)
      return {
        success: true,
        data: data.result,
      }
    } catch (error) {
      logger.error('工作流操作失败:', error)
      return {
        success: false,
        message: (error as Error).message,
      }
    }
  }

  /**
   * 处理AI助手消息
   * @param data 消息数据
   */
  async handleAIAssistantMessage(data: any): Promise<any> {
    try {
      // 调用中台AI服务
      const result = await this.callAIService(data)

      return {
        success: true,
        message: result.message || '已收到您的请求，正在处理中...',
      }
    } catch (error) {
      logger.error('AI助手消息处理失败:', error)
      return {
        success: false,
        message: `处理失败: ${(error as Error).message}`,
      }
    }
  }

  /**
   * 调用中台AI服务
   * @param data 请求数据
   * @private
   */
  private async callAIService(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        if (!this.rpcMgr) {
          throw new Error('RpcMgr 未初始化')
        }

        this.rpcMgr.call(
          'defaultService',
          {
            method: 'aiWorkflowGenerate',
            params: JSON.stringify(data),
          },
          (error, response) => {
            if (error) {
              reject(error)
            } else {
              resolve(response)
            }
          },
        )
      } catch (error) {
        logger.error('调用AI服务失败:', error)
        reject(error)
      }
    })
  }

  /**
   * AI工作流生成处理函数 - 注册到中台
   * @param request 请求数据
   */
  async aiWorkflowGenerate(request: any): Promise<any> {
    try {
      // 从请求中获取数据
      const { message, workflowId, currentNodes, currentEdges } = JSON.parse(request.params)

      logger.info('AI工作流生成请求:', { message, workflowId })

      // 发送到渲染进程处理
      const mainWindow = BrowserWindow.getAllWindows()[0]
      if (!mainWindow) {
        throw new Error('主窗口未创建')
      }

      mainWindow.webContents.send('ai-workflow-generate', {
        message,
        workflowId,
        operation: request.operation || 'create_workflow',
        nodes: request.nodes || [],
        edges: request.edges || [],
      })

      // 等待渲染进程的响应
      return new Promise((resolve) => {
        const handler = (_, result) => {
          mainWindow.webContents.removeListener('ai-workflow-generate-result', handler)
          resolve({
            status: result.success ? 0 : 1,
            message: result.message,
            result: JSON.stringify(result.data),
          })
        }

        mainWindow.webContents.once('ai-workflow-generate-result', handler)
      })
    } catch (error) {
      logger.error('AI工作流生成失败:', error)
      return {
        status: 1,
        message: (error as Error).message,
        result: '{}',
      }
    }
  }

  /**
   * 工作流运行处理函数 - 注册到中台
   * @param request 请求数据
   */
  async runWorkflow(request: any): Promise<any> {
    try {
      // 从请求中获取数据
      const { workflowId, params } = JSON.parse(request.params)

      logger.info('工作流运行请求:', { workflowId })

      // 发送到渲染进程处理
      const mainWindow = BrowserWindow.getAllWindows()[0]
      if (!mainWindow) {
        throw new Error('主窗口未创建')
      }

      mainWindow.webContents.send('run-workflow', {
        workflowId,
        params,
      })

      // 等待渲染进程的响应
      return new Promise((resolve) => {
        const handler = (_, result) => {
          mainWindow.webContents.removeListener('run-workflow-result', handler)
          resolve({
            status: result.success ? 0 : 1,
            message: result.message,
            result: JSON.stringify(result.data),
          })
        }

        mainWindow.webContents.once('run-workflow-result', handler)
      })
    } catch (error) {
      logger.error('工作流运行失败:', error)
      return {
        status: 1,
        message: (error as Error).message,
        result: '{}',
      }
    }
  }

  /**
   * 获取服务配置，用于注册到中台
   */
  getServicesConfig(): {
    service_name_list: string[]
    service_version_list: string[]
    service_access_level_list: number[]
  } {
    // 返回要注册的服务方法列表
    return {
      service_name_list: ['aiWorkflowGenerate', 'runWorkflow'],
      service_version_list: ['1.0.0', '1.0.0'],
      service_access_level_list: [1, 1], // 1 << 0 = 1 表示访问级别
    }
  }
}

// 导出创建工作流服务的工厂函数
export function createWorkflowService(rpcMgr: RpcMgr | null): WorkflowService {
  return new WorkflowService(rpcMgr)
}
