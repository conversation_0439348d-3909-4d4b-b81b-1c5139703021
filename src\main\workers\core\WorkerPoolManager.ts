// src/utils/ThreadPoolManager.ts
import { cpus } from 'os'
import Piscina from 'piscina'

// 定义 Worker 任务类型
export type WorkerTasks = {
  analysisUploadFile: (data: any) => Promise<any>
}

export class ThreadPoolManager {
  private static instance: ThreadPoolManager
  private pool: Piscina

  private constructor() {
    const workerUrl = new URL('./workers/core/workerWrapper.mjs', import.meta.url).href
    this.pool = new Piscina({
      filename: workerUrl,
      maxThreads: Math.max(1, cpus().length - 1),
      idleTimeout: 30000,
    })
  }

  public static getInstance(): ThreadPoolManager {
    if (!ThreadPoolManager.instance) {
      ThreadPoolManager.instance = new ThreadPoolManager()
    }
    return ThreadPoolManager.instance
  }

  public getPool(): Piscina {
    return this.pool
  }

  public async runTask<T extends keyof WorkerTasks>(
    taskName: T,
    data: Parameters<WorkerTasks[T]>[0],
    list?: any[],
  ): Promise<ReturnType<WorkerTasks[T]>> {
    // logger.info(list)
    if (list) {
      return this.pool.run(data, { name: taskName, transferList: list })
    } else {
      return this.pool.run(data, { name: taskName })
    }
  }

  public destroy(): Promise<void> {
    return this.pool.destroy()
  }
}

// 导出单例实例
export const threadPool = ThreadPoolManager.getInstance()
