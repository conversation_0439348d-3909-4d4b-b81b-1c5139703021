<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">基本参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <ChevronDown
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <!-- 电池体系选择 -->
            <div class="mb-4">
              <div class="flex flex-col space-y-2">
                <Label for="Battery-Type" class="text-sm font-medium">待标定电池体系</Label>
                <Select
                  id="Battery-Type"
                  v-model="localParams.batteryType"
                  :disabled="isProcessing"
                  class="w-full"
                  @update:model-value="updateParams"
                >
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择电池体系" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="battery in batteryTypes"
                      :key="battery.value"
                      :value="battery.value"
                    >
                      {{ battery.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <!-- 运行工况和充电功率，仅在老化参数选项时显示 -->
            <div v-if="showAgingOptions" class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="Charge-Type" class="text-sm font-medium">运行工况</Label>
                <Select
                  id="Charge-Type"
                  v-model="localParams.chargeType"
                  :disabled="isProcessing"
                  class="w-full"
                  @update:model-value="updateParams"
                >
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择运行工况类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="charge in chargeTypes"
                      :key="charge.value"
                      :value="charge.value"
                    >
                      {{ charge.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="flex flex-col space-y-2">
                <Label for="charge-power" class="text-sm font-medium">充电功率 (W)</Label>
                <Input
                  id="charge-power"
                  v-model="localParams.chargeValue"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                  :disabled="isProcessing"
                  @change="updateParams"
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup>
import { ChevronDown } from 'lucide-vue-next'
import { ref, watch } from 'vue'
const props = defineProps({
  // 基本参数对象
  basicParams: {
    type: Object,
    required: true,
  },
  // 是否正在处理任务
  isProcessing: {
    type: Boolean,
    default: false,
  },
  // 是否显示老化参数选项
  showAgingOptions: {
    type: Boolean,
    default: false,
  },
  // 电池类型列表
  batteryTypes: {
    type: Array,
    default: () => [],
  },
  // 充电类型列表
  chargeTypes: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:basic-params'])

const localParams = ref({
  batteryType: props.basicParams.batteryType,
  chargeType: props.basicParams.chargeType,
  chargeValue: props.basicParams.chargeValue,
})

watch(
  () => props.basicParams,
  (newParams) => {
    localParams.value = {
      batteryType: newParams.batteryType,
      chargeType: newParams.chargeType,
      chargeValue: newParams.chargeValue,
    }
  },
  { deep: true },
)

const updateParams = () => {
  emit('update:basic-params', localParams.value)
}
</script>
