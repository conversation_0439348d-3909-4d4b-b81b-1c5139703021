<template>
  <div class="absolute inset-0 flex items-center justify-center bg-white/70 z-10">
    <div class="flex flex-col items-center bg-white p-6 rounded-lg shadow-md">
      <LoaderCircle class="w-8 h-8 mb-2 animate-spin" />
      <span class="text-base text-gray-700">{{ text }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LoaderCircle } from 'lucide-vue-next'

defineProps({
  text: {
    type: String,
    default: 'Loading...'
  }
})
</script>
