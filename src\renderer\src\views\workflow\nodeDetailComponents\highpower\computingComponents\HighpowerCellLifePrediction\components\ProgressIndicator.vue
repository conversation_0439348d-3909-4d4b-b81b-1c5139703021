<template>
  <div class="p-4 mt-2 rounded-lg bg-muted">
    <div class="flex justify-between mb-2">
      <span class="text-sm font-medium text-foreground dark:text-muted-foreground">预测进度</span>
      <div class="flex space-x-2">
        <span class="text-sm font-medium text-primary">{{ progressValue.toFixed(2) }} %</span>
        <div class="border-l border-gray-300 dark:border-muted-foreground pl-2 ml-1">
          <span class="text-sm font-medium text-indigo-600 dark:text-indigo-900">
            {{ currentCycle }}/{{ totalCycle || 0 }} 圈
          </span>
        </div>
      </div>
    </div>
    <Progress :model-value="progressValue" class="w-full" />
  </div>
</template>

<script setup lang="ts">
defineProps({
  progressValue: {
    type: Number,
    default: 0,
  },
  currentCycle: {
    type: Number,
    default: 0,
  },
  totalCycle: {
    type: Number,
    default: 0,
  },
})
</script>
