import log from 'electron-log'
import fs from 'fs'
import path from 'path'

/**
 * 日志级别类型
 */
export type LogLevel = 'error' | 'warn' | 'info' | 'verbose' | 'debug' | 'silly'

/**
 * 日志工具类
 * 封装 electron-log 功能，提供统一的日志接口
 */
class Logger {
  private initialized: boolean = false

  /**
   * 初始化日志系统
   * @param options 日志配置选项
   */
  init(
    options: {
      processType?: string
      logLevel?: LogLevel
      consoleLevel?: LogLevel | false
      maxSize?: number
      showInConsole?: boolean
    } = {},
  ) {
    if (this.initialized) {
      return log
    }

    const {
      processType = 'renderer',
      logLevel = 'info',
      consoleLevel = 'debug',
      maxSize = 10 * 1024 * 1024, // 10MB
      showInConsole = false, // 默认不在控制台显示
    } = options

    try {
      // 获取应用根目录
      const rootPath = this.getAppRootPath()
      const logsPath = path.join(rootPath, 'logs')

      // 确保日志目录存在
      this.ensureDirectoryExists(logsPath)

      // 配置日志文件路径
      log.transports.file.resolvePath = () => {
        // 获取当前日期，格式为 YYYY-MM-DD
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const dateStr = `${year}-${month}-${day}`

        // 构建日志文件路径：logs/processType-YYYY-MM-DD.log
        return path.join(logsPath, `${processType}-${dateStr}.log`)
      }

      // 设置日志级别
      log.transports.file.level = logLevel
      log.transports.console.level = showInConsole ? consoleLevel : false

      // 设置日志文件大小上限
      log.transports.file.maxSize = maxSize

      // 日志归档处理
      log.transports.file.archiveLog = (oldLog) => {
        try {
          // 归档文件格式: processType-YYYY-MM-DD.old.N.log
          const logFilePath = oldLog.path
          const dirName = path.dirname(logFilePath)
          const fileName = path.basename(logFilePath)
          const baseName = fileName.replace('.log', '')

          // 查找现有归档文件，确定新的序号
          const files = fs.readdirSync(dirName)
          const archiveFiles = files.filter(
            (f) => f.startsWith(`${baseName}.old.`) && f.endsWith('.log'),
          )
          const nextNumber = archiveFiles.length + 1

          const newName = path.join(dirName, `${baseName}.old.${nextNumber}.log`)
          fs.renameSync(logFilePath, newName)
          return newName
        } catch (e) {
          console.error('无法归档日志文件', e)
          return null
        }
      }

      this.initialized = true
      log.info(`${processType} 日志系统初始化完成`, {
        path: log.transports.file.getFile().path,
        level: logLevel,
        time: new Date().toISOString(),
      })
    } catch (error) {
      console.error('日志系统初始化失败:', error)
    }

    return log
  }

  /**
   * 获取应用根目录
   */
  private getAppRootPath(): string {
    try {
      // 尝试从 electron 获取应用路径
      if (window.electron?.app) {
        return window.electron.app.getAppPath()
      }

      // 从环境变量获取
      if (process.env.APP_PATH) {
        return process.env.APP_PATH
      }

      // 默认使用当前目录
      return '.'
    } catch (error) {
      console.error('获取应用根目录失败:', error)
      return '.'
    }
  }

  /**
   * 确保目录存在
   */
  private ensureDirectoryExists(dirPath: string): void {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true })
      }
    } catch (error) {
      console.error(`创建目录失败: ${dirPath}`, error)
    }
  }

  /**
   * 获取日志文件路径
   */
  getLogFilePath(): string {
    try {
      return log.transports.file.getFile().path
    } catch (error) {
      console.error('获取日志文件路径失败:', error)
      return ''
    }
  }

  /**
   * 打开日志文件所在目录
   */
  openLogDirectory(): boolean {
    try {
      const logPath = this.getLogFilePath()
      if (logPath && window.electron?.shell) {
        window.electron.shell.showItemInFolder(logPath)
        return true
      }
      return false
    } catch (error) {
      console.error('打开日志目录失败:', error)
      return false
    }
  }

  // 以下是日志方法的代理
  error(...params: any[]): void {
    log.error(...params)
  }

  warn(...params: any[]): void {
    log.warn(...params)
  }

  info(...params: any[]): void {
    log.info(...params)
  }

  verbose(...params: any[]): void {
    log.verbose(...params)
  }

  debug(...params: any[]): void {
    log.debug(...params)
  }

  silly(...params: any[]): void {
    log.silly(...params)
  }
}

// 创建并导出单例实例
export const logger = new Logger()

// 默认导出
export default logger
