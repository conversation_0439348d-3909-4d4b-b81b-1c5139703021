<script setup lang="ts">
import { ConfigPanel, TitleBar } from '@renderer/components'
import { Toaster } from '@renderer/components/ui/sonner'
import { useAuthStore } from '@renderer/store'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useFont } from './config/hooks'
import { useAppConfig } from './config/hooks/useAppConfig'

useFont()
const route = useRoute()
const authStore = useAuthStore()

// 判断当前是否在登录页面
const isLoginPage = computed(() => route.name === 'auth')

const { appMeta } = useAppConfig()
const appTitle = ref(appMeta.value.title)

// 开发环境标识
const isDev = import.meta.env.DEV
onMounted(async () => {
  console.log('App.vue mounted')
  // 更新文档标题
  document.title = appTitle.value
  // 检查登录状态并自动重连
  try {
    const isLoggedIn = await authStore.checkLoginStatus()
    console.log('登录状态检查完成:', isLoggedIn)
  } catch (error) {
    console.error('登录状态检查失败:', error)
  }
})
</script>

<template>
  <div class="flex flex-col h-screen overflow-hidden">
    <!-- 只在非登录页显示标题栏 -->
    <TitleBar v-if="!isLoginPage" :title="appTitle" />
    <div class="flex-1 overflow-auto pt-safe">
      <RouterView />
      <!--
        Position:  top-center | top-center | top-right | bottom-left | bottom-center | bottom-right
        Expand:  true | false
        Theme:  light | dark
       -->
      <Toaster close-button rich-colors position="bottom-right" theme="light" />

      <!-- 配置面板 (仅开发环境) -->
      <ConfigPanel v-if="isDev" />
    </div>
  </div>
</template>

<style>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}
</style>
