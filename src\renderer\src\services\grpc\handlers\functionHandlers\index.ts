/**
 * 功能处理器入口
 */
import { handleLoadDataFunction } from './loadDataHandler'
import { handleWorkflowGeneration } from './workflowHandler'

/**
 * 处理功能调用
 * @param data 功能调用数据
 */
export function handleFunctionCall(data: any): void {
  if (!data.key_value_pairs) return

  const functionName = data.key_value_pairs.function_name
  const sessionId = data.key_value_pairs.session_id || ''
  const taskId = data.key_value_pairs.task_id || ''

  switch (functionName) {
    case 'generate_workflow':
      handleWorkflowGeneration(data)
      break
    case 'loadData':
      handleLoadDataFunction(data)
      break
    default:
      window.logger?.warn(`未知功能调用: ${functionName}`)
  }
}
