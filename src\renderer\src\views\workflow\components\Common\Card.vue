<template>
  <div class="box-border">
    <Card class="p-2">
      <CardHeader v-if="slotHeader || title">
        <CardTitle v-if="title" class="flex justify-start items-center dark:text-white/80">
          <span>{{ title }}</span>
        </CardTitle>
        <CardTitle v-else>
          <slot name="header"></slot>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="slotContent">
          <slot name="content"></slot>
        </div>
        <div v-if="!slotContent">
          <slot></slot>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
<script setup lang="ts">
import { defineProps, useSlots } from 'vue'
const slotHeader = !!useSlots().header
const slotContent = !!useSlots().content
defineProps({
  title: {
    type: String,
    default: '',
  },
})
</script>
