<template>
  <div>
    <Container>
      <HeaderTitle :title="t('settings.title')" :tooltip="t('settings.tooltip')" :show-icon="true" />
      <div class="setting-container mt-4 flex w-full" style="height: calc(100vh - 80px)">
        <div class="w-1/4">
          <Left @update:component="handleComponentChange" />
        </div>
        <div class="w-3/4 p-2">
          <Right :active-component="activeComponent" />
        </div>
      </div>
    </Container>
  </div>
</template>
<script setup>
import { ref } from 'vue'

import { Container, HeaderTitle } from '@renderer/components'
import { Left, Right } from './components'
import { useLanguage } from '@renderer/config/hooks'

const { t } = useLanguage()

const activeComponent = ref('BaseSetting')

const handleComponentChange = (component) => {
  activeComponent.value = component
}
</script>
<style lang="scss" scoped>
.setting-container {
  @apply bg-background text-foreground;

  :deep(.border) {
    @apply border-border;
  }
}

/* 确保深色模式下的样式正确 */
:deep(.dark) {
  .setting-container {
    @apply bg-background text-foreground;
  }
}
</style>
