import { ipcMain } from 'electron'
import { cacheService } from '../services/cacheService'
import logger from '../utils/logger'
// 跟踪处理程序是否已设置
let handlersSetup = false
/**
 * 设置缓存管理相关的IPC处理程序
 */
export function setupCacheHandlers(): void {
  // 如果处理程序已设置，则直接返回
  if (handlersSetup) {
    logger.info('缓存管理IPC处理程序已设置，跳过重复设置')
    return
  }
  // 清除应用缓存
  ipcMain.handle('clear-app-cache', async () => {
    try {
      return await cacheService.clearAppCache()
    } catch (error) {
      logger.error('清除应用缓存失败:', error)
      return { success: false, message: `清除失败: ${(error as Error).message}` }
    }
  })

  // 清除特定类型缓存
  ipcMain.handle('clear-storage-data', async (_, options) => {
    try {
      return await cacheService.clearStorageData(options)
    } catch (error) {
      logger.error('清除存储数据失败:', error)
      return { success: false, message: `清除失败: ${(error as Error).message}` }
    }
  })
  // 标记处理程序已设置
  handlersSetup = true
  logger.info('缓存管理IPC处理程序已设置')
}
