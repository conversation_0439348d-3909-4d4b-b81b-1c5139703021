import { convertToCamelCase } from '../../../utils/utils'

export type ResponseStatus = 'Success' | 'Failed'

export interface BaseResponse {
  status: ResponseStatus
  message: string
}

export class BaseService {
  protected async call<T>(apiName: string, params: any): Promise<T> {
    try {
      const response = await window.grpcApi.call(apiName, params)
      return convertToCamelCase(response) as T
    } catch (error) {
      console.error(`<PERSON><PERSON><PERSON> calling ${apiName}:`, error)
      throw error
    }
  }

  protected async default<T>(
    serviceName: string,
    serverId: string,
    isSave: boolean,
    params: any,
  ): Promise<T> {
    try {
      const response = await window.grpcApi.default(serviceName, serverId, isSave, params)
      return convertToCamelCase(response) as T
    } catch (error) {
      console.error(`Error calling task ${serviceName}:`, error)
      throw error
    }
  }

  protected async submit<T>(
    serviceName: string,
    serverId: string,
    isSave: boolean,
    params: any,
  ): Promise<T> {
    try {
      const response = await window.grpcApi.submit(serviceName, serverId, isSave, params)
      return convertToCamelCase(response) as T
    } catch (error) {
      console.error(`Error calling task ${serviceName}:`, error)
      throw error
    }
  }
}
