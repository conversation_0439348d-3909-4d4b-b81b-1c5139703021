{"name": "mattverse", "version": "1.0.0", "license": "MIT", "description": "mattverse application", "main": "./out/main/index.js", "author": "mattverse.com", "homepage": "https://electron-vite.org", "scripts": {"prepare": "husky install", "lint:lint-staged": "lint-staged", "format": "prettier --write .", "commit": "git-cz", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "clean": "node scripts/clean.js", "clear-icon-cache": "node scripts/clear-icon-cache.js", "build": "electron-vite build  --mode production", "postinstall": "electron-builder install-app-deps", "build:mattverse:win": "yarn clear-icon-cache && cross-env VITE_APP_IS_MATT=1 ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true DEBUG=electron-builder node scripts/build.js win", "build:mattverse:mac": "yarn clear-icon-cache && cross-env VITE_APP_IS_MATT=1 ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true DEBUG=electron-builder node scripts/build.js mac", "build:highpower:win": "yarn clear-icon-cache && cross-env VITE_APP_IS_MATT=0 ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true DEBUG=electron-builder node scripts/build.js win", "build:highpower:mac": "yarn clear-icon-cache && cross-env VITE_APP_IS_MATT=0 ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true DEBUG=electron-builder node scripts/build.js mac", "build:unpack": "npm run build && electron-builder --dir", "build:win": "cross-env NODE_ENV=production electron-vite build --mode production && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@grpc/grpc-js": "1.9.13", "@grpc/proto-loader": "0.7.10", "@iconify-json/flowbite": "^1.2.4", "@msgpack/msgpack": "^3.1.1", "@types/papaparse": "^5.3.16", "@unovis/ts": "^1.5.1", "@unovis/vue": "^1.5.1", "@vee-validate/zod": "^4.15.0", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.42.1", "@vue-flow/minimap": "^1.5.2", "@vueuse/core": "^13.0.0", "animejs": "^4.0.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dexie": "^4.0.11", "dompurify": "^3.2.5", "dotenv": "^16.4.7", "echarts": "^5.6.0", "electron-log": "^5.4.0", "gsap": "^3.12.7", "highlight.js": "^11.11.1", "lucide-vue-next": "^0.475.0", "markdown-it": "^14.1.0", "marked": "^15.0.11", "mitt": "^3.0.1", "msgpack5": "^6.0.2", "nanoid": "^5.1.0", "papaparse": "^5.5.3", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "piscina": "^5.1.3", "plotly.js": "^3.0.1", "plotly.js-dist-min": "^3.0.1", "prismjs": "^1.30.0", "protobufjs": "7.2.5", "radix-vue": "^1.9.14", "reka-ui": "^2.2.1", "shadcn-vue": "0.11.4", "tailwind-merge": "^3.0.2", "tippy.js": "^6.3.7", "ts-node": "^10.9.2", "vee-validate": "^4.15.0", "vue-i18n": "9", "vue-prism-component": "^2.0.0", "vue-router": "^4.5.0", "vue-sonner": "^1.3.2", "xlsx": "^0.18.5", "zipson": "^0.2.12", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@iconify/json": "^2.2.305", "@iconify/vue": "^4.3.0", "@rushstack/eslint-patch": "^1.7.1", "@types/node": "^18.19.9", "@unocss/preset-icons": "^65.4.3", "@unocss/preset-uno": "^65.4.3", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "10.4.19", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "cz-git": "^1.11.1", "electron": "^28.2.0", "electron-builder": "^24.9.1", "electron-devtools-installer": "^4.0.0", "electron-vite": "2.3.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "husky": "^9.1.7", "js-yaml": "^4.1.0", "lint-staged": "^15.5.1", "postcss": "8.5.3", "prettier": "^3.2.4", "rimraf": "^6.0.1", "sass": "^1.83.0", "sass-loader": "^16.0.4", "tailwindcss": "3.4.14", "tailwindcss-animate": "^1.0.7", "terser": "^5.39.0", "typescript": "^5.8.3", "unocss": "^65.4.3", "vite": "5.4.14", "vite-plugin-clean": "^2.0.1", "vite-svg-loader": "^5.1.0", "vue": "^3.5.13", "vue-tsc": "^2.2.8", "web-storage-cache": "^1.1.1"}, "engines": {"node": ">=16.0.0"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write"], "*.{scss,css}": ["prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}, "electron_mirror": "https://npmmirror.com/mirrors/electron/", "electron-builder-binaries_mirror": "https://npmmirror.com/mirrors/electron-builder-binaries/"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}