<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-5xl max-h-[90vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle class="text-lg font-semibold text-neutral-900">
          数据集图表 - {{ datasetName }}
        </DialogTitle>
        <DialogDescription class="text-sm text-neutral-600">
          循环容量数据可视化分析
        </DialogDescription>
      </DialogHeader>

      <div class="flex flex-col h-[75vh]">
        <!-- 控制面板 -->
        <div class="flex-shrink-0 flex flex-col space-y-2 p-3 bg-neutral-50 rounded-lg border mb-3">
          <div class="flex items-center justify-between">
            <span class="text-xs font-medium text-neutral-700">数据范围控制</span>
            <Button variant="outline" size="sm" class="text-xs h-6 px-2" @click="resetRange">
              重置
            </Button>
          </div>

          <!-- Slider 控制 -->
          <div class="space-y-2">
            <div class="flex items-center space-x-3">
              <Label class="text-xs text-neutral-600 w-10">范围:</Label>
              <div class="flex-1">
                <Slider
                  v-model="rangeValues"
                  :min="minCycle"
                  :max="maxCycle"
                  :step="1"
                  class="w-full"
                />
              </div>
            </div>

            <!-- 数值输入控制 -->
            <div class="flex items-center space-x-3">
              <Label class="text-xs text-neutral-600 w-10">起始:</Label>
              <Input
                v-model.number="startValue"
                type="number"
                :min="minCycle"
                :max="maxCycle"
                class="w-16 h-6 text-xs"
                @input="updateRangeFromInput"
              />
              <Label class="text-xs text-neutral-600">终止:</Label>
              <Input
                v-model.number="endValue"
                type="number"
                :min="minCycle"
                :max="maxCycle"
                class="w-16 h-6 text-xs"
                @input="updateRangeFromInput"
              />
              <div class="text-xs text-neutral-500">总计: {{ filteredData.length }} 个数据点</div>
            </div>
          </div>
        </div>

        <!-- 图表容器 -->
        <div class="flex-1 border rounded-lg bg-white relative min-h-0">
          <!-- 警告提示 - 绝对定位在图表上方 -->
          <Alert
            v-if="isRangeTooSmall"
            class="absolute top-2 left-2 right-2 z-10 bg-yellow-50 border-yellow-200 shadow-md"
          >
            <AlertTriangle class="h-4 w-4 text-yellow-600" />
            <AlertTitle class="text-yellow-800 text-sm">范围警告</AlertTitle>
            <AlertDescription class="text-yellow-700 text-xs">
              圈数范围过小，请重新选择！至少20圈！
            </AlertDescription>
          </Alert>

          <div ref="chartContainer" class="w-full h-full min-h-[450px]"></div>

          <!-- 图例 -->
          <div
            class="absolute top-2 left-1/2 transform -translate-x-1/2 flex items-center space-x-4 bg-white/90 px-3 py-1 rounded shadow-sm"
            :class="{ 'top-16': isRangeTooSmall }"
          >
            <div class="flex items-center space-x-1">
              <div class="w-4 h-0.5 bg-blue-500"></div>
              <span class="text-xs text-neutral-600">实验数据</span>
            </div>
            <div class="flex items-center space-x-1">
              <div class="w-4 h-0.5 bg-red-500 border-dashed border-t"></div>
              <span class="text-xs text-neutral-600">选择范围</span>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="closeDialog">关闭</Button>
        <Button
          :disabled="isRangeTooSmall"
          class="bg-neutral-900 hover:bg-neutral-800 disabled:bg-neutral-400 disabled:cursor-not-allowed"
          @click="confirmSelection"
        >
          确认
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AlertTriangle } from 'lucide-vue-next'
import { Alert, AlertDescription, AlertTitle } from '@renderer/components/ui/alert'
import { useDatasetChart } from '../composables/useDatasetChart'

// 定义数据集项的接口
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// Props
const props = defineProps<{
  dataset?: DatasetItem
}>()

// 使用 defineModel 管理弹框状态
const isOpen = defineModel<boolean>('open', { default: false })

// 定义 emits
const emit = defineEmits<{
  'update-chart-settings': [itemId: string, startCycle: number, endCycle: number]
}>()

// 使用组合式函数
const {
  chartContainer,
  rangeValues,
  startValue,
  endValue,
  datasetName,
  minCycle,
  maxCycle,
  filteredData,
  isRangeTooSmall,
  resetRange,
  updateRangeFromInput,
  confirmSelection,
  exportData,
} = useDatasetChart(
  computed(() => props.dataset),
  isOpen,
  emit,
)

// 关闭对话框
const closeDialog = () => {
  isOpen.value = false
}
</script>

<style scoped></style>
