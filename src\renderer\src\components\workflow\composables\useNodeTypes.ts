import { shallowRef, markRaw } from 'vue'

// 导入节点组件
import { BatteryDatabaseNode, BatteryDataChartNode, CustomNode } from '../nodes'

// 节点类型映射表
const nodeTypeMap = {
  custom: CustomNode,
  BatteryDatabase: BatteryDatabaseNode,
  BatteryDataChart: BatteryDataChartNode,
}

/**
 * 节点类型管理 Hook
 * 用于集中管理和注册工作流节点类型
 */
export function useNodeTypes() {
  // 使用 shallowRef 优化性能
  const nodeTypes = shallowRef(nodeTypeMap)

  /**
   * 注册新的节点类型
   * @param typeName 节点类型名称
   * @param component 节点组件
   */
  const registerNodeType = (typeName: string, component: any) => {
    nodeTypes.value = {
      ...nodeTypes.value,
      [typeName]: component,
    }
  }

  /**
   * 批量注册节点类型
   * @param types 节点类型映射对象
   */
  const registerNodeTypes = (types: Record<string, any>) => {
    nodeTypes.value = {
      ...nodeTypes.value,
      ...types,
    }
  }

  /**
   * 获取节点类型组件
   * @param typeName 节点类型名称
   * @returns 节点组件或undefined
   */
  const getNodeTypeComponent = (typeName: string) => {
    return nodeTypes.value[typeName]
  }

  return {
    nodeTypes,
    registerNodeType,
    registerNodeTypes,
    getNodeTypeComponent,
  }
}
