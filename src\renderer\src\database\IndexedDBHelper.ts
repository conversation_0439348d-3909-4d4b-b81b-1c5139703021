export interface UploadData {
  id?: string
  data: any
}

export class IndexedDBHelper {
  private dbName: string
  private dbVersion: number
  private db: IDBDatabase | null

  constructor(dbName: string, dbVersion: number) {
    this.dbName = dbName
    this.dbVersion = dbVersion
    this.db = null
  }

  // 打开数据库
  open(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)

      // 数据库打开成功
      request.onsuccess = (event) => {
        this.db = (event.target as IDBRequest).result as IDBDatabase
        //console.log('Database opened successfully')
        resolve(this.db)
      }

      // 数据库升级成功
      request.onupgradeneeded = (event) => {
        this.db = (event.target as IDBRequest).result as IDBDatabase
        if (!this.db.objectStoreNames.contains('UploadData')) {
          this.db.createObjectStore('UploadData', { keyPath: 'id' })
          //console.log("Object store 'UploadData' created")
        }
      }

      // 打开失败
      request.onerror = (event) => {
        reject(`Database failed to open: ${(event.target as IDBRequest).error}`)
      }
    })
  }

  // 添加数据
  addData(tableName: string, data: UploadData): Promise<number> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        return reject('Database is not open')
      }

      const transaction = this.db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      const request = store.add(data)

      request.onsuccess = () => {
        //console.log('Data added successfully')
        resolve(request.result as number)
      }

      request.onerror = (event) => {
        reject(`Failed to add data: ${(event.target as IDBRequest).error}`)
      }
    })
  }

  getData(tableName: string, id: string): Promise<UploadData | undefined> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        return reject('Database is not open')
      }

      const transaction = this.db.transaction([tableName], 'readonly')
      const store = transaction.objectStore(tableName)
      const request = store.get(id) //

      request.onsuccess = (event) => {
        const result = (event.target as IDBRequest).result as UploadData | undefined
        resolve(result) // 返回找到的单条数据（可能是 undefined）
      }

      request.onerror = (event) => {
        reject(`Failed to get data by uploadId: ${(event.target as IDBRequest).error}`)
      }
    })
  }

  // 获取所有数据
  getAllData(tableName: string): Promise<UploadData[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        return reject('Database is not open')
      }

      const transaction = this.db.transaction([tableName], 'readonly')
      const store = transaction.objectStore(tableName)
      const request = store.getAll()

      request.onsuccess = (event) => {
        resolve((event.target as IDBRequest).result as UploadData[])
      }

      request.onerror = (event) => {
        reject(`Failed to get data: ${(event.target as IDBRequest).error}`)
      }
    })
  }

  // 删除数据，如果没有数据则创建
  deleteData(tableName: string, id: string): Promise<number> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        return reject('Database is not open')
      }

      const transaction = this.db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)
      const request = store.get(id) // 获取指定 ID 的数据

      request.onsuccess = (event) => {
        const data = (event.target as IDBRequest).result as UploadData

        if (data) {
          // 如果数据存在，进行删除操作
          const deleteRequest = store.delete(id)

          deleteRequest.onsuccess = () => {
            //console.log(`Data with ID ${id} deleted successfully`)
            resolve(1)
          }

          deleteRequest.onerror = (deleteEvent) => {
            reject(
              `Failed to delete data with ID ${id}: ${(deleteEvent.target as IDBRequest).error}`,
            )
          }
        }
      }

      request.onerror = (event) => {
        reject(`Failed to retrieve data with ID ${id}: ${(event.target as IDBRequest).error}`)
      }
    })
  }
}
