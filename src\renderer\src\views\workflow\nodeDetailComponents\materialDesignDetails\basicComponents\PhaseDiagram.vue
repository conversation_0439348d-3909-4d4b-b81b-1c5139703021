<template>
  <div class="w-full h-full">
    <CardVue>
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center justify-center gap-2 text-2xl font-bold">
            <SvgIcon :name="svg.phaseDiagram" />
            <span>相图</span>
          </div>
          <div class="flex gap-2">
            <Button size="sm" @click="selectStructure()">
              <LucideIcon name="Search" class="h-4 w-4" />
              <span>选择结构</span>
            </Button>
          </div>
        </div>
      </template>
      <form @submit="onSubmit">
        <FormField v-slot="{ componentField }" name="formula">
          <FormItem class="flex justify-start items-center">
            <FormLabel class="w-[80px] truncate line-height-1 mt-[5px]">
              <span class="text-red-500 mr-1">*</span>
              <span class="text-sm">化学式</span>
            </FormLabel>
            <FormControl class="w-[calc(100%-80px)]">
              <Input type="text" disabled placeholder="化学式" v-bind="componentField" />
            </FormControl>
          </FormItem>
        </FormField>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="resetFormValues()">重置</Button>
          <Button type="submit">
            <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
            <span>提交</span>
          </Button>
        </div>
      </form>
    </CardVue>
    <CardVue class="mt-6">
      <CardVue :title="'结果'" class="mt-6 shadow-md rounded-xl">
        <div class="min-h-[200px] mt-2">
          <PhaseDiagram
            :data="diagram.data"
            :layout="diagram.layout"
            :is-loading="diagram.isLoading"
          />
        </div>
      </CardVue>
    </CardVue>
    <SelectStructureDialog
      v-if="structureVisiable"
      v-model:visiable="structureVisiable"
      :form-data="selectForm"
      @ok="okStructure"
    />
  </div>
</template>
<script setup lang="ts">
import { LucideIcon, SvgIcon } from '@renderer/components'
import { createMaterialService } from '@renderer/config/api/grpc/materialService'
import svg from '@renderer/config/constants/svg'
import { toTypedSchema } from '@vee-validate/zod'
import { Loader2 } from 'lucide-vue-next'
import { useForm } from 'vee-validate'
import { ref, Ref } from 'vue'
import * as z from 'zod'
import { CardVue, PhaseDiagram, SelectStructureDialog } from '../../../components'
const structureVisiable: Ref<boolean> = ref(false)
const selectForm = ref({})
const selectStructure = () => {
  structureVisiable.value = true
}
let selectColumn: any = null
const okStructure = (ev: any) => {
  selectColumn = ev
  const fromData: any = selectColumn.selectItem
  setValues({
    formula: fromData.formula,
  })
}
// 表单内容
const formSchema = toTypedSchema(
  z.object({
    formula: z.string().optional(),
  }),
)

const { handleSubmit, resetForm, setValues } = useForm({
  validationSchema: formSchema,
})
const resetFormValues = () => {
  resetForm({
    values: {
      formula: '',
    },
  })
}
const materialService = createMaterialService()
const loading = ref(false)
const diagram = ref({
  data: [],
  layout: {},
  isLoading: false,
})
const onSubmit = handleSubmit(async (values: any) => {
  if (loading.value) {
    return
  }
  const formula = values.formula
  loading.value = true
  diagram.value.isLoading = true
  if (formula) {
    const res = await materialService.getPhaseDiagram(formula)
    loading.value = false
    const resData = res.keyValuePairs.htmlString
    if (resData) {
      const resDataObj = JSON.parse(resData)
      if (resDataObj?.layout?.margin?.l) {
        resDataObj.layout.margin.l += 20
      }
      diagram.value = {
        data: resDataObj.data,
        layout: resDataObj.layout,
        isLoading: false,
      }
    }
  }
})
</script>
<style scoped lang="scss"></style>
