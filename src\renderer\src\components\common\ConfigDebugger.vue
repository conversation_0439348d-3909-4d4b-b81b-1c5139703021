<template>
  <div class="fixed bottom-4 right-4 z-[1000]">
    <!-- 调试按钮 -->
    <button
      v-if="!showDebugPanel"
      class="bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
      title="显示配置调试面板"
      @click="showDebugPanel = true"
    >
      <LucideIcon name="Settings" :width="20" :height="20" />
    </button>

    <!-- 调试面板 -->
    <div
      v-if="showDebugPanel"
      class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4 w-96 max-h-96 overflow-y-auto"
    >
      <!-- 头部 -->
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">应用配置调试</h3>
        <button
          class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          @click="showDebugPanel = false"
        >
          <LucideIcon name="X" :width="20" :height="20" />
        </button>
      </div>

      <!-- 当前应用信息 -->
      <div class="mb-4">
        <h4 class="font-medium mb-2">当前应用</h4>
        <div class="text-sm space-y-1">
          <div>
            <strong>类型:</strong>
            {{ appType }}
          </div>
          <div>
            <strong>名称:</strong>
            {{ appMeta.name }}
          </div>
          <div>
            <strong>标题:</strong>
            {{ appMeta.title }}
          </div>
          <div>
            <strong>版本:</strong>
            {{ appMeta.version }}
          </div>
        </div>
      </div>

      <!-- 功能状态 -->
      <div class="mb-4">
        <h4 class="font-medium mb-2">功能状态</h4>
        <div class="text-sm space-y-1">
          <div class="flex justify-between">
            <span>侧边栏:</span>
            <span :class="canShowSidebar ? 'text-green-600' : 'text-red-600'">
              {{ canShowSidebar ? '✓' : '✗' }}
            </span>
          </div>
          <div class="flex justify-between">
            <span>AI浮动框:</span>
            <span :class="canShowAIFloatingBox ? 'text-green-600' : 'text-red-600'">
              {{ canShowAIFloatingBox ? '✓' : '✗' }}
            </span>
          </div>
          <div class="flex justify-between">
            <span>工具栏:</span>
            <span :class="canShowToolbar ? 'text-green-600' : 'text-red-600'">
              {{ canShowToolbar ? '✓' : '✗' }}
            </span>
          </div>
          <div class="flex justify-between">
            <span>小地图:</span>
            <span :class="canShowMiniMap ? 'text-green-600' : 'text-red-600'">
              {{ canShowMiniMap ? '✓' : '✗' }}
            </span>
          </div>
          <div class="flex justify-between">
            <span>主题切换:</span>
            <span :class="canSwitchTheme ? 'text-green-600' : 'text-red-600'">
              {{ canSwitchTheme ? '✓' : '✗' }}
            </span>
          </div>
          <div class="flex justify-between">
            <span>认证系统:</span>
            <span :class="canEnableAuth ? 'text-green-600' : 'text-red-600'">
              {{ canEnableAuth ? '✓' : '✗' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 配置切换 -->
      <div class="mb-4">
        <h4 class="font-medium mb-2">配置切换 (开发用)</h4>
        <div class="space-y-2">
          <button
            :class="[
              'w-full px-3 py-2 text-sm rounded border',
              appType === 'mattverse'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200',
            ]"
            @click="switchConfig('mattverse')"
          >
            MattVerse 配置
          </button>
          <button
            :class="[
              'w-full px-3 py-2 text-sm rounded border',
              appType === 'highpower'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200',
            ]"
            @click="switchConfig('highpower')"
          >
            Highpower 配置
          </button>
        </div>
      </div>

      <!-- 环境变量信息 -->
      <div class="mb-4">
        <h4 class="font-medium mb-2">环境变量</h4>
        <div class="text-sm">
          <div>
            <strong>VITE_APP_IS_MATT:</strong>
            {{ envIsMatt }}
          </div>
        </div>
      </div>

      <!-- 刷新按钮 -->
      <button
        class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 transition-colors"
        @click="refreshConfig"
      >
        刷新配置
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { ref } from 'vue'
import { toast } from 'vue-sonner'

const showDebugPanel = ref(false)

const {
  appType,
  appMeta,
  canShowSidebar,
  canShowAIFloatingBox,
  canShowToolbar,
  canShowMiniMap,
  canSwitchTheme,
  canEnableAuth,
  switchAppConfig,
  resetConfig,
} = useAppConfig()

// 环境变量信息
const envIsMatt = import.meta.env.VITE_APP_IS_MATT

// 切换配置
const switchConfig = (type: string) => {
  try {
    switchAppConfig(type as any)
    toast.success(`已切换到 ${type} 配置`)
  } catch (error) {
    toast.error(`切换配置失败: ${error}`)
  }
}

// 刷新配置
const refreshConfig = () => {
  try {
    resetConfig()
    toast.success('配置已刷新')
  } catch (error) {
    toast.error(`刷新配置失败: ${error}`)
  }
}
</script>

<style scoped>
/* 确保调试面板在最顶层 */
.z-\[1000\] {
  z-index: 1000;
}
</style>
