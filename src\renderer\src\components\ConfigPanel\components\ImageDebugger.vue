<template>
  <Card class="border-muted">
    <CardHeader class="pb-3">
      <CardTitle class="text-sm flex items-center">
        <Bug class="h-4 w-4 mr-2" />
        图标调试信息
      </CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 配置信息 -->
      <div class="text-xs space-y-1">
        <div>
          <strong>应用类型:</strong>
          {{ appType }}
        </div>
        <div>
          <strong>图标文件:</strong>
          {{ appMeta.icon }}
        </div>
        <div>
          <strong>Logo文件:</strong>
          {{ appMeta.logo }}
        </div>
      </div>

      <!-- URL 测试 -->
      <div class="space-y-2">
        <div class="text-xs font-medium">生成的URL:</div>
        <div class="text-xs font-mono bg-muted p-2 rounded break-all">
          <div>
            <strong>图标URL:</strong>
            {{ iconUrl }}
          </div>
          <div>
            <strong>Logo URL:</strong>
            {{ logoUrl }}
          </div>
        </div>
      </div>

      <!-- 图片测试 -->
      <div class="grid grid-cols-2 gap-4">
        <!-- 图标测试 -->
        <div class="text-center">
          <div class="text-xs font-medium mb-2">图标测试</div>
          <div
            class="w-16 h-16 bg-background border rounded-lg flex items-center justify-center mb-2"
          >
            <img
              :src="iconUrl"
              :alt="appMeta.name + ' icon'"
              class="max-w-12 max-h-12 object-contain"
              @load="onIconLoad"
              @error="onIconError"
            />
          </div>
          <Badge :variant="iconLoaded ? 'default' : 'destructive'" class="text-xs">
            {{ iconLoaded ? '加载成功' : '加载失败' }}
          </Badge>
        </div>

        <!-- Logo测试 -->
        <div class="text-center">
          <div class="text-xs font-medium mb-2">Logo测试</div>
          <div
            class="w-16 h-16 bg-background border rounded-lg flex items-center justify-center mb-2"
          >
            <img
              :src="logoUrl"
              :alt="appMeta.name + ' logo'"
              class="max-w-14 max-h-12 object-contain"
              @load="onLogoLoad"
              @error="onLogoError"
            />
          </div>
          <Badge :variant="logoLoaded ? 'default' : 'destructive'" class="text-xs">
            {{ logoLoaded ? '加载成功' : '加载失败' }}
          </Badge>
        </div>
      </div>

      <!-- 替代方案测试 -->
      <div class="space-y-2">
        <div class="text-xs font-medium">替代方案测试:</div>
        <div class="grid grid-cols-2 gap-2">
          <div class="text-center">
            <div class="text-xs mb-1">相对路径</div>
            <img
              :src="`/src/renderer/src/assets/logo/${appMeta.icon}`"
              :alt="appMeta.name + ' icon'"
              class="w-8 h-8 object-contain mx-auto border rounded"
              @load="() => console.log('相对路径图标加载成功')"
              @error="() => console.log('相对路径图标加载失败')"
            />
          </div>
          <div class="text-center">
            <div class="text-xs mb-1">直接路径</div>
            <img
              :src="`./src/renderer/src/assets/logo/${appMeta.logo}`"
              :alt="appMeta.name + ' logo'"
              class="w-8 h-8 object-contain mx-auto border rounded"
              @load="() => console.log('直接路径Logo加载成功')"
              @error="() => console.log('直接路径Logo加载失败')"
            />
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessages.length > 0" class="space-y-1">
        <div class="text-xs font-medium text-destructive">错误信息:</div>
        <div class="text-xs bg-destructive/10 p-2 rounded">
          <div v-for="(error, index) in errorMessages" :key="index" class="text-destructive">
            {{ error }}
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { Bug } from 'lucide-vue-next'
import { computed, ref } from 'vue'

const { appType, appMeta } = useAppConfig()

const iconLoaded = ref(false)
const logoLoaded = ref(false)
const errorMessages = ref<string[]>([])

// 动态导入图标和Logo
const iconUrl = computed(() => {
  try {
    return new URL(`../../../assets/logo/${appMeta.value.icon}`, import.meta.url).href
  } catch (error) {
    const errorMsg = `图标URL生成失败: ${error}`
    console.warn(errorMsg)
    if (!errorMessages.value.includes(errorMsg)) {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      errorMessages.value.push(errorMsg)
    }
    return ''
  }
})

const logoUrl = computed(() => {
  try {
    return new URL(`../../../assets/logo/${appMeta.value.logo}`, import.meta.url).href
  } catch (error) {
    const errorMsg = `Logo URL生成失败: ${error}`
    console.warn(errorMsg)
    if (!errorMessages.value.includes(errorMsg)) {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      errorMessages.value.push(errorMsg)
    }
    return ''
  }
})

// 图片加载事件处理
const onIconLoad = () => {
  iconLoaded.value = true
  console.log('图标加载成功:', iconUrl.value)
}

const onIconError = (event: Event) => {
  iconLoaded.value = false
  const target = event.target as HTMLImageElement
  const errorMsg = `图标加载失败: ${target.src}`
  console.error(errorMsg)
  if (!errorMessages.value.includes(errorMsg)) {
    errorMessages.value.push(errorMsg)
  }
}

const onLogoLoad = () => {
  logoLoaded.value = true
  console.log('Logo加载成功:', logoUrl.value)
}

const onLogoError = (event: Event) => {
  logoLoaded.value = false
  const target = event.target as HTMLImageElement
  const errorMsg = `Logo加载失败: ${target.src}`
  console.error(errorMsg)
  if (!errorMessages.value.includes(errorMsg)) {
    errorMessages.value.push(errorMsg)
  }
}
</script>
