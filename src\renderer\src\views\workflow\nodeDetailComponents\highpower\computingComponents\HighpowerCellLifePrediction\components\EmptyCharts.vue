<template>
  <div class="flex flex-col w-full">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
      <div
        v-for="(series, index) in chartSeries"
        :key="index"
        class="chart-container show w-full aspect-[4/3] relative"
      >
        <div
          :ref="
            (el) => {
              if (el) chartRefs[index] = el
            }
          "
          class="w-full h-full absolute inset-0"
        ></div>
        <!-- 加载状态图标 -->
        <!-- <div class="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
          <div class="relative">
            <div class="flex flex-col items-center space-y-2">
              <LucideIcon
                :name="isLoading ? 'Loader2' : 'BarChart2'"
                class="w-8 h-8 text-muted-foreground/50"
                :class="{ '!animate-ping': isLoading }"
              />
              <span class="text-xs text-muted-foreground">
                {{ isLoading ? '加载中...' : '等待数据...' }}
              </span>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onUnmounted, ref } from 'vue'
import { LucideIcon } from '@renderer/components'
defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const chartSeries = ['预测容量', 'SEI造成的容量损失', '析锂造成的容量损失', '总锂量']

const chartRefs = ref<any[]>([])
const chartInstances = ref<Record<number, echarts.ECharts>>({})

// 获取图表配置
const getChartOptions = (seriesName: string, index: number) => {
  const colors = [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
  ]

  // 为特定系列设置默认单位和标题
  let yUnitName = ''
  if (seriesName === '预测容量') {
    yUnitName = 'A.h'
  } else if (seriesName === 'SEI造成的容量损失') {
    yUnitName = 'A.h'
  } else if (seriesName === '析锂造成的容量损失') {
    yUnitName = 'A.h'
  } else if (seriesName === '总锂量') {
    yUnitName = 'mol'
  }

  // 创建模拟的循环圈数（x轴）
  const cycleData = Array.from({ length: 10 }, (_, i) => i + 1)

  // 设置y轴范围
  let yAxisMin = 0
  let yAxisMax = 300

  if (seriesName === '预测容量') {
    yAxisMin = 0
    yAxisMax = 300
  } else if (seriesName === 'SEI造成的容量损失') {
    yAxisMin = -0.2
    yAxisMax = 0
  } else if (seriesName === '析锂造成的容量损失') {
    yAxisMin = 0
    yAxisMax = 0.05
  } else if (seriesName === '总锂量') {
    yAxisMin = 0
    yAxisMax = 15
  }

  return {
    title: {
      text: seriesName,
      left: 'center',
      top: '2%',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        if (!params[0]) return ''
        return `循环圈数: ${params[0].axisValue}<br>${seriesName}: 暂无数据`
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '18%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: cycleData,
      name: '循环圈数',
      nameLocation: 'middle',
      nameGap: 30,
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        align: 'center',
      },
    },
    yAxis: {
      type: 'value',
      name: `单位(${yUnitName})`,
      nameLocation: 'end',
      nameGap: 15,
      min: yAxisMin,
      max: yAxisMax,
      nameTextStyle: {
        padding: [0, 30, 0, 0],
        align: 'center',
        fontSize: 14,
        color: '#666',
      },
    },
    series: [
      {
        name: seriesName,
        type: 'line',
        smooth: true,
        data: [], // 空数据
        symbol: 'none',
        itemStyle: { color: colors[index % colors.length] },
        sampling: 'lttb',
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomLock: false,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
        preventDefaultMouseMove: true,
      },
      {
        type: 'slider',
        show: false,
        start: 0,
        end: 100,
        height: 12,
        bottom: 1,
      },
    ],
    animation: false,
  }
}

// 初始化图表
const initCharts = () => {
  // 清理已有的图表实例
  Object.values(chartInstances.value).forEach((chart: any) => {
    if (chart && typeof chart.dispose === 'function') chart.dispose()
  })
  chartInstances.value = {}

  // 使用 setTimeout 确保 DOM 已完全渲染
  setTimeout(() => {
    try {
      chartSeries.forEach((seriesName, index) => {
        if (!chartRefs.value[index]) return

        // 确保 DOM 元素有尺寸
        const el = chartRefs.value[index]
        if (!el.offsetWidth || !el.offsetHeight) return

        const chart = echarts.init(el)
        chartInstances.value[index] = chart

        // 设置图表配置
        chart.setOption(getChartOptions(seriesName, index))
      })
    } catch (error) {
      console.error('初始化空图表失败:', error)
    }
  }, 100)
}

// 组件挂载时初始化图表
onMounted(() => {
  initCharts()
})

// 组件卸载时销毁图表实例
onUnmounted(() => {
  Object.values(chartInstances.value).forEach((chart: any) => {
    if (chart && typeof chart.dispose === 'function') {
      try {
        chart.dispose()
      } catch (e) {
        console.error('销毁图表失败:', e)
      }
    }
  })
  chartInstances.value = {}
})
</script>

<style scoped>
.chart-container.show {
  opacity: 1;
  transform: translateY(0);
}
</style>
