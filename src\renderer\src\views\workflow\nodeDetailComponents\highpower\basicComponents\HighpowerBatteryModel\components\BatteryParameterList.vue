<template>
  <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
    <div class="flex flex-row items-center justify-between p-2 border-b">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <Badge variant="secondary">{{ parameters.length }} 个参数</Badge>
    </div>
    <div class="p-2">
      <div
        v-if="parameters && parameters.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2"
      >
        <div
          v-for="(param, index) in parameters"
          :key="index"
          class="relative flex flex-col p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all bg-white dark:bg-gray-800"
        >
          <Button
            v-if="editable"
            variant="ghost"
            size="icon"
            class="absolute top-2 right-2 h-6 w-6 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
            @click="$emit('edit', index)"
          >
            <LucideIcon name="Edit2" class="h-3.5 w-3.5" />
          </Button>

          <div class="flex flex-col space-y-1 pr-6">
            <h4
              class="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1"
              :title="param.zh_description"
            >
              {{ param.zh_description || '--' }}
            </h4>
            <p
              class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2"
              :title="param.en_description"
            >
              {{ param.en_description || '--' }}
            </p>
          </div>

          <div class="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700 space-y-1">
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-500 dark:text-gray-400">当前值:</span>
              <span class="text-xs font-medium text-orange-600 dark:text-orange-400">
                {{ param.value || '--' }}
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-500 dark:text-gray-400">参考范围:</span>
              <span class="text-xs text-blue-600 dark:text-blue-300">
                {{ param.min || '0' }} - {{ param.max || '1' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <EmptyState
        v-else
        :title="`暂无${title}`"
        description="请上传模型文件或从标定节点获取参数"
        icon="lsicon:list-outline"
        :icon-size="60"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon, EmptyState } from '@renderer/components'

interface Parameter {
  zh_description: string
  en_description: string
  value: number
  min: number
  max: number
  param_name: string
  is_recommended?: boolean
}

defineProps({
  title: {
    type: String,
    required: true,
  },
  parameters: {
    type: Array as () => Parameter[],
    default: () => [],
  },
  editable: {
    type: Boolean,
    default: true,
  },
})

defineEmits(['edit'])
</script>
