const path = require('path')
const grpc = require('@grpc/grpc-js')
const protoLoader = require('@grpc/proto-loader')
import logger from '../utils/logger'
function loadProto(protoFile) {
  let PROTO_PATH = ''
  if (process.env.NODE_ENV === 'production') {
    PROTO_PATH = path.join(__dirname, 'protos', protoFile)
  } else {
    PROTO_PATH = path.join(__dirname, 'protos', protoFile)
  }

  logger.info(`加载 proto 文件: ${PROTO_PATH} (环境: ${process.env.NODE_ENV})`)

  try {
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    })
    const protoDescriptor = grpc.loadPackageDefinition(packageDefinition)
    return protoDescriptor
  } catch (error) {
    logger.error(`加载 ${PROTO_PATH} 失败:`, error)
    throw error
  }
}

export function getProtocol(packageName) {
  switch (packageName.toLowerCase()) {
    case 'matt':
      return loadProto('matt.proto').matt
    case 'linker':
      return loadProto('linker.proto').linker
    case 'db':
      return loadProto('db.proto').db
    case 'common':
      return loadProto('common.proto').common
    default:
      throw new Error(`未知的包名: ${packageName}`)
  }
}

module.exports = { getProtocol }
