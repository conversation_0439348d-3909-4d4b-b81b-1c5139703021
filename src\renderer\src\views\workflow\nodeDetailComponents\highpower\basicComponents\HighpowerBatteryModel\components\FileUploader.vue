<template>
  <div>
    <FileDropUpload
      :accept-types="acceptTypes"
      :max-size="maxSize"
      @file-selected="handleFile"
      @error="$emit('error', $event)"
      @progress="$emit('progress', $event)"
      @upload-complete="$emit('upload-complete')"
    />
  </div>
</template>

<script setup lang="ts">
import { FileDropUpload } from '@renderer/components'

const props = defineProps({
  acceptTypes: {
    type: Array as () => string[],
    default: () => ['.model'],
  },
  maxSize: {
    type: Number,
    default: 20,
  },
})

const emit = defineEmits(['file-data', 'error', 'progress', 'upload-complete'])

// 处理文件上传
const handleFile = async (file: File) => {
  const reader = new FileReader()

  reader.onload = (e) => {
    try {
      // 获取文件内容的 ArrayBuffer
      const buffer = e.target?.result as ArrayBuffer

      // 发送文件数据
      emit('file-data', { file, buffer })
    } catch (error) {
      emit('error', error instanceof Error ? error.message : '文件读取失败')
    }
  }

  reader.onerror = () => {
    emit('error', '读取文件失败')
  }

  // 以 ArrayBuffer 格式读取文件
  reader.readAsArrayBuffer(file)
}
</script>
