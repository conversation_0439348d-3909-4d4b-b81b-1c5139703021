/**
 * 工作流画布操作
 */
import { useFlowsStore } from '@renderer/store'
import emitter from '@renderer/utils/mitt'
import { nanoid } from 'nanoid'

/**
 * 将工作流数据添加到画布
 * @param workflowId 工作流ID
 * @param nodes 节点数据
 * @param edges 边数据
 */
export function addWorkflowDataToCanvas(workflowId: string, nodes: any[], edges: any[] = []): void {
  if (!workflowId) {
    window.logger.error('无法添加节点：未提供工作流ID')
    return
  }

  try {
    window.logger.info(`开始添加节点到画布，节点数量: ${nodes.length}`)

    const flowsStore = useFlowsStore()

    // 获取当前工作流
    const workflow = flowsStore.getWorkflow(workflowId)

    // 确保工作流存在
    if (!workflow) {
      flowsStore.createWorkflow(workflowId)
    }

    // 获取现有工作流数据
    const existingWorkflow = flowsStore.getWorkflow(workflowId)

    // 计算新节点的偏移位置，避免重叠
    const baseOffset = { x: 100, y: 100 }
    const existingNodesCount = existingWorkflow.nodes?.length || 0

    // 创建节点ID映射表，用于连线
    const nodeIdMap = {}

    // 处理节点数据
    const processedNodes = nodes.map((node) => {
      // 生成新的唯一ID
      const newId = nanoid()

      // 将原始ID映射到新ID
      if (node.id) {
        nodeIdMap[node.id] = newId
      }

      // 计算新位置，根据现有节点数量添加偏移
      const position = {
        x: (node.position?.x || 100) + baseOffset.x + existingNodesCount * 20,
        y: (node.position?.y || 100) + baseOffset.y + existingNodesCount * 20,
      }

      // 保留原始节点类型，如果没有则使用 'custom'
      const nodeType = node.type || 'custom'

      // 返回完整的节点结构
      return {
        ...node,
        id: newId,
        originalId: node.id, // 保存原始ID，用于连线匹配
        position,
        type: nodeType, // 确保使用已注册的节点类型
        sourcePosition: 'right', // 添加源连接点位置
        targetPosition: 'left', // 添加目标连接点位置
        data: {
          ...node.data,
          originalId: node.id, // 在data中也保存原始ID
          workflowId: workflowId, // 添加工作流ID
          params: node.data?.params || {}, // 确保params对象存在
          label: node.data?.label || '未命名节点', // 确保有标签
          description: node.data?.description || '', // 确保有描述
          icon: node.data?.icon || {
            // 确保有图标
            type: 'icon',
            value: 'flowbite:draw-square-outline',
          },
        },
      }
    })

    // 处理现有的边数据，更新节点ID引用
    const processedEdges = edges.map((edge, index) => {
      // 映射源节点和目标节点ID
      const source = nodeIdMap[edge.source] || edge.source
      const target = nodeIdMap[edge.target] || edge.target

      return {
        id: `edge-${Date.now()}-${index}`,
        source,
        target,
        type: 'bezier',
        animated: false,
        style: {
          strokeWidth: 2,
          stroke: '#000000',
        },
        markerEnd: {
          type: 'arrowclosed',
          width: 10,
          height: 10,
          color: '#000000',
        },
      }
    })

    // 根据节点的outputType自动创建连线
    const autoGeneratedEdges = []

    // 遍历所有节点，检查outputType和其他节点的type是否匹配
    for (const sourceNode of processedNodes) {
      const outputTypes = sourceNode.data.outputType || []

      // 跳过没有输出类型的节点
      if (!outputTypes.length) continue

      // 遍历所有可能的目标节点
      for (const targetNode of processedNodes) {
        // 跳过自己
        if (sourceNode.id === targetNode.id) continue

        // 检查目标节点的类型是否在源节点的outputType中
        const targetType = targetNode.data.type
        if (outputTypes.includes(targetType)) {
          // 创建新的边
          autoGeneratedEdges.push({
            id: `edge-auto-${Date.now()}-${autoGeneratedEdges.length}`,
            source: sourceNode.id,
            target: targetNode.id,
            type: 'bezier',
            animated: true,
            style: {
              strokeWidth: 2,
              stroke: '#000000',
            },
            markerEnd: {
              type: 'arrowclosed',
              width: 10,
              height: 10,
              color: '#000000',
            },
          })

          window.logger.info(`自动创建连线: ${sourceNode.data.label} -> ${targetNode.data.label}`)
        }
      }
    }

    // 合并所有边
    const allEdges = [...processedEdges, ...autoGeneratedEdges]

    // 添加节点和边到画布
    addNodesWithAnimation(
      workflowId,
      processedNodes,
      allEdges,
      existingWorkflow.nodes,
      existingWorkflow.edges,
      flowsStore,
    )
  } catch (error) {
    window.logger.error('添加工作流数据到画布失败:', error)
  }
}

/**
 * 使用动画效果添加节点
 * @param workflowId 工作流ID
 * @param nodes 要添加的节点数据
 * @param edges 要添加的边数据
 * @param existingNodes 现有节点数组
 * @param existingEdges 现有边数组
 * @param flowsStore 流程存储实例
 */
function addNodesWithAnimation(
  workflowId: string,
  nodes: any[],
  edges: any[],
  existingNodes: any[],
  existingEdges: any[],
  flowsStore: any,
): void {
  const delay = 1000 // 每个节点添加的延迟时间(毫秒)

  // 递归添加节点的函数
  const addNodeWithDelay = (index: number) => {
    if (index >= nodes.length) {
      // 所有节点添加完成后，添加边
      const updatedNodes = [...existingNodes, ...nodes]
      const updatedEdges = [...existingEdges, ...edges]

      // 立即保存更新后的工作流数据
      flowsStore.saveWorkflow(workflowId, {
        nodes: updatedNodes,
        edges: updatedEdges,
      })

      // 触发工作流更新事件，只使用 emitter
      window.logger?.info(`触发工作流更新事件: workflowId=${workflowId}`)

      // 使用 emitter 触发事件
      emitter.emit('workflow-updated', {
        workflowId,
        detail: { workflowId },
      })

      return
    }

    // 延迟添加下一个节点
    setTimeout(() => addNodeWithDelay(index + 1), delay)
  }

  // 开始添加第一个节点
  addNodeWithDelay(0)
}
