import CryptoJS from 'crypto-js'

/**
 * 加密工具类
 */
export class CryptoHelper {
  /**
   * 加密数据
   * @param data 要加密的数据
   * @param key 加密密钥
   * @returns 加密后的字符串
   */
  static encrypt(data: any, key: string): string {
    if (data === undefined || data === null) return data
    try {
      return CryptoJS.AES.encrypt(JSON.stringify(data), key).toString()
    } catch (error) {
      console.error('加密失败:', error)
      return ''
    }
  }

  /**
   * 解密数据
   * @param ciphertext 密文
   * @param key 解密密钥
   * @returns 解密后的原始数据
   */
  static decrypt(ciphertext: string, key: string): any {
    if (!ciphertext) return ciphertext
    try {
      const bytes = CryptoJS.AES.decrypt(ciphertext, key)
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8))
    } catch (error) {
      console.warn('解密失败:', error)
      return ciphertext
    }
  }

  /**
   * 默认密钥派生函数
   * @param rawKey 原始密钥
   * @returns 派生后的密钥
   */
  static defaultKeyDerivation(rawKey: string): string {
    return CryptoJS.SHA256(rawKey).toString()
  }
}
