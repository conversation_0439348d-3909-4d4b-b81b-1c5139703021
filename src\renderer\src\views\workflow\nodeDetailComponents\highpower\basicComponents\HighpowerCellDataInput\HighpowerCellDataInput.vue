<template>
  <div>
    <Card class="">
      <CardHeader>
        <CardTitle class="flex justify-between dark:text-white/80">
          <span class="text-2xl font-bold">电芯数据输入</span>
          <div class="flex gap-2">
            <Button
              v-if="!isParamsExtract"
              variant="default"
              size="sm"
              :disabled="isExtract"
              @click="extractParmas"
            >
              <FileText v-if="!isExtract" class="mr-1 h-4 w-4" />
              <span v-if="isExtract" class="animate-spin mr-1">
                <RefreshCw class="h-4 w-4" />
              </span>
              {{ isExtract ? '提取中...' : '模型参数提取' }}
            </Button>
            <Button v-if="isParamsExtract" variant="outline" size="sm" @click="handleEditExtract">
              <RotateCcw class="mr-1 h-4 w-4" />
              重新输入
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <!-- 模型参数提取进度条 -->
        <div v-if="isExtract" class="mb-4 space-y-2">
          <div class="flex justify-between text-sm">
            <span>模型构建进度</span>
            <span>{{ buildProgress }}%</span>
          </div>
          <Progress v-model="buildProgress" class="w-full" />
        </div>

        <!-- 模型构建完成提示 -->
        <Alert
          v-if="isParamsExtract"
          class="mb-4 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-300"
        >
          <CheckCircle class="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle class="text-green-800 dark:text-green-300 font-medium">
            模型参数提取完成
          </AlertTitle>
          <AlertDescription class="text-green-700 dark:text-green-400">
            模型参数已成功提取，可以进行后续操作。
          </AlertDescription>
        </Alert>

        <div class="">
          <Tabs v-model="currentTab" :default-value="tabMapping[0].value" class="space-y-2">
            <div class="relative">
              <div class="overflow-x-auto scrollbar">
                <TabsList class="flex w-max space-x-2">
                  <TabsTrigger v-for="(tab, index) in tabMapping" :key="index" :value="tab.value">
                    {{ tab.label }}
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            <!-- 材料参数 -->
            <TabsContent value="MaterialParameters">
              <ParameterPanel
                title="材料参数"
                :parameters="materialParameters"
                :open-states="materialOpenStates"
                :is-resetting="isResetting.MaterialParameters"
                :is-params-extract="isParamsExtract"
                category-type="MaterialParameters"
                @reset="resetTabData('MaterialParameters')"
                @file-selected="(file) => handleFile(file, 'MaterialParameters')"
                @param-change="handleParamChangeGrouped"
                @upload-error="handleUploadError"
                @upload-progress="handleProgress"
                @upload-complete="handleUploadComplete"
                @update:open-states="(states) => (materialOpenStates = states)"
              />
            </TabsContent>

            <!-- 几何参数 -->
            <TabsContent value="GeometryParameters">
              <ParameterPanel
                title="几何参数"
                :parameters="geometryParameters"
                :open-states="geometryOpenStates"
                :is-resetting="isResetting.GeometryParameters"
                :is-params-extract="isParamsExtract"
                category-type="GeometryParameters"
                @reset="resetTabData('GeometryParameters')"
                @file-selected="(file) => handleFile(file, 'GeometryParameters')"
                @param-change="handleParamChangeGrouped"
                @upload-error="handleUploadError"
                @upload-progress="handleProgress"
                @upload-complete="handleUploadComplete"
                @update:open-states="(states) => (geometryOpenStates = states)"
              />
            </TabsContent>

            <!-- SOC-OCV曲线部分 -->
            <TabsContent value="SOCOCVCurve">
              <SOCOCVChartPanel
                ref="SOCOCVChartRef"
                title="SOC-OCV曲线"
                :is-resetting-positive="isResetting.SOCOCVPositive"
                :is-resetting-negative="isResetting.SOCOCVNegative"
                :is-params-extract="isParamsExtract"
                :positive-upload-stored-id="storedData.id.SOCOCVPositive.value"
                :negative-upload-stored-id="storedData.id.SOCOCVNegative.value"
                @reset="resetTabData('SOCOCVCurve')"
                @reset-positive="resetTabData('SOCOCVPositive')"
                @reset-negative="resetTabData('SOCOCVNegative')"
                @file-selected-positive="(file) => handleFile(file, 'SOCOCVPositive')"
                @file-selected-negative="(file) => handleFile(file, 'SOCOCVNegative')"
                @upload-error="handleUploadError"
                @upload-progress="handleProgress"
                @upload-complete="handleUploadComplete"
                @chart-ready="handleChartReady"
              />
            </TabsContent>

            <!-- 电池循环数据 -->
            <TabsContent value="CycleData">
              <CycleDataChartPanel
                ref="cycleDataChartRef"
                title="电池循环数据"
                :is-resetting="isResetting.CycleData"
                :is-params-extract="isParamsExtract"
                :cycle-params="cycleParams"
                :upload-stored-id="storedData.id.CycleData.value"
                @reset="resetTabData('CycleData')"
                @file-selected="(file) => handleFile(file, 'CycleData')"
                @upload-error="handleUploadError"
                @upload-progress="handleProgress"
                @upload-complete="handleUploadComplete"
                @chart-ready="handleChartReady"
                @update:cycle-params="handleCycleParamsUpdate"
              />
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { useTaskStore } from '@renderer/store'
import { getNodeParams, saveNodeParams } from '@renderer/utils/nodeUtils'
import { inputParse } from '@renderer/utils/rpcParser'
import { useIntervalFn } from '@vueuse/core'
import { CheckCircle, FileText, RefreshCw, RotateCcw } from 'lucide-vue-next'
import { nextTick, onMounted, ref, watch } from 'vue'
//import { fileURLToPath } from 'url'
import { IndexedDBHelper } from '@renderer/database/IndexedDBHelper'
import { nanoid } from 'nanoid'
import { toast } from 'vue-sonner'
import * as XLSX from 'xlsx'
import { CycleDataChartPanel, ParameterPanel, SOCOCVChartPanel } from './components'
import { EXPECTED_GEOMETRY_PARAMETERS, EXPECTED_MATERIAL_PARAMETERS } from './constants'

const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

const taskStore = useTaskStore()
const taskService = createTaskService()
const cycleDataChartRef = ref(null)

const SOCOCVChartRef = ref(null)

const storedData = {
  id: {
    CycleData: ref(''),
    SOCOCVPositive: ref(''),
    SOCOCVNegative: ref(''),
  },
  path: {
    CycleData: ref(''),
    Capacity: ref(''),
    SOCOCVPositive: ref(''),
    SOCOCVNegative: ref(''),
  },
}

// 数据状态
const materialParameters = ref({})
const geometryParameters = ref({})
// const socOcvPositive = ref([])
// const socOcvNegative = ref([])
const cycleData = ref([])
// const cycleDataStoredPath = ref('')
// const cycleDataStoredId = ref('')
const material_data = ref([])
const geometry_data = ref([])
// 循环参数状态
const cycleParams = ref({
  startCycleType: 'auto',
  startCycleValue: 0,
})

// UI状态
const isUploading = ref(false)
const dataLoaded = ref(false)
const isExtract = ref(false)
const isParamsExtract = ref(false)
const buildProgress = ref(0)
const materialOpenStates = ref([])
const geometryOpenStates = ref([])
const currentTab = ref('MaterialParameters')

const dbHelper = new IndexedDBHelper('uploadDatabase', 1)

// 重置状态
const isResetting = ref({
  MaterialParameters: false,
  GeometryParameters: false,
  SOCOCVCurve: false,
  SOCOCVPositive: false,
  SOCOCVNegative: false,
  CycleData: false,
})

// 图表实例
let socOcvChart = null
let cycleChart = null

// 标签映射
const tabMapping = [
  { label: '材料参数', value: 'MaterialParameters' },
  { label: '几何参数', value: 'GeometryParameters' },
  { label: 'SOC-OCV曲线', value: 'SOCOCVCurve' },
  { label: '电池循环数据', value: 'CycleData' },
]

// 数据格式化
const formatData = (data) => {
  return Object.values(data).flatMap((items) =>
    items
      .filter((item) => item.value !== '' && item.value !== null && item.value !== undefined)
      .map((item) => ({ [item.variableName]: item.value })),
  )
}

// 保存所有参数
const saveAllParams = async () => {
  try {
    // 获取当前已保存的参数
    const currentParams = await getNodeParams(props.nodeData as any)

    material_data.value = formatData(materialParameters.value)
    geometry_data.value = formatData(geometryParameters.value)
    // 获取循环参数
    const currentCycleParams = cycleDataChartRef.value?.cycleParams || cycleParams.value

    const storedDataObj = {
      id: {
        CycleData: storedData.id.CycleData.value,
        SOCOCVPositive: storedData.id.SOCOCVPositive.value,
        SOCOCVNegative: storedData.id.SOCOCVNegative.value,
      },
      path: {
        CycleData: storedData.path.CycleData.value,
        SOCOCVPositive: storedData.path.SOCOCVPositive.value,
        SOCOCVNegative: storedData.path.SOCOCVNegative.value,
        Capacity: storedData.path.Capacity.value,
      },
    }

    // 只更新当前组件相关的参数，不覆盖其他参数
    const paramsToUpdate = {
      MaterialParameters: JSON.stringify(materialParameters.value),
      GeometryParameters: JSON.stringify(geometryParameters.value),
      storedData: JSON.stringify(storedDataObj),
      material_data: JSON.stringify(material_data.value),
      geometry_data: JSON.stringify(geometry_data.value),
      isParamsExtract: isParamsExtract.value,
      cycleParams: JSON.stringify(currentCycleParams),
    }

    // 合并参数，确保不覆盖不相关的参数（如 model_base_params）
    await saveNodeParams(props.nodeData as any, {
      ...currentParams, // 保留所有现有参数
      ...paramsToUpdate, // 只更新当前组件的参数
    })
  } catch (error) {
    console.error('保存参数失败:', error)
  }
}

// 使用 watchDebounced 监听数据变化，自动保存
// watchDebounced(
//   [
//     materialParameters,
//     geometryParameters,
//     // socOcvPositive,
//     // socOcvNegative,
//     isParamsExtract,
//     cycleParams,
//     storedData.id.CycleData,
//     storedData.id.SOCOCVPositive,
//     storedData.id.SOCOCVNegative,
//     storedData.path.CycleData,
//     storedData.path.SOCOCVPositive,
//     storedData.path.SOCOCVNegative,
//     storedData.path.Capacity,
//   ],
//   () => {
//     saveAllParams()
//   },
//   { debounce: 500, maxWait: 1000 },
// )
watch(
  [
    materialParameters,
    geometryParameters,
    isParamsExtract,
    cycleParams,
    storedData.id.CycleData,
    storedData.id.SOCOCVPositive,
    storedData.id.SOCOCVNegative,
    storedData.path.CycleData,
    storedData.path.SOCOCVPositive,
    storedData.path.SOCOCVNegative,
    storedData.path.Capacity,
  ],
  (newValue, oldValue) => {
    // console.log('debug watch')
    // console.log('new', newValue)
    // console.log('old', oldValue)
    // 执行操作
  },
  { immediate: false, deep: false }, // 可选配置
)

// 模型参数提取
const extractParmas = () => {
  // 检查必要数据
  if (
    Object.keys(materialParameters.value).length === 0 ||
    Object.keys(geometryParameters.value).length === 0
  ) {
    toast.warning('无法提取模型参数', {
      description: '请先上传材料参数和几何参数文件。',
    })
    return
  }
  // console.log(storedData?.path?.CycleData.value, '----', storedData?.path?.Capacity.value)
  if (storedData?.path?.CycleData.value === '' || !storedData?.path?.Capacity.value === '') {
    toast.warning('无法提取模型参数', {
      description: '请先上传电池循环数据文件。',
    })
    return
  }

  // if (!cycleData.value || cycleData.value.length === 0) {
  //   toast.warning('无法提取模型参数', {
  //     description: '请先上传电池循环数据文件。',
  //   })
  //   return
  // }

  // if (socOcvPositive.value.length === 0 || socOcvNegative.value.length === 0) {
  //   toast.warning('无法提取模型参数', {
  //     description: '请先上传SOC-OCV曲线文件。',
  //   })
  //   return
  // }
  if (
    storedData?.path?.SOCOCVPositive.value === '' ||
    storedData?.path?.SOCOCVNegative.value === ''
  ) {
    toast.warning('无法提取模型参数', {
      description: '请先上传SOC-OCV曲线文件。',
    })
    return
  }

  if (isExtract.value) return

  isExtract.value = true
  buildProgress.value = 0

  // 模拟进度更新
  const { pause } = useIntervalFn(() => {
    buildProgress.value += Math.floor(Math.random() * 10) + 1

    if (buildProgress.value >= 100) {
      buildProgress.value = 100
      pause()

      setTimeout(() => {
        isExtract.value = false
        isParamsExtract.value = true

        material_data.value = formatData(materialParameters.value)
        geometry_data.value = formatData(geometryParameters.value)

        nextTick(() => {
          getHpParserUserInputData()
        })

        // 保存参数时保留现有数据
        getNodeParams(props.nodeData as any)
          .then((currentParams) => {
            return saveNodeParams(props.nodeData as any, {
              ...currentParams, // 保留所有现有参数
              isParamsExtract: true,
              material_data: JSON.stringify(material_data.value),
              geometry_data: JSON.stringify(geometry_data.value),
            })
          })
          .catch((error) => {
            console.error('保存参数失败:', error)
          })

        toast.success('模型参数提取成功', {
          description: '电芯输入模型参数已经提取成功',
        })
      }, 500)
    }
  }, 200)
}

// 重新提取模型参数
const handleEditExtract = async () => {
  isParamsExtract.value = false

  try {
    // 获取当前所有参数，避免覆盖其他数据
    const currentParams = await getNodeParams(props.nodeData as any)

    await saveNodeParams(props.nodeData as any, {
      ...currentParams, // 保留所有现有参数
      isParamsExtract: false,
      material_data: '',
      geometry_data: '',
      storedData: '',
    })

    toast.success('操作成功', {
      description: '您可以修改参数并重新提取模型参数',
    })
  } catch (error) {
    console.error('保存参数失败:', error)
    toast.error('操作失败', {
      description: '保存参数时发生错误',
    })
  }
}

// 处理分组参数变更
const handleParamChangeGrouped = (category, groupName, index, value) => {
  if (category === 'MaterialParameters') {
    materialParameters.value[groupName][index].value = value
  } else if (category === 'GeometryParameters') {
    geometryParameters.value[groupName][index].value = value
  }
  saveAllParams().catch((error) => {
    console.error('保存参数失败:', error)
  })
}

// 重置tab数据
const resetTabData = (category) => {
  isResetting.value[category] = true

  setTimeout(() => {
    if (category === 'MaterialParameters') {
      materialParameters.value = {}
    } else if (category === 'GeometryParameters') {
      geometryParameters.value = {}
    } else if (category === 'SOCOCVCurve') {
      // socOcvPositive.value = []
      // socOcvNegative.value = []
      storedData.id.SOCOCVPositive.value = ''
      storedData.id.SOCOCVNegative.value = ''
      storedData.path.SOCOCVPositive.value = ''
      storedData.path.SOCOCVNegative.value = ''
      storedData.path.Capacity.value = ''
      if (socOcvChart) {
        if (socOcvChart.positive) socOcvChart.positive.dispose()
        if (socOcvChart.negative) socOcvChart.negative.dispose()
        socOcvChart = null
      }
    } else if (category === 'SOCOCVPositive') {
      //socOcvPositive.value = []
      storedData.id.SOCOCVPositive.value = ''
      storedData.path.SOCOCVPositive.value = ''
      if (socOcvChart && socOcvChart.positive) {
        socOcvChart.positive.dispose()
        socOcvChart.positive = null
      }
    } else if (category === 'SOCOCVNegative') {
      storedData.id.SOCOCVNegative.value = ''
      storedData.path.SOCOCVNegative.value = ''
      if (socOcvChart && socOcvChart.negative) {
        socOcvChart.negative.dispose()
        socOcvChart.negative = null
      }
    } else if (category === 'CycleData') {
      storedData.id.CycleData.value = ''
      storedData.path.CycleData.value = ''
      storedData.path.Capacity.value = ''
      //cycleData.value = []
      if (cycleChart) {
        if (cycleChart.current) cycleChart.current.dispose()
        if (cycleChart.voltage) cycleChart.voltage.dispose()
        if (cycleChart.capacity) cycleChart.capacity.dispose()
        cycleChart = null
      }
    }

    saveAllParams().catch((error) => {
      console.error('保存参数失败:', error)
    })
    isResetting.value[category] = false
  }, 0)
}

// 处理图表准备就绪
const handleChartReady = (chartInstance) => {
  if (currentTab.value === 'SOCOCVCurve') {
    socOcvChart = chartInstance
  } else if (currentTab.value === 'CycleData') {
    cycleChart = chartInstance
  }
}

// 处理CSV文件
const handleCSVFile = async (data, category) => {
  try {
    const workbook = XLSX.read(data, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (jsonData.length > 0) {
      const headers = jsonData[0]
      const hasMetadataRows =
        jsonData.length > 2 && jsonData[1].some((val) => typeof val === 'string' && isNaN(val))
      const dataStartRow = hasMetadataRows ? 3 : 1
      const dataRows = jsonData.slice(dataStartRow)

      // 获取单位信息
      const unitRow = hasMetadataRows ? jsonData[2] : null
      const capacityUnit = unitRow ? unitRow[headers.indexOf('capacity')] : 'Ah'

      return dataRows.map((row) => {
        const obj = {}
        headers.forEach((header, index) => {
          if (row[index] !== undefined) {
            let value = isNaN(row[index]) ? row[index] : parseFloat(row[index])
            // 如果是容量字段且单位为mAh，则进行转换
            if (header.toLowerCase() === 'capacity' && capacityUnit.toLowerCase() === 'mah') {
              value = value / 1000
            }
            obj[header.toLowerCase()] = value
          }
        })
        return obj
      })
    }
    return []
  } catch (error) {
    toast.error('处理CSV文件时出错', {
      description: error.message,
    })
    throw error
  }
}

// 参数校验辅助函数
const validateParameters = (dataRows, expectedStructure, fileTypeDescription) => {
  const parsedFileCategories = {}
  let currentCategoryFromFile = ''

  for (const row of dataRows) {
    if (row.category) {
      currentCategoryFromFile = String(row.category).trim()
    }
    if (!currentCategoryFromFile) continue

    const paramName = row.parameter ? String(row.parameter).trim() : ''
    const varName = row.variableName ? String(row.variableName).trim() : ''

    if (!paramName && !varName) continue

    if (!parsedFileCategories[currentCategoryFromFile]) {
      parsedFileCategories[currentCategoryFromFile] = []
    }

    parsedFileCategories[currentCategoryFromFile].push({
      parameter: paramName,
      variableName: varName,
    })
  }

  // 检查文件中的类别和参数是否符合预期
  for (const categoryNameInFile in parsedFileCategories) {
    if (!Object.prototype.hasOwnProperty.call(expectedStructure, categoryNameInFile)) {
      toast.error('文件参数校验失败', {
        description: `${fileTypeDescription} 文件中包含无效的参数类别: "${categoryNameInFile}"。`,
      })
      return false
    }

    const paramsInFile = parsedFileCategories[categoryNameInFile]
    const expectedParamsForCategory = expectedStructure[categoryNameInFile]

    for (const paramInFile of paramsInFile) {
      const expectedParam = expectedParamsForCategory.find(
        (p) => p.parameter === paramInFile.parameter,
      )

      if (!expectedParam) {
        toast.error('文件参数校验失败', {
          description: `${fileTypeDescription} 文件类别 "${categoryNameInFile}" 中包含未知参数: "${paramInFile.parameter}"。`,
        })
        return false
      }

      if (expectedParam.variableName !== paramInFile.variableName) {
        toast.error('文件参数校验失败', {
          description: `${fileTypeDescription} 文件类别 "${categoryNameInFile}" 中参数 "${paramInFile.parameter}" 的变量名不匹配 (预期: "${expectedParam.variableName}", 文件中: "${paramInFile.variableName}")。`,
        })
        return false
      }
    }

    // 检查是否缺少预期的参数
    for (const expectedParam of expectedParamsForCategory) {
      const paramInFile = paramsInFile.find((p) => p.parameter === expectedParam.parameter)
      if (!paramInFile) {
        toast.error('文件参数校验失败', {
          description: `${fileTypeDescription} 文件类别 "${categoryNameInFile}" 中缺少预期的参数: "${expectedParam.parameter}"。`,
        })
        return false
      }
    }
  }

  // 检查是否缺少预期的类别
  for (const expectedCategoryName in expectedStructure) {
    if (!Object.hasOwn(parsedFileCategories, expectedCategoryName)) {
      toast.error('文件参数校验失败', {
        description: `${fileTypeDescription} 文件中缺少预期的参数类别: "${expectedCategoryName}"。`,
      })
      return false
    }
  }

  return true
}

// 校验CSV数据
const validateHeader = (data, expectedHeader) => {
  const workbook = XLSX.read(data, { type: 'array' })
  const sheetName = workbook.SheetNames[0]
  const worksheet = workbook.Sheets[sheetName]
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

  if (!jsonData.length) return false

  const header = jsonData[0].map((h) => String(h).trim().toLowerCase())
  return expectedHeader.every((h, i) => header[i] === h) && header.length === expectedHeader.length
}

// 处理文件上传
const handleFile = async (file, category) => {
  if (category === 'CycleData' || category === 'SOCOCVPositive' || category === 'SOCOCVNegative') {
    //await cycleDataVerify(file, category)
    await uploadCsvFile(file, category)
    return
  }
  const reader = new FileReader()
  reader.onload = async (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      if (category === 'MaterialParameters' || category === 'GeometryParameters') {
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: ['category', 'parameter', 'variableName', 'value', 'unit'],
        })

        const dataRows = jsonData.slice(1)
        const expectedStructure =
          category === 'MaterialParameters'
            ? EXPECTED_MATERIAL_PARAMETERS
            : EXPECTED_GEOMETRY_PARAMETERS
        const fileTypeDescription = category === 'MaterialParameters' ? '材料参数' : '几何参数'

        if (!validateParameters(dataRows, expectedStructure, fileTypeDescription)) {
          isResetting.value[category] = false
          return
        }

        const groupedData = {}
        let currentCategory = ''

        dataRows.forEach((row) => {
          let value = row.value
          if (typeof value === 'string' && value.includes('%')) {
            value = parseFloat(value.replace('%', ''))
          }
          if (value === '/') value = ''
          if (row.category) currentCategory = row.category
          if (!groupedData[currentCategory]) groupedData[currentCategory] = []

          groupedData[currentCategory].push({
            category: currentCategory,
            parameter: row.parameter || '',
            variableName: row.variableName || '',
            value: value,
            unit: row.unit || '',
            selected: false,
          })
        })

        if (category === 'MaterialParameters') {
          materialParameters.value = groupedData
          materialOpenStates.value = new Array(Object.keys(groupedData).length).fill(true)
        } else {
          geometryParameters.value = groupedData
          geometryOpenStates.value = new Array(Object.keys(groupedData).length).fill(true)
        }
      } else if (category === 'SOCOCVCurve' || category === 'SOCOCVPositive') {
        // 校验SOC-OCV表头
        // if (!validateHeader(data, ['soc', 'ocv'])) {
        //   toast.error('文件上传失败', {
        //     description: '文件不符合规范请重新上传',
        //   })
        //   isResetting.value[category] = false
        //   return
        // }
        // const csvData = await handleCSVFile(data, category)
        // socOcvPositive.value = csvData.map((row) => ({
        //   soc: row.soc || 0,
        //   ocv: row.ocv || 0,
        // }))
      } else if (category === 'SOCOCVNegative') {
        // const csvData = await handleCSVFile(data, category)
        // socOcvNegative.value = csvData.map((row) => ({
        //   soc: row.soc || 0,
        //   ocv: row.ocv || 0,
        // }))
      } else if (category === 'CycleData') {
        // 校验表头
        // if (!validateHeader(data, ['cycle', 'time', 'current', 'voltage', 'capacity'])) {
        //   toast.error('文件上传失败', {
        //     description: '文件不符合规范请重新上传',
        //   })
        //   isResetting.value[category] = false
        //   return
        // }
        // const csvData = await handleCSVFile(data, category)
        // console.log(csvData)
        // cycleData.value = csvData.map((row) => ({
        //   cycle: row.cycle || 0,
        //   time: row.time || 0,
        //   current: row.current || 0,
        //   voltage: row.voltage || 0,
        //   capacity: row.capacity || 0,
        // }))
      }

      saveAllParams().catch((error) => {
        console.error('保存参数失败:', error)
      })
      isResetting.value[category] = false
    } catch (error) {
      toast.error('文件处理失败', {
        description: error.message,
      })
    }
  }

  reader.readAsArrayBuffer(file)
}
// const worker = new Worker(new URL('./components/uploadWebWorker.ts', import.meta.url), {
//   type: 'module',
// })
// const fileService = new FileService()

const handleUploadResult = (data) => {
  if (data.status !== 'error') {
    switch (data.dataType) {
      case 'CycleData':
        cycleDataChartRef.value?.updateProgress(data.step, data.progress)
        break
      case 'SOCOCVPositive':
        SOCOCVChartRef.value?.updateProgress(data.step, data.progress, 'positive')
        break
      case 'SOCOCVNegative':
        SOCOCVChartRef.value?.updateProgress(data.step, data.progress, 'negative')
        break
      default:
        break
    }
    if (data.status === 'finished') {
      if (data.step === 'processData') {
        switch (data.dataType) {
          case 'CycleData':
            cycleDataChartRef.value?.setData(data.data)
            break
          case 'SOCOCVPositive':
            SOCOCVChartRef.value?.setData({ positive: data.data?.sococv })
            break
          case 'SOCOCVNegative':
            SOCOCVChartRef.value?.setData({ negative: data.data?.sococv })
            break
          default:
            break
        }
        dbHelper
          .open()
          .then(() => {
            const uploadData = {
              id: data.uploadId,
              data: data.data,
            }
            dbHelper.addData('UploadData', uploadData)
            storedData.id[data.dataType].value = data.uploadId
            saveAllParams().catch((error) => {
              console.error('保存参数失败:', error)
            })
          })
          .catch((error) => {
            console.error('Error opening database:', error)
          })
      } else if (data.step === 'upload') {
        if (data.data.stored_filepath) {
          storedData.path[data.dataType].value = data.data.stored_filepath
          saveAllParams().catch((error) => {
            console.error('保存参数失败:', error)
          })
        }
      }
    }
  }
}
const uploadCsvFile = async (file, dataType) => {
  //const cycleDataVerify = async (file, dataType) => {
  const uploadId = nanoid()
  const filePath = file.path
  const metadata = {}
  metadata.filePath = filePath
  metadata.uploadId = uploadId
  metadata.userId = '0'
  metadata.dataType = dataType
  try {
    // console.log(file, dataType)
    const response = await window.grpcApi.uploadCsvFile(metadata, handleUploadResult)
  } catch (err) {
    console.error('IPC send failed:', err.message || err)
  }
}

// 其他辅助方法
const handleProgress = (progress) => {}
const handleUploadComplete = () => {
  dataLoaded.value = true
}
const handleUploadError = (errorMsg) => {
  toast.error('文件上传错误', {
    description: errorMsg,
  })
}

// 获取豪鹏输入数据
const getHpParserUserInputData = async () => {
  try {
    const keyTypePairs = {
      material_data: 'Jsonarray',
      geometry_data: 'Jsonarray',
    }

    const keyValuePairs = {
      material_data: JSON.stringify(material_data.value),
      geometry_data: JSON.stringify(geometry_data.value),
    }

    const paramData = inputParse(keyValuePairs, keyTypePairs)
    const res = await taskService.callTask('hpParserUserInputData', '', paramData)
    if (res.status === 'Success') {
      // 获取当前所有参数，确保不会覆盖 elecInputData 和 agingInputData
      const currentParams = await getNodeParams(props.nodeData)

      await saveNodeParams(props.nodeData, {
        ...currentParams,
        model_base_params: res.result,
      })
    } else {
      toast.error('获取数据失败', {
        description: res.message,
      })
    }
  } catch (error) {
    toast.error('获取数据失败', {
      description: error.message,
    })
  }
}

// 初始化加载数据
const initData = async () => {
  try {
    const params = await getNodeParams(props.nodeData)
    // console.log('All parasm', params)

    if (Object.keys(params).length > 0) {
      try {
        materialParameters.value = JSON.parse(params.MaterialParameters || '{}')
        geometryParameters.value = JSON.parse(params.GeometryParameters || '{}')
        // socOcvPositive.value = JSON.parse(params.SOCOCVPositive || '[]')
        // socOcvNegative.value = JSON.parse(params.SOCOCVNegative || '[]')
        // console.log(params.storedData)
        cycleData.value = JSON.parse(params.CycleData || '{}')
        let storedOrigin = null
        if (params?.storedData) {
          storedOrigin = JSON.parse(params.storedData)
          storedData.id.CycleData.value = storedOrigin.id.CycleData
          storedData.id.SOCOCVPositive.value = storedOrigin.id.SOCOCVPositive
          storedData.id.SOCOCVNegative.value = storedOrigin.id.SOCOCVNegative

          storedData.path.CycleData.value = storedOrigin.path.CycleData
          storedData.path.SOCOCVPositive.value = storedOrigin.path.SOCOCVPositive
          storedData.path.SOCOCVNegative.value = storedOrigin.path.SOCOCVNegative
          storedData.path.Capacity.value = storedOrigin.path.Capacity
        }
        // JSON.parse(params?.storedData || '{}')
        // console.log(storedOrigin, '------------')
        // cycleDataStoredId.value = JSON.parse(params.CycleDataStoredId || '')
        // cycleDataStoredPath.value = JSON.parse(params.CycleDataStoredId || '')

        // 加载循环参数
        if (params.cycleParams) {
          cycleParams.value = JSON.parse(params.cycleParams)
        }

        if (params.material_data) {
          material_data.value = JSON.parse(params.material_data)
        }

        if (params.geometry_data) {
          geometry_data.value = JSON.parse(params.geometry_data)
        }

        dataLoaded.value = true
        isParamsExtract.value = params.isParamsExtract || false

        // 初始化展开状态
        materialOpenStates.value = new Array(Object.keys(materialParameters.value).length).fill(
          true,
        )
        geometryOpenStates.value = new Array(Object.keys(geometryParameters.value).length).fill(
          true,
        )
      } catch (error) {
        toast.error('加载数据失败', {
          description: error.message,
        })
      }
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

const uploadLargeDataStream = async (data, meta) => {
  const stream = createStream('grpc-upload-stream', meta)

  const chunkSize = 1000 * 1000
  for (let i = 0; i < data.length; i += chunkSize) {
    const chunk = data.slice(i, i + chunkSize)
    const buffer = Buffer.from(JSON.stringify(chunk), 'utf-8')
    stream.write(buffer)
  }

  stream.end()

  // window.ipcRenderer?.once('upload-status', (_event, result) => {
  //   if (result.status === 'error') {
  //     console.error('上传失败:', result.message)
  //   } else {
  //     console.log('上传完成:', result)
  //   }
  // })
}

onMounted(async () => {
  await initData()
})

// 处理循环参数更新
const handleCycleParamsUpdate = (newParams) => {
  cycleParams.value = { ...newParams }
  saveAllParams().catch((error) => {
    console.error('保存参数失败:', error)
  })
}
</script>

<style lang="scss" scoped>
:deep(.input) {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &[type='number'] {
    -moz-appearance: textfield;
  }
}
:deep(.collapsible-content) {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}
</style>
