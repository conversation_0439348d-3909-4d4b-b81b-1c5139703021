<template>
  <div class="flex h-screen overflow-hidden bg-background">
    <!-- <Sidebar /> -->
    <SessionSidebar v-if="mdAndUp" />
    <ChatMain @open-knowledge-reference="showKnowledgeReference" />
    <!-- <InfoPanel v-if="lgAndUp" /> -->
    <KnowledgeReferenceDialog
      :is-open="isKnowledgeReferenceOpen"
      @close="closeKnowledgeReference"
      @reference="handleKnowledgeReference"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useBreakpoints, breakpointsTailwind } from '@vueuse/core'
// import { Sidebar } from '../components'
import SessionSidebar from './components/SessionSidebar.vue'
import ChatMain from './components/ChatMain/index.vue'
import InfoPanel from './components/InfoPanel.vue'
import KnowledgeReferenceDialog from './components/KnowledgeReferenceDialog.vue'

import { useAIChatStore } from '@renderer/store'
const aiChatStore = useAIChatStore()

const breakpoints = useBreakpoints(breakpointsTailwind)
const mdAndUp = breakpoints.greaterOrEqual('md')
const lgAndUp = breakpoints.greaterOrEqual('lg')

const isKnowledgeReferenceOpen = ref(false)

const showKnowledgeReference = () => {
  isKnowledgeReferenceOpen.value = true
}

const closeKnowledgeReference = () => {
  isKnowledgeReferenceOpen.value = false
}

const handleKnowledgeReference = (file: any) => {
  console.log('引用文件:', file)
  aiChatStore.addKnowledgeReferenceToCurrentConversation(file)
  closeKnowledgeReference()
}
</script>

<style scoped></style>
