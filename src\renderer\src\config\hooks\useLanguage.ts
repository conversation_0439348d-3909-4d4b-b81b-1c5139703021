import { useSettingsStore } from '@renderer/store'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'

export function useLanguage() {
  const { locale, t } = useI18n()
  const settingsStore = useSettingsStore()

  // 从 store 初始化语言
  locale.value = settingsStore.language

  // 监听 store 中语言的变化
  watch(
    () => settingsStore.language,
    (newLanguage) => {
      locale.value = newLanguage
    },
    { immediate: true },
  )

  return {
    locale,
    t,
  }
}
