/**
 * 工作流处理器
 */
import { useWorkflowStore } from '@renderer/store'
import { showToast } from '../../connection/eventDispatcher'
import { addWorkflowDataToCanvas } from '../../workflow/canvas'

/**
 * 处理工作流生成
 * @param data 工作流生成数据
 */
export function handleWorkflowGeneration(data: any): void {
  try {
    window.logger.info('收到工作流生成请求:', data)

    // 确保有有效的参数
    if (!data.key_value_pairs || !data.key_value_pairs.call_params) {
      window.logger.warn('工作流生成请求缺少必要参数')
      return
    }

    // 解析工作流数据
    let workflowData
    try {
      // 如果是字符串，则解析为JSON
      if (typeof data.key_value_pairs.call_params === 'string') {
        workflowData = JSON.parse(data.key_value_pairs.call_params)
      } else {
        // 如果已经是对象，直接使用
        workflowData = data.key_value_pairs.call_params
      }
    } catch (error) {
      window.logger.error('解析工作流数据失败:', error)
      return
    }

    // 确保有工作流名称、节点和边
    if (!workflowData.workflow_name || !workflowData.nodes) {
      window.logger.warn('工作流数据格式不正确:', workflowData)
      return
    }

    // 获取当前工作流ID
    const workflowStore = useWorkflowStore()
    const currentWorkflowId = workflowStore.currentWfId

    if (!currentWorkflowId) {
      window.logger.warn('未找到当前工作流ID，无法添加节点')
      return
    }

    // 添加到画布
    window.logger.info(`开始将 "${workflowData.workflow_name}" 添加到画布...`)
    addWorkflowDataToCanvas(currentWorkflowId, workflowData.nodes, workflowData.edges || [])

    // 通知用户
    showToast('工作流已生成', `"${workflowData.workflow_name}" 已添加到画布`, 'success')
  } catch (error) {
    window.logger.error('处理工作流生成失败:', error)
  }
}
