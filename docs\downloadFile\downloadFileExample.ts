import { FileService } from '../config/api/grpc/fileService'

/**
 * downloadFile 使用示例
 *
 * 这个示例展示了如何使用 FileService 的 downloadFile 方法
 * 用户只需要提供文件路径，其他所有操作都会自动处理
 */

// 创建 FileService 实例
const fileService = new FileService()

/**
 * 示例1: 基本文件下载
 */
export async function basicDownloadExample() {
  try {
    console.log('=== 基本文件下载示例 ===')

    // 用户只需要提供存储的文件路径
    const storedFilepath = '1/W_7swJXiBW_EoL.ml'

    // 调用下载方法，自动处理用户认证和分块下载
    const result = await fileService.downloadFile(
      storedFilepath,
      // 可选的进度回调
      (progress, fileName) => {
        console.log(`下载进度: ${progress.toFixed(1)}%`)
        if (fileName) {
          console.log(`文件名: ${fileName}`)
        }
      },
    )

    console.log('下载完成!')
    console.log(`文件名: ${result.fileName}`)
    console.log(`文件大小: ${result.content.length} 字节`)
    console.log(`SHA256: ${result.sha256}`)

    // 可以将文件保存到本地
    saveFileToLocal(result.content, result.fileName)

    return result
  } catch (error) {
    console.error('下载失败:', error.message)
    throw error
  }
}

/**
 * 示例2: 自定义分块大小下载
 */
export async function customChunkSizeExample() {
  try {
    console.log('=== 自定义分块大小下载示例 ===')

    const storedFilepath = '1/W_7swJXiBW_EoL.ml'

    // 使用较小的分块大小 (256KB)，适合网络较慢的情况
    const result = await fileService.downloadFile(
      storedFilepath,
      (progress, fileName) => {
        console.log(`小分块下载进度: ${progress.toFixed(1)}% - ${fileName}`)
      },
      256 * 1024, // 256KB 分块大小
    )

    console.log('小分块下载完成!')
    console.log(`文件大小: ${result.content.length} 字节`)

    return result
  } catch (error) {
    console.error('小分块下载失败:', error.message)
    throw error
  }
}

/**
 * 示例3: 批量文件下载
 */
export async function batchDownloadExample() {
  try {
    console.log('=== 批量文件下载示例 ===')

    const filePaths = ['1/W_7swJXiBW_EoL.ml', '2/another_file.txt', '3/document.pdf']

    const results = []

    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i]
      console.log(`\n下载文件 ${i + 1}/${filePaths.length}: ${filePath}`)

      try {
        const result = await fileService.downloadFile(filePath, (progress, fileName) => {
          console.log(`  进度: ${progress.toFixed(1)}% - ${fileName}`)
        })

        results.push({
          filePath,
          success: true,
          result,
        })

        console.log(`  ✅ 下载成功: ${result.fileName}`)
      } catch (error) {
        results.push({
          filePath,
          success: false,
          error: error.message,
        })

        console.log(`  ❌ 下载失败: ${error.message}`)
      }
    }

    console.log('\n=== 批量下载结果 ===')
    const successCount = results.filter((r) => r.success).length
    console.log(`成功: ${successCount}/${results.length}`)

    return results
  } catch (error) {
    console.error('批量下载失败:', error.message)
    throw error
  }
}

/**
 * 示例4: 带错误重试的下载
 */
export async function downloadWithRetryExample() {
  const maxRetries = 3
  const storedFilepath = '1/W_7swJXiBW_EoL.ml'

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`=== 下载尝试 ${attempt}/${maxRetries} ===`)

      const result = await fileService.downloadFile(storedFilepath, (progress, fileName) => {
        console.log(`尝试 ${attempt} 进度: ${progress.toFixed(1)}% - ${fileName}`)
      })

      console.log(`✅ 第 ${attempt} 次尝试成功!`)
      return result
    } catch (error) {
      console.log(`❌ 第 ${attempt} 次尝试失败: ${error.message}`)

      if (attempt === maxRetries) {
        console.log('所有重试都失败了')
        throw error
      }

      // 等待一段时间后重试
      const delay = attempt * 1000 // 递增延迟
      console.log(`等待 ${delay}ms 后重试...`)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }
}

/**
 * 将文件内容保存到本地（浏览器下载）
 */
function saveFileToLocal(content: Uint8Array, fileName: string) {
  try {
    // 创建 Blob
    const blob = new Blob([content])

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    a.style.display = 'none'

    // 触发下载
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    // 清理 URL
    setTimeout(() => URL.revokeObjectURL(url), 1000)

    console.log(`文件已保存到本地: ${fileName}`)
  } catch (error) {
    console.error('保存文件到本地失败:', error)
  }
}

/**
 * 在浏览器控制台中运行示例的便捷函数
 */
export function runExamples() {
  console.log('downloadFile 使用示例')
  console.log('可用的示例函数:')
  console.log('- basicDownloadExample(): 基本下载示例')
  console.log('- customChunkSizeExample(): 自定义分块大小示例')
  console.log('- batchDownloadExample(): 批量下载示例')
  console.log('- downloadWithRetryExample(): 带重试的下载示例')
  console.log('')
  console.log('使用方法:')
  console.log('await basicDownloadExample()')
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
  ;(window as any).downloadExamples = {
    basicDownloadExample,
    customChunkSizeExample,
    batchDownloadExample,
    downloadWithRetryExample,
    runExamples,
  }

  // 自动显示使用说明
  runExamples()
}
