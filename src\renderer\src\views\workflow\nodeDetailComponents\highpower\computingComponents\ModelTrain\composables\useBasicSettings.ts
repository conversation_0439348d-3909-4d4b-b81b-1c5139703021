import { computed, ref, type Ref } from 'vue'

export function useBasicSettings() {
  // 展开/收缩状态
  const showMoreParams = ref(false)

  // 参数配置
  const totalParams = 14 // 总参数数量
  const defaultShowParams = 6 // 默认显示的参数数量

  // 计算隐藏的参数数量
  const hiddenParamsCount = computed(() => totalParams - defaultShowParams)

  // 切换更多参数显示状态
  const toggleMoreParams = () => {
    showMoreParams.value = !showMoreParams.value
  }

  return {
    // 状态
    showMoreParams,

    // 配置
    totalParams,
    defaultShowParams,

    // 计算属性
    hiddenParamsCount,

    // 方法
    toggleMoreParams,
  }
}
