// 参数映射和处理工具函数

export interface ParamConfig {
  value: any
  type: string
  desc_cn: string
  choices?: any[]
}

export interface ModelParams {
  inChannels: ParamConfig
  channels: ParamConfig
  inputSegmentHeight: ParamConfig
  inputSegmentWidth: ParamConfig
  alpha: ParamConfig
  kernelSize: ParamConfig
  trainSupportSize: ParamConfig
  testSupportSize: ParamConfig
  actFn: ParamConfig
  useFcForPrediction: ParamConfig
  filterCyclesFlag: ParamConfig
  featuresToDrop: ParamConfig
  cyclesToDropInSegment: ParamConfig
  returnPointwisePredictions: ParamConfig
}

export interface ModelResult {
  modelName: string
  modelDesc: string
  modelParams: ModelParams
}

/**
 * 参数映射配置：后端字段名 -> 前端字段名
 */
export const PARAM_MAPPING = {
  // 数值参数
  in_channels: 'inChannels',
  channels: 'channels',
  input_segment_height: 'inputSegmentHeight',
  input_segment_width: 'inputSegmentWidth',
  alpha: 'alpha',
  kernel_size: 'kernelSize',
  train_support_size: 'trainSupportSize',
  test_support_size: 'testSupportSize',
  // 布尔值参数
  use_fc_for_prediction: 'useFcForPrediction',
  filter_cycles_flag: 'filterCyclesFlag',
  return_pointwise_predictions: 'returnPointwisePredictions',
  // 字符串参数
  features_to_drop: 'featuresToDrop',
  cycles_to_drop_in_segment: 'cyclesToDropInSegment',
} as const

/**
 * 创建默认的模型参数
 */
export const createDefaultModelParams = (): ModelParams => ({
  inChannels: {
    value: 7,
    type: 'int',
    desc_cn: '特征数',
  },
  channels: {
    value: 64,
    type: 'int',
    desc_cn: '模型通道数',
  },
  inputSegmentHeight: {
    value: 20,
    type: 'int',
    desc_cn: '输入片段高度（采样循环数）',
  },
  inputSegmentWidth: {
    value: 200,
    type: 'int',
    desc_cn: '输入片段宽度（单循环采样数）',
  },
  alpha: {
    value: 0.3,
    type: 'float',
    desc_cn: '支持集融合权重',
  },
  kernelSize: {
    value: 3,
    type: 'int',
    desc_cn: '卷积核大小',
  },
  trainSupportSize: {
    value: 50,
    type: 'int',
    desc_cn: '训练时支持集大小',
  },
  testSupportSize: {
    value: 50,
    type: 'int',
    desc_cn: '测试时支持集大小',
  },
  actFn: {
    value: 'relu',
    choices: ['relu', 'sigmoid', 'tanh'],
    type: 'str',
    desc_cn: '激活函数',
  },
  useFcForPrediction: {
    value: true,
    type: 'bool',
    desc_cn: '是否使用全连接层做最终预测',
  },
  filterCyclesFlag: {
    value: true,
    type: 'bool',
    desc_cn: '是否过滤无效循环',
  },
  featuresToDrop: {
    value: 'None',
    type: 'str',
    desc_cn: '待丢弃特征',
  },
  cyclesToDropInSegment: {
    value: 'None',
    type: 'str',
    desc_cn: '片段中待丢弃的周期数',
  },
  returnPointwisePredictions: {
    value: false,
    type: 'bool',
    desc_cn: '返回逐点预测结果',
  },
})

/**
 * 创建默认的模型结果
 */
export const createDefaultModelResult = (): ModelResult => ({
  modelName: '钴酸锂终点预测模型',
  modelDesc: '基于钴酸锂恒功率衰减实验数据训练，容量80%作为终点的预测模型',
  modelParams: createDefaultModelParams(),
})

/**
 * 从配置中解析模型结果
 */
export const parseModelResultFromConfig = (config: any): ModelResult | null => {
  if (!config) return null

  // 尝试从 modelResult 中解析
  if (config.modelResult) {
    try {
      const modelData = JSON.parse(config.modelResult)
      return {
        modelName: modelData.name || '',
        modelDesc: modelData.description || '',
        modelParams: mapBackendParamsToFrontend(
          modelData.model_params || {},
          createDefaultModelParams(),
        ),
      }
    } catch (e) {
      console.error('解析 modelResult 失败:', e)
    }
  }

  // 兼容旧的格式
  if (config.model_params) {
    return {
      modelName: config.name || '',
      modelDesc: config.description || '',
      modelParams: mapBackendParamsToFrontend(config.model_params, createDefaultModelParams()),
    }
  }

  return null
}

/**
 * 从配置中解析模型参数
 */
export const parseModelParamsFromConfig = (config: any): any | null => {
  if (!config) return null

  // 尝试从 modelResult 中解析
  if (config.modelResult) {
    try {
      const modelData = JSON.parse(config.modelResult)
      return modelData.model_params
    } catch (e) {
      console.error('解析 modelResult 失败:', e)
    }
  }

  // 直接从 config 中获取
  if (config.model_params) {
    return config.model_params
  }

  return null
}

/**
 * 映射后端参数到前端参数
 */
export const mapBackendParamsToFrontend = (
  backendParams: any,
  targetParams: ModelParams,
): ModelParams => {
  const updatedParams = { ...targetParams }

  // 自动映射参数
  Object.entries(PARAM_MAPPING).forEach(([backendKey, frontendKey]) => {
    if (backendParams[backendKey] !== undefined) {
      const backendValue = backendParams[backendKey]
      const targetParam = (updatedParams as any)[frontendKey]

      if (targetParam && typeof targetParam === 'object' && 'value' in targetParam) {
        //参数格式（包含 value, type, desc_cn）
        if (typeof backendValue === 'object' && 'value' in backendValue) {
          targetParam.value = backendValue.value
          if (backendValue.type) targetParam.type = backendValue.type
          if (backendValue.desc_cn) targetParam.desc_cn = backendValue.desc_cn
          if (backendValue.choices) targetParam.choices = backendValue.choices
        } else {
          // 如果后端参数是旧格式（直接值）
          targetParam.value = backendValue
        }
      }
    }
  })

  // 处理特殊的嵌套参数 act_fn
  if (backendParams.act_fn) {
    if (typeof backendParams.act_fn === 'object' && 'value' in backendParams.act_fn) {
      // 新格式
      updatedParams.actFn.value = backendParams.act_fn.value || 'relu'
      updatedParams.actFn.choices = backendParams.act_fn.choices || ['relu', 'sigmoid', 'tanh']
      if (backendParams.act_fn.type) updatedParams.actFn.type = backendParams.act_fn.type
      if (backendParams.act_fn.desc_cn) updatedParams.actFn.desc_cn = backendParams.act_fn.desc_cn
    } else if (typeof backendParams.act_fn === 'object' && 'default' in backendParams.act_fn) {
      // 旧格式兼容
      updatedParams.actFn.value = backendParams.act_fn.default || 'relu'
      updatedParams.actFn.choices = backendParams.act_fn.choices || ['relu', 'sigmoid', 'tanh']
    }
  }

  return updatedParams
}

/**
 * 更新模型结果从配置
 */
export const updateModelResultFromConfig = (
  config: any,
  currentResult: ModelResult,
  hasUserModified: boolean = false,
): ModelResult => {
  // 如果用户已经修改过参数，则不更新
  if (hasUserModified) {
    return currentResult
  }

  const parsedResult = parseModelResultFromConfig(config)

  if (parsedResult) {
    console.log('找到 modelResult，使用连接获取的默认值:', parsedResult)
    return parsedResult
  }

  return currentResult
}

/**
 * 更新模型参数从配置
 */
export const updateModelParamsFromConfig = (
  config: any,
  currentParams: ModelParams,
  hasUserModified: boolean = false,
): ModelParams => {
  // 如果用户已经修改过参数，则不更新
  if (hasUserModified) {
    return currentParams
  }

  const backendParams = parseModelParamsFromConfig(config)

  if (backendParams) {
    console.log('找到 modelParams，使用连接获取的默认值:', backendParams)
    return mapBackendParamsToFrontend(backendParams, currentParams)
  }

  return currentParams
}
