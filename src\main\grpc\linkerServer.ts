import * as grpc from '@grpc/grpc-js'
import * as protoLoader from '@grpc/proto-loader'
import path from 'path'
import logger from '../utils/logger'
import { LinkerClient } from './linkerClient'
export class LinkerServer {
  private server: grpc.Server
  private linkerClient: LinkerClient
  private port: string

  constructor(linkerClient: LinkerClient, port: string = '50051') {
    this.linkerClient = linkerClient
    this.port = port
    this.server = new grpc.Server()
    this.initServer()
  }

  // 初始化服务器
  private initServer() {
    const PROTO_PATH = path.resolve(__dirname, './protos/linker.proto')
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
      includeDirs: [path.resolve(__dirname, './protos')],
    })

    const linkerProto = grpc.loadPackageDefinition(packageDefinition).linker

    // 添加服务实现
    this.server.addService(linkerProto.LinkerService.service, {
      defaultService: (call, callback) => {
        this.linkerClient.handleDefaultService(call, callback)
      },
    })
  }

  // 启动服务器
  start() {
    return new Promise<void>((resolve, reject) => {
      this.server.bindAsync(
        `0.0.0.0:${this.port}`,
        grpc.ServerCredentials.createInsecure(),
        (err) => {
          if (err) {
            logger.error(`gRPC服务器启动失败: ${err.message}`)
            reject(err)
            return
          }

          this.server.start()
          logger.info(`gRPC服务器运行在端口 ${this.port}`)
          resolve()
        },
      )
    })
  }

  // 停止服务器
  stop() {
    return new Promise<void>((resolve) => {
      this.server.tryShutdown(() => {
        console.log('gRPC服务器已关闭')
        resolve()
      })
    })
  }
}
