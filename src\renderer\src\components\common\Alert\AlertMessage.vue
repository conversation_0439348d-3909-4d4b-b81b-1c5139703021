<template>
  <Transition name="fade">
    <div v-if="show" class="alert-message" :class="positionClass">
      <Alert :variant="variant">
        <component :is="icon" class="h-4 w-4" />
        <AlertTitle>{{ title }}</AlertTitle>
        <AlertDescription>
          {{ message }}
        </AlertDescription>
      </Alert>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, computed } from 'vue'
// import { Alert, AlertTitle, AlertDescription } from '@renderer/components/ui'
import { AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-vue-next'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  message: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '提示',
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'destructive', 'success', 'warning', 'info'].includes(value),
  },
  duration: {
    type: Number,
    default: 3000,
  },
  position: {
    type: String,
    default: 'top-center',
    validator: (value) =>
      [
        'top-center',
        'top-left',
        'top-right',
        'bottom-center',
        'bottom-left',
        'bottom-right',
      ].includes(value),
  },
  autoClose: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:show'])

// 根据variant选择对应的图标
const icon = computed(() => {
  switch (props.variant) {
    case 'destructive':
      return AlertCircle
    case 'success':
      return CheckCircle
    case 'warning':
      return AlertTriangle
    case 'info':
    case 'default':
    default:
      return Info
  }
})

// 根据position设置位置类
const positionClass = computed(() => {
  return `position-${props.position}`
})

// 自动关闭定时器
const closeTimeout = ref(null)

// 监听show变化，设置自动关闭
watch(
  () => props.show,
  (newValue) => {
    if (newValue && props.autoClose) {
      // 清除之前的定时器
      if (closeTimeout.value) {
        clearTimeout(closeTimeout.value)
      }

      // 设置新的定时器
      closeTimeout.value = setTimeout(() => {
        emit('update:show', false)
      }, props.duration)
    }
  },
)

// 组件卸载时清除定时器
onUnmounted(() => {
  if (closeTimeout.value) {
    clearTimeout(closeTimeout.value)
  }
})
</script>

<style lang="scss" scoped>
.alert-message {
  position: absolute;
  z-index: 1000;
  width: 350px;
  animation: fadeIn 0.3s ease-in-out;

  &.position-top-center {
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
  }

  &.position-top-left {
    top: 20px;
    left: 20px;
  }

  &.position-top-right {
    top: 20px;
    right: 20px;
  }

  &.position-bottom-center {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  &.position-bottom-left {
    bottom: 20px;
    left: 20px;
  }

  &.position-bottom-right {
    bottom: 20px;
    right: 20px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) translateX(-50%);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
}

.position-top-left,
.position-top-right,
.position-bottom-left,
.position-bottom-right {
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.position-top-center .fade-enter-from,
.position-top-center .fade-leave-to,
.position-bottom-center .fade-enter-from,
.position-bottom-center .fade-leave-to {
  transform: translateY(-20px) translateX(-50%);
}
</style>
