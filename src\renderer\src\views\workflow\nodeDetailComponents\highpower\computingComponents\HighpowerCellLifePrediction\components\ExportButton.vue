<template>
  <Button
    :disabled="!canExport"
    size="sm"
    class="dark:text-muted-foreground dark:bg-muted-foreground"
    @click="onExport"
  >
    <LucideIcon name="FileUp" class="w-4 h-4 mr-2" />
    导出结果
  </Button>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

defineProps({
  canExport: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['export'])

const onExport = () => {
  emit('export')
}
</script>
