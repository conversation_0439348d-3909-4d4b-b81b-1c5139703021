<template>
  <header class="bg-background text-foreground shadow-sm border-b border-gray-200">
    <div class="container mx-auto px-2 py-4 flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <Bot class="text-green-500 w-6 h-6" />
        <span class="font-bold text-xl text-foreground">Matt AI助手</span>
      </div>
      <nav class="hidden md:flex items-center space-x-8 cursor-pointer">
        <template v-for="(item, index) in titleList" :key="index">
          <router-link
            :to="item.path"
            :exact="item.path === '/ai'"
            :active-class="item.path === '/ai' ? '' : 'text-green-600'"
            :exact-active-class="item.path === '/ai' ? 'text-green-600' : ''"
            class="text-foreground font-medium hover:text-green-600 transition duration-300 flex gap-2 items-center"
          >
            <component :is="item.icon" class="w-4 h-4" />
            <span>{{ item.title }}</span>
          </router-link>
        </template>
      </nav>
      <div class="flex items-center space-x-4">
        <button
          class="btn btn-outline flex items-center space-x-1 text-sm px-3 py-1.5 rounded-md border transition-all duration-200"
          :class="[
            isChatRoute
              ? 'border-border hover:bg-muted/70 opacity-100 cursor-pointer'
              : 'border-transparent opacity-0 pointer-events-none',
          ]"
          @click="handleBackToWorkflow"
        >
          <Undo2 class="w-4 h-4"></Undo2>
          <span>返回工作流</span>
        </button>
      </div>
    </div>
  </header>
</template>

<script setup>
import { useAIChatStore, useNavbarStore, useWorkflowStore } from '@renderer/store'
import { BookOpen, Bot, House, MessageSquareMore, Undo2 } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()
const aiChatStore = useAIChatStore()
const navbarStore = useNavbarStore()
const workflowStore = useWorkflowStore()

// 判断当前是否是聊天路由
const isChatRoute = computed(() => {
  return route.path.startsWith('/ai/chat')
})

const titleList = ref([
  {
    path: '/ai',
    title: '首页',
    icon: House,
  },
  {
    path: '/ai/chat',
    title: 'AI科研助手',
    icon: MessageSquareMore,
  },
  {
    path: '/ai/agent',
    title: '工作模板',
    icon: Bot,
  },
  {
    path: '/ai/knowledge',
    title: '知识库',
    icon: BookOpen,
  },
  // {
  //   path: '/ai/aiSetting',
  //   title: '设置',
  //   icon: Settings,
  // },
])

// 检查是否有来自URL查询参数的工作流ID
const workflowIdFromQuery = computed(() => route.query.workflowId)

// 处理返回工作流按钮点击
const handleBackToWorkflow = () => {
  let targetWorkflowId = null

  // 如果从URL查询参数中有工作流ID，优先返回该工作流
  if (workflowIdFromQuery.value) {
    targetWorkflowId = workflowIdFromQuery.value
  }
  // 如果当前对话关联了工作流，返回到该工作流
  else if (aiChatStore.currentSession?.workflowId) {
    targetWorkflowId = aiChatStore.currentSession.workflowId
  }

  if (targetWorkflowId) {
    const tagId = `${targetWorkflowId}`

    // 检查导航栏中是否已存在此工作流的标签
    const existingTag = navbarStore.tags.find((tag) => tag.id === tagId)
    console.log('检查导航栏中是否已存在此工作流的标签', existingTag)

    if (existingTag) {
      // 如果标签已存在，直接激活它
      navbarStore.setActiveTag(tagId)
      // 导航到工作流编辑器，不重新创建标签
      router.push(existingTag.path)
      return
    }

    // 查找工作流信息，用于创建导航标签
    const workflow = workflowStore.workflows.find((wf) => wf.id === targetWorkflowId)
    if (workflow) {
      // 标签不存在，添加新标签
      navbarStore.addTag({
        id: tagId,
        title: workflow.title || '工作流',
        path: `/workflow/editor/${targetWorkflowId}`,
      })

      // 导航到工作流编辑器
      router.push(`/workflow/editor/${targetWorkflowId}`)
      return
    }
  }

  // 如果没有找到目标工作流，检查路由历史
  if (window.history.length > 1) {
    // 尝试返回上一页
    router.back()
  } else {
    // 否则返回工作流列表
    router.push('/workflow')
  }
}
</script>

<style lang="scss" scoped></style>
