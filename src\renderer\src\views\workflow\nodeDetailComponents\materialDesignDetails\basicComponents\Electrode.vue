<template>
  <div class="w-full h-full">
    <CardVue>
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center justify-center gap-2 text-2xl font-bold">
            <SvgIcon :name="svg.structure" />
            <span>电极</span>
          </div>
          <div class="flex gap-2">
            <div class="flex gap-2">
              <Button size="sm" @click="optionsHandel('selectStructure')">
                <LucideIcon name="Search" class="h-4 w-4" />
                选择结构
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="outline" size="xs" class="p-1">
                    <LucideIcon name="EllipsisVertical" class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    class="hover:bg-orange-50"
                    @click="optionsHandel('uploadStructure')"
                  >
                    <LucideIcon name="FileUp" class="w-4 h-4 mr-2 text-orange-500" />
                    <span class="text-orange-600 font-medium">上传结构</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    class="hover:bg-red-50"
                    @click="optionsHandel('exportStructure')"
                  >
                    <LucideIcon name="FileDown" class="w-4 h-4 mr-2 text-red-500" />
                    <span class="text-red-600 font-medium">导出结构</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </template>
      <template #content>
        <div>
          <div class="w-full h-[360px]">
            <ElementThreeVue :data="threeData" :loading="threeLoading" />
          </div>
          <div class="flex justify-center items-center mt-2">
            <Button type="primary" size="sm" @click="dopingHandel()">
              <Loader2 v-if="dopingLoading" class="w-3 h-3 animate-spin" />
              <span>掺杂</span>
            </Button>
            <Button type="primary" size="sm" class="ml-4" @click="relaxingHandel()">
              <Loader2 v-if="relaxingLoading" class="w-3 h-3 animate-spin" />
              <span>结构优化</span>
            </Button>
          </div>
        </div>
      </template>
    </CardVue>
    <CardVue class="mt-6">
      <CardVue :title="'材料参数细节'" class="mt-6 shadow-md rounded-xl">
        <div class="flex justify-center items-center flex-col p-4 box-border">
          <div
            v-for="(item, index) in resColumns"
            :key="index"
            class="w-full flex justify-start border-l border-r border-b"
            :class="{ 'border-t': index === 0 }"
          >
            <div class="w-[50%] border-r bg-[#F5F7FA] px-4 py-2">
              <span class="text-sm">{{ item.title }}</span>
            </div>
            <div class="w-[50%] px-4 py-2 text-sm">
              <span>{{ item.value }}</span>
            </div>
          </div>
        </div>
      </CardVue>
    </CardVue>
    <SelectStructureDialog
      v-if="selectStuctureModalVisiable"
      v-model:visiable="selectStuctureModalVisiable"
      :form-data="selectForm"
      @ok="okStructure"
    />
    <DopingDialogVue
      v-if="dopingVisiable"
      v-model:visiable="dopingVisiable"
      :data-param="dopingData"
      @ok="okDoping"
    />
    <RelaxingDialogVue v-model:visiable="relaxingVisiable" @ok="okRelaxing" />
  </div>
</template>

<script setup lang="ts">
import { LucideIcon, SvgIcon } from '@renderer/components'
import { createMaterialService } from '@renderer/config/api/grpc/materialService'
import svg from '@renderer/config/constants/svg'
import { useNodeParams } from '@renderer/config/hooks'
import { useTaskStore } from '@renderer/store'
import { Loader2 } from 'lucide-vue-next'
import { onMounted, onUnmounted, Ref, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import {
  CardVue,
  DopingDialogVue,
  RelaxingDialogVue,
  SelectStructureDialog,
} from '../../../components'
import ElementThreeVue from '../../../components/Threejs/elementThree.vue'

const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

const selectStuctureModalVisiable = ref(false)
const selectForm = ref({})
// 节点参数管理
const { saveParams } = useNodeParams(props.nodeData, {
  defaultValue: {
    nodeId: props.nodeData.id,
    selectForm: {},
  },
  autoLoad: true,
  autoSave: true,
  onLoad: (loadedParams) => {
    console.log(`节点 ${props.nodeData.id} 加载参数:`, loadedParams)
  },
})
// 电极节点参数
const electrodeParams: any = {
  structureData: '',
  name: '',
  formula: '',
  structurePlotData: '',
  density: '',
  numberOfAtoms: '',
  reducedFormula: '',
  status: '',
}
const setElementParams = () => {
  saveParams(electrodeParams)
}
/**
 * 结构操作事件
 * @param type selectStructure | uploadStructure | exportStructure
 */
const optionsHandel = (type: string) => {
  switch (type) {
    case 'selectStructure':
      selectStuctureModalVisiable.value = true
      break
    case 'uploadStructure':
      importStructure()
      break
    case 'exportStructure':
      exportCif()
      break
  }
}
// 导出cif文件
const exportCif = async () => {
  const name: any = handleElementParams.name
  const options: any = {
    title: 'Save File',
    defaultPath: `${name}.cif`,
    filters: [
      { name: 'CIF Files', extensions: ['cif'] },
      { name: 'All Files', extensions: ['*'] },
    ],
  }
  // 获取保存路径
  const res = await window.electronAPI.openSaveFileDialog(options)
  const filePath = res.filePath
  if (!res.filePath) return
  // 保存文件
  const savaFileRes = await window.electronAPI.saveFile({
    filePath: filePath,
    content: handleElementParams.structureData,
  })
  if (savaFileRes.success) {
    toast.success('保存成功')
  }
}
// 导入结构
const importStructure = async () => {
  // 调用打开文件对话框
  const res = await window.electronAPI.openFile({
    title: '上传结构',
    filters: [{ name: '结构文件', extensions: ['cif'] }],
    properties: ['openFile'],
  })
  const filePaths = res.filePaths
  if (filePaths.length === 0) return
  // 读取文件内容
  const path: string = filePaths[0]
  const fileBuffer = await window.electronAPI.readFile({
    filePath: path,
    encoding: null,
  })
  const fileName: any = path.split('.').pop()?.toLowerCase()
  const params: any = {
    fileName,
    str: fileBuffer,
  }
  // 根据文件内容获取结构信息
  const structureData = await materialService.getStructureInfoByFile(params.fileName, params.str)
  electrodeParams.status = 'FAILED'
  if (Number(structureData.statusCode) === 200) {
    const ret: any = structureData.keyValuePairs
    // 设置化学式信息
    handleElementParams.structureData = ret.structureData
    handleElementParams.formula = ret.formula
    // 更新三维
    threeData.value = JSON.parse(ret.structurePlotData)
    updateInfo(ret)
  }
}

const materialService = createMaterialService()
// 关闭提交任务弹框
const resColumns: any = ref([
  {
    title: '化学式',
    value: '',
  },
  {
    title: '密度(g/cm³)',
    value: '',
  },
  {
    title: '原子数',
    value: '',
  },
])
const threeData = ref({})
const threeLoading = ref(false)
// 目前的化学式信息
const handleElementParams = {
  structureData: null,
  formula: '',
  name: '',
}
const okStructure = async (e: any) => {
  const fileName = e.selectItem.name
  // 设置化学式信息
  handleElementParams.structureData = e.structureData
  handleElementParams.formula = e.selectItem.formula
  handleElementParams.name = fileName
  threeLoading.value = true
  resColumns.value[0].value = e.selectItem.formula
  resColumns.value[1].value = e.density
  resColumns.value[2].value = e['Number of Atoms']
  electrodeParams.reducedFormula = e.selectItem.reduced_formula
  const res = await materialService.getStructurePlotData(fileName)
  if (Number(res.statusCode) === 200) {
    const ret: any = res.keyValuePairs
    ret['formula'] = e.selectItem.formula
    ret['density'] = e.density
    ret['Number of Atoms'] = e['Number of Atoms']
    threeLoading.value = false
    // 更新三维
    threeData.value = JSON.parse(ret.structurePlotData)
    updateInfo(ret)
  }
}
const dopingVisiable = ref(false)
let preFormula: any = null
const dopingData: any = ref({})
const dopingHandel = () => {
  if (dopingVisiable.value) {
    return
  }
  dopingVisiable.value = true
  const formula = handleElementParams.formula
  const structureData = handleElementParams.structureData
  if (formula !== preFormula) {
    const elementList = getDopingElementList(formula)
    dopingData.value = {
      elements: elementList,
      structureData: structureData,
      formula: formula,
    }
    preFormula = formula
  }
}
const getDopingElementList = (formula) => {
  const regex = /([A-Z][a-z]*)(\d*)/g
  const elementsList: string[] = []
  let match: RegExpExecArray | null

  while ((match = regex.exec(formula)) !== null) {
    const elementName = match[1]
    elementsList.push(elementName)
  }
  return elementsList
}
// 服务和状态管理
const currentTaskId = ref('')
const taskStore = useTaskStore()
const dopingLoading: Ref<any> = ref(false)
const okDoping = async (params: any) => {
  dopingLoading.value = true
  threeLoading.value = true
  const res = await materialService.getDopingStructure(params.com, params.host, params.str)
  if (Number(res.statusCode) === 200) {
    const ret: any = res
    setTask(ret.taskId)
  }
}
// 监听任务状态变化
watch(
  () => (currentTaskId.value ? taskStore.getTaskStatus(currentTaskId.value).value : null),
  (newStatus) => {
    if (!currentTaskId.value) return

    // 如果任务状态变为已完成，获取最终结果
    if (newStatus && ['Finished', 'Error', 'Abort'].includes(newStatus)) {
      // taskStore.updateTaskResult(currentTaskId.value)
      dopingLoading.value = false
      relaxingLoading.value = false
      threeLoading.value = false
    }
  },
)
// 监听任务结果变化
watch(
  () => (currentTaskId.value ? taskStore.getTaskResultById(currentTaskId.value).value : null),
  (newResult) => {
    if (newResult) {
      const ret: any = convertKey(newResult.values)
      if (!Object.prototype.hasOwnProperty.call(ret, 'progress')) {
        // 设置化学式信息
        let structureData: any = null
        if (ret.dopingStructure) {
          structureData = ret.dopingStructure
        }
        if (ret.relaxingStructure) {
          structureData = ret.relaxingStructure
        }
        handleElementParams.structureData = structureData
        handleElementParams.formula = ret.formula
        // 更新三维
        threeData.value = JSON.parse(ret.structurePlotData)
        electrodeParams.reducedFormula = ret.reducedFormula
        updateInfo(ret)
      }
    }
  },
)
const convertKey = (obj) => {
  // 先判断是否是对象
  if (typeof obj !== 'object' || !obj) return obj
  const newObj = {}
  for (const key in obj) {
    // 判断key是否是这个对象的
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      // 全局匹配下划线 然后转换为大写字母
      const newKey = key.replace(/_([a-z])/g, (res) => res[1].toUpperCase())
      // 递归
      newObj[newKey] = convertKey(obj[key])
    }
  }
  return newObj
}
const relaxingVisiable = ref(false)
const relaxingHandel = () => {
  relaxingVisiable.value = true
}
const relaxingLoading: Ref<any> = ref(false)
const okRelaxing = async (val: any) => {
  const params: any = {
    scheme: val,
    str: handleElementParams.structureData,
  }
  relaxingLoading.value = true
  threeLoading.value = true
  const res = await materialService.getRelaxedStructure(params.scheme, params.str)
  // if (Number(res.statusCode) === 200) {
  // const ret: any = res.keyValuePairs
  // // 设置化学式信息
  // handleElementParams.structureData = ret.relaxingStructure
  // handleElementParams.formula = ret.formula
  // // 更新三维
  // electrodeParams.reducedFormula = ret.reducedFormula
  // threeData.value = JSON.parse(ret.structurePlotData)
  // updateInfo(ret)
  // }
  if (Number(res.statusCode) === 200) {
    const ret: any = res
    setTask(ret.taskId)
  }
}
// 更新页面信息,结构优化和掺杂用的都是，同时设置节点参数信息
const updateInfo = (ret: any) => {
  resColumns.value[0].value = ret['formula']
  resColumns.value[1].value = ret['density']
  resColumns.value[2].value = ret['Number of Atoms']
  // 更新节点参数
  electrodeParams.formula = ret['formula']
  electrodeParams.density = ret['density']
  electrodeParams.numberOfAtoms = ret['Number of Atoms']
  electrodeParams.structurePlotData = ret.structurePlotData
  electrodeParams.structureData = handleElementParams.structureData as any
  electrodeParams.name = handleElementParams.name
  setElementParams()
}
onMounted(() => {
  // console.log('Component mounted', props.nodeData)
  // 如果有任务ID，更新任务结果
})
const setTask = async (taskId: string) => {
  currentTaskId.value = taskId
  // 如果有任务ID，更新任务结果
  await taskStore.updateTaskResult(currentTaskId.value)
  // 获取任务结果，如果任务没有执行完毕，则开启轮询，判断标准暂时指定为是否有progress字段
  const status: any = await taskStore.getTaskResultById(currentTaskId.value).value
  if (status?.values && Object.prototype.hasOwnProperty.call(status.values, 'progress')) {
    taskStore.startPolling(currentTaskId.value)
  } else {
    dopingLoading.value = false
    threeLoading.value = false
  }
}
// 组件卸载时
onUnmounted(() => {
  // 停止任务轮询
  if (currentTaskId.value) {
    taskStore.stopPolling(currentTaskId.value)
  }
})
</script>

<style scoped></style>
