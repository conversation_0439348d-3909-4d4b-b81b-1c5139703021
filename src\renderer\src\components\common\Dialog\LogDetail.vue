<template>
  <Dialog :open="isOpen" class="overflow-hidden" @update:open="closeDialog">
    <DialogContent class="max-w-6xl max-h-[90vh] p-0 bg-background border-border">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between px-4 py-3 border-b border-border bg-muted/30">
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <FileText class="h-4 w-4 text-primary" />
            <h2 class="text-lg font-medium text-foreground">日志详情</h2>
          </div>
          <Badge v-if="log" :variant="getStatusVariant(log.taskStatus)" class="text-xs font-medium">
            {{ taskStore.getStatusText(log.taskStatus) }}
          </Badge>
        </div>
        <!-- <Button variant="ghost" size="icon" class="rounded-full h-8 w-8" @click="closeDialog">
          <X class="h-4 w-4" />
        </Button> -->
      </div>

      <!-- 任务信息栏已移除 -->
      <!-- 内容区域 -->
      <div v-if="log" class="flex flex-col h-[calc(90vh-6rem)] overflow-hidden">
        <Tabs default-value="details" class="w-full">
          <TabsList class="w-full justify-start px-4 pt-2 bg-background border-b border-border">
            <TabsTrigger value="details" class="text-sm">详细信息</TabsTrigger>
            <TabsTrigger value="logs" class="text-sm">日志信息</TabsTrigger>
            <TabsTrigger value="results" class="text-sm">计算结果</TabsTrigger>
          </TabsList>

          <TabsContent value="details" class="p-0 m-0 h-full">
            <ScrollArea class="h-[calc(90vh-10rem)]">
              <div class="p-4 space-y-4">
                <!-- 基本信息卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card class="border border-border shadow-sm bg-card/50">
                    <CardHeader class="p-3 pb-0">
                      <CardTitle class="text-sm font-medium flex items-center gap-2">
                        <Server class="h-3.5 w-3.5 text-primary" />
                        服务信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="p-3 pt-2">
                      <div class="text-sm">
                        <div class="font-medium text-foreground">
                          {{ log.serviceId ? log.serviceId.split('::')[1] || log.serviceId : '--' }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          服务ID: {{ log.serviceId }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          任务ID: {{ log.taskId }}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card class="border border-border shadow-sm bg-card/50">
                    <CardHeader class="p-3 pb-0">
                      <CardTitle class="text-sm font-medium flex items-center gap-2">
                        <Clock class="h-3.5 w-3.5 text-primary" />
                        时间信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="p-3 pt-2">
                      <div class="text-sm">
                        <div class="font-medium text-foreground">
                          {{ formatDuration(log.startTime, log.endTime) }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          开始: {{ formatTimestamp(log.startTime) }}
                          {{
                            log.endTime && log.endTime !== 0
                              ? ' / 结束: ' + formatTimestamp(log.endTime)
                              : ''
                          }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          创建时间: {{ formatTimestamp(log.createTime) }}
                        </div>
                        <div
                          v-if="log.taskProcess !== undefined"
                          class="text-xs text-muted-foreground mt-1"
                        >
                          进度: {{ log.taskProcess }}%
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="logs" class="p-0 m-0 h-full">
            <div class="flex flex-col h-full">
              <div
                class="flex items-center justify-between px-4 py-2 bg-muted/20 border-b border-border"
              >
                <div class="text-sm font-medium">日志信息</div>
                <Button variant="ghost" size="sm" @click="copyData('message')">
                  <Copy class="h-3.5 w-3.5 mr-1.5" />
                  <span class="text-xs">复制</span>
                </Button>
              </div>
              <ScrollArea class="h-[calc(90vh-14rem)]">
                <div class="bg-muted/10 h-full">
                  <pre class="h-[calc(90vh-14rem)] scrollbar p-4 rounded-md">
                    <code class="language-json text-xs break-words" v-html="highlightJson(log.taskLog)"></code>
                  </pre>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="results" class="p-0 m-0 h-full">
            <div class="flex flex-col h-full">
              <div
                class="flex items-center justify-between px-4 py-2 bg-muted/20 border-b border-border"
              >
                <div class="text-sm font-medium">计算结果</div>
                <div class="flex items-center gap-2">
                  <Button variant="outline" size="sm" class="h-8" @click="openJsonViewer">
                    <Eye class="h-3.5 w-3.5 mr-1.5" />
                    <span class="text-xs">查看原始数据</span>
                  </Button>
                  <Button variant="ghost" size="sm" class="h-8" @click="copyData('result')">
                    <Copy class="h-3.5 w-3.5 mr-1.5" />
                    <span class="text-xs">复制</span>
                  </Button>
                </div>
              </div>
              <ScrollArea class="h-[calc(90vh-14rem)]">
                <div class="p-4 space-y-4">
                  <!-- 无数据状态 -->
                  <EmptyState
                    v-if="!parsedResult || taskResult === '--'"
                    title="暂无计算结果数据"
                    description="当前没有可显示的计算结果"
                    icon="flowbite:rectangle-list-outline"
                  />

                  <!-- 通用结果展示 -->
                  <div v-else-if="typeof parsedResult === 'object'" class="space-y-4">
                    <!-- 遍历顶层属性，为每个属性创建卡片 -->
                    <div v-for="(value, key) in parsedResult" :key="key" class="space-y-2">
                      <h3 class="text-sm font-medium text-foreground">{{ key }}</h3>

                      <!-- 递归组件：处理嵌套对象和数组 -->
                      <JsonDataRenderer :data="value" :property-name="key" />
                    </div>
                  </div>

                  <!-- 非对象类型结果展示 -->
                  <div v-else class="p-3 border border-border rounded-md bg-card/30">
                    <div class="text-sm whitespace-pre-wrap break-words">{{ taskResult }}</div>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <!-- 加载状态 -->
      <div v-else class="flex justify-center items-center h-[calc(90vh-6rem)]">
        <div class="flex flex-col items-center gap-2 text-muted-foreground">
          <Loader2 class="h-6 w-6 animate-spin" />
          <span class="text-sm">加载中...</span>
        </div>
      </div>
    </DialogContent>
  </Dialog>

  <!-- JSON查看器组件 -->
  <EnhancedJsonViewer v-model:is-open="isJsonViewerOpen" :initial-data="getJsonViewerData()" />
</template>

<script setup lang="ts">
import { EmptyState, EnhancedJsonViewer, JsonDataRenderer } from '@renderer/components'
import { useTaskStore } from '@renderer/store'
import emitter from '@renderer/utils/mitt'
import { formatDuration, formatTimestamp } from '@renderer/utils/utils'
import { Clock, Copy, Eye, FileText, Loader2, Server } from 'lucide-vue-next'
import Prism from 'prismjs'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps<{
  isOpen: boolean
  logId?: string
}>()

const emit = defineEmits<{
  'update:isOpen': [value: boolean]
}>()

const taskStore = useTaskStore()
const log = computed(() => taskStore.tasks.find((t) => t.taskId === props.logId))

// JSON查看器状态
const isJsonViewerOpen = ref(false)

// 安全的 JSON 序列化函数，避免循环引用
const safeStringify = (obj: any): string => {
  try {
    // 创建一个 Set 来跟踪已访问的对象，避免循环引用
    const seen = new WeakSet()
    return JSON.stringify(
      obj,
      (_key, val) => {
        if (val != null && typeof val === 'object') {
          if (seen.has(val)) {
            return '[Circular Reference]'
          }
          seen.add(val)
        }
        return val
      },
      2,
    )
  } catch (error) {
    console.error('JSON序列化失败:', error)
    return String(obj)
  }
}

const taskResult = computed(() => {
  if (!log.value) return '--'

  // 优先显示 log.result
  if (log.value.result && log.value.result.trim() !== '') return log.value.result

  // 其次显示 store 缓存的结果
  const storeResult = taskStore.getTaskResultById?.(log.value.taskId)?.value

  if (storeResult && typeof storeResult === 'string' && storeResult.trim() !== '')
    return storeResult
  if (storeResult && typeof storeResult === 'object') {
    // 使用安全的序列化函数
    return safeStringify(storeResult)
  }

  // 如果没有结果，尝试主动获取
  if (log.value.taskId && !storeResult) {
    // 使用nextTick避免在渲染期间触发状态更新
    // eslint-disable-next-line vue/no-async-in-computed-properties
    setTimeout(() => {
      if (log.value?.taskId) {
        taskStore.updateTaskResult(log.value.taskId)
      }
    }, 0)
  }

  return '--'
})

// 解析结果数据为结构化对象
const parsedResult = computed(() => {
  if (!taskResult.value || taskResult.value === '--') return null

  try {
    // 如果是字符串，尝试解析为JSON对象
    if (typeof taskResult.value === 'string') {
      return JSON.parse(taskResult.value)
    }
    // 如果已经是对象，直接返回
    return taskResult.value
  } catch (e) {
    console.error('解析结果数据失败:', e)
    return null
  }
})

// 获取状态对应的Badge样式
const getStatusVariant = (status: string | number) => {
  const statusMap: Record<string, string> = {
    '0': 'default', // 等待中
    '1': 'secondary', // 运行中
    '2': 'success', // 成功
    '3': 'destructive', // 失败
    '4': 'outline', // 其他
  }
  return statusMap[String(status)] || 'default'
}

// 打开JSON查看器
const openJsonViewer = () => {
  if (taskResult.value && taskResult.value !== '--') {
    isJsonViewerOpen.value = true
  } else {
    toast.warning('暂无计算结果可查看')
  }
}

// 获取JSON查看器数据
const getJsonViewerData = () => {
  if (!taskResult.value || taskResult.value === '--') return null

  try {
    // 如果是字符串，尝试解析为JSON对象
    if (typeof taskResult.value === 'string') {
      return JSON.parse(taskResult.value)
    }
    // 如果已经是对象，直接返回
    return taskResult.value
  } catch (e) {
    // 如果解析失败，返回原始字符串
    return taskResult.value
  }
}

// 监听任务更新事件，当任务结果更新时刷新界面
onMounted(() => {
  emitter.on('task-updated', ({ taskId }) => {
    if (taskId === props.logId) {
      // 强制刷新计算属性
      nextTick()
    }
  })
})

onUnmounted(() => {
  emitter.off('task-updated')
})

watch(
  () => props.isOpen,
  (open) => {
    if (open && props.logId) {
      const task = taskStore.tasks.find((t) => t.taskId === props.logId)
      if (task) {
        // 无论任务状态如何，都尝试获取最新结果
        taskStore.updateTaskResult(props.logId)
      }
    }
  },
  { immediate: true }, // 立即执行一次，确保初始化时也能获取结果
)

const closeDialog = () => {
  emit('update:isOpen', false)
}

const highlightJson = (jsonString?: string) => {
  if (!jsonString || jsonString === '--') return jsonString || '--'
  try {
    // 只高亮合法 JSON，否则直接返回原字符串
    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString
    const formatted = JSON.stringify(obj, null, 2)
    return Prism.highlight(formatted, Prism.languages.json, 'json')
  } catch {
    return jsonString.replace(/(.{120})/g, '$1\n') // 每120字符换行
  }
}

const copyData = async (type: 'message' | 'input' | 'result' | 'node') => {
  if (!log.value) return
  let data = ''
  switch (type) {
    case 'input':
      //data = log.value.rawData?.input_data || ''
      break
    case 'result':
      data =
        log.value.result ||
        (taskStore.getTaskResultById?.(log.value.taskId)?.value
          ? safeStringify(taskStore.getTaskResultById(log.value.taskId)?.value)
          : '')
      break
    case 'node':
      //data = log.value.rawData?.nodeData || ''
      break
    case 'message':
      data = log.value.taskLog || ''
      break
  }
  try {
    await navigator.clipboard.writeText(data)
    toast.success('复制成功！')
  } catch (err) {
    toast.error('复制失败！')
  }
}
</script>

<style>
pre {
  background: #1e1e1e;
  padding: 1rem;
  border-radius: 0.5rem;
  font-family: 'Fira Code', monospace;
  white-space: pre-wrap; /* 保证内容自动换行 */
  word-break: break-word; /* 长单词或字符串换行 */
  overflow-wrap: anywhere;
  word-wrap: break-word;
  max-width: 100%;
}

code {
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  white-space: pre-wrap; /* 保证内容自动换行 */
  word-break: break-word; /* 长单词或字符串换行 */
  overflow-wrap: break-word;
  word-wrap: break-word;
  display: block;
}
</style>
