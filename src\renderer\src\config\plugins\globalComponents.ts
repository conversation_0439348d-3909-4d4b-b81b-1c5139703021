import { App } from 'vue'
import * as components from '@renderer/components/ui'
import { LucideIcon } from '@renderer/components'

export default {
  install(app: App) {
    // 注册所有组件
    Object.entries(components).forEach(([name, component]) => {
      // console.log('注册所有组件', name, component)
      app.component(name, component)
    })
    // 注册图标组件
    app.component('LucideIcon', LucideIcon)
    console.log('全局组件注册成功！')
  },
}
