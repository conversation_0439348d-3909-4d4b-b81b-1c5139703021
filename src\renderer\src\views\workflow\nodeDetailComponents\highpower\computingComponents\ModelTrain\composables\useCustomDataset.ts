import { computed, ref, watch, type Ref, shallowRef, shallowReactive } from 'vue'
import { useDebounceFn } from '@vueuse/core'

// 定义数据集项的类型
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

// 定义数据集数据结构
interface DatasetData {
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

export function useCustomDataset(
  datasetData: Ref<DatasetData>,
  isOpen: Ref<boolean>,
  emit: ((event: 'confirm', datasetData: DatasetData) => void) &
    ((event: 'cancel') => void) &
    ((event: 'chart-click', item: DatasetItem) => void),
) {
  // 数据集类型配置
  const datasetTypes = [
    {
      key: 'train' as const,
      label: '训练集',
      colorClass: 'bg-green-500',
      activeClass: 'bg-green-50 text-green-700 border-green-300',
      dragOverClass: 'border-green-400 bg-green-50',
      itemBgClass: 'bg-green-50 border-green-200',
    },
    {
      key: 'test' as const,
      label: '测试集',
      colorClass: 'bg-violet-500',
      activeClass: 'bg-violet-50 text-violet-700 border-violet-300',
      dragOverClass: 'border-violet-400 bg-violet-50',
      itemBgClass: 'bg-violet-50 border-violet-200',
    },
    {
      key: 'val' as const,
      label: '验证集',
      colorClass: 'bg-yellow-500',
      activeClass: 'bg-yellow-50 text-yellow-700 border-yellow-300',
      dragOverClass: 'border-yellow-400 bg-yellow-50',
      itemBgClass: 'bg-yellow-50 border-yellow-200',
    },
    {
      key: 'support' as const,
      label: '支持集',
      colorClass: 'bg-gray-400',
      activeClass: 'bg-gray-50 text-gray-700 border-gray-300',
      dragOverClass: 'border-gray-400 bg-gray-50',
      itemBgClass: 'bg-gray-50 border-gray-200',
    },
  ]

  // 使用 shallowRef 和 shallowReactive 减少深度响应式开销
  const localDatasetItems = shallowRef<DatasetItem[]>([])
  // 新增：原始数据备份，用于取消时恢复
  const originalDatasetItems = shallowRef<DatasetItem[]>([])
  // 新增：标记是否有未保存的更改
  const hasUnsavedChanges = ref(false)

  const activeFilter = ref<'all' | 'train' | 'test' | 'val' | 'support'>('all')
  const searchQuery = ref('')
  const draggedItem = ref<DatasetItem | null>(null)
  const dragOverType = ref<string | null>(null)
  const chartDialogOpen = ref(false)
  const selectedDatasetItem = ref<DatasetItem | null>(null)

  // 深拷贝函数，用于备份数据
  const deepCloneDatasetItems = (items: DatasetItem[]): DatasetItem[] => {
    return items.map((item) => ({
      ...item,
      rawData: item.rawData ? { ...item.rawData } : undefined,
    }))
  }

  // 监听 props 变化，更新本地数据
  const updateLocalDatasetItems = (newData: DatasetData) => {
    if (newData) {
      const allItems = [
        ...newData.trainData,
        ...newData.testData,
        ...newData.valData,
        ...newData.supportData,
      ]
      localDatasetItems.value = deepCloneDatasetItems(allItems)
      // 同时备份原始数据
      originalDatasetItems.value = deepCloneDatasetItems(allItems)
      hasUnsavedChanges.value = false
    }
  }

  watch(() => datasetData.value, updateLocalDatasetItems, { immediate: true })

  // 监听弹框打开状态，重置本地数据
  watch(isOpen, (isOpenValue) => {
    if (isOpenValue && datasetData.value) {
      updateLocalDatasetItems(datasetData.value)
      activeFilter.value = 'all'
      searchQuery.value = ''
    }
  })

  // 防抖搜索函数
  const debouncedSearch = ref('')
  const updateDebouncedSearch = useDebounceFn((query: string) => {
    debouncedSearch.value = query
  }, 300)

  // 监听搜索查询变化
  watch(searchQuery, (newQuery) => {
    updateDebouncedSearch(newQuery)
  })

  // 筛选后的数据
  const filteredItems = computed(() => {
    let items = localDatasetItems.value

    // 搜索筛选
    if (debouncedSearch.value.trim()) {
      const query = debouncedSearch.value.toLowerCase()
      items = items.filter(
        (item) =>
          item.name.toLowerCase().includes(query) ||
          (item.rawData?.file_path || '').toLowerCase().includes(query),
      )
    }

    return items
  })

  // 优化：使用计算属性缓存类型数量
  const typeCounts = computed(() => {
    const counts: Record<string, number> = {
      train: 0,
      test: 0,
      val: 0,
      support: 0,
    }

    localDatasetItems.value.forEach((item) => {
      if (counts[item.type] !== undefined) {
        counts[item.type]++
      }
    })

    return counts
  })

  // 获取指定类型的数据集数量
  const getTypeCount = (type: string): number => {
    return typeCounts.value[type] || 0
  }

  // 优化：缓存筛选结果
  const filteredItemsByType = computed(() => {
    const result: Record<string, DatasetItem[]> = {
      train: [],
      test: [],
      val: [],
      support: [],
    }

    filteredItems.value.forEach((item) => {
      if (result[item.type]) {
        result[item.type].push(item)
      }
    })

    return result
  })

  // 获取指定类型的筛选数据
  const getFilteredItems = (type: string) => {
    return filteredItemsByType.value[type] || []
  }

  // 获取类型标签
  const getTypeLabel = (type: string): string => {
    const typeConfig = datasetTypes.find((t) => t.key === type)
    return typeConfig?.label || type
  }

  // 获取Badge样式类
  const getBadgeClass = (type: string): string => {
    const classes = {
      train: 'border-green-300 bg-green-50 text-green-700',
      test: 'border-violet-300 bg-violet-50 text-violet-700',
      val: 'border-yellow-300 bg-yellow-50 text-yellow-700',
      support: 'border-gray-300 bg-gray-50 text-gray-700',
    }
    return classes[type as keyof typeof classes] || classes.support
  }

  // 获取数据项背景样式类
  const getItemBgClass = (type: string): string => {
    const typeConfig = datasetTypes.find((t) => t.key === type)
    return typeConfig?.itemBgClass || 'bg-white border-neutral-200'
  }

  // 缓存短路径结果
  const shortPathCache = new Map<string, string>()

  const getShortPath = (path?: string): string => {
    if (!path) return '未知路径'

    // 检查缓存
    if (shortPathCache.has(path)) {
      return shortPathCache.get(path)!
    }

    const parts = path.split('/')
    let result: string
    if (parts.length > 3) {
      result = `.../${parts.slice(-2).join('/')}`
    } else {
      result = path
    }

    // 缓存结果
    shortPathCache.set(path, result)
    return result
  }

  // 设置活动筛选器
  const setActiveFilter = (filter: 'all' | 'train' | 'test' | 'val' | 'support') => {
    activeFilter.value = filter
  }

  // 获取当前活动类型的配置
  const getActiveTypeConfig = () => {
    if (activeFilter.value === 'all') return null
    return datasetTypes.find((type) => type.key === activeFilter.value)
  }

  // 拖拽处理
  const handleDragStart = (event: DragEvent, item: DatasetItem) => {
    draggedItem.value = item
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/plain', item.id)
    }
  }

  const handleDragEnd = () => {
    draggedItem.value = null
    dragOverType.value = null
  }

  const handleDragOver = (type: string) => {
    dragOverType.value = type
  }

  const handleDragLeave = () => {
    dragOverType.value = null
  }

  const handleDrop = (event: DragEvent, targetType: 'train' | 'test' | 'val' | 'support') => {
    event.preventDefault()
    dragOverType.value = null

    if (draggedItem.value && draggedItem.value.type !== targetType) {
      changeItemType(draggedItem.value.id, targetType)
    }

    draggedItem.value = null
  }

  // 改变数据集项的类型（仅更新本地数据，标记为有未保存更改）
  const changeItemType = (itemId: string, newType: 'train' | 'test' | 'val' | 'support') => {
    const itemIndex = localDatasetItems.value.findIndex((item) => item.id === itemId)
    if (itemIndex !== -1 && localDatasetItems.value[itemIndex].type !== newType) {
      // 创建新的数组来触发响应式更新
      const newItems = [...localDatasetItems.value]
      newItems[itemIndex] = { ...newItems[itemIndex], type: newType }
      localDatasetItems.value = newItems
      hasUnsavedChanges.value = true
    }
  }

  // 处理图表点击
  const handleChartClick = (item: DatasetItem) => {
    selectedDatasetItem.value = item
    chartDialogOpen.value = true
    emit('chart-click', item)
  }

  // 处理确认
  const handleConfirm = () => {
    // 根据类型重新分组数据
    const newDatasetData: DatasetData = {
      trainData: localDatasetItems.value.filter((item) => item.type === 'train'),
      testData: localDatasetItems.value.filter((item) => item.type === 'test'),
      valData: localDatasetItems.value.filter((item) => item.type === 'val'),
      supportData: localDatasetItems.value.filter((item) => item.type === 'support'),
    }

    // 更新原始数据备份，清除未保存更改标记
    originalDatasetItems.value = deepCloneDatasetItems(localDatasetItems.value)
    hasUnsavedChanges.value = false

    emit('confirm', newDatasetData)
    isOpen.value = false
  }

  // 处理取消 - 恢复到原始状态但不关闭窗口
  const handleCancel = () => {
    if (hasUnsavedChanges.value) {
      // 恢复到原始数据
      localDatasetItems.value = deepCloneDatasetItems(originalDatasetItems.value)
      hasUnsavedChanges.value = false
    } else {
      // 如果没有未保存的更改，则关闭窗口
      emit('cancel')
      isOpen.value = false
    }
  }

  // 强制关闭窗口的方法
  const handleClose = () => {
    if (hasUnsavedChanges.value) {
      // 如果有未保存的更改，先恢复数据再关闭
      localDatasetItems.value = deepCloneDatasetItems(originalDatasetItems.value)
      hasUnsavedChanges.value = false
    }
    emit('cancel')
    isOpen.value = false
  }

  return {
    // 配置
    datasetTypes,

    // 状态
    localDatasetItems,
    hasUnsavedChanges,
    activeFilter,
    searchQuery,
    draggedItem,
    dragOverType,
    chartDialogOpen,
    selectedDatasetItem,

    // 计算属性
    filteredItems,

    // 方法
    getTypeCount,
    getFilteredItems,
    getTypeLabel,
    getBadgeClass,
    getItemBgClass,
    getShortPath,
    setActiveFilter,
    getActiveTypeConfig,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    changeItemType,
    handleChartClick,
    handleConfirm,
    handleCancel,
    handleClose,
  }
}
