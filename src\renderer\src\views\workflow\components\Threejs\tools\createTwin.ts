import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js'

class CreateTwin {
  // 内容节点
  // containerNode: HTMLElement | null = null
  // 渲染节点
  renderNode: HTMLElement | null = null
  // 构造函数,传入渲染的节点
  constructor(container: HTMLElement) {
    this.renderNode = container
    // this.renderNode = this.createCanvas()
    this.initThreejs()
  }
  // 获取渲染节点的尺寸
  getRenderNodeSize() {
    let width: number = 0
    let height: number = 0
    if (this.renderNode) {
      width = this.renderNode.clientWidth
      height = this.renderNode.clientHeight
    }
    return { width, height }
  }
  // 初始化threejs
  scene: any = null
  camera: any = null
  renderer: any = null
  // 控制器
  controls: any = null
  // 平行光源参数
  directionalLight: any = null
  DLightConfig: any = {
    color: 0xffffff,
    intensity: 1.0,
    position: new THREE.Vector3(20, 20, 20),
  }
  // 环境光 (AmbientLight)
  ambientLight: any = null
  ALightConfig: any = {
    color: 0xffffff,
    intensity: 0.5,
  }
  bgColor = '#D6DCFF'
  initThreejs() {
    const { width, height } = this.getRenderNodeSize()
    // 添加场景
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(this.bgColor)
    // 添加相机
    this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 3000)
    this.camera.position.set(10, 10, 10)
    // 添加渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true, logarithmicDepthBuffer: true })
    this.renderer.setSize(width, height)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.outputEncoding = THREE.sRGBEncoding
    // 添加光源
    // 环境光
    this.ambientLight = new THREE.AmbientLight(this.ALightConfig.color, this.ALightConfig.intensity)
    this.scene.add(this.ambientLight)
    // 平行光
    this.directionalLight = new THREE.DirectionalLight(
      this.DLightConfig,
      this.DLightConfig.intensity,
    )
    this.directionalLight.position.set(
      this.DLightConfig.position.x,
      this.DLightConfig.position.y,
      this.DLightConfig.position.z,
    )
    this.scene.add(this.directionalLight)
    // 相机控件
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.target.set(0, 0, 0)
    this.controls.update()
    // 添加控制器
    this.controls.addEventListener('change', () => {
      this.render()
    })
    if (this.renderNode) {
      this.renderNode.appendChild(this.renderer.domElement)
    }
    this.addEnvMap()
    this.listenResize()
    this.render()
  }
  // 根据节点的变化监听窗口大小变化
  resizeObserver: any = null
  listenResize() {
    this.resizeObserver = new ResizeObserver((entries) => {
      let width: number = 0
      let height: number = 0
      for (const entry of entries) {
        width = Math.ceil(entry.contentRect.width)
        height = Math.ceil(entry.contentRect.height)
      }
      this.renderer.setSize(width, height)
      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.render(this.scene, this.camera)
    })
    if (this.renderNode) {
      this.resizeObserver.observe(this.renderNode)
    }
  }
  // 设置.envMap
  addEnvMap() {
    const rgbeLoader = new RGBELoader()
    const path = new URL('../../../../../assets/three/hdr/puresky_4k.hdr', import.meta.url).href
    rgbeLoader.load(path, (envMap) => {
      this.scene.environment = envMap
      envMap.mapping = THREE.EquirectangularReflectionMapping
    })
  }
  requestAnimationFrame: any = null
  isRender: boolean = true
  // 绘制场景
  render() {
    if (!this.isRender) {
      return
    }
    this.isRender = false
    this.requestAnimationFrame = requestAnimationFrame(() => {
      this.renderer.render(this.scene, this.camera)
      this.isRender = true
    })
  }
  // 重置场景
  resetScene() {
    const children = this.scene.children
    for (let i = children.length - 1; i >= 0; i--) {
      const obj = children[i]
      if (obj.type === 'Group' || obj.type === 'Line' || obj.type === 'Mesh') {
        obj.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose()
            child.material.dispose()
          }
        })
        this.scene.remove(obj)
      }
    }
  }
  // 销毁threejs
  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    if (this.controls) {
      this.controls.dispose()
    }
    if (this.renderer) {
      this.renderer.dispose()
    }
    // if (this.scene) {
    //   this.scene.dispose()
    // }
  }
}
export default CreateTwin
