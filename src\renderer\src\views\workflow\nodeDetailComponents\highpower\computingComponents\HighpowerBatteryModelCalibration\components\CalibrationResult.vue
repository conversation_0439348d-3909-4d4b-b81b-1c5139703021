<template>
  <Card v-if="shouldShowResult" class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="!!hasResult">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">
              标定结果
              <Badge v-if="hasResult" variant="secondary" class="ml-2">
                {{ Object.keys(currentResultData?.params || {}).length }} 个参数
              </Badge>
            </CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">{{ hasResult ? '收起' : '查看' }}</span>
              <ChevronDown
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div class="mt-4 rounded-lg p-4 bg-white dark:bg-gray-900 shadow-sm">
            <div class="flex justify-between items-center mb-4">
              <div class="flex items-center gap-2">
                <h3 class="text-lg font-medium">
                  {{ paramType === 'elec' ? '电化学参数' : '老化参数' }}
                  <Badge :class="getStatusClass">{{ getStatusText }}</Badge>
                </h3>
              </div>

              <HoverCard>
                <HoverCardTrigger as-child>
                  <Button variant="outline" size="sm" class="flex items-center gap-1">
                    <InfoIcon class="h-4 w-4" />
                    <span>标定信息</span>
                  </Button>
                </HoverCardTrigger>
                <HoverCardContent class="w-80">
                  <div class="flex justify-between space-x-4">
                    <div class="space-y-1 flex-1">
                      <h4 class="text-sm font-semibold">
                        {{ paramType === 'elec' ? '电化学参数' : '老化参数' }}标定结果
                      </h4>
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">总迭代次数:</span>
                        <span class="font-medium">{{ currentResultData?.generation || 0 }} 次</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">总耗时:</span>
                        <span class="font-medium">{{ currentTaskDuration }}</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">RMSE:</span>
                        <span class="font-medium">
                          {{ currentResultData?.rmse?.toFixed(6) || 0 }}
                        </span>
                      </div>
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                    总迭代次数
                  </span>
                  <span class="font-semibold">{{ currentResultData?.generation || 0 }} 次</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">总耗时</span>
                  <span class="font-semibold">{{ currentTaskDuration }}</span>
                </div>
              </div>

              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">RMSE</span>
                  <span class="font-semibold">{{ currentResultData?.rmse?.toFixed(6) || 0 }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">参数数量</span>
                  <span class="font-semibold">
                    {{ Object.keys(currentResultData?.params || {}).length }} 个
                  </span>
                </div>
              </div>
            </div>

            <div class="mt-4 flex justify-end gap-2">
              <Button size="sm" variant="outline" @click="showTaskLogs">
                <FileText class="h-4 w-4 mr-2" />
                详细日志
              </Button>
              <Button size="sm" @click="showParamsDetail">
                <ListIcon class="h-4 w-4 mr-2" />
                参数详情
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
  <!-- 参数详情对话框 -->
  <Dialog v-model:open="isDialogOpen">
    <DialogContent class="sm:max-w-[700px]">
      <DialogHeader>
        <DialogTitle>{{ paramType === 'elec' ? '电化学参数' : '老化参数' }}详情</DialogTitle>
        <DialogDescription>标定完成的参数详细信息</DialogDescription>
      </DialogHeader>
      <div v-if="currentResultData?.params" class="py-4 h-[400px] overflow-auto scrollbar">
        <div class="flex justify-between items-center mb-4">
          <div class="text-sm text-gray-500">
            共 {{ Object.keys(currentResultData.params).length }} 个参数
          </div>
          <Button size="sm" variant="outline" @click="copyParamsToClipboard">
            <ClipboardCopyIcon class="h-4 w-4 mr-2" />
            复制参数
          </Button>
        </div>

        <div class="space-y-4">
          <div
            v-for="(value, key) in currentResultData.params"
            :key="key"
            class="p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div class="flex justify-between">
              <div>
                <span
                  v-tooltip="getParamDescription(key)"
                  class="font-medium text-gray-700 dark:text-gray-300 cursor-help"
                >
                  {{ key }}
                </span>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-gray-900 dark:text-gray-100 font-mono">
                  {{ formatValue(value) }}
                </span>
                <Button
                  size="icon"
                  variant="ghost"
                  class="h-6 w-6"
                  @click="copyValueToClipboard(value)"
                >
                  <ClipboardCopyIcon class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button @click="isDialogOpen = false">关闭</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- 日志查看器 -->
  <LogViewer
    :is-open="showLogViewer"
    :task-id="props.taskId"
    :server-id="props.serverInfo.server_id"
    :total-samples="props.totalSamples"
    @update:is-open="showLogViewer = $event"
  />
</template>

<script setup>
import { useTaskStore } from '@renderer/store'
import { ChevronDown, ClipboardCopyIcon, FileText, InfoIcon, ListIcon } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'
import LogViewer from './LogViewer.vue'

const props = defineProps({
  // 参数类型：'elec' 或 'aging'
  paramType: {
    type: String,
    required: true,
    validator: (value) => ['elec', 'aging'].includes(value),
  },
  // 任务ID
  taskId: {
    type: String,
    default: '',
  },
  // 是否已完成
  completed: {
    type: Boolean,
    default: false,
  },
  // 服务器信息
  serverInfo: {
    type: Object,
    default: () => ({ server_id: '', service_name: '' }),
  },
  totalSamples: {
    type: Number,
    default: 0,
  },
  // 直接传递的结果数据，避免重复请求
  resultData: {
    type: Object,
    default: null,
  },
  // 任务状态，从父组件传递
  taskStatus: {
    type: String,
    default: 'Initializing',
  },
  // 任务持续时间，从父组件传递
  taskDuration: {
    type: String,
    default: '--',
  },
})

const taskStore = useTaskStore()
const isDialogOpen = ref(false)
const showLogViewer = ref(false)

// 使用传递的结果数据或本地状态
const currentResultData = computed(() => props.resultData || null)
const hasResult = computed(
  () => currentResultData.value !== null && currentResultData.value?.params,
)

// 是否应该显示结果组件
const shouldShowResult = computed(() => {
  // 如果有任务ID，就显示组件
  if (props.taskId) return true

  // 如果标记为完成，也显示组件
  if (props.completed) return true

  // 如果有结果数据，也显示组件
  if (hasResult.value) return true

  return false
})

// 使用传递的任务状态或从 store 获取
const currentTaskStatus = computed(() => {
  return (
    props.taskStatus ||
    (() => {
      if (!props.taskId) return 'Initializing'
      const task = taskStore.tasks.find((t) => t.taskId === props.taskId)
      return task?.taskStatus || 'Initializing'
    })()
  )
})

// 获取状态文本
const getStatusText = computed(() => {
  return taskStore.getStatusText(currentTaskStatus.value)
})

// 获取状态样式
const getStatusClass = computed(() => {
  return taskStore.getStatusClass(currentTaskStatus.value)
})

// 使用传递的任务持续时间或从 store 获取
const currentTaskDuration = computed(() => {
  return props.taskDuration !== '--'
    ? props.taskDuration
    : (() => {
        const task = taskStore.tasks.find((t) => t.taskId === props.taskId)
        return task?.duration || '--'
      })()
})

// 显示参数详情
const showParamsDetail = () => {
  isDialogOpen.value = true
}

// 显示任务日志
const showTaskLogs = () => {
  if (!props.taskId) {
    toast.error('无法查看日志', { description: '任务ID不存在' })
    return
  }

  if (!props.serverInfo || !props.serverInfo.server_id) {
    toast.error('无法查看日志', { description: '服务器信息不存在' })
    return
  }

  showLogViewer.value = true
}

// 注意：移除了所有 watch 监听器和 fetchTaskResult 函数
// 现在结果数据直接从父组件通过 props.resultData 传递

// 格式化参数值，处理科学计数法
const formatValue = (value) => {
  if (typeof value === 'number') {
    // 如果是非常小或非常大的数字，使用科学计数法
    if (Math.abs(value) < 0.0001 || Math.abs(value) > 10000) {
      return value.toExponential(8)
    }
    // 否则保留合适的小数位数
    return value.toFixed(value % 1 === 0 ? 0 : 8)
  }
  return value
}

// 复制参数到剪贴板
const copyParamsToClipboard = () => {
  if (!currentResultData.value?.params) return

  try {
    const formattedParams = JSON.stringify(currentResultData.value.params, null, 2)
    navigator.clipboard.writeText(formattedParams)
    toast.success('参数已复制到剪贴板')
  } catch (error) {
    toast.error('复制失败', { description: error.message })
  }
}

// 复制单个值到剪贴板
const copyValueToClipboard = (value) => {
  try {
    navigator.clipboard.writeText(String(value))
    toast.success('值已复制到剪贴板')
  } catch (error) {
    toast.error('复制失败', { description: error.message })
  }
}

// 获取参数描述
const getParamDescription = (key) => {
  const descriptions = {
    negative_particle_radius: '负极粒子半径',
    degradation_of_negative_electrode_diffusivity: '负极扩散系数退化率',
    positive_particle_radius: '正极粒子半径',
    positive_electrode_init_concentration: '正极初始浓度',
    degradation_of_positive_electrode_diffusivity: '正极扩散系数退化率',
    degradation_of_electrolyte_diffusivity: '电解液扩散系数退化率',
    degradation_of_electrolyte_conductivity: '电解液电导率退化率',
    init_stable_SEI_thickness: '初始稳定SEI厚度',
    init_aging_SEI_thickness: '初始老化SEI厚度',
    aging_SEI_electron_conductivity: '老化SEI电子电导率',
    init_dead_lithium_concentration: '初始死锂浓度',
    plated_lithium_kinetic_rate_constant: '锂沉积动力学速率常数',
  }

  return descriptions[key] || '参数描述暂无'
}
</script>
