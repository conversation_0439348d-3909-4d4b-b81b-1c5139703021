import * as Papa from 'papaparse'
// 我们需要的格式
const headerList = ['cycle', 'time', 'current', 'voltage', 'capacity']
const headerType = ['float', 'float', 'float', 'float', 'float']
const headerUnit = ['/', 's', 'A', 'V', 'mAh']
let isFile = false
onmessage = function (e) {
  const file: any = e.data.data
  isFile = e.data.isFile
  // 分片读取文件
  // let resList: any = []
  // readChunkFile(file, (chunkData: any) => {
  //   resList = [...resList, ...chunkData.data]
  //   // 处理进度
  //   const progress = ((chunkData.chunkIndex + 1) / chunkData.chunkCount) * 100
  //   sendMsg({
  //     step: 'read',
  //     progress,
  //     data: null,
  //   })
  //   // 处理完成
  //   if (chunkData.chunkIndex === chunkData.chunkCount - 1) {
  //     sendMsg({
  //       step: 'readEnd',
  //       progress,
  //       data: null,
  //     })
  //     headerToFile(resList)
  //   }
  // })
  sendMsg({
    step: 'read',
    progress: 0,
    data: null,
  })
  readAllFile(file, (dataList: any) => {
    if (dataList.length > 0) {
      sendMsg({
        step: 'readEnd',
        progress: 100,
        data: null,
      })
      headerToFile(dataList)
    }
  })
}
// 发送信息
const sendMsg = (data: any) => {
  postMessage(data)
}
// 根据表头信息，我们把新威，蓝电两种格式的文件，转化成我们自己的格式
// const neware = ['循环号', '电流(A)', '电压(V)', '容量(Ah)', '绝对时间'] // 新威
// const lanhe = ['电流/A', '容量/Ah', '电压/V', '系统时间', '循环序号'] // 蓝电
const headerToFile = (dataList: any) => {
  sendMsg({
    step: 'editing',
    progress: 0,
    data: null,
  })
  const headerCol = dataList[0]
  // 判断是否是默认格式
  const type = isDefault(headerCol.join(''))
  if (type === 'default') {
    let contentData: any = []
    if (dataList.length > 3) {
      const header = headerCol
      const content = dataList.slice(3)
      contentData = addHeaderKey(header, content)
    }
    sendMsg({
      step: 'editEnd',
      progress: 0,
      data: contentData,
    })
    if (isFile) {
      writeFile(dataList)
    }
    return
  }
  const defaultData: any = []
  // 1.建立关联 2.提取单位
  const headerMap = new Map()
  for (let i = 0; i < headerCol.length; i++) {
    // 从新威，蓝电两种格式的表头，转化成我们的自己格式
    const header = headerCol[i]
    const unit = extractUnit(header)
    let key = ''
    if (header.indexOf('循环') !== -1) {
      key = 'cycle'
    }
    if (header.indexOf('时间') !== -1) {
      key = 'time'
    }
    if (header.indexOf('电流') !== -1) {
      key = 'current'
    }
    if (header.indexOf('电压') !== -1) {
      key = 'voltage'
    }
    if (header.indexOf('容量') !== -1) {
      key = 'capacity'
    }
    if (key) {
      headerMap.set(key, {
        i: i,
        name: header,
        unit,
      })
    }
  }
  let initTime: any = null
  for (let i = 1; i < dataList.length; i++) {
    const row = dataList[i]
    const newRow: any = []
    // 计算相对时间
    if (initTime === null) {
      initTime = new Date(row[headerMap.get('time')?.i])
    }
    for (let j = 0; j < headerList.length; j++) {
      const key = headerList[j]
      const mapVal = headerMap.get(key)
      if (mapVal) {
        const unit = mapVal.unit
        if (unit === 'mA') {
          // 转换单位
          newRow.push(row[mapVal.i] / 1000)
        } else if (unit === 'mV') {
          newRow.push(row[mapVal.i] / 1000)
        } else if (unit === 'Ah') {
          newRow.push(Math.round(row[mapVal.i] * 1000 * 100) / 100)
        } else {
          // 计算相对时间
          if (key === 'time') {
            const time = row[mapVal.i]
            const relativeTime = (new Date(time).getTime() - initTime) / 1000
            const timeStr = formatTimeDiff(relativeTime)
            newRow.push(timeStr)
          } else {
            newRow.push(row[mapVal.i])
          }
        }
      }
    }
    defaultData.push(newRow)
  }
  // 将我们的格式加入其中
  defaultData.unshift(headerUnit)
  defaultData.unshift(headerType)
  defaultData.unshift(headerList)
  const contentData = addHeaderKey(headerList, defaultData.slice(3))
  sendMsg({
    step: 'editEnd',
    progress: 0,
    data: contentData,
  })
  if (isFile) {
    writeFile(defaultData)
  }
}
// 将内容中添加上表头
const addHeaderKey = (header: any, content: any) => {
  const result: any = []
  for (let i = 0; i < content.length; i++) {
    const row: any = {}
    for (let j = 0; j < header.length; j++) {
      row[header[j]] = content[i][j]
    }
    result.push(row)
  }
  return result
}
const isDefault = (header: string) => {
  const neware = ['循环号', '电流', '电压', '容量', '绝对时间'] // 新威
  const lanhe = ['电流', '容量', '电压', '系统时间', '循环序号'] // 蓝电
  let newWare: number = 0
  let lanHe: number = 0
  for (let i = 0; i < header.length; i++) {
    for (let j = 0; j < neware.length; j++) {
      if (header.indexOf(neware[j]) !== -1) {
        newWare++
      }
    }
    for (let j = 0; j < lanhe.length; j++) {
      if (header.indexOf(lanhe[j]) !== -1) {
        lanHe++
      }
    }
  }
  if (newWare >= 4) {
    return 'neware'
  }
  if (lanHe >= 4) {
    return 'lanhe'
  }
  return 'default'
}
// 将数组修改完成之后，写入文件
const writeFile = (data: any) => {
  sendMsg({
    step: 'creating',
    progress: 0,
    data: null,
  })
  const csvString = Papa.unparse(
    {
      ...data,
    },
    {
      quotes: true,
      quoteChar: '"',
      escapeChar: '"',
      delimiter: ',',
      header: true,
      newline: '\r\n',
      skipEmptyLines: true,
      // encoding: 'UTF-8',
    },
  )
  // 2. 添加BOM头(解决中文乱码)
  const contentWithBOM = '\uFEFF' + csvString
  // 3. 转换为ArrayBuffer
  const encoder = new TextEncoder()
  const uint8Array = encoder.encode(contentWithBOM)
  const blob = new Blob([uint8Array], { type: 'text/csv;charset=utf-8;' })
  const file: File = new File([blob], 'fileName.csv', {
    type: 'text/csv;charset=utf-8;',
    lastModified: Date.now(),
  })
  sendMsg({
    step: 'createEnd',
    progress: 0,
    data: null,
    file: file,
  })
}
// 提取单位
const extractUnit = (header: string) => {
  const unit = header.replace(/[^a-zA-Z]/g, '')
  return unit || ''
}
// 将时间差转化成 【时：分：秒】的格式
const formatTimeDiff = (milliseconds: number) => {
  // 计算总秒数
  const totalSeconds = Math.floor(milliseconds / 1000)
  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  // 格式化为两位数
  const pad = (num) => num.toString().padStart(2, '0')

  return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
}
// 一次性读取文件
const readAllFile = (file: any, callback: any) => {
  const fileReader = new FileReader()
  fileReader.onload = function (e: any) {
    const csvData = e.target?.result
    if (csvData) {
      Papa.parse(csvData.replace(/^\uFEFF/, ''), {
        encoding: 'GB18030', // 直接指定编码
        skipEmptyLines: true,
        complete: (results: any) => {
          callback(results.data)
        },
      })
    }
  }
  fileReader.readAsText(file, 'GB18030')
}
// 分段读取文件，分的越小，不一定速度就越快，所以我们把数据按照5M分段，速度会达到一个平衡，解决大文件读取慢的问题
// const chunkSize = 1024 * 1024 * 5 // 每次读取的字节数
// // 读取文件分段
// const readChunkFile = (file: any, callback: any) => {
//   const fileSize = file.size // 文件总字节数
//   let start = 0 // 开始字节
//   let end = chunkSize // 结束字节
//   const chunkCount = Math.ceil(fileSize / chunkSize) // 文件分段数
//   let chunkIndex = 0 // 当前分段索引
//   // const chunkData: any = [] // 当前分段数据
//   const fileReader = new FileReader()
//   let buffer = '' // 保存最后一行不完整的数据
//   const chunkFile = (file: any) => {
//     fileReader.onload = function (e: any) {
//       const chunk = e.target?.result
//       if (chunk) {
//         const csvData = buffer ? buffer + chunk : chunk
//         Papa.parse(csvData.replace(/^\uFEFF/, ''), {
//           chunk: (results: any) => {
//             // 最后一行可能不完整，保存到buffer
//             const linebreak = results.meta.linebreak
//             const lastNewline = csvData.lastIndexOf(linebreak)
//             buffer = csvData.slice(lastNewline + 1)?.replaceAll('\n', '')
//             const data = results.data
//             data.pop()
//             callback({
//               data,
//               chunkIndex,
//               chunkCount,
//             })
//           },
//           complete: () => {
//             chunkIndex++
//             if (chunkIndex < chunkCount) {
//               start = end
//               end += chunkSize
//               chunkFile(file)
//             }
//           },
//           error: (error: any) => {
//             console.log(error)
//           },
//         })
//       }
//     }
//     fileReader.readAsText(file.slice(start, end), 'GB18030')
//   }
//   chunkFile(file)
// }
