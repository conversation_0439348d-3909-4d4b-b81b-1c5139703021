export interface ThemeChangedEvent {
  detail: {
    theme: string
  }
}

export interface FlowConfigChangedEvent {
  detail: {
    config: any
  }
}

export interface NodeDataUpdatedEvent {
  detail: {
    nodeId: string
    data: any
  }
}

export interface NodeParamsUpdatedEvent {
  detail: {
    nodeId: string
    workflowId: string
    params: any
  }
}

export interface TaskStatusChangedEvent {
  taskId: string
  status: string
  progress: number
}

export interface TaskProgressUpdatedEvent {
  taskId: string
  progress: number
  status: string
}

export interface TaskResultUpdatedEvent {
  taskId: string
  status: string
  isCompleted: boolean
}

export interface TaskCompletedEvent {
  taskId: string
  status: string
}

export interface TaskErrorEvent {
  taskId: string
  error: any
}
export interface TaskUpdatedEvent {
  taskId: string
  result: any
}

export interface RefreshNodeTask {
  nodeId: string
  taskId: string
}
// 所有事件类型的集合
export type Events = {
  'theme-changed': ThemeChangedEvent // 主题变化事件监听
  'flow-config-changed': FlowConfigChangedEvent // 流程图配置变化事件监听
  'node-data-updated': NodeDataUpdatedEvent // 监听节点数据更新事件
  'node-params-updated': NodeParamsUpdatedEvent //保存节点参数更新事件
  'task-status-changed': TaskStatusChangedEvent // 任务状态变化事件
  'task-progress-updated': TaskProgressUpdatedEvent // 任务进度更新事件
  'task-result-updated': TaskResultUpdatedEvent // 任务结果更新事件
  'task-completed': TaskCompletedEvent // 任务完成事件
  'task-error': TaskErrorEvent // 任务错误事件
  'task-updated': TaskUpdatedEvent // 任务更新事件
  'refresh-node-task': RefreshNodeTask // 用于刷新任务状态
}
