<template>
  <div class="w-full">
    <div v-if="robots.length === 0" class="text-center py-10">
      <EmptyState
        title="暂无机器人"
        description='请点击右上角的"导入机器人"按钮导入机器人'
        icon="mdi:robot-outline"
        class="transition-all duration-500 hover:scale-105"
      />
    </div>

    <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
      <Card
        v-for="(robot, index) in robots"
        :key="index"
        class="group hover:shadow-lg transition-all duration-300 relative overflow-hidden border border-border/40 bg-gradient-to-br from-card to-card/95"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        ></div>

        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle
            class="text-lg font-medium group-hover:text-blue-500 transition-colors duration-300"
          >
            {{ robot.name }}
          </CardTitle>
          <div
            class="rounded-full bg-blue-500/10 p-2.5 transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-500/20"
          >
            <Icon icon="mdi:robot" class="w-5 h-5 text-blue-500" />
          </div>
        </CardHeader>

        <CardContent>
          <p
            class="text-sm text-muted-foreground line-clamp-2 h-10 group-hover:text-foreground/90 transition-colors duration-300"
          >
            {{ robot.description || '暂无描述' }}
          </p>
          <div class="flex items-center justify-between mt-4">
            <span
              class="text-sm font-medium text-gray-500 group-hover:text-gray-700 transition-colors duration-300"
            >
              是否启用
            </span>
            <Switch
              :checked="robot.enabled"
              class="transition-all duration-300"
              @update:checked="(val) => updateRobotStatus(index, val)"
            />
          </div>
        </CardContent>

        <!-- 删除按钮 -->
        <Button
          variant="destructive"
          size="icon"
          class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 scale-90 group-hover:scale-100 hover:bg-destructive/90"
          @click="openDeleteDialog(index)"
        >
          <Trash class="h-4 w-4" />
        </Button>

        <!-- 机器人类型标记 -->
        <Badge
          class="absolute top-1 left-2 bg-blue-400/80 backdrop-blur-sm transition-all duration-300 group-hover:bg-blue-500"
        >
          {{ robot.type === 'custom' ? '自定义' : robot.type }}
        </Badge>
      </Card>
    </div>

    <!-- 导入对话框 -->
    <Dialog :open="isImportDialogOpen" @update:open="isImportDialogOpen = $event">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>导入机器人</DialogTitle>
          <DialogDescription>请选择一个机器人文件 (.robot) 进行导入</DialogDescription>
        </DialogHeader>
        <div class="grid gap-4 py-4">
          <FileDropUpload
            :accept-types="['.robot', '.json']"
            :max-size="5"
            @file-selected="handleFileSelected"
            @error="handleUploadError"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" @click="isImportDialogOpen = false">取消</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 删除确认对话框 -->
    <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除"{{
              robotToDelete !== null ? robots[robotToDelete]?.name : ''
            }}"机器人吗？此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction @click="confirmDelete">确认删除</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { EmptyState, FileDropUpload } from '@renderer/components'
import { Trash } from 'lucide-vue-next'
import { Icon } from '@iconify/vue'
import { toast } from 'vue-sonner'

interface Robot {
  name: string
  description: string
  enabled: boolean
  type: string
  config?: Record<string, any>
}

// 机器人列表
const robots = ref<Robot[]>([])

// 导入对话框状态
const isImportDialogOpen = ref(false)
const isDeleteDialogOpen = ref(false)
const robotToDelete = ref<number | null>(null)

// 打开导入对话框
const openImportDialog = () => {
  isImportDialogOpen.value = true
}

// 处理文件选择
const handleFileSelected = async (file: File) => {
  if (!file.name.endsWith('.robot') && !file.name.endsWith('.json')) {
    toast.error('文件格式错误', {
      description: '请选择.robot或.json格式的文件',
    })
    return
  }

  try {
    const text = await file.text()
    const robotData = JSON.parse(text)

    // 简单验证
    if (!robotData.name) {
      throw new Error('无效的机器人配置文件')
    }

    // 添加到列表
    robots.value.push({
      name: robotData.name,
      description: robotData.description || '无描述',
      enabled: true,
      type: robotData.type || 'custom',
      config: robotData.config || {},
    })

    toast.success('导入成功', {
      description: `成功导入机器人 ${robotData.name}`,
    })
    isImportDialogOpen.value = false
  } catch (error) {
    toast.error('导入失败', {
      description: error instanceof Error ? error.message : '无效的机器人配置文件',
    })
  }
}

// 处理上传错误
const handleUploadError = (error: string) => {
  toast.error('上传错误', {
    description: error,
  })
}

// 更新机器人状态
const updateRobotStatus = (index: number, enabled: boolean) => {
  if (index >= 0 && index < robots.value.length) {
    robots.value[index].enabled = enabled
    toast.info(enabled ? '已启用' : '已禁用', {
      description: `${robots.value[index].name} 已${enabled ? '启用' : '禁用'}`,
    })
  }
}

// 打开删除对话框
const openDeleteDialog = (index: number) => {
  robotToDelete.value = index
  isDeleteDialogOpen.value = true
}

// 确认删除
const confirmDelete = () => {
  if (
    robotToDelete.value !== null &&
    robotToDelete.value >= 0 &&
    robotToDelete.value < robots.value.length
  ) {
    const robotName = robots.value[robotToDelete.value].name
    robots.value.splice(robotToDelete.value, 1)

    toast.success('删除成功', {
      description: `成功删除机器人 ${robotName}`,
    })
  }

  isDeleteDialogOpen.value = false
  robotToDelete.value = null
}

defineExpose({
  openImportDialog,
})
</script>
<style lang="scss" scoped>
.card {
  @apply backdrop-blur-sm;

  &:hover {
    @apply shadow-md shadow-blue-500/5;
  }
}

.badge {
  @apply text-xs font-medium px-2 py-0.5 rounded-full;
}
</style>
