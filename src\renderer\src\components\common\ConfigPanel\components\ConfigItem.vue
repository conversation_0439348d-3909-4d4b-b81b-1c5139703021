<template>
  <Card
    :class="[
      'transition-all duration-200 hover:shadow-sm',
      size === 'sm' ? 'text-xs' : '',
      highlight
        ? 'border-orange-200 bg-orange-50 dark:border-orange-700 dark:bg-orange-900/20'
        : 'border-muted/50',
      editable ? 'cursor-pointer hover:border-primary/50' : '',
    ]"
    @click="handleClick"
  >
    <CardContent :class="[size === 'sm' ? 'p-2' : 'p-3']">
      <div class="flex items-center justify-between">
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <span
              :class="[
                'font-medium truncate',
                size === 'sm' ? 'text-xs' : 'text-sm',
                highlight ? 'text-orange-900 dark:text-orange-100' : 'text-foreground',
              ]"
            >
              {{ label }}
            </span>

            <!-- 状态指示器 -->
            <div
              :class="[
                'flex-shrink-0 rounded-full',
                size === 'sm' ? 'w-1.5 h-1.5' : 'w-2 h-2',
                enabled ? 'bg-green-500' : 'bg-muted-foreground',
              ]"
            ></div>
          </div>

          <div
            v-if="description"
            :class="[
              'mt-1 truncate',
              size === 'sm' ? 'text-[10px]' : 'text-xs',
              highlight ? 'text-orange-700 dark:text-orange-300' : 'text-muted-foreground',
            ]"
            :title="description"
          >
            {{ description }}
          </div>
        </div>

        <div class="flex items-center space-x-2 flex-shrink-0">
          <!-- 可编辑时显示开关 -->
          <Switch
            v-if="editable"
            :checked="enabled"
            :size="size === 'sm' ? 'sm' : 'default'"
            class="ml-2"
            @update:checked="handleToggle"
          />

          <!-- 不可编辑时显示状态徽章 -->
          <Badge
            v-else
            :variant="enabled ? 'default' : 'secondary'"
            :class="[size === 'sm' ? 'text-[10px] px-1.5 py-0.5' : 'text-xs']"
          >
            {{ enabled ? '启用' : '禁用' }}
          </Badge>

          <!-- 高亮标识 -->
          <Tooltip v-if="highlight">
            <TooltipTrigger>
              <AlertTriangle
                :class="[
                  'text-orange-600 dark:text-orange-400',
                  size === 'sm' ? 'h-3 w-3' : 'h-3.5 w-3.5',
                ]"
              />
            </TooltipTrigger>
            <TooltipContent>
              <p class="text-xs">此配置项在当前应用中被特别关注</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent } from '@renderer/components/ui/card'
import { Switch } from '@renderer/components/ui/switch'
import { Tooltip, TooltipContent, TooltipTrigger } from '@renderer/components/ui/tooltip'
import { AlertTriangle } from 'lucide-vue-next'

interface Props {
  label: string
  enabled: boolean
  description?: string
  highlight?: boolean
  editable?: boolean
  size?: 'default' | 'sm'
  configPath?: string
}

interface Emits {
  (e: 'toggle', configPath: string, newValue: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  editable: false,
})

const emit = defineEmits<Emits>()

const handleToggle = (newValue: boolean) => {
  if (props.configPath) {
    emit('toggle', props.configPath, newValue)
  }
}

const handleClick = () => {
  if (props.editable) {
    handleToggle(!props.enabled)
  }
}
</script>
