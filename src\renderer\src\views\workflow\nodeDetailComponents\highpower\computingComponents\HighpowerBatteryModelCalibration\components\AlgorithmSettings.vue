<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible>
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">参数识别算法参数设置</CardTitle>
            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <ChevronDown
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-2">
              <div class="space-y-1">
                <Label for="max-iterations" class="text-xs">最大迭代次数</Label>
                <Input
                  id="max-iterations"
                  v-model="localParams.max_iter"
                  type="number"
                  min="1"
                  class="h-8 text-sm"
                  placeholder="请输入最大迭代次数"
                  :disabled="isProcessing"
                  @change="updateParams"
                />
              </div>
              <div class="space-y-1">
                <Label for="max-rmse" class="text-xs">
                  最大允许均方根误差 ({{ paramType === 'elec' ? 'V' : 'A.h' }})
                </Label>
                <Input
                  id="max-rmse"
                  v-model="localParams.max_rmse"
                  type="number"
                  min="0.1"
                  step="0.1"
                  class="h-8 text-sm"
                  placeholder="请输入最大允许RMSE"
                  :disabled="isProcessing"
                  @change="updateParams"
                />
              </div>
              <div class="space-y-1">
                <Label for="max-iterations" class="text-xs">每代样本数</Label>
                <Input
                  id="max-iterations"
                  v-model="localParams.nums_per_iter"
                  type="number"
                  min="1"
                  class="h-8 text-sm"
                  placeholder="请输入"
                  :disabled="isProcessing"
                  @change="updateParams"
                />
              </div>
              <div class="space-y-1">
                <Label for="max-iterations" class="text-xs">最大并行任务数</Label>
                <Input
                  id="max-iterations"
                  v-model="localParams.max_workers"
                  type="number"
                  min="1"
                  class="h-8 text-sm"
                  placeholder="请输入"
                  :disabled="isProcessing"
                  @change="updateParams"
                />
              </div>
              <div class="space-y-1">
                <Label for="max-iterations" class="text-xs">单任务超时时间（秒）</Label>
                <Input
                  id="max-iterations"
                  v-model="localParams.task_timeout"
                  type="number"
                  min="1"
                  class="h-8 text-sm"
                  placeholder="请输入"
                  :disabled="isProcessing"
                  @change="updateParams"
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup>
import { ChevronDown } from 'lucide-vue-next'
import { ref, watch } from 'vue'

const props = defineProps({
  // 算法参数对象，包含最大迭代圈数和最大允许均方根误差
  algorithmParams: {
    type: Object,
    required: true,
  },
  // 是否正在处理任务
  isProcessing: {
    type: Boolean,
    default: false,
  },
  // 参数类型：elec(电化学参数) 或 aging(老化参数)
  paramType: {
    type: String,
    default: 'elec',
    validator: (value) => ['elec', 'aging'].includes(value),
  },
})

const emit = defineEmits(['update:algorithm-params'])

const localParams = ref({
  max_iter: props.algorithmParams.max_iter,
  max_rmse: props.algorithmParams.max_rmse,
  nums_per_iter: props.algorithmParams.nums_per_iter,
  task_timeout: props.algorithmParams.task_timeout,
  max_workers: props.algorithmParams.max_workers,
})

watch(
  () => props.algorithmParams,
  (newParams) => {
    localParams.value = {
      max_iter: newParams.max_iter,
      max_rmse: newParams.max_rmse,
      nums_per_iter: newParams.nums_per_iter,
      task_timeout: newParams.task_timeout,
      max_workers: newParams.max_workers,
    }
  },
  { deep: true },
)

const updateParams = () => {
  emit('update:algorithm-params', localParams.value)
}
</script>
