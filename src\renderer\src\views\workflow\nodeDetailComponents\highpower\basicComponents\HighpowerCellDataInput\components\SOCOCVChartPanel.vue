<template>
  <BaseChartPanel
    :title="title"
    :has-data="isPositiveFinished || isNegativeFinished"
    :is-disabled="isParamsExtract"
    @reset="resetAll"
  >
    <!-- 正极上传面板 -->
    <div class="mb-6 border border-gray-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-lg font-medium text-foreground">正极数据</h3>
        <Button
          v-if="isPositiveFinished"
          variant="outline"
          size="sm"
          :disabled="isParamsExtract"
          @click="resetPositive"
        >
          重置
        </Button>
      </div>
      <FileDropUpload
        v-show="!isPositiveFinished || isResettingPositive"
        ref="positiveUploadRef"
        :accept-types="['.csv']"
        :max-size="20"
        :progress-control="true"
        :is-disabled="isParamsExtract"
        @file-selected="(file) => handleFileSelected('positive', file)"
        @error="(error) => $emit('upload-error', error)"
        @progress="(progress) => $emit('upload-progress', progress)"
        @upload-complete="$emit('upload-complete')"
      />
      <div v-show="isPositiveFinished && !isResettingPositive">
        <div class="w-full aspect-[4/3] relative">
          <div
            ref="positiveChartRef"
            v-chart-resize="chartInstances.positive"
            class="w-full h-full absolute inset-0"
          ></div>
        </div>
      </div>
    </div>

    <!-- 负极上传面板 -->
    <div class="border border-gray-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-lg font-medium text-foreground">负极数据</h3>
        <Button
          v-if="isNegativeFinished"
          variant="outline"
          size="sm"
          :disabled="isParamsExtract"
          @click="resetNegative"
        >
          重置
        </Button>
      </div>
      <FileDropUpload
        v-show="!isNegativeFinished || isResettingNegative"
        ref="negativeUploadRef"
        :accept-types="['.csv']"
        :max-size="20"
        :progress-control="true"
        :is-disabled="isParamsExtract"
        @file-selected="(file) => handleFileSelected('negative', file)"
        @error="(error) => $emit('upload-error', error)"
        @progress="(progress) => $emit('upload-progress', progress)"
        @upload-complete="$emit('upload-complete')"
      />
      <div v-show="isNegativeFinished && !isResettingNegative">
        <div class="w-full aspect-[4/3] relative">
          <div
            ref="negativeChartRef"
            v-chart-resize="chartInstances.negative"
            class="w-full h-full absolute inset-0"
          ></div>
        </div>
      </div>
    </div>

    <!-- 单调性校验对话框 -->
    <Dialog :open="showMonotonicityDialog" @close="showMonotonicityDialog = false">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>数据单调性校验</DialogTitle>
          <DialogDescription>
            检测到{{
              currentElectrodeType === 'positive' ? '正极' : '负极'
            }}数据不满足单调性要求，是否进行仍然上传？
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" @click="handleMonotonicityCancel">重新上传</Button>
          <Button @click="handleMonotonicityOptimize">仍然上传</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </BaseChartPanel>
</template>

<script setup lang="ts">
import { FileDropUpload } from '@renderer/components'
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import BaseChartPanel from './BaseChartPanel.vue'
import { IndexedDBHelper } from '@renderer/database/IndexedDBHelper'
interface DataPoint {
  soc: number
  ocv: number
}

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    required: true,
  },
  // 正极数据
  // positiveData: {
  //   type: Array as () => DataPoint[],
  //   default: () => [],
  // },
  // // 负极数据
  // negativeData: {
  //   type: Array as () => DataPoint[],
  //   default: () => [],
  // },
  // 正极数据是否处于重置状态
  isResettingPositive: {
    type: Boolean,
    default: false,
  },
  // 负极数据是否处于重置状态
  isResettingNegative: {
    type: Boolean,
    default: false,
  },
  //参数是否提取
  isParamsExtract: {
    type: Boolean,
    default: false,
  },
  //正极SOCOCV数据存储ID
  positiveUploadStoredId: {
    type: String,
    default: '',
  },
  //负极SOCOCV数据存储ID
  negativeUploadStoredId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits([
  'reset', // 重置事件
  'reset-positive', // 重置正极数据事件
  'reset-negative', // 重置负极数据事件
  'file-selected-positive', // 正极文件选择事件
  'file-selected-negative', // 负极文件选择事件
  'upload-error', // 上传错误事件
  'upload-progress', // 上传进度事件
  'upload-complete', // 上传完成事件
  'chart-ready', // 图表准备就绪事件
  'data-optimized', // 数据优化完成事件
])

// 判断是否有数据
// const hasData = computed(() => {
//   return props.positiveData.length > 0 || props.negativeData.length > 0
// })

// 单调性校验对话框状态
const showMonotonicityDialog = ref(false)
const currentElectrodeType = ref<'positive' | 'negative'>('positive')
const currentFile = ref<File | null>(null)

const positiveChartRef = ref<HTMLElement | null>(null)
const negativeChartRef = ref<HTMLElement | null>(null)

// 图表实例
const chartInstances = ref<Record<string, echarts.ECharts>>({})
const isActive = ref(true)
const isFinished = ref(false)
const isPositiveFinished = computed(() => props.positiveUploadStoredId !== '')
const isNegativeFinished = computed(() => props.negativeUploadStoredId !== '')
const positiveSOCOCVData = ref({} as any)
const negativeSOCOCVData = ref({} as any)
const dbHelper = new IndexedDBHelper('uploadDatabase', 1)
// 图表类型配置
const chartTypes = [
  {
    key: 'positive',
    label: '正极',
    title: '正极 SOC-OCV曲线',
    color: '#f56c6c',
    ref: positiveChartRef,
  },
  {
    key: 'negative',
    label: '负极',
    title: '负极 SOC-OCV曲线',
    color: '#409eff',
    ref: negativeChartRef,
  },
]

// 处理文件选择，增加单调性校验
const handleFileSelected = (type: 'positive' | 'negative', file: File) => {
  // const stack = new Error().stack
  // console.log('增强调用栈', stack)
  currentElectrodeType.value = type
  currentFile.value = file
  emitFileSelected(type, file)

  // 先检查文件内容是否满足单调性要求
  // const reader = new FileReader()
  // reader.onload = (e) => {
  //   try {
  //     const content = e.target?.result as string
  //     const lines = content.split('\n')
  //     const data: DataPoint[] = []

  //     // 简单解析CSV，假设第一列是SOC，第二列是OCV
  //     // 跳过标题行
  //     for (let i = 1; i < lines.length; i++) {
  //       const line = lines[i].trim()
  //       if (!line) continue

  //       const values = line.split(',')
  //       if (values.length >= 2) {
  //         const soc = parseFloat(values[0])
  //         const ocv = parseFloat(values[1])
  //         if (!isNaN(soc) && !isNaN(ocv)) {
  //           data.push({ soc, ocv })
  //         }
  //       }
  //     }

  //     // 检查单调性
  //     if (data.length > 0 && !checkMonotonicity(data)) {
  //       // 数据不满足单调性，显示对话框
  //       showMonotonicityDialog.value = true
  //     } else {
  //       // 数据满足单调性，直接发送文件
  //       emitFileSelected(type, file)
  //     }
  //   } catch (error) {
  //     console.error('解析文件失败:', error)
  //     emit('upload-error', '文件解析失败，请检查文件格式')
  //   }
  // }
  // reader.readAsText(file)
}

// 检查数据单调性
const checkMonotonicity = (data: DataPoint[]) => {
  // 对于正极，OCV应该随SOC增加而增加
  // 对于负极，OCV应该随SOC增加而减少
  const isPositive = currentElectrodeType.value === 'positive'

  for (let i = 1; i < data.length; i++) {
    const prevOcv = data[i - 1].ocv
    const currOcv = data[i].ocv

    if (isPositive) {
      // 正极：OCV应该随SOC增加而增加
      if (currOcv < prevOcv) {
        return false
      }
    } else {
      // 负极：OCV应该随SOC增加而减少
      if (currOcv > prevOcv) {
        return false
      }
    }
  }

  return true
}

// 优化数据单调性
const optimizeMonotonicity = (data: DataPoint[]) => {
  const isPositive = currentElectrodeType.value === 'positive'
  const optimizedData = [...data]

  // 使用简单的移动平均法进行平滑处理
  const windowSize = 3 // 平滑窗口大小

  for (let i = 1; i < optimizedData.length - 1; i++) {
    const prevOcv = optimizedData[i - 1].ocv
    const currOcv = optimizedData[i].ocv
    const nextOcv = optimizedData[i + 1].ocv

    if ((isPositive && currOcv < prevOcv) || (!isPositive && currOcv > prevOcv)) {
      // 如果不满足单调性，使用移动平均值替换
      optimizedData[i].ocv = (prevOcv + currOcv + nextOcv) / 3
    }
  }

  // 再次检查并强制单调性
  for (let i = 1; i < optimizedData.length; i++) {
    if (isPositive) {
      // 正极：确保OCV随SOC增加而增加
      if (optimizedData[i].ocv < optimizedData[i - 1].ocv) {
        optimizedData[i].ocv = optimizedData[i - 1].ocv + 0.001 // 微小增加
      }
    } else {
      // 负极：确保OCV随SOC增加而减少
      if (optimizedData[i].ocv > optimizedData[i - 1].ocv) {
        optimizedData[i].ocv = optimizedData[i - 1].ocv - 0.001 // 微小减少
      }
    }
  }

  return optimizedData
}

// 处理单调性优化
const handleMonotonicityOptimize = () => {
  return
  // showMonotonicityDialog.value = false

  // // 读取文件内容并优化
  // const reader = new FileReader()
  // reader.onload = (e) => {
  //   try {
  //     const content = e.target?.result as string
  //     const lines = content.split('\n')
  //     const data: DataPoint[] = []

  //     // 解析CSV
  //     for (let i = 1; i < lines.length; i++) {
  //       const line = lines[i].trim()
  //       if (!line) continue

  //       const values = line.split(',')
  //       if (values.length >= 2) {
  //         const soc = parseFloat(values[0])
  //         const ocv = parseFloat(values[1])
  //         if (!isNaN(soc) && !isNaN(ocv)) {
  //           data.push({ soc, ocv })
  //         }
  //       }
  //     }

  //     // 优化数据
  //     //const optimizedData = optimizeMonotonicity(data)
  //     const optimizedData = data

  //     // 发送优化后的数据
  //     emit('data-optimized', {
  //       type: currentElectrodeType.value,
  //       data: optimizedData,
  //     })

  //     // 创建一个新的文件对象，包含优化后的数据
  //     const header = 'SOC,OCV\n'
  //     const rows = optimizedData.map((item) => `${item.soc},${item.ocv}`).join('\n')
  //     const optimizedContent = header + rows

  //     const optimizedFile = new File(
  //       [optimizedContent],
  //       currentFile.value!.name.replace('.csv', '_optimized.csv'),
  //       { type: 'text/csv' },
  //     )

  //     // 发送优化后的文件
  //     emitFileSelected(currentElectrodeType.value, optimizedFile)
  //   } catch (error) {
  //     console.error('优化数据失败:', error)
  //     emit('upload-error', '数据优化失败，请重新上传文件')
  //   }
  // }
  // reader.readAsText(currentFile.value!)
}

// 处理取消优化
const handleMonotonicityCancel = () => {
  showMonotonicityDialog.value = false
  // 不进行优化，需要用户重新上传
  emit(
    'upload-error',
    `${currentElectrodeType.value === 'positive' ? '正极' : '负极'}数据不满足单调性要求，请重新上传`,
  )
}

// 发送文件选择事件
const emitFileSelected = (type: 'positive' | 'negative', file: File) => {
  if (type === 'positive') {
    emit('file-selected-positive', file)
  } else {
    emit('file-selected-negative', file)
  }
}

// 安全地清理图表实例
const safeDispose = (chart: echarts.ECharts | null) => {
  if (!chart) return null
  try {
    if (typeof chart.dispose === 'function') {
      chart.dispose()
    }
  } catch (e) {
    console.error('清理图表实例失败:', e)
  }
  return null
}

// 清理所有图表实例
const cleanupCharts = () => {
  try {
    Object.keys(chartInstances.value).forEach((key) => {
      chartInstances.value[key] = safeDispose(chartInstances.value[key]) as any
      delete chartInstances.value[key]
    })
  } catch (error) {
    console.error('清理图表实例失败:', error)
  }
}

// 创建图表配置
const createChartOption = (type: 'positive' | 'negative', data) => {
  const chartType = chartTypes.find((t) => t.key === type)!

  return {
    title: {
      text: chartType.title,
      left: 'center',
      top: 5,
      textStyle: { fontSize: 14 },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        const param = Array.isArray(params) ? params[0] : params
        return (
          (type === 'positive' ? '正极' : '负极') +
          '<br/>SOC: ' +
          param.axisValue +
          '<br/>OCV: ' +
          param.value +
          ' V'
        )
      },
    },
    grid: { top: 40, right: 50, bottom: 60, left: 60 },
    xAxis: {
      type: 'value',
      name: 'SOC',
      nameLocation: 'center',
      nameGap: 30,
      nameTextStyle: {
        fontSize: 14,
        fontWeight: 'bold',
      },
    },
    yAxis: { type: 'value', name: 'OCV (V)' },
    series: [
      {
        name: 'OCV',
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'none',
        lineStyle: { color: chartType.color },
        sampling: 'lttb',
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomLock: false, // 允许缩放
        zoomOnMouseWheel: true, // 鼠标滚轮缩放
        moveOnMouseMove: true, // 鼠标拖动
        moveOnMouseWheel: false, // 禁用鼠标滚轮平移
        preventDefaultMouseMove: true, // 阻止默认行为
      },
      {
        type: 'slider',
        show: false,
        start: 0,
        end: 100,
        height: 12, // 调整滑块高度
        bottom: 2, // 调整滑块位置
        // backgroundColor: '#f5f5f5', // 背景色
        // fillerColor: '#409eff', // 选中区域颜色
        // borderColor: '#ddd', // 边框颜色
        // handleSize: 8, // 调整手柄大小
        // handleStyle: {
        //   color: '#409eff', // 手柄颜色
        //   borderColor: '#fff', // 手柄边框颜色
        //   borderWidth: 1, // 手柄边框宽度
        // },
        // textStyle: {
        //   color: '#666', // 文字颜色
        //   fontSize: 10, // 文字大小
        // },
      },
    ],
    animation: false, // 关闭动画提升性能
  }
}
const setData = (newData = {}) => {
  if (!isActive.value) return
  if (newData && Object.keys(newData).length !== 0) {
    if ('positive' in newData) {
      positiveSOCOCVData.value = newData.positive
    }
    if ('negative' in newData) {
      negativeSOCOCVData.value = newData.negative
    }
  }
  isFinished.value = true
  nextTick(() => {
    try {
      cleanupCharts()

      // 初始化正极图表
      if (positiveSOCOCVData.value?.length > 0) {
        try {
          const chart = echarts.init(positiveChartRef.value)
          chart.setOption(createChartOption('positive', positiveSOCOCVData.value))
          chartInstances.value['positive'] = chart
        } catch (e) {
          console.error('初始化正极图表失败:', e)
        }
      }

      // 初始化负极图表
      if (negativeSOCOCVData.value?.length > 0) {
        try {
          const chart = echarts.init(negativeChartRef.value)
          chart.setOption(createChartOption('negative', negativeSOCOCVData.value))
          chartInstances.value['negative'] = chart
        } catch (e) {
          console.error('初始化负极图表失败:', e)
        }
      }

      // 发出图表准备就绪事件
      if (Object.keys(chartInstances.value).length > 0) {
        emit('chart-ready', chartInstances.value)
      }
    } catch (error) {
      console.error('初始化图表过程中出错:', error)
    }
  })
}
// 初始化图表
// const initChart = () => {
//   if (!isActive.value) return

//   nextTick(() => {
//     try {
//       cleanupCharts()

//       // 初始化正极图表
//       if (props.positiveData.length && !props.isResettingPositive && positiveChartRef.value) {
//         try {
//           const chart = echarts.init(positiveChartRef.value)
//           chart.setOption(createChartOption('positive', props.positiveData))
//           chartInstances.value['positive'] = chart
//         } catch (e) {
//           console.error('初始化正极图表失败:', e)
//         }
//       }

//       // 初始化负极图表
//       if (props.negativeData.length && !props.isResettingNegative && negativeChartRef.value) {
//         try {
//           const chart = echarts.init(negativeChartRef.value)
//           chart.setOption(createChartOption('negative', props.negativeData))
//           chartInstances.value['negative'] = chart
//         } catch (e) {
//           console.error('初始化负极图表失败:', e)
//         }
//       }

//       // 发出图表准备就绪事件
//       if (Object.keys(chartInstances.value).length > 0) {
//         emit('chart-ready', chartInstances.value)
//       }
//     } catch (error) {
//       console.error('初始化图表过程中出错:', error)
//     }
//   })
// }

// 在组件挂载后初始化图表
onMounted(() => {
  isActive.value = true
  setDataFromStoredId(props.positiveUploadStoredId, 'positive')
  setDataFromStoredId(props.negativeUploadStoredId, 'negative')
})

const setDataFromStoredId = (uploadStoredId, dataType: 'positive' | 'negative') => {
  if (uploadStoredId !== '') {
    setTimeout(() => {
      if (isActive.value) {
        dbHelper
          .open()
          .then(() => {
            dbHelper
              .getData('UploadData', uploadStoredId)
              .then((storedData) => {
                if (storedData) {
                  setData({ [dataType]: storedData.data.sococv })
                }
              })
              .catch((error) => {})
          })
          .catch((error) => {})
      }
    }, 300)
  }
}

const resetAll = () => {
  deleteStoredData(props.positiveUploadStoredId)
  deleteStoredData(props.negativeUploadStoredId)
  progressStatus.positive.read.value = 0
  progressStatus.positive.processData.value = 0
  progressStatus.positive.upload.value = 0
  progressStatus.negative.read.value = 0
  progressStatus.negative.processData.value = 0
  progressStatus.negative.upload.value = 0

  setTimeout(() => {
    emit('reset')
  }, 100)
}

const resetPositive = () => {
  deleteStoredData(props.positiveUploadStoredId)
  progressStatus.positive.read.value = 0
  progressStatus.positive.processData.value = 0
  progressStatus.positive.upload.value = 0
  if (isActive.value) {
    fileDropUploadRef.positive.value?.updateProgress(0)
  }
  setTimeout(() => {
    emit('reset-positive')
  }, 100)
}

const resetNegative = () => {
  deleteStoredData(props.negativeUploadStoredId)
  progressStatus.negative.read.value = 0
  progressStatus.negative.processData.value = 0
  progressStatus.negative.upload.value = 0
  fileDropUploadRef.negative.value?.updateProgress(0)
  setTimeout(() => {
    emit('reset-negative')
  }, 100)
}

const deleteStoredData = (storedId) => {
  if (storedId !== '') {
    {
      dbHelper
        .open()
        .then(() => {
          dbHelper.deleteData('UploadData', storedId)
        })
        .catch((error) => {
          console.error('Error opening database:', error)
        })
    }
  }
}

// 监听数据变化，重新渲染图表
watch(
  [
    // () => props.positiveSOCOCVData,
    // () => props.negativeSOCOCVData,
    () => props.isResettingPositive,
    () => props.isResettingNegative,
  ],
  () => {
    if (props.isResettingPositive || props.isResettingNegative) {
      cleanupCharts()
    }
    if (isActive.value) {
      setTimeout(() => {
        //initChart()
        setData()
      }, 300)
    }
  },
  { deep: true },
)

// 在组件销毁前清理图表实例和事件监听器
onUnmounted(() => {
  isActive.value = false
  cleanupCharts()
})

const progressStatus = {
  positive: {
    read: ref(0),
    processData: ref(0),
    upload: ref(0),
  },
  negative: {
    read: ref(0),
    processData: ref(0),
    upload: ref(0),
  },
}

const negativeUploadRef = ref(null)
const positiveUploadRef = ref(null)

// 如果需要集中管理
const fileDropUploadRef = {
  positive: positiveUploadRef,
  negative: negativeUploadRef,
}

const updateProgress = (step, progress, dataType) => {
  progressStatus[dataType][step].value = progress
  const uploadProgress =
    Math.round(
      (5 +
        progressStatus[dataType].read.value * 0.2 +
        progressStatus[dataType].processData.value * 0.5 +
        progressStatus[dataType].upload.value * 0.2) *
        100,
    ) / 100
  if (isActive.value) {
    fileDropUploadRef[dataType].value.updateProgress(uploadProgress)
  }
}

defineExpose({ setData, updateProgress })
</script>

<style scoped>
.chart-container {
  transition: all 0.3s ease;
}
</style>
