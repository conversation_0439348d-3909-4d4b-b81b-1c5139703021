import { electronAPI } from '@electron-toolkit/preload'
import { contextBridge } from 'electron'
import { appApi } from './api/appApi'
import { appInfoApi } from './api/appInfoApi'
import { cacheApi } from './api/cacheApi'
import { fileApi } from './api/fileApi'
import { grpcApi } from './api/grpcApi'
import { loggerApi } from './api/loggerApi'
import { windowApi } from './api/windowApi'
import logger from './utils/logger'
import { setupSecureIpc } from './utils/secureIpc'
// 输出环境变量信息
logger.info('环境变量:', {
  VITE_APP_LINKER_API: process.env.VITE_APP_LINKER_API,
  VITE_APP_BASE_API: process.env.VITE_APP_BASE_API,
})

// 设置应用类型标识
const isMatt = process.env.VITE_APP_IS_MATT === '1'
if (process.contextIsolated) {
  logger.info(`已暴露应用类型: isMatt=${isMatt}`)
  contextBridge.exposeInMainWorld('__IS_MATT__', isMatt)
} else {
  // @ts-ignore: Expose isMatt to window for renderer process
  window.__IS_MATT__ = isMatt
}

// 设置安全的IPC通信
const validChannels = [
  'get-versions',
  'get-env-vars',
  'show-save-dialog',
  'save-file',
  'show-open-dialog',
  'read-file',
  'grpc-call',
  'grpc-submit',
  'grpc-agent',
  'window-minimize',
  'window-maximize',
  'window-close',
  'is-window-maximized',
  'window-maximized-change',
  'get-grpc-status',
  'update-linker-api',
  'restart-app',
  'encrypt-and-save-file',
  'clear-app-cache',
  'clear-storage-data',
  'set-login-window-size',
  'adjust-window-after-login',
  'update-auth-info',
  'ping',
  'grpc-stop-generation',
  'grpc-client-stream',
  'upload-csv-file',
  'upload-status',
]

// 暴露API到渲染进程
if (process.contextIsolated) {
  try {
    // 设置安全的IPC通信
    setupSecureIpc(validChannels)

    // 暴露各种API
    contextBridge.exposeInMainWorld('api', appApi)
    contextBridge.exposeInMainWorld('grpcApi', grpcApi)
    contextBridge.exposeInMainWorld('windowControl', windowApi)
    contextBridge.exposeInMainWorld('electronAPI', fileApi)
    contextBridge.exposeInMainWorld('cacheApi', cacheApi)
    contextBridge.exposeInMainWorld('appInfo', appInfoApi)
    contextBridge.exposeInMainWorld('logger', loggerApi)

    logger.info('APIs exposed successfully')
  } catch (error) {
    logger.error('Failed to expose APIs:', error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = appApi
  // @ts-ignore (define in dts)
  window.grpcApi = grpcApi
  // @ts-ignore (define in dts)
  window.windowControl = windowApi
  // @ts-ignore (define in dts)
  window.electronAPI = fileApi
  // @ts-ignore (define in dts)
  window.cacheApi = cacheApi
  // @ts-ignore (define in dts)
  window.appInfo = appInfoApi
  // @ts-ignore (define in dts)
  window.logger = loggerApi
}

// 添加 CSP meta 标签
if (typeof document !== 'undefined') {
  const addCspMeta = () => {
    try {
      const cspMeta = document.createElement('meta')
      cspMeta.httpEquiv = 'Content-Security-Policy'
      cspMeta.content = `
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval';
        style-src 'self' 'unsafe-inline';
        img-src 'self' data: https: http:;
        connect-src 'self' https: http: ws: wss: grpc:;
        font-src 'self' data:
      `
      if (document.head) {
        document.head.appendChild(cspMeta)
        logger.info('CSP meta 标签已添加')
      } else {
        console.warn('document.head 不可用，无法添加 CSP meta 标签')
      }
    } catch (error) {
      logger.error('添加 CSP meta 标签失败:', error)
    }
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addCspMeta)
  } else {
    addCspMeta()
  }
} else {
  console.warn('document 对象不可用，无法添加 CSP meta 标签')
}
