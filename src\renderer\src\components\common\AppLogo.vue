<template>
  <div class="app-logo-container">
    <!-- 应用Logo -->
    <img
      v-if="showLogo"
      :src="logoUrl"
      :alt="`${appMeta.title} Logo`"
      :class="logoClass"
      @error="handleImageError"
    />

    <!-- 应用图标 -->
    <img
      v-if="showIcon"
      :src="iconUrl"
      :alt="`${appMeta.title} Icon`"
      :class="iconClass"
      @error="handleImageError"
    />

    <!-- 应用标题 -->
    <div v-if="showTitle" :class="titleClass">
      {{ appMeta.title }}
    </div>

    <!-- 应用版本 -->
    <div v-if="showVersion" :class="versionClass">v{{ appMeta.version }}</div>
  </div>
</template>

<script setup lang="ts">
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { computed } from 'vue'

interface Props {
  /** 是否显示Logo */
  showLogo?: boolean
  /** 是否显示图标 */
  showIcon?: boolean
  /** 是否显示标题 */
  showTitle?: boolean
  /** 是否显示版本 */
  showVersion?: boolean
  /** Logo样式类 */
  logoClass?: string
  /** 图标样式类 */
  iconClass?: string
  /** 标题样式类 */
  titleClass?: string
  /** 版本样式类 */
  versionClass?: string
  /** 容器样式类 */
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  showLogo: true,
  showIcon: false,
  showTitle: true,
  showVersion: false,
  logoClass: 'h-8 w-auto',
  iconClass: 'h-6 w-6',
  titleClass: 'text-lg font-semibold text-gray-900 dark:text-white',
  versionClass: 'text-xs text-gray-500 dark:text-gray-400',
  containerClass: 'flex items-center space-x-3',
})

const { appMeta, getIconUrl, getLogoUrl } = useAppConfig()

// 获取图标和Logo URL
const iconUrl = computed(() => getIconUrl.value)
const logoUrl = computed(() => getLogoUrl.value)

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  console.warn(`Failed to load image: ${target.src}`)

  // 可以设置一个默认图片或隐藏图片
  target.style.display = 'none'
}
</script>

<style scoped>
.app-logo-container {
  @apply v-bind(containerClass);
}

/* 确保图片不会超出容器 */
img {
  max-width: 100%;
  height: auto;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .app-logo-container {
    @apply space-x-2;
  }
}
</style>
