<template>
  <Dialog :open="isOpen" @update:open="$emit('update:isOpen', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>编辑参数</DialogTitle>
        <DialogDescription>
          修改 "{{ parameter?.zh_description || '--' }}" 的参数值
        </DialogDescription>
      </DialogHeader>

      <Form
        :validation-schema="validationSchema"
        :initial-values="{ value: paramValue }"
        class="space-y-4 py-2"
        @submit="handleSave"
      >
        <FormField v-slot="{ field, errorMessage }" name="value">
          <FormItem>
            <FormLabel>参数值</FormLabel>
            <FormControl>
              <Input
                v-model="paramValue"
                type="text"
                v-bind="field"
                :step="calculateStep(parameter?.min || 0, parameter?.max || 1)"
              />
            </FormControl>
            <FormDescription>
              参考范围: {{ parameter?.min || '0' }} - {{ parameter?.max || '1' }}
            </FormDescription>
            <FormMessage>{{ errorMessage }}</FormMessage>
          </FormItem>
        </FormField>

        <DialogFooter class="mt-4">
          <Button variant="outline" type="button" @click="$emit('update:isOpen', false)">
            取消
          </Button>
          <Button type="submit">保存更改</Button>
        </DialogFooter>
      </Form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { z } from 'zod'
import { toFormValidator } from '@vee-validate/zod'

interface Parameter {
  zh_description: string
  en_description: string
  value: number
  min: number
  max: number
  param_name: string
  is_recommended?: boolean
}

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  parameter: {
    type: Object as () => Parameter | null,
    default: null,
  },
})

const emit = defineEmits(['update:isOpen', 'save'])

const paramValue = ref('')

// 监听参数变化，更新编辑值
watch(
  () => props.parameter,
  (newParam) => {
    if (newParam) {
      paramValue.value = String(newParam.value)
    }
  },
  { immediate: true },
)

// 使用 Zod 创建校验规则
const validationSchema = computed(() => {
  if (!props.parameter) return toFormValidator(z.object({}))

  const min = props.parameter.min
  const max = props.parameter.max

  return toFormValidator(
    z.object({
      value: z
        .string()
        .nonempty('参数值不能为空')
        .refine((val) => !isNaN(Number(val)), { message: '请输入有效的数字' })
        .refine((val) => Number(val) >= min, { message: `值必须大于或等于 ${min}` })
        .refine((val) => Number(val) <= max, { message: `值必须小于或等于 ${max}` }),
    }),
  )
})

// 计算输入框的步进值
const calculateStep = (min: number, max: number) => {
  const range = Math.abs(max - min)
  const magnitude = Math.floor(Math.log10(range))
  return Math.pow(10, magnitude - 2)
}

// 保存编辑
const handleSave = () => {
  if (!props.parameter) return

  const numValue = Number(paramValue.value)
  emit('save', numValue)
}
</script>
