const fs = require('fs')
const path = require('path')

// 需要清理的目录
const dirsToClean = [
  'out',
  'out-mattverse-backup',
  'out-highpower-backup',
  '.electron-builder-cache',
  '.electron-builder-cache-mattverse',
  '.electron-builder-cache-highpower',
  'node_modules/.cache',
  'dist',
]

console.log('开始清理所有构建缓存和临时文件...')

// 清理所有目录
dirsToClean.forEach((dir) => {
  const fullPath = path.resolve(__dirname, '..', dir)
  if (fs.existsSync(fullPath)) {
    console.log(`清理目录: ${fullPath}`)
    fs.rmSync(fullPath, { recursive: true, force: true })
    console.log(`已清理: ${fullPath}`)
  } else {
    console.log(`目录不存在，跳过: ${fullPath}`)
  }
})

console.log('所有构建缓存和临时文件清理完成!')
