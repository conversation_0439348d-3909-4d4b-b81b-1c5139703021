<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-foreground mb-2">知识库管理</h1>
      <p class="text-foreground">上传和管理您的知识文件，提升AI助手的专业能力</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧上传区域 -->
      <div class="lg:col-span-1">
        <div class="bg-background rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-foreground mb-4 flex items-center">
            <BookOpen class="w-5 h-5 mr-2 text-green-600" />
            上传知识文件
          </h2>

          <FileDropUpload
            :accept-types="['.pdf', '.docx', '.txt', '.md']"
            :max-size="20"
            title="点击或拖拽上传文件"
            description="支持 PDF、Word、TXT、Markdown 格式"
            @file-selected="handleFileSelected"
            @error="handleUploadError"
            @upload-complete="handleUploadComplete"
          />

          <div class="mt-4 text-sm text-gray-500">
            <p class="flex items-center mb-1">
              <CheckCircle2 class="w-4 h-4 mr-1 text-green-600" />
              文件将自动处理并添加到知识库
            </p>
            <p class="flex items-center">
              <AlertCircle class="w-4 h-4 mr-1 text-amber-600" />
              单个文件大小不超过20MB
            </p>
          </div>
        </div>

        <!-- 知识库统计 -->
        <div class="bg-background rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <h2 class="text-lg font-semibold text-foreground mb-4 flex items-center">
            <BarChart3 class="w-5 h-5 mr-2 text-green-600" />
            知识库统计
          </h2>

          <div class="space-y-4">
            <div class="flex justify-between items-center pb-2 border-b border-gray-100">
              <span class="text-foreground">文档总数</span>
              <span class="font-medium text-foreground">{{ knowledgeStats.totalDocs }}</span>
            </div>
            <div class="flex justify-between items-center pb-2 border-b border-gray-100">
              <span class="text-foreground">知识点总数</span>
              <span class="font-medium text-foreground">{{ knowledgeStats.totalPoints }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-foreground">最近更新</span>
              <span class="font-medium text-foreground">{{ knowledgeStats.lastUpdate }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧知识库列表 -->
      <div class="lg:col-span-2">
        <div class="bg-background rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-lg font-semibold text-foreground flex items-center">
              <Database class="w-5 h-5 mr-2 text-green-600" />
              知识库文件
            </h2>

            <!-- 搜索和筛选 -->
            <div class="flex space-x-2">
              <div class="relative">
                <Search
                  class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground"
                />
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索文件名"
                  class="pl-9 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 w-48"
                />
              </div>
              <select
                v-model="filterType"
                class="border bg-background border-gray-300 rounded-md text-sm px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">全部类型</option>
                <option value="pdf">PDF文件</option>
                <option value="docx">Word文件</option>
                <option value="txt">文本文件</option>
              </select>
            </div>
          </div>

          <!-- 知识库列表 -->
          <div v-if="filteredKnowledgeFiles.length > 0" class="space-y-4">
            <div
              v-for="(file, index) in filteredKnowledgeFiles"
              :key="index"
              class="flex items-center justify-between p-4 border border-gray-100 rounded-lg hover:bg-muted/70 transition-colors"
            >
              <div class="flex items-center space-x-3">
                <div :class="`p-2 rounded-lg ${getFileIconClass(file.type)}`">
                  <component :is="getFileIcon(file.type)" class="w-5 h-5 text-foreground" />
                </div>
                <div>
                  <h3 class="font-medium text-foreground">{{ file.name }}</h3>
                  <div class="flex items-center text-sm text-foreground mt-1">
                    <Calendar class="w-3.5 h-3.5 mr-1" />
                    <span>{{ file.uploadDate }}</span>
                    <span class="mx-2">•</span>
                    <FileText class="w-3.5 h-3.5 mr-1" />
                    <span>{{ file.size }}</span>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <button
                  class="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                  title="预览"
                  @click="previewFile(file)"
                >
                  <Eye class="w-4 h-4" />
                </button>
                <button
                  class="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                  title="删除"
                  @click="deleteFile(file)"
                >
                  <Trash2 class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="flex flex-col items-center justify-center py-12 text-center">
            <FolderOpen class="w-16 h-16 text-gray-300 mb-4" />
            <h3 class="text-lg font-medium text-foreground mb-2">暂无知识库文件</h3>
            <p class="text-foreground max-w-md">
              您可以通过左侧上传区域添加文件到知识库，或者尝试调整搜索条件
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import {
  BookOpen,
  CheckCircle2,
  AlertCircle,
  BarChart3,
  Database,
  Search,
  Calendar,
  FileText,
  Eye,
  Trash2,
  FolderOpen,
  FileType,
  FileText as TextIcon,
  FileImage,
} from 'lucide-vue-next'
import FileDropUpload from '@renderer/components/common/Upload/FileDropUpload.vue'

// 知识库统计数据
const knowledgeStats = ref({
  totalDocs: 0,
  totalPoints: 0,
  lastUpdate: '2025-03-15 14:30',
})

// 搜索和筛选
const searchQuery = ref('')
const filterType = ref('all')

// 知识库文件列表
const knowledgeFiles = ref([
  // {
  //   id: 1,
  //   name: '新能源电池技术白皮书.pdf',
  //   type: 'pdf',
  //   size: '4.2MB',
  //   uploadDate: '2023-12-10',
  //   status: 'processed',
  // },
  // {
  //   id: 2,
  //   name: '锂电池性能测试报告.docx',
  //   type: 'docx',
  //   size: '2.8MB',
  //   uploadDate: '2023-12-08',
  //   status: 'processed',
  // },
  // {
  //   id: 3,
  //   name: '电池管理系统说明文档.pdf',
  //   type: 'pdf',
  //   size: '5.1MB',
  //   uploadDate: '2023-12-05',
  //   status: 'processed',
  // },
  // {
  //   id: 4,
  //   name: '充电策略优化方案.txt',
  //   type: 'txt',
  //   size: '1.3MB',
  //   uploadDate: '2023-11-28',
  //   status: 'processed',
  // },
])

// 过滤后的知识库文件
const filteredKnowledgeFiles = computed(() => {
  return knowledgeFiles.value.filter((file) => {
    // 类型筛选
    if (filterType.value !== 'all' && file.type !== filterType.value) {
      return false
    }

    // 搜索筛选
    if (searchQuery.value && !file.name.toLowerCase().includes(searchQuery.value.toLowerCase())) {
      return false
    }

    return true
  })
})

// 获取文件图标
const getFileIcon = (type) => {
  switch (type) {
    case 'pdf':
      return FileType
    case 'docx':
      return FileText
    case 'txt':
      return TextIcon
    default:
      return FileImage
  }
}

// 获取文件图标背景颜色
const getFileIconClass = (type) => {
  switch (type) {
    case 'pdf':
      return 'bg-red-500'
    case 'docx':
      return 'bg-blue-500'
    case 'txt':
      return 'bg-green-500'
    default:
      return 'bg-purple-500'
  }
}

// 处理文件上传
const handleFileSelected = (file) => {
  console.log('文件已选择:', file)
  // 这里可以添加文件上传到服务器的逻辑
}

// 处理上传错误
const handleUploadError = (error) => {
  console.error('上传错误:', error)
  // 这里可以添加错误处理逻辑
}

// 处理上传完成
const handleUploadComplete = () => {
  console.log('上传完成')
  // 这里可以添加上传完成后的逻辑，如刷新知识库列表
}

// 预览文件
const previewFile = (file) => {
  console.log('预览文件:', file)
  // 这里可以添加文件预览逻辑
}

// 删除文件
const deleteFile = (file) => {
  console.log('删除文件:', file)
  // 这里可以添加文件删除逻辑
  knowledgeFiles.value = knowledgeFiles.value.filter((item) => item.id !== file.id)
}

// 页面加载时获取知识库数据
onMounted(() => {
  // 这里可以添加获取知识库数据的逻辑
  console.log('知识库页面已加载')
})
</script>

<style lang="scss" scoped>
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-300;
  &:hover {
    @apply shadow-md border-green-200;
  }
}
</style>
