<template>
  <div
    ref="editorRef"
    class="w-full h-full bg-[#fafafa] dark:bg-[#18181b] focus:outline-none"
    tabindex="0"
    @keydown="handleKeyDown"
  >
    <vue-flow
      v-model="elements"
      :default-viewport="settingsStore.flowConfig.defaultViewport"
      :fit-view-on-init="settingsStore.flowConfig.fitViewOnInit"
      :node-types="nodeTypes.nodeTypes.value"
      :edge-types="edgeTypes"
      :min-zoom="settingsStore.flowConfig.minZoom"
      :max-zoom="settingsStore.flowConfig.maxZoom"
      :snap-grid="[settingsStore.flowConfig.snapGrid.x, settingsStore.flowConfig.snapGrid.y]"
      :snap-to-grid="settingsStore.flowConfig.snapToGrid"
      :snap-to-lines="settingsStore.flowConfig.snapToLines"
      :default-edge-options="{
        type: settingsStore.flowConfig.edgeConfig.type,
        animated: settingsStore.flowConfig.edgeConfig.animated,
        style: settingsStore.flowConfig.edgeConfig.style,
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: settingsStore.flowConfig.edgeConfig.style.stroke,
          width: 10,
          height: 10,
          strokeWidth: settingsStore.flowConfig.edgeConfig.markerEnd.strokeWidth,
        },
      }"
      class="w-full h-full relative [will-change:transform] [transform:translateZ(0)] [backface-visibility:hidden] [perspective:1000px]"
      :class="{
        'selecting-mode': isSelecting,
        'moving-mode': isMoving,
      }"
      :connection-mode="ConnectionMode.Loose"
      :elevate-edges-on-select="false"
      :disable-keyboard="isDragging"
      :update-edge-on-node-drag="true"
      :edge-updater-radius="30"
      :max-height="10000"
      :max-width="10000"
      @drop="onDrop"
      @dragover.prevent
      @pane-click="handlePaneClick"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
    >
      <!-- 自定义节点 -->
      <template #node-custom="nodeProps">
        <CustomNode
          v-bind="nodeProps"
          :is-box-selected="isNodeSelected(nodeProps.id)"
          @delete="handleNodeDelete(nodeProps.id)"
          @settings="handleNodeSettings(nodeProps.id)"
          @dblclick="handleNodeSettings(nodeProps.id)"
          @click="handleNodeClick(nodeProps.id, $event)"
        />
      </template>

      <!-- 工具栏面板 -->
      <ToolbarPanel
        v-if="canShowToolbar"
        :is-toolbar-visible="isToolbarVisible"
        @toggle-toolbar="toggleToolbar"
        @open-logger="openLogger"
      />

      <!-- 控制面板 -->
      <ControlPanel
        v-if="workflowEditorConfig.showLeftControls"
        :show-left-controls="settingsStore.flowConfig.showLeftControls"
        :can-undo="canUndo"
        :can-redo="canRedo"
        :has-multiple-nodes-selected="hasMultipleNodesSelected"
        @undo="undo"
        @redo="redo"
        @align-horizontal="alignHorizontal"
        @align-vertical="alignVertical"
      />

      <!-- 缩放面板 -->
      <ZoomPanel
        v-if="workflowEditorConfig.showRightControls"
        :show-right-controls="settingsStore.flowConfig.showRightControls"
        @fit-view="fitView"
        @zoom-in="zoomIn"
        @zoom-out="zoomOut"
      />

      <!-- 背景和小地图 -->
      <FlowBackground
        :is-matt="isMattVerse"
        :hp-logo-url="images.HPlogo"
        :show-mini-map="workflowEditorConfig.showMiniMap && settingsStore.flowConfig.showMiniMap"
        :mini-map-config="settingsStore.flowConfig.miniMapConfig"
        :background-config="settingsStore.flowConfig.backgroundConfig"
        :get-node-color="getNodeColor"
      />

      <!-- 选择框 -->
      <div
        v-if="selectionBox.visible"
        class="selection-box"
        :style="selectionBoxStyle"
        @mousedown="handleSelectionBoxMouseDown"
      ></div>

      <!-- 选择框工具提示 -->
      <div v-if="tooltipText" class="selection-tooltip" :style="tooltipStyle">
        {{ tooltipText }}
      </div>
    </vue-flow>

    <!-- 对话框容器 -->
    <DialogsContainer
      v-model:is-edge-delete-dialog-open="isEdgeDeleteDialogOpen"
      v-model:is-logger-open="isLoggerOpen"
      v-model:show-warning="showWarning"
      :current-edge-id="currentEdgeId"
      :workflow-id="props.workflowId"
      :warning-message="warningMessage"
      @edge-delete="handleEdgeDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { images } from '@renderer/config/constants'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { useFlowsStore, useNodeNavbarStore, useSettingsStore, useTaskStore } from '@renderer/store'
import emitter from '@renderer/utils/mitt'
import {
  BezierEdge,
  ConnectionMode,
  MarkerType,
  Position,
  SmoothStepEdge,
  StepEdge,
  StraightEdge,
  useVueFlow,
  VueFlow,
} from '@vue-flow/core'
import { useDebounceFn, useEventListener, useResizeObserver, useThrottleFn } from '@vueuse/core'
import { computed, markRaw, nextTick, onMounted, onUnmounted, ref, shallowRef, watch } from 'vue'
// 导入样式
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/core/dist/style.css'
import '@vue-flow/minimap/dist/style.css'

import {
  useNodeDragDrop,
  useNodeTheme,
  useNodeTypes,
  useSelectionBox,
  useWorkflowEdges,
  useWorkflowElements,
} from '../composables'

// 导入节点工具函数
import { saveNodeParams, getNodeParams } from '@renderer/utils/nodeUtils'

// 性能监控工具
const createPerformanceMonitor = (operationName: string) => {
  const startTime = performance.now()
  return {
    end: () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      if (duration > 100) {
        // 只记录超过100ms的操作
        console.warn(`${operationName} 耗时: ${duration.toFixed(2)}ms`)
      }
      return duration
    },
  }
}

// 导入节点组件
import { CustomNode } from '../nodes'

// 导入拆分的组件
import {
  ControlPanel,
  DialogsContainer,
  FlowBackground,
  ToolbarPanel,
  ZoomPanel,
} from './components'

const props = defineProps({
  workflowId: {
    type: String,
    required: true,
  },
})
const emit = defineEmits(['toggle-toolbar'])

// 使用stores
const nodeNavbarStore = useNodeNavbarStore()
const taskStore = useTaskStore()
const settingsStore = useSettingsStore()
const flowsStore = useFlowsStore()

// 使用应用配置
const { canShowToolbar, canShowMiniMap, workflowEditorConfig, isMattVerse } = useAppConfig()

const nodeTypes = useNodeTypes()
const {
  elements,
  loadWorkflow,
  saveWorkflow,
  saveWorkflowImmediate,
  addNode,
  updateNode,
  deleteNode,
  deleteMultipleNodes,
  addEdge,
  deleteEdge,
  alignHorizontal,
  alignVertical,
  hasMultipleNodesSelected,
  undo,
  redo,
  canUndo,
  canRedo,
} = useWorkflowElements(props.workflowId)

const { updateNodesBackground } = useNodeTheme()

const { isDragging, handleDragStart, handleDrag, handleDragStop, handleDrop } = useNodeDragDrop()

const {
  isEdgeDeleteDialogOpen,
  currentEdgeId,
  showWarning,
  warningMessage,
  validateConnection,
  handleEdgeClick,
  handleEdgeDoubleClick,
  validateConnectionGroup,
} = useWorkflowEdges()

// 选择框功能
const {
  isSelecting,
  isMoving,
  selectionBox,
  selectedNodeIds,
  justFinishedSelection,
  selectionBoxStyle,
  tooltipStyle,
  tooltipText,
  startSelection,
  updateSelection,
  endSelection,
  clearSelection,
  isNodeSelected,
  selectAll,
  toggleNodeSelection,
  startMoveSelection,
  moveSelection,
  endMoveSelection,
} = useSelectionBox(
  props.workflowId,
  computed(() => elements.value.filter((el) => !el.source)),
)

// 边类型定义
const edgeTypes = markRaw({
  default: BezierEdge,
  bezier: BezierEdge,
  smoothstep: SmoothStepEdge,
  step: StepEdge,
  straight: StraightEdge,
})

// 使用 shallowRef 减少深度监听开销
const isToolbarVisible = shallowRef(true)
const isLoggerOpen = shallowRef(false)

const isDraggingOptimized = ref(false) //优化状态
const pauseParamsUpdate = ref(false) // 暂停参数更新状态

// 编辑器容器引用
const editorRef = ref(null)

// 使用 vue-flow hooks
const {
  onNodeDragStart,
  onNodeDrag,
  onNodeDragStop,
  onConnect,
  fitView,
  zoomIn,
  zoomOut,
  project,
  onEdgeClick,
  onEdgeDoubleClick,
  setViewport,
  getNodes,
  getEdges,
} = useVueFlow(props.workflowId)

const openLogger = () => {
  isLoggerOpen.value = true
}

// 选择框事件处理
const handleMouseDown = (event: MouseEvent) => {
  if (event.button !== 0) return // 只处理左键

  const container = event.currentTarget as HTMLElement
  if (container) {
    startSelection(event, container)
  }
}

const handleMouseMove = (event: MouseEvent) => {
  if (isSelecting.value) {
    const container = event.currentTarget as HTMLElement
    if (container) {
      updateSelection(event, container)
    }
  } else if (isMoving.value) {
    moveSelection(event)
  }
}

const handleMouseUp = () => {
  if (isSelecting.value) {
    endSelection()
    // 选择完成后让容器获得焦点，确保键盘事件能正常工作
    if (selectedNodeIds.value.size > 0) {
      editorRef.value?.focus()
    }
  } else if (isMoving.value) {
    endMoveSelection()
  }
}

// 选择框鼠标按下事件
const handleSelectionBoxMouseDown = (event: MouseEvent) => {
  if (event.button !== 0) return // 只处理左键

  startMoveSelection(event)
}

const handlePaneClick = (event?: MouseEvent) => {
  resetEdgeStyles()
  // 只有在没有进行选择框操作且没有刚完成选择时才清空选择
  if (!isSelecting.value && !justFinishedSelection.value) {
    clearSelection()
  }
  // 确保容器获得焦点，以便键盘事件正常工作
  editorRef.value?.focus()
}

const handleNodeClick = (nodeId: string, event: MouseEvent) => {
  if (event.ctrlKey) {
    toggleNodeSelection(nodeId)
    editorRef.value?.focus() // 确保容器获得焦点
    event.preventDefault()
    event.stopPropagation()
  }
}

// 获取节点颜色的函数
const getNodeColor = (node) => {
  return node.data?.backgroundColor || '#ddd'
}

// 使用防抖函数优化视图适配
// const debouncedFitView = useDebounceFn(fitView, 200)

// 处理拖拽放置
const onDrop = (event) => {
  const result = handleDrop(event, project, props.workflowId)

  if (result) {
    const { nodeData, position, backgroundColor } = result

    // 创建新节点
    const newNode = {
      ...nodeData,
      position,
      type: nodeData.type || 'custom', // 确保使用已注册的节点类型
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
      data: {
        ...nodeData.data,
        workflowId: props.workflowId,
        params: nodeData.data.params || {},
        backgroundColor,
      },
    }

    // 添加节点
    addNode(newNode, position)

    // 保存当前状态
    saveWorkflow()
  }
}

// 清除所有边的虚线样式
const clearEdgeDashStyles = () => {
  const latestNodes = getNodes.value
  const latestEdges = getEdges.value
  const edgeConfig = settingsStore.flowConfig.edgeConfig

  const clearedEdges = latestEdges.map((edge) => ({
    ...edge,
    animated: edgeConfig.animated,
    style: {
      strokeWidth: edgeConfig.style.strokeWidth,
      stroke: edgeConfig.style.stroke,
      strokeDasharray: undefined, // 清除虚线
      transition: undefined, // 清除过渡
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: edgeConfig.style.stroke,
      width: 10,
      height: 10,
      strokeWidth: edgeConfig.markerEnd.strokeWidth,
    },
  }))

  elements.value = [...latestNodes, ...clearedEdges]
}

// 重置边样式
const resetEdgeStyles = (event = null, selectedEdgeId = null) => {
  // 先清除虚线样式
  clearEdgeDashStyles()
}

const toggleToolbar = () => {
  isToolbarVisible.value = !isToolbarVisible.value
  emit('toggle-toolbar')
}

// 处理节点删除
const handleNodeDelete = (nodeId) => {
  // 从 nodeNavbarStore 中删除对应的标签页
  const tabToRemove = nodeNavbarStore.tabs.find((tab) => tab.nodeId === nodeId)
  if (tabToRemove) nodeNavbarStore.removeTab(tabToRemove.id)

  // 删除节点
  deleteNode(nodeId)

  // 保存当前状态
  saveWorkflow()
}

// 处理节点设置
const handleNodeSettings = (nodeId) => {
  const node = elements.value.find((el) => el.id === nodeId)
  if (node) {
    // 更新节点数据
    updateNode(nodeId, {
      data: {
        ...node.data,
        isEditing: true,
      },
    })
    nodeNavbarStore.addTab(node)

    // 立即保存工作流，确保节点设置时位置不丢失
    saveWorkflowImmediate()
  }
}

// 处理边删除确认
const handleEdgeDelete = (edgeId) => deleteEdge(edgeId)

// 监听连线变化
onConnect(async (params) => {
  // 获取源节点和目标节点
  const sourceNode = elements.value.find((el) => el.id === params.source)
  const targetNode = elements.value.find((el) => el.id === params.target)

  // 验证连接
  if (
    !validateConnection(sourceNode, targetNode) ||
    !validateConnectionGroup(params, elements.value)
  ) {
    return
  }

  // 创建边配置
  const edgeConfig = settingsStore.flowConfig.edgeConfig
  const edgeOptions = {
    type: edgeConfig.type,
    markerEnd: edgeConfig.showArrow
      ? {
          type: MarkerType.ArrowClosed,
          color: edgeConfig.style.stroke,
          width: 10,
          height: 10,
          strokeWidth: edgeConfig.markerEnd.strokeWidth,
        }
      : undefined,
    style: {
      strokeWidth: edgeConfig.style.strokeWidth,
      stroke: edgeConfig.style.stroke,
    },
    animated: edgeConfig.animated,
  }

  // 添加边
  addEdge(params, edgeOptions)

  // 异步处理参数复制 - 避免阻塞UI
  try {
    await handleNodeConnection(sourceNode, targetNode)
  } catch (error) {
    console.error('处理节点连接参数失败:', error)
  }
})

// 处理节点连接的参数复制逻辑 - 提取为独立函数提高可维护性
const handleNodeConnection = async (sourceNode: any, targetNode: any) => {
  const monitor = createPerformanceMonitor(
    `节点连接处理: ${sourceNode?.data?.label} -> ${targetNode?.data?.label}`,
  )

  if (!sourceNode?.data || !targetNode?.data) {
    console.warn('源节点或目标节点数据无效')
    return
  }

  const workflowId = props.workflowId
  const sourceType = sourceNode.data.type

  try {
    // 并行获取源节点和目标节点参数
    const [sourceParams, targetParams] = await Promise.all([
      getNodeParams(sourceNode),
      getNodeParams(targetNode),
    ])

    // 构建更新后的参数 - 修复参数传递结构
    const updatedParams = {
      ...targetParams,
      // ...sourceParams,
      [sourceType]: {
        ...sourceParams,
        nodeId: sourceNode.id,
        nodeLabel: sourceNode.data.label,
      },
    }

    // 保存参数
    await saveNodeParams(targetNode, updatedParams)

    // 调用监听函数
    const nodeListeners: any = flowsStore.getListeners()
    if (nodeListeners?.nodeLink) {
      nodeListeners.nodeLink({ workflowId, sourceNode, targetNode })
    }
  } catch (error) {
    console.error('节点连接参数处理失败:', error)
  } finally {
    monitor.end()
  }
}
// 注册边点击事件
onEdgeClick(({ edge }) => {
  // 重置所有边的样式
  resetEdgeStyles(null, edge.id)

  // 更新被点击的边样式
  const edgeStyle = {
    id: edge.id,
    style: {
      strokeWidth: settingsStore.flowConfig.edgeConfig.style.strokeWidth + 1,
      stroke: '#3b82f6',
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#3b82f6',
      width: 10,
      height: 10,
      strokeWidth: settingsStore.flowConfig.edgeConfig.markerEnd.strokeWidth,
    },
    animated: true,
  }

  // 直接更新被点击的边
  const edgeIndex = elements.value.findIndex((el) => el.id === edge.id)
  if (edgeIndex !== -1) {
    elements.value[edgeIndex] = {
      ...elements.value[edgeIndex],
      ...edgeStyle,
    }
  }
})

// 注册边双击事件
onEdgeDoubleClick(({ edge }) => {
  handleEdgeDoubleClick(edge)
})

// 处理键盘事件
const handleKeyDown = (event) => {
  if (event.ctrlKey && event.key === 'a') {
    event.preventDefault()
    selectAll()
    return
  }

  if (event.key === 'Backspace' || event.key === 'Delete') {
    event.preventDefault()
    const selectedIds = Array.from(selectedNodeIds.value)
    if (selectedIds.length > 0) {
      deleteMultipleNodes(selectedIds)
      clearSelection()
    } else {
      const selectedNodes = elements.value.filter((el) => !el.source && el.selected) as any
      if (selectedNodes.length > 0) {
        handleNodeDelete(selectedNodes[0]?.id)
      }
    }
  }
}

// 处理节点数据更新事件
const handleNodeDataUpdated = (event) => {
  if (event.detail && event.detail.nodeId && event.detail.data) {
    // 查找并更新相应节点
    const nodeIndex = elements.value.findIndex((el) => el.id === event.detail.nodeId)
    if (nodeIndex !== -1) {
      updateNode(event.detail.nodeId, {
        data: {
          ...elements.value[nodeIndex].data,
          ...event.detail.data,
        },
      })
    }
  }
}

// 处理工作流更新事件
const handleWorkflowUpdated = (data) => {
  // 从 emitter 事件中提取 workflowId
  const workflowId = data.workflowId || (data.detail && data.detail.workflowId)

  // 检查是否是当前工作流的更新
  if (workflowId === props.workflowId) {
    // 延迟适应视图以确保所有元素都已渲染
    // nextTick(() => {
    //   debouncedFitView({
    //     padding: 0.2,
    //     includeHiddenNodes: true,
    //     duration: 200,
    //   })
    // })
  }
}

// 实时更新连接的边
const updateConnectedEdges = useThrottleFn(() => {
  if (!isDragging.value) return

  // 获取当前选中的节点ID
  const selectedNodeIds = elements.value
    .filter((el) => !el.source && el.selected)
    .map((node) => node.id)

  if (selectedNodeIds.length === 0) return

  // 找出与选中节点相连的边并强制更新
  const connectedEdges = elements.value.filter(
    (el) =>
      el.source && (selectedNodeIds.includes(el.source) || selectedNodeIds.includes(el.target)),
  )

  // 减少不必要的更新，只在有边时更新
  if (connectedEdges.length > 0) {
    // 使用 requestAnimationFrame 优化渲染
    requestAnimationFrame(() => {
      // 仅在拖动时临时简化边的样式，提高性能
      if (isDraggingOptimized.value) {
        // 避免直接修改整个elements数组，只更新必要的边
        connectedEdges.forEach((edge) => {
          const edgeIndex = elements.value.findIndex((el) => el.id === edge.id)
          if (edgeIndex !== -1) {
            elements.value[edgeIndex] = {
              ...edge,
              animated: false,
              style: { ...edge.style, strokeDasharray: '5,5' },
            }
          }
        })
      }
    })
  }
}, 32) // 降低更新频率以提高性能

// 注册拖拽事件
onNodeDragStart(() => {
  try {
    handleDragStart()
    isDraggingOptimized.value = true
    pauseParamsUpdate.value = true // 拖拽时暂停参数更新
    document.body.classList.add('workflow-drag-optimized')

    // 拖拽开始时暂时禁用所有边的动画效果和简化样式
    requestAnimationFrame(() => {
      const edgeElements = elements.value.filter((el) => el.source)
      edgeElements.forEach((edge) => {
        const edgeIndex = elements.value.findIndex((el) => el.id === edge.id)
        if (edgeIndex !== -1) {
          elements.value[edgeIndex] = {
            ...edge,
            animated: false,
            style: {
              ...edge.style,
              strokeWidth: Math.max(1, (edge.style?.strokeWidth || 2) - 1),
              transition: 'none', // 禁用过渡动画
            },
          }
        }
      })
    })
  } catch (error) {
    console.error('节点拖拽开始错误:', error)
  }
})

onNodeDrag(() => {
  try {
    handleDrag()
    // 实时更新连接的边
    updateConnectedEdges()
  } catch (error) {
    console.error('节点拖拽中错误:', error)
  }
})

onNodeDragStop((event) => {
  try {
    handleDragStop()
    isDraggingOptimized.value = false
    pauseParamsUpdate.value = false // 拖拽结束后恢复参数更新
    document.body.classList.remove('workflow-drag-optimized')

    // 拖拽结束后恢复边的样式
    requestAnimationFrame(() => {
      // 使用专门的函数清除虚线样式
      clearEdgeDashStyles()

      // nextTick(() => {
      //   const latestNodes = getNodes.value
      //   const latestEdges = getEdges.value

      //   // 直接保存 Vue Flow 的最新状态
      //   if (latestNodes && latestNodes.length > 0) {
      //     const workflowData = {
      //       nodes: latestNodes,
      //       edges: latestEdges,
      //     }
      //     flowsStore.saveWorkflow(props.workflowId, workflowData)
      //   }
      // })
    })
  } catch (error) {
    console.error('节点拖拽结束错误:', error)
  }
})

// 使用 useResizeObserver 监听容器大小变化
useResizeObserver(editorRef, (entries) => {
  const entry = entries[0]
  // if (entry && elements.value.length > 0) {
  //   // 当容器大小变化时，延迟执行 fitView
  //   setTimeout(() => {
  //     debouncedFitView({
  //       padding: 0.2,
  //       includeHiddenNodes: true,
  //       duration: 200,
  //     })
  //   }, 100)
  // }
})
// 处理工作流切换事件
const handleWorkflowSwitch = (event: Event) => {
  const customEvent = event as CustomEvent
  const { workflowId } = customEvent.detail

  // 检查是否切换到当前工作流
  if (workflowId === props.workflowId) {
    window.logger?.info(`工作流切换到当前编辑器: ${workflowId}`)
    // 重新加载工作流数据以确保显示最新状态
    loadWorkflow()
  }
}
useEventListener(window, 'workflow-switch', handleWorkflowSwitch)
// 全局鼠标事件监听，用于选择框移动
useEventListener(document, 'mousemove', (e: MouseEvent) => {
  if (isMoving.value) {
    moveSelection(e)
  }
})

useEventListener(document, 'mouseup', () => {
  if (isMoving.value) {
    endMoveSelection()
  }
})
// 组件挂载时加载保存的数据
onMounted(async () => {
  // 事件监听
  emitter.on('node-data-updated', handleNodeDataUpdated)
  emitter.on('workflow-updated', handleWorkflowUpdated)

  // 加载工作流数据
  const loaded = loadWorkflow()

  if (loaded) {
    // 收集所有任务ID进行批量查询
    const taskIds = elements.value
      .filter((el) => !el.source && el.data?.taskId)
      .map((node) => node.data.taskId)

    // 如果有任务ID，批量查询
    if (taskIds.length > 0) {
      try {
        // 批量获取任务状态
        const batchTaskIds = taskIds.join(',')
        await taskStore.updateTaskList(batchTaskIds)

        // 批量获取已完成任务的结果
        const finishedTaskIds = taskStore.tasks
          .filter((task) => ['Finished', 'Error', 'Abort'].includes(task.taskStatus))
          .map((task) => task.taskId)

        if (finishedTaskIds.length > 0) {
          await taskStore.updateTaskResult(finishedTaskIds.join(','))
        }
      } catch (e) {
        console.warn(`批量获取任务状态失败:`, e)
      }
    }

    // 适应视图
    // await nextTick()
    // debouncedFitView({
    //   padding: 0.5,
    //   includeHiddenNodes: true,
    //   duration: 300,
    //   maxZoom: 1,
    // })
  } else {
    // 如果是空画布，设置一个合理的初始视图
    nextTick(() => {
      setViewport({ x: 0, y: 0, zoom: 1 })
    })
  }
})

onUnmounted(() => {
  // 清理事件监听
  emitter.off('node-data-updated', handleNodeDataUpdated)
  emitter.off('workflow-updated', handleWorkflowUpdated)
})

/// 监听工作流变化
const workflowChangeHandler = useDebounceFn(async (newWorkflows, oldWorkflows) => {
  if (!newWorkflows || !oldWorkflows) return

  const workflow = newWorkflows[props.workflowId]
  if (!workflow?.edges?.length) return

  // 批量处理所有边的参数同步
  const edgeProcessingPromises = workflow.edges.map(async (edge) => {
    await processEdgeParameterSync(edge, workflow)
  })

  await Promise.allSettled(edgeProcessingPromises)
}, 100)

watch(() => flowsStore.workflows, workflowChangeHandler, { deep: true })

// 处理单个边的参数同步
const processEdgeParameterSync = async (edge: any, workflow: any) => {
  const sourceNodeId = edge.source
  const targetNodeId = edge.target

  // 获取源节点和目标节点
  const sourceNode = workflow.nodes.find((n: any) => n.id === sourceNodeId)
  const targetNode = workflow.nodes.find((n: any) => n.id === targetNodeId)

  if (!sourceNode?.data || !targetNode?.data) {
    return
  }

  const sourceType = sourceNode.data.type
  const monitor = createPerformanceMonitor(
    `参数同步: ${sourceNode.data.label} -> ${targetNode.data.label}`,
  )

  try {
    // 并行获取参数
    const [sourceParams, targetParams] = await Promise.all([
      getNodeParams(sourceNode),
      getNodeParams(targetNode),
    ])

    // 获取目标节点中保存的源节点参数
    const savedSourceParams = targetParams[sourceType] || {}

    // 优化参数比较
    if (!areParametersEqual(sourceParams, savedSourceParams)) {
      // 构建更新后的参数
      const updatedParams = {
        ...targetParams,
        // 直接将源节点参数合并到目标节点，保持原有结构
        ...sourceParams,
        // 同时保存源节点信息到特定键下，用于追踪
        [sourceType]: {
          ...sourceParams,
          nodeId: sourceNodeId,
          nodeLabel: sourceNode.data.label,
        },
      }

      // 保存参数
      await saveNodeParams(targetNode, updatedParams)
    }
  } catch (error) {
    console.error(`处理边参数同步失败:`, error)
  } finally {
    monitor.end()
  }
}

// 优化的参数比较函数
const areParametersEqual = (sourceParams: any, savedParams: any): boolean => {
  // 排除特殊字段
  const excludeFields = ['nodeId', 'nodeLabel']

  // 获取所有键
  const sourceKeys = Object.keys(sourceParams).filter((key) => !excludeFields.includes(key))
  const savedKeys = Object.keys(savedParams).filter((key) => !excludeFields.includes(key))

  // 键数量不同
  if (sourceKeys.length !== savedKeys.length) {
    return false
  }

  // 逐个比较值
  for (const key of sourceKeys) {
    if (!savedKeys.includes(key)) {
      return false
    }

    const sourceValue = sourceParams[key]
    const savedValue = savedParams[key]

    // 对于对象类型，使用深度比较
    if (typeof sourceValue === 'object' && typeof savedValue === 'object') {
      if (JSON.stringify(sourceValue) !== JSON.stringify(savedValue)) {
        return false
      }
    } else if (sourceValue !== savedValue) {
      return false
    }
  }

  return true
}

// 监听 workflowId 变化
watch(
  () => props.workflowId,
  useDebounceFn(async (newId) => {
    // 重置节点设置面板状态
    nodeNavbarStore.reset()
    loadWorkflow()

    // 适应视图
    // await nextTick()
    // debouncedFitView({
    //   padding: 0.2,
    //   includeHiddenNodes: true,
    //   duration: 200,
    // })
  }, 300),
)

// 监听设置存储中的主题变化
watch(
  () => settingsStore.theme,
  (newTheme, oldTheme) => {
    if (newTheme !== oldTheme) {
      // 更新节点背景色
      nextTick(() => {
        const nodes = elements.value.filter((el) => !el.source)
        const updatedNodes = updateNodesBackground(nodes)
        const edges = elements.value.filter((el) => el.source)

        elements.value = [...updatedNodes, ...edges]
        saveWorkflow()
      })
    }
  },
)

// 监听连线配置变化
watch(
  () => settingsStore.flowConfig,
  useDebounceFn((newConfig) => {
    // 更新现有边的配置
    const updatedElements = elements.value.map((el) => {
      if (el.source) {
        // 如果是边
        return {
          ...el,
          type: newConfig.edgeConfig.type,
          animated: newConfig.edgeConfig.animated,
          style: newConfig.edgeConfig.style,
          markerEnd: newConfig.edgeConfig.showArrow
            ? {
                type: MarkerType.ArrowClosed,
                color: newConfig.edgeConfig.markerEnd.color,
                width: newConfig.edgeConfig.markerEnd.width,
                height: newConfig.edgeConfig.markerEnd.height,
                strokeWidth: newConfig.edgeConfig.markerEnd.strokeWidth,
              }
            : undefined,
        }
      }
      return el
    })

    elements.value = updatedElements
    saveWorkflow()

    // 强制更新视图
    // nextTick(() => {
    //   // 如果修改了视口相关配置，重新适应视图
    //   if (newConfig.fitViewOnInit) {
    //     debouncedFitView({
    //       padding: 0.2,
    //       includeHiddenNodes: true,
    //       duration: 200,
    //     })
    //   }
    // })
  }, 300),
  { deep: true },
)
</script>

<style>
/* 全局样式 - 节点拖动时的样式 */
.node-dragging {
  cursor: grabbing !important;
}
.node-dragging .vue-flow_edge {
  pointer-events: none;
}

/* 拖动时优化画布性能 */
.vue-flow__pane.dragging {
  cursor: grabbing;
  contain: strict;
}

/* 优化拖动时的性能 */
.workflow-drag-optimized .vue-flow__edge {
  opacity: 0.6 !important;
  transition: none !important;
  contain: strict;
  will-change: none !important;
}
.workflow-drag-optimized .vue-flow__node {
  transition: none !important;
  contain: layout style;
}

.workflow-drag-optimized .vue-flow__minimap,
.workflow-drag-optimized .vue-flow__background {
  opacity: 0.3;
  transition: none !important;
  contain: strict;
  will-change: none !important;
}

/* 边样式 */
.vue-flow__edge-smoothstep path {
  stroke: hsl(var(--muted-foreground));
  border-radius: 20px;
}
.vue-flow__edge {
  will-change: transform;
  contain: layout style;
  pointer-events: stroke;
}
.vue-flow__edge path {
  transition:
    stroke 0.2s ease,
    stroke-width 0.2s ease;
}

/* 对齐辅助线样式 */
.vue-flow__guide-line {
  background-color: hsl(var(--primary));
  opacity: 0.6;
  border-radius: 1px;
  box-shadow: 0 0 5px rgba(var(--primary), 0.3);
}

/* 拖拽节点样式 */
.vue-flow__node-drag-active {
  cursor: grabbing;
  opacity: 0.8;
  contain: layout style;
}
/* 拖拽时的样式 */
.node-dragging .vue-flow__edge {
  pointer-events: none;
  transition: none !important;
  contain: strict;
}
</style>
