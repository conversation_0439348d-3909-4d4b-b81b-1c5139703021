import { BrowserWindow, dialog } from 'electron'
import fs from 'fs'
import msgpack5 from 'msgpack5'
import logger from '../utils/logger'
/**
 * 文件服务类
 * 封装与文件操作相关的所有功能
 */
export class FileService {
  /**
   * 显示保存文件对话框
   * @param options 对话框选项
   * @returns Promise<Electron.SaveDialogReturnValue>
   */
  async showSaveDialog(
    options?: Electron.SaveDialogOptions,
  ): Promise<Electron.SaveDialogReturnValue> {
    const win = BrowserWindow.getFocusedWindow()
    if (!win) {
      throw new Error('没有找到焦点窗口')
    }

    const defaultOptions: Electron.SaveDialogOptions = {
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] },
      ],
      properties: ['createDirectory'],
    }

    const dialogOptions = options ? { ...defaultOptions, ...options } : defaultOptions

    try {
      return await dialog.showSaveDialog(win, dialogOptions)
    } catch (error) {
      logger.error('显示保存对话框失败:', error)
      throw error
    }
  }

  /**
   * 显示打开文件对话框
   * @param options 对话框选项
   * @returns Promise<Electron.OpenDialogReturnValue>
   */
  async showOpenDialog(
    options?: Electron.OpenDialogOptions,
  ): Promise<Electron.OpenDialogReturnValue> {
    const win = BrowserWindow.getFocusedWindow()
    if (!win) {
      throw new Error('没有找到焦点窗口')
    }

    const defaultOptions: Electron.OpenDialogOptions = {
      title: '打开文件',
      filters: [{ name: '所有文件', extensions: ['*'] }],
      properties: ['openFile'],
    }

    const dialogOptions = options ? { ...defaultOptions, ...options } : defaultOptions

    try {
      return await dialog.showOpenDialog(win, dialogOptions)
    } catch (error) {
      logger.error('显示打开文件对话框失败:', error)
      throw error
    }
  }

  /**
   * 保存文件
   * @param options 保存选项
   * @returns Promise<{success: boolean, filePath?: string, error?: string, canceled?: boolean}>
   */
  async saveFile(options: {
    title?: string
    defaultPath?: string
    filters?: Electron.FileFilter[]
    content?: string | Buffer | ArrayBuffer | number[]
    filePath?: string
    data?: string | Buffer | ArrayBuffer | number[]
    encoding?: string
    isBinary?: boolean
  }): Promise<{ success: boolean; filePath?: string; error?: string; canceled?: boolean }> {
    try {
      const { title, defaultPath, filters, content, filePath, data, encoding, isBinary } = options

      // 兼容旧的API调用方式
      const fileContent = content || data || ''

      // 如果提供了filePath，直接保存文件
      if (filePath && (content !== undefined || data !== undefined)) {
        if (isBinary) {
          // 处理二进制数据
          fs.writeFileSync(filePath, Buffer.from(fileContent as any))
        } else {
          fs.writeFileSync(filePath, fileContent as string, encoding || 'utf-8')
        }
        return { success: true, filePath }
      }

      // 否则打开保存对话框
      const { canceled, filePath: selectedPath } = await this.showSaveDialog({
        title: title || '保存文件',
        defaultPath: defaultPath || 'export.txt',
        filters: filters || [{ name: '所有文件', extensions: ['*'] }],
      })

      if (canceled || !selectedPath) {
        return { success: false, canceled: true }
      }

      // 保存文件内容
      if (isBinary) {
        // 处理二进制数据
        fs.writeFileSync(selectedPath, Buffer.from(fileContent as any))
      } else {
        fs.writeFileSync(selectedPath, fileContent as string, encoding || 'utf-8')
      }
      return { success: true, filePath: selectedPath }
    } catch (error) {
      logger.error('保存文件失败:', error)
      return { success: false, error: (error as Error).message || String(error) }
    }
  }

  /**
   * 加密并保存文件
   * @param options 加密保存选项
   * @returns Promise<{success: boolean, filePath?: string, error?: string, canceled?: boolean}>
   */
  async encryptAndSaveFile(options: {
    jsonData: string
    fileType?: string
    defaultPath?: string
    title?: string
    filters?: Electron.FileFilter[]
  }): Promise<{ success: boolean; filePath?: string; error?: string; canceled?: boolean }> {
    try {
      const { jsonData, fileType, defaultPath, title, filters } = options

      // 显示保存对话框
      const { canceled, filePath } = await this.showSaveDialog({
        title,
        defaultPath,
        filters,
      })

      if (canceled || !filePath) {
        return { canceled: true, success: false }
      }

      // 编码处理
      const msgpack = msgpack5()
      const base64Encoded = Buffer.from(jsonData, 'utf-8').toString('base64')
      const binaryData = msgpack.encode({ data: base64Encoded })

      // 写入文件
      fs.writeFileSync(filePath, binaryData)

      return { success: true, filePath }
    } catch (error) {
      logger.error('文件保存失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 读取文件
   * @param options 文件路径或读取选项
   * @returns Promise<string | number[] | {success: boolean, error: string}>
   */
  async readFile(
    options:
      | string
      | {
          filePath: string
          encoding?: string
          isBinary?: boolean
        },
  ): Promise<string | number[] | { success: boolean; error: string }> {
    try {
      // 处理不同类型的参数
      let filePath = ''
      let encoding = 'utf-8'
      let isBinary = false

      if (typeof options === 'string') {
        filePath = options
      } else if (options && typeof options === 'object') {
        filePath = options.filePath
        encoding = options.encoding || encoding
        isBinary = options.isBinary || false
      } else {
        return { success: false, error: '无效的参数' }
      }

      if (!filePath) {
        return { success: false, error: '未提供文件路径' }
      }

      // 读取文件内容
      if (isBinary || encoding === null) {
        // 读取为二进制数据
        const buffer = fs.readFileSync(filePath)
        return Array.from(buffer) // 转换为数组以便通过IPC传输
      } else {
        // 读取为文本
        return fs.readFileSync(filePath, encoding as BufferEncoding)
      }
    } catch (error) {
      logger.error('读取文件失败:', error)
      return { success: false, error: (error as Error).message || String(error) }
    }
  }

  /**
   * 检查文件是否存在
   * @param filePath 文件路径
   * @returns boolean
   */
  fileExists(filePath: string): boolean {
    return fs.existsSync(filePath)
  }

  /**
   * 创建目录
   * @param dirPath 目录路径
   * @param recursive 是否递归创建
   */
  createDirectory(dirPath: string, recursive = true): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive })
    }
  }
}

// 导出单例实例
export const fileService = new FileService()
