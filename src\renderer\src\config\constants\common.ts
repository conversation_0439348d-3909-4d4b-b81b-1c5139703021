import type { SideBarItem } from '@renderer/config/interface/common'
import svg from './svg'
import { nanoid } from 'nanoid'
//侧边栏列表菜单
export const SIDEBAR_LIST: SideBarItem[] = [
  {
    icon: 'flowbite:draw-square-outline',
    showTopLine: false,
    tooltip: {
      content: '工作流',
      placement: 'right',
      theme: 'light',
    },
  },
  {
    icon: 'flowbite:clock-outline',
    showTopLine: false,
    tooltip: {
      content: '计划',
      placement: 'right',
      theme: 'light',
    },
  },
  {
    icon: 'flowbite:database-outline',
    showTopLine: false,
    tooltip: {
      content: '存储',
      placement: 'right',
      theme: 'light',
    },
  },
  {
    icon: 'flowbite:clock-arrow-outline',
    showTopLine: false,
    tooltip: {
      content: '日志',
      placement: 'right',
      theme: 'light',
    },
  },
  {
    icon: 'flowbite:cog-outline',
    showTopLine: true,
    tooltip: {
      content: '设置',
      placement: 'right',
      theme: 'light',
    },
  },
  {
    icon: 'flowbite:user-circle-outline',
    showTopLine: false,
    tooltip: {
      content: '用户',
      placement: 'right',
      theme: 'light',
    },
  },
  {
    icon: 'flowbite:exclamation-circle-outline',
    showTopLine: false,
    tooltip: {
      content: '关于',
      placement: 'right',
      theme: 'light',
    },
  },
]
// 工作流节点对象
export const WORKFLOW_NODE_OBJ = {
  豪鹏电池寿命预测: {
    icon: 'material-symbols:battery-charging-full-sharp',
    type: 'HighpowerLifePrediction',
    categories: [
      {
        name: '基础组件',
        nodes: [
          // {
          //   id: nanoid(),
          //   type: 'custom',
          //   data: {
          //     label: '数据输入',
          //     type: 'DataEntry',
          //     icon: { type: 'svg', value: svg.splice },
          //     description: '',
          //     // backgroundColor: 'rgba(76, 175, 80, 0.3)',
          //     category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
          //     nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
          //     inputType: [],
          //     outputType: ['LifespanPrediction'],
          //     params: {},
          //   },
          // },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '豪鹏电池模型',
              type: 'HighpowerBatteryModel',
              icon: { type: 'svg', value: svg.batteryModel },
              description: '',
              // backgroundColor: 'rgba(76, 175, 80, 0.3)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: [],
              outputType: ['LifespanPrediction'],
              params: {},
            },
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '寿命预测',
              type: 'LifespanPrediction',
              icon: { type: 'svg', value: svg.lifePrediction },
              description: '',
              // backgroundColor: 'rgb(200 180 75 / 60%)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['DataEntry', 'HighpowerBatteryModel'],
              outputType: [],
              params: {},
            },
          },
        ],
      },
    ],
  },
  材料计算: {
    icon: 'gravity-ui:molecule',
    type: 'materialCalculation',
    description: "这是材料计算模块",
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电极',
              type: 'Electrode',
              icon: { type: 'svg', value: svg.structure },
              description: '',
              // backgroundColor: 'rgba(76, 175, 80, 0.3)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: [],
              outputType: ['Energy', 'Modulus', 'Diffusion', 'Conductivity', 'PhaseDiagram', 'MD'],
              params: {
                material: '',
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电解液',
              type: 'Electrolyte',
              icon: { type: 'svg', value: svg.structure },
              description: '电解液节点',
              // backgroundColor: 'rgba(76, 175, 80, 0.3)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: [],
              outputType: ['Energy', 'Modulus', 'Diffusion', 'Conductivity', 'PhaseDiagram', 'MD'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '能量',
              type: 'Energy',
              icon: { type: 'svg', value: svg.energy },
              description: '能量节点',
              // backgroundColor: 'rgba(255, 235, 59, 0.5)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrode'],
              outputType: [],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '模量',
              type: 'Modulus',
              icon: { type: 'svg', value: svg.spring },
              description: '模量节点',
              // backgroundColor: 'rgba(255, 193, 7, 0.5)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrode'],
              outputType: ['Diffusion', 'Conductivity'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '扩散系数',
              type: 'Diffusion',
              icon: { type: 'svg', value: svg.diffusion },
              description: '扩散系数节点',
              // backgroundColor: 'rgba(139, 195, 74, 0.5)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrode', 'Electrolyte'],
              outputType: ['Cathode', 'Anode', 'Separator', 'Electrolyte'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电导率',
              type: 'Conductivity',
              icon: { type: 'svg', value: svg.splice },
              description: '电导率节点',
              // backgroundColor: 'rgba(205, 220, 57, 0.5)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrode', 'Electrolyte'],
              outputType: ['Cathode', 'Anode', 'Separator', 'Electrolyte', 'Electrode'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '黏度',
              type: 'Viscosity',
              icon: { type: 'svg', value: svg.splice },
              description: '黏度节点',
              // backgroundColor: 'rgba(205, 220, 57, 0.5)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrolyte'],
              outputType: ['Electrolyte'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '溶剂化结构',
              type: 'SolvationStructure',
              icon: { type: 'svg', value: svg.splice },
              description: '溶剂化结构节点',
              // backgroundColor: 'rgba(205, 220, 57, 0.5)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrolyte'],
              outputType: ['Electrolyte'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '相图',
              type: 'PhaseDiagram',
              icon: { type: 'svg', value: svg.phaseDiagram },
              description: '相图节点',
              // backgroundColor: 'rgba(76, 175, 80, 0.3)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrode'],
              outputType: [],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '自定义计算',
              type: 'materialDesign',
              icon: { type: 'svg', value: svg.md },
              description: '自定义计算节点',
              // backgroundColor: 'rgba(0, 150, 136, 0.3)',
              category: 'materialDesign', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Electrode'],
              outputType: [],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
        ],
      },
    ],
  },
  电池仿真: {
    icon: 'fluent-emoji-high-contrast:battery',
    type: 'batterySimulation',
    description: "这是电池仿真模块",
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '正极',
              type: 'Cathode',
              icon: { type: 'svg', value: svg.output },
              description: '正极节点',
              // backgroundColor: 'rgb(145 188 130 / 75%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Conductivity', 'Diffusion'],
              outputType: ['Geometry'],
              params: {
                material: '',
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '负极',
              type: 'Anode',
              icon: { type: 'svg', value: svg.condition },
              description: '负极节点',
              // backgroundColor: 'rgb(220 200 80 / 60%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Conductivity', 'Diffusion'],
              outputType: ['Geometry'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '隔膜',
              type: 'Separator',
              icon: { type: 'svg', value: svg.sequence },
              description: '隔膜节点',
              // backgroundColor: 'rgb(170 150 50 / 40%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: [],
              outputType: ['Geometry'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电解液',
              type: 'Electrolyte',
              icon: { type: 'svg', value: svg.sequence },
              description: '电解液节点',
              // backgroundColor: 'rgb(170 150 50 / 40%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: [],
              outputType: ['Geometry'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电芯模型',
              type: 'Geometry',
              icon: { type: 'svg', value: svg.splice },
              description: '电芯模型',
              // backgroundColor: 'rgb(200 180 75 / 60%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Basic', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Cathode', 'Anode', 'Separator', 'Electrolyte'],
              outputType: ['Simulation'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '仿真计算',
              type: 'Geometry',
              icon: { type: 'svg', value: svg.splice },
              description: '仿真计算',
              // backgroundColor: 'rgb(200 180 75 / 60%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['Geometry'],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '寿命预测',
              type: 'LifespanPrediction',
              icon: { type: 'svg', value: svg.splice },
              description: '寿命预测',
              // backgroundColor: 'rgb(200 180 75 / 60%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Compute', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['FeatureExtraction'],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '数据组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '数据库',
              type: 'DataBase',
              icon: { type: 'svg', value: svg.mysql },
              description: '数据库',
              // backgroundColor: 'rgb(220 180 50 / 50%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Data', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: [],
              outputType: ['FeatureExtraction'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '特征提取',
              type: 'batterySimulation',
              icon: { type: 'svg', value: svg.structure },
              description: '特征提取',
              // backgroundColor: 'rgb(220 180 50 / 50%)',
              category: 'batterySimulation', // 1、batterySimulation 电池模拟 2、materialDesign  材料设计节点
              nodeType: 'Data', // 1、Basic 基础组件 2、Compute 计算组件 3、Data 数据组件
              inputType: ['DataBase'],
              outputType: ['LifespanPrediction'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
    ],
  },
  电池管理: {
    icon: 'fluent-emoji-high-contrast:battery',
    type: 'batteryManagement',
    categories: [],
  },
}

export default {
  SIDEBAR_LIST,
  WORKFLOW_NODE_OBJ,
}
