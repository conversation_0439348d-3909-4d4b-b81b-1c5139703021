import { ipcMain } from 'electron'
import { WorkflowService } from '../services/workflowService'
import logger from '../utils/logger'

// 跟踪处理程序是否已设置
let handlersSetup = false

/**
 * 设置工作流相关的IPC处理程序
 * @param workflowService 工作流服务实例
 */
export function setupWorkflowHandlers(workflowService: WorkflowService): void {
  // 如果处理程序已设置，则直接返回
  if (handlersSetup) {
    logger.info('工作流IPC处理程序已设置，跳过重复设置')
    return
  }

  // 处理工作流操作
  ipcMain.handle('workflow-operation', async (_, data) => {
    try {
      logger.info('工作流操作请求:', data)
      return await workflowService.handleWorkflowOperation(data)
    } catch (error) {
      logger.error('工作流操作失败:', error)
      return { success: false, message: `操作失败: ${(error as Error).message}` }
    }
  })

  // 处理AI助手消息
  ipcMain.handle('send-to-ai-assistant', async (_, data) => {
    try {
      logger.info('AI助手消息请求:', { message: data.message, workflowId: data.workflowId })
      return await workflowService.handleAIAssistantMessage(data)
    } catch (error) {
      logger.error('AI助手消息处理失败:', error)
      return { success: false, message: `处理失败: ${(error as Error).message}` }
    }
  })

  // 监听AI工作流生成结果 - 这是从渲染进程到主进程的事件，在workflowService中处理
  ipcMain.on('ai-workflow-generate-result', (event, result) => {
    logger.debug('收到AI工作流生成结果:', result.success)
  })

  // 监听工作流运行结果 - 这是从渲染进程到主进程的事件，在workflowService中处理
  ipcMain.on('run-workflow-result', (event, result) => {
    logger.debug('收到工作流运行结果:', result.success)
  })

  // 标记处理程序已设置
  handlersSetup = true
  logger.info('工作流IPC处理程序已设置')
}
