import { is } from '@electron-toolkit/utils'
import { BrowserWindow, screen } from 'electron'
import { join } from 'path'
import { cacheService } from '../services/cacheService'
import { getIconPath } from '../utils/icons'
import logger from '../utils/logger'
import { setupWindowControls } from './windowControls'
// 主窗口实例
let mainWindow: BrowserWindow | null = null
// 默认工作流ID
export const DEFAULT_WORKFLOW_ID = 'workflow-default'
/**
 * 创建主窗口
 */
export function createMainWindow(): BrowserWindow {
  const isMattIcon = process.env.VITE_APP_IS_MATT === '1'
  const appTitle = isMattIcon ? 'MattVerse 电池设计自动化平台' : '豪鹏电池寿命预测软件'
  const iconPath = getIconPath(isMattIcon)

  // 在开发模式下安装Vue Devtools
  if (is.dev) {
    try {
      // 尝试安装Vue Devtools
      import('electron-devtools-installer').then(
        ({ default: installExtension, VUEJS_DEVTOOLS }) => {
          installExtension(VUEJS_DEVTOOLS)
            .then((name) => logger.info(`已安装扩展: ${name}`))
            .catch((err) => logger.error('安装Vue Devtools失败:', err))
        },
      )
    } catch (e) {
      logger.error('加载electron-devtools-installer失败:', e)
    }
  }

  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    title: appTitle,
    show: false,
    frame: false,
    titleBarStyle: 'hidden',
    autoHideMenuBar: true,
    icon: iconPath,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
    },
  })

  // 设置内容安全策略
  cacheService.setupContentSecurityPolicy(mainWindow)

  // 窗口准备好时显示
  mainWindow.on('ready-to-show', () => {
    if (mainWindow) {
      // 获取屏幕尺寸并居中窗口
      const workAreaSize = screen.getPrimaryDisplay().workAreaSize
      const width = 1000
      const height = 650
      const x = Math.floor(workAreaSize.width / 2 - width / 2)
      const y = Math.floor(workAreaSize.height / 2 - height / 2)

      mainWindow.setBounds({ x, y, width, height })
      mainWindow.show()
    }
  })

  // 设置窗口控制事件，但不再调用setupWindowHandlers()
  setupWindowControls(mainWindow)

  // 加载应用
  loadApplication(mainWindow)

  return mainWindow
}

/**
 * 获取默认路由路径
 * 根据应用类型返回不同的默认路径
 */
export function getDefaultRoutePath(): string {
  const isMatt = process.env.VITE_APP_IS_MATT === '1'
  return isMatt ? '/' : `/workflow/editor/${DEFAULT_WORKFLOW_ID}`
}

/**
 * 加载应用内容
 */
function loadApplication(window: BrowserWindow): void {
  const defaultHash = getDefaultRoutePath()

  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    window.loadURL(`${process.env['ELECTRON_RENDERER_URL']}#${defaultHash}`)
  } else {
    window.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: defaultHash,
    })
  }
}

/**
 * 获取主窗口实例
 */
export function getMainWindow(): BrowserWindow | null {
  return mainWindow
}
