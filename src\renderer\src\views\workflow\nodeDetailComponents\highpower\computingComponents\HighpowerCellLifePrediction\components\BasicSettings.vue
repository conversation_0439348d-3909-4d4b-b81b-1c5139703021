<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">运行参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <ChevronDown
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 运行工况 -->
              <div class="flex flex-col space-y-2">
                <Label for="Charge-Type" class="text-sm font-medium">运行工况</Label>
                <Select
                  id="Charge-Type"
                  v-model="localParams.chargeType"
                  :disabled="isDisabled"
                  class="w-full"
                  @update:model-value="updateParams"
                >
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择运行工况类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="charge in chargeTypes"
                      :key="charge.value"
                      :value="charge.value"
                    >
                      {{ charge.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- 充电功率 -->
              <div class="flex flex-col space-y-2">
                <Label for="charge-power" class="text-sm font-medium">充电功率 (W)</Label>
                <Input
                  id="charge-power"
                  v-model="localParams.chargeValue"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                  :disabled="isDisabled"
                  @change="updateParams"
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup>
import { ChevronDown } from 'lucide-vue-next'
import { ref, watch } from 'vue'

const props = defineProps({
  // 基本参数对象
  basicParams: {
    type: Object,
    required: true,
    default: () => ({
      chargeType: 'CP',
      chargeValue: 896,
    }),
  },
  // 是否禁用输入
  isDisabled: {
    type: Boolean,
    default: false,
  },
  // 充电类型列表
  chargeTypes: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:basic-params'])

const localParams = ref({
  chargeType: props.basicParams?.chargeType || 'CP',
  chargeValue: props.basicParams?.chargeValue || 896,
})

watch(
  () => props.basicParams,
  (newParams) => {
    localParams.value = {
      chargeType: newParams.chargeType,
      chargeValue: newParams.chargeValue,
    }
  },
  { deep: true, immediate: true },
)

const updateParams = () => {
  emit('update:basic-params', localParams.value)
}
</script>
