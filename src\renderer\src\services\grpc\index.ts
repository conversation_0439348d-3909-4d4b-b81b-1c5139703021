/**
 * GRPC服务主入口
 */

// 导出连接服务
export { grpcConnectionService } from './connection'

// 导出工作流服务
export {
  addWorkflowDataToCanvas,
  extractLifePredictionNodes,
  registerLifePredictionWorkflow,
  registerNodeModules,
  workflowEventService,
} from './workflow'

// 导出事件分发工具
export {
  addGrpcEventListener,
  dispatchGrpcEvent,
  removeGrpcEventListener,
  showToast,
} from './connection/eventDispatcher'

// 导出类型定义
export * from './types'
