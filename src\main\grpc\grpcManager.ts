import { ClientOptions, grpcClient } from './grpcClient'
const grpc = require('@grpc/grpc-js')
class GrpcManager {
  public initGrpcClient(options: ClientOptions) {
    return grpcClient.initialize(options)
  }
  public call(apiName, params, metadata?: any, option?: any) {
    const grpcMeta = new grpc.Metadata()
    if (metadata) {
      for (const key of metadata) {
        // if (metadata.hasOwnProperty(key)) {
        //   console.log(key, metadata[key])
        // }
      }
    }
    return grpcClient.callMethods(apiName, params, {
      metadata,
      ...option,
    })
  }
}
export default GrpcManager
