<template>
  <ScrollArea class="h-full w-full">
    <div class="about-container">
      <Card class="about-card">
        <CardHeader class="text-center">
          <div class="logo-container">
            <img :src="images.logoIcon" alt="logo" class="app-logo" />
          </div>
          <CardTitle class="app-title">MattVerse</CardTitle>
          <!-- <CardDescription class="mt-2">基于 Electron + Vue3 开发的电池管理系统</CardDescription> -->
        </CardHeader>

        <CardContent>
          <div class="version-info">
            <div class="version-item">
              <span class="label">应用版本</span>
              <span class="value">v{{ version }}</span>
            </div>
            <div class="version-item">
              <span class="label">用户标识码</span>
              <span class="value">{{ userId }}</span>
            </div>
            <div class="version-item">
              <span class="label">Licence有效期</span>
              <span class="value">永久有效</span>
            </div>
          </div>

          <Separator class="my-2" />
          <div class="links-container">
            <Button
              v-tooltip="{
                content: '敬请期待',
                placement: 'top',
                theme: 'light',
              }"
              variant="outline"
              class="link-button cursor-pointer"
              as-child
            >
              <a target="_blank">
                <Icon icon="flowbite:github-solid" :width="24" :height="24" class="mr-2" />
                GitHub
              </a>
            </Button>

            <Button variant="outline" class="link-button" as-child>
              <a href="https://www.mattverse.com" target="_blank">
                <Icon icon="flowbite:book-solid" :width="24" :height="24" class="mr-2" />
                文档
              </a>
            </Button>

            <Button variant="outline" class="link-button" as-child>
              <a href="https://www.mattverse.com" target="_blank">
                <Icon icon="flowbite:globe-solid" :width="24" :height="24" class="mr-2" />
                官网
              </a>
            </Button>
          </div>

          <div class="copyright mt-2 text-center text-sm text-muted-foreground">
            Copyright © {{ new Date().getFullYear() }} MattVerse. All rights reserved.
          </div>
        </CardContent>
      </Card>
    </div>
  </ScrollArea>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

import { Icon } from '@iconify/vue'
import { images } from '@renderer/config/constants'
import { useLanguage } from '@renderer/config/hooks'

const { t } = useLanguage()
const userId = import.meta.env.VITE_APP_USER_ID

const version = ref('--')

// 检查是否在 Electron 环境中
const isElectron = ref(!!window.api)
// const serverVersion = await window.grpcApi.call('getVersion', params);
// console.log(serverVersion)
onMounted(async () => {
  if (isElectron.value) {
    // Electron 环境 - 获取真实版本信息
    try {
      console.log('Requesting versions...')
      const versions = await window.api.getVersions()

      version.value = versions.app
    } catch (error) {
      console.error('Error in loadVersions:', error)
      setMockVersions() // 如果获取失败，使用模拟数据
    }
  } else {
    // 浏览器环境 - 使用模拟数据
    setMockVersions()
  }
})

// 设置模拟版本数据
const setMockVersions = () => {
  version.value = '1.0.0'
}
</script>

<style lang="scss" scoped>
.about-container {
  display: flex;
  justify-content: center;
  align-items: center;
  /* min-height: 100vh; */
  /* padding: 2rem; */
}

.about-card {
  /* max-width: 600px; */
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.logo-container {
  display: flex;
  justify-content: center;
}

.app-logo {
  width: auto;
  height: 3.25rem;
  animation: logo-float 3s ease-in-out infinite;
}

.app-title {
  margin-top: 1rem;
  margin-left: 0.15rem;
  color: rgb(112, 108, 175);
  font-size: 2rem;
}

.version-info {
  margin-top: 1rem;
  display: grid;
  gap: 0.3rem;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
}

.version-item .label {
  color: var(--ev-c-text-2);
}

.version-item .value {
  color: var(--ev-c-text-1);
}

.links-container {
  display: flex;
  justify-content: center;
  gap: 0.3rem;
  margin-top: 1rem;
}

.link-button {
  min-width: 120px;
}

@keyframes logo-float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
@renderer/config/constants
