<template>
  <div class="w-full h-full flex justify-center items-center relative">
    <div v-if="!isNull" ref="chartContainer" class="w-full h-full"></div>
    <div v-if="isNull" class="w-full min-h-[200px] flex justify-center items-center">
      <div class="text-center text-gray-500">No Data</div>
    </div>
    <div
      v-if="isLoading"
      class="absolute top-0 left-0 w-full h-full z-10 flex justify-center items-center bg-gray-200 bg-opacity-75"
    >
      <Loader2 class="w-10 h-10 animate-spin text-white" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next'
import * as Plotly from 'plotly.js-dist-min'
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
const chartContainer: any = ref(null)
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  layout: {
    type: Object,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})
watch(
  () => props.data,
  () => {
    if (props.data.length > 0) {
      initChart()
      isNull.value = false
      nextTick(() => {
        resizeObserver.observe(chartContainer.value)
      })
    }
  },
)
watch(
  () => props.layout,
  () => {
    initChart()
  },
)
let resizeObserver: any = null
onMounted(() => {
  if (!isNull.value) {
    initChart()
  }
  resizeObserver = new ResizeObserver(() => {
    if (chartContainer.value) {
      const width: any = chartContainer.value.clientWidth
      const height: any = chartContainer.value.clientHeight
      Plotly.relayout(chartContainer.value, {
        width,
        height,
      })
    }
  })
})
const isNull = ref(true)
const initChart = () => {
  nextTick(() => {
    Plotly.newPlot(chartContainer.value, props.data, {
      ...props.layout,
    })
  })
}
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})
</script>
<style scoped>
::v-deep .js-plotly-plot .plotly .modebar-btn {
  display: inline-block;
}
</style>
