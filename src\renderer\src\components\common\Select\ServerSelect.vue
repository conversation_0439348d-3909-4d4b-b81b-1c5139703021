<template>
  <div>
    <Select
      :model-value="modelValue"
      :disabled="disabled"
      @update:model-value="(val) => $emit('update:modelValue', val)"
    >
      <SelectTrigger :class="[triggerClass, { 'border-red-500': errorMessage }]">
        <SelectValue :placeholder="placeholder" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel v-if="label">{{ label }}</SelectLabel>
          <div v-for="server in servers" :key="server.serverId">
            <HoverCard>
              <HoverCardTrigger as-child>
                <div>
                  <SelectItem :value="server.serverId">
                    <div class="flex items-center justify-between w-full">
                      <span>{{ server.serverName }}</span>
                    </div>
                  </SelectItem>
                </div>
              </HoverCardTrigger>
              <HoverCardContent class="w-72 p-3">
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <LucideIcon name="Server" class="w-4 h-4 text-muted-foreground" />
                      <h4 class="text-sm font-semibold">{{ server.serverName }}</h4>
                    </div>
                    <Badge v-if="showStatus" :class="getStatusClass(server.serverStatus)">
                      {{ getStatusText(server.serverStatus) }}
                    </Badge>
                  </div>
                  <div class="space-y-1.5 text-sm">
                    <div v-for="field in displayFields" :key="field.key" class="flex items-start">
                      <span class="w-20 text-muted-foreground">{{ field.label }}:</span>
                      <span :class="{ 'break-all': field.breakAll }">
                        {{ getFieldValue(server, field.key) || '未知' }}
                      </span>
                    </div>
                  </div>
                </div>
              </HoverCardContent>
            </HoverCard>
          </div>
        </SelectGroup>
      </SelectContent>
    </Select>
    <p v-if="errorMessage" class="mt-1 text-sm text-red-500">{{ errorMessage }}</p>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

// 定义字段配置接口
interface FieldConfig {
  key: string
  label: string
  breakAll?: boolean
}

// 定义组件属性
const props = withDefaults(
  defineProps<{
    modelValue: string
    servers: any[]
    placeholder?: string
    label?: string
    errorMessage?: string
    disabled?: boolean
    triggerClass?: string
    showStatus?: boolean
    displayFields?: FieldConfig[]
  }>(),
  {
    placeholder: '选择服务器',
    disabled: false,
    triggerClass: '',
    showStatus: true,
    displayFields: () => [
      { key: 'serverId', label: 'ID', breakAll: true },
      { key: 'serverType', label: '类型' },
      { key: 'version', label: '版本' },
      { key: 'url', label: 'URL', breakAll: true },
    ],
  },
)

// 定义事件
defineEmits<{
  'update:modelValue': [value: string]
}>()

// 获取字段值（支持嵌套属性）
const getFieldValue = (obj: any, key: string) => {
  return key.split('.').reduce((o, k) => (o || {})[k], obj)
}

// 获取状态显示文本
const getStatusText = (status) => {
  const statusText = {
    Running: '正常运行',
    Stopped: '已停止运行',
    Expired: '已过期',
    Overloaded: '服务器超载',
    Stay: '原始状态',
    Unknown: '未知状态',
  }
  return statusText[status] || status
}

// 获取状态对应的样式
const getStatusClass = (status) => {
  const classes = {
    Running: 'bg-green-500 hover:bg-green-600',
    Stopped: 'bg-blue-500 hover:bg-blue-600',
    Expired: 'bg-yellow-500 hover:bg-yellow-600',
    Overloaded: 'bg-orange-500 hover:bg-orange-600',
    Stay: 'bg-gray-500 hover:bg-gray-600',
    Unknown: 'bg-gray-500 hover:bg-gray-600',
  }
  return classes[status] || 'bg-gray-500 hover:bg-gray-600'
}
</script>
