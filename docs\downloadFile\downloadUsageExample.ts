/**
 * 文件下载功能使用示例
 *
 * 展示如何使用通用的文件下载方法
 */

import { downloadFileFromServer, downloadModelFile } from '@renderer/utils/utils'

/**
 * 示例1: 使用通用下载方法下载任意文件
 */
export async function downloadGeneralFileExample() {
  try {
    console.log('=== 通用文件下载示例 ===')

    const filePath = '1/W_7swJXiBW_EoL.ml' // 服务器上的文件路径

    const success = await downloadFileFromServer(filePath, {
      defaultFileName: 'my_downloaded_file.ml',
      fileExtension: 'ml',
      fileTypeDescription: 'Machine Learning Files',
      showProgress: true,
      onProgress: (progress, fileName) => {
        console.log(`下载进度: ${progress.toFixed(1)}% - ${fileName}`)
      },
    })

    if (success) {
      console.log('✅ 文件下载成功')
    } else {
      console.log('❌ 文件下载失败或被用户取消')
    }

    return success
  } catch (error) {
    console.error('下载示例失败:', error)
    return false
  }
}

/**
 * 示例2: 使用便捷的模型文件下载方法
 */
export async function downloadModelFileExample() {
  try {
    console.log('=== 模型文件下载示例 ===')

    const modelFilePath = '1/W_7swJXiBW_EoL.ml' // 模型文件路径

    const success = await downloadModelFile(modelFilePath, (progress, fileName) => {
      console.log(`模型下载进度: ${progress.toFixed(1)}% - ${fileName}`)
    })

    if (success) {
      console.log('✅ 模型文件下载成功')
    } else {
      console.log('❌ 模型文件下载失败或被用户取消')
    }

    return success
  } catch (error) {
    console.error('模型下载示例失败:', error)
    return false
  }
}

/**
 * 示例3: 在组件中使用下载功能
 */
export function useDownloadInComponent() {
  // 在 Vue 组件的 setup 函数中使用

  const handleDownloadFile = async (filePath: string) => {
    try {
      const success = await downloadFileFromServer(filePath, {
        defaultFileName: `export_${Date.now()}.ml`,
        fileExtension: 'ml',
        fileTypeDescription: 'Model Files',
        showProgress: true,
        onProgress: (progress, fileName) => {
          // 可以在这里更新 UI 进度条
          console.log(`进度: ${progress}% - ${fileName}`)
        },
      })

      return success
    } catch (error) {
      console.error('下载失败:', error)
      return false
    }
  }

  const handleDownloadModel = async (modelPath: string) => {
    try {
      const success = await downloadModelFile(modelPath)
      return success
    } catch (error) {
      console.error('模型下载失败:', error)
      return false
    }
  }

  return {
    handleDownloadFile,
    handleDownloadModel,
  }
}

/**
 * 示例4: 批量下载文件
 */
export async function batchDownloadExample() {
  try {
    console.log('=== 批量文件下载示例 ===')

    const filePaths = ['1/file1.ml', '2/file2.ml', '3/file3.ml']

    const results = []

    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i]
      console.log(`\n下载文件 ${i + 1}/${filePaths.length}: ${filePath}`)

      try {
        const success = await downloadFileFromServer(filePath, {
          defaultFileName: `batch_file_${i + 1}.ml`,
          fileExtension: 'ml',
          fileTypeDescription: 'Model Files',
          showProgress: true,
          onProgress: (progress, fileName) => {
            console.log(`  文件 ${i + 1} 进度: ${progress.toFixed(1)}% - ${fileName}`)
          },
        })

        results.push({
          filePath,
          success,
          error: null,
        })

        if (success) {
          console.log(`  ✅ 文件 ${i + 1} 下载成功`)
        } else {
          console.log(`  ❌ 文件 ${i + 1} 下载失败或被取消`)
        }
      } catch (error: any) {
        results.push({
          filePath,
          success: false,
          error: error.message,
        })

        console.log(`  ❌ 文件 ${i + 1} 下载失败: ${error.message}`)
      }
    }

    console.log('\n=== 批量下载结果 ===')
    const successCount = results.filter((r) => r.success).length
    console.log(`成功: ${successCount}/${results.length}`)

    return results
  } catch (error) {
    console.error('批量下载失败:', error)
    return []
  }
}

/**
 * 在浏览器控制台中运行示例的便捷函数
 */
export function runDownloadExamples() {
  console.log('文件下载功能使用示例')
  console.log('可用的示例函数:')
  console.log('- downloadGeneralFileExample(): 通用文件下载示例')
  console.log('- downloadModelFileExample(): 模型文件下载示例')
  console.log('- batchDownloadExample(): 批量下载示例')
  console.log('')
  console.log('使用方法:')
  console.log('await downloadGeneralFileExample()')
  console.log('await downloadModelFileExample()')
  console.log('await batchDownloadExample()')
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
  ;(window as any).downloadExamples = {
    downloadGeneralFileExample,
    downloadModelFileExample,
    batchDownloadExample,
    useDownloadInComponent,
    runDownloadExamples,
  }

  // 自动显示使用说明
  runDownloadExamples()
}
