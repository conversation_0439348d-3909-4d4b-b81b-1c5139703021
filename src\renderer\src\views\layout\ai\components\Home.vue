<template>
  <div class="min-h-screen flex flex-col bg-background text-foreground font-sans">
    <main class="flex-grow container mx-auto px-4 py-6">
      <div class="mb-10 text-center">
        <h1 class="text-4xl font-bold mb-4 dynamic-text-gradient">Matt AI电池智能助手</h1>
        <p class="text-muted-foreground max-w-2xl mx-auto text-base leading-relaxed">
          专为新能源电池行业设计的AI助手，助您洞悉技术前沿、精析电池性能、优化充电策略，提供深度的专业洞察与决策支持。
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-5 max-w-4xl mx-auto">
        <router-link
          v-for="(feature, index) in features"
          :key="index"
          :to="feature.to"
          class="group p-5 rounded-xl bg-card border border-border shadow-sm hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center space-x-3 mb-4">
            <div class="icon-wrapper p-2.5 rounded-lg">
              <component :is="feature.icon" class="w-6 h-6 text-white"></component>
            </div>
            <h2
              class="text-xl font-semibold text-card-foreground group-hover:text-primary transition-colors duration-300"
            >
              {{ feature.title }}
            </h2>
          </div>
          <p class="text-muted-foreground mb-4 text-sm leading-normal">
            {{ feature.description }}
          </p>
          <div class="bg-muted/50 p-3 rounded-lg space-y-2.5">
            <template v-if="feature.examples">
              <div
                v-for="(example, i) in feature.examples"
                :key="i"
                class="flex items-start"
                :class="[example.icon === User ? 'justify-end' : 'justify-start']"
              >
                <template v-if="example.icon !== User">
                  <div :class="`shrink-0 bg-${example.color}-100 rounded-full p-1.5 self-start`">
                    <component
                      :is="example.icon"
                      :class="`w-4 h-4 text-${example.color}-600`"
                    ></component>
                  </div>
                  <div class="ml-2 max-w-[80%]">
                    <p
                      class="px-3 py-1.5 bg-background shadow-sm rounded-lg text-foreground/90 text-xs leading-relaxed"
                    >
                      {{ example.text }}
                    </p>
                  </div>
                </template>

                <template v-if="example.icon === User">
                  <div class="mr-2 max-w-[80%]">
                    <p
                      :class="`px-3 py-1.5 bg-${example.color}-500 text-white shadow-sm rounded-lg text-xs leading-relaxed`"
                    >
                      {{ example.text }}
                    </p>
                  </div>
                  <div :class="`shrink-0 bg-${example.color}-100 rounded-full p-1.5 self-start`">
                    <component
                      :is="example.icon"
                      :class="`w-4 h-4 text-${example.color}-700`"
                    ></component>
                  </div>
                </template>
              </div>
            </template>
            <template v-if="feature.items">
              <div class="flex flex-col space-y-2.5">
                <div
                  v-for="(item, j) in feature.items"
                  :key="j"
                  class="flex items-center justify-between p-2.5 bg-card hover:bg-muted/70 border border-border rounded-md group-hover:border-primary/30 transition-colors duration-300"
                >
                  <div class="flex items-center space-x-2">
                    <component
                      :is="item.icon"
                      :class="`w-4 h-4 text-${item.color}-500`"
                    ></component>
                    <span class="text-xs text-foreground/90">{{ item.text }}</span>
                  </div>
                  <span :class="`custom-badge badge-${item.color}`">{{ item.status }}</span>
                </div>
              </div>
            </template>
          </div>
        </router-link>
      </div>

      <div class="mt-12 max-w-4xl mx-auto">
        <h2 class="text-2xl font-bold text-foreground mb-8 text-center">
          专为新能源电池行业
          <span class="dynamic-text-gradient-sub">精心设计</span>
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
          <div
            v-for="(feature, index) in industryFeatures"
            :key="index"
            class="p-5 rounded-xl bg-card border border-border shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <div class="flex items-center space-x-3 mb-3">
              <div :class="`p-2 rounded-lg bg-${feature.color}-100`">
                <component
                  :is="feature.icon"
                  :class="`w-5 h-5 text-${feature.color}-600`"
                ></component>
              </div>
              <h3 class="font-semibold text-base text-card-foreground">{{ feature.title }}</h3>
            </div>
            <p class="text-muted-foreground text-xs leading-normal">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </main>

    <footer class="bg-background border-t border-border py-5 mt-10">
      <div class="container mx-auto px-4 text-center text-muted-foreground text-xs">
        <p>&copy; {{ new Date().getFullYear() }} 电池AI助手 - 新能源电池行业的智能解决方案</p>
        <p class="mt-1">AI驱动未来，智能引领革新</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import {
  Activity,
  BarChart,
  BookOpen,
  Bot,
  Cpu,
  MessageCircle,
  Settings,
  Sparkles,
  User,
  Zap,
} from 'lucide-vue-next'

const features = [
  {
    title: 'AI科研助手',
    icon: MessageCircle,
    description: '与AI进行实时对话，解答新能源电池相关的技术问题，获取专业建议和行业洞察。',
    to: '/ai/chat',
    examples: [
      {
        icon: User,
        text: '如何提高锂离子电池的循环寿命？',
        color: 'green',
      },
      {
        icon: Bot,
        text: '提高锂离子电池循环寿命的关键因素包括温度控制、充放电速率管理和电池材料优化...',
        color: 'cyan',
      },
      {
        icon: User,
        text: '固态电池和液态电池的主要区别是什么？',
        color: 'green',
      },
      {
        icon: Bot,
        text: '主要区别在于电解质的状态。固态电池使用固体电解质，而传统锂离子电池（液态电池）使用液体电解质...',
        color: 'cyan',
      },
    ],
  },
  {
    title: '流程模板',
    icon: Cpu,
    description: '创建和运行定制化的流程模板，自动分析电池性能、优化充电策略、诊断潜在问题。',
    to: '/ai/agent',
    items: [
      {
        icon: Activity,
        text: '电池性能分析',
        status: '活跃',
        color: 'green',
      },
      {
        icon: Zap,
        text: '充电策略优化',
        status: '运行中',
        color: 'blue',
      },
      {
        icon: Sparkles,
        text: '智能故障诊断',
        status: '待启动',
        color: 'purple',
      },
    ],
  },
]

const industryFeatures = [
  {
    icon: BarChart,
    title: '数据可视化',
    description: '通过直观的图表和曲线，清晰展示电池性能数据，帮助您快速理解和分析。',
    color: 'emerald',
  },
  {
    icon: BookOpen,
    title: '术语解析',
    description: '自动识别并解释复杂的专业术语，降低理解门槛，使技术内容更易于理解。',
    color: 'sky',
  },
  {
    icon: Settings,
    title: '简化操作',
    description: '优化用户界面和交互流程，降低技术门槛，使复杂的数据分析和监控变得简单易用。',
    color: 'violet',
  },
]
</script>

<style lang="scss" scoped>
.dynamic-text-gradient {
  background: linear-gradient(135deg, #10b981 0%, #0ea5e9 50%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient-flow 5s ease infinite alternate;
  background-size: 200% auto;
}

.dynamic-text-gradient-sub {
  background: linear-gradient(135deg, #34d399 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.icon-wrapper {
  background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
  box-shadow: 0 3px 10px rgba(16, 185, 129, 0.15);
}

.custom-badge {
  padding: 0.2rem 0.6rem;
  font-size: 0.6875rem;
  font-weight: 600;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.04em;

  &.badge-green {
    background-color: theme('colors.green.100');
    color: theme('colors.green.700');
    border: 1px solid theme('colors.green.200');
  }
  &.badge-blue {
    background-color: theme('colors.blue.100');
    color: theme('colors.blue.700');
    border: 1px solid theme('colors.blue.200');
  }
  &.badge-purple {
    background-color: theme('colors.purple.100');
    color: theme('colors.purple.700');
    border: 1px solid theme('colors.purple.200');
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.5);
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}
</style>
