<template>
  <div class="empty-state-container">
    <div class="empty-state">
      <div class="empty-state-icon">
        <slot name="icon">
          <Icon :icon="icon" :width="iconSize" :height="iconSize" class="text-muted-foreground" />
        </slot>
      </div>
      <h3 class="empty-state-title">{{ title }}</h3>
      <p class="empty-state-description">{{ description }}</p>
      <div class="empty-state-action" v-if="$slots.action">
        <slot name="action"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue'

defineProps({
  title: {
    type: String,
    default: '暂无数据'
  },
  description: {
    type: String,
    default: '当前没有可显示的内容'
  },
  icon: {
    type: String,
    default: 'flowbite:file-chart-outline'
  },
  iconSize: {
    type: Number,
    default: 80
  }
})
</script>

<style lang="scss" scoped>
.empty-state-container {
  @apply flex items-center justify-center h-full w-full;
}

.empty-state {
  @apply flex flex-col items-center justify-center p-8 text-center max-w-md;
  animation: fadeIn 0.5s ease-in-out;
}

.empty-state-icon {
  @apply mb-6 p-4 rounded-full bg-muted/30 dark:bg-muted/10;
}

.empty-state-title {
  @apply text-xl font-semibold mb-2 text-foreground;
}

.empty-state-description {
  @apply text-sm text-muted-foreground mb-6;
}

.empty-state-action {
  @apply mt-2;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
