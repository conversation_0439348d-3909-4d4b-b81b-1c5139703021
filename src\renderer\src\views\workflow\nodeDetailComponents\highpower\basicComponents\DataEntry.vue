<template>
  <Card class="p-2">
    <CardHeader>
      <CardTitle class="text-2xl font-bold dark:text-white/80">数据输入</CardTitle>
    </CardHeader>
    <CardContent>
      <!-- 文件上传组件 -->
      <FileDropUpload
        v-if="!isUploading && !dataLoaded"
        :accept-types="['.xlsx', '.xls', '.csv']"
        :max-size="20"
        @file-selected="handleFile"
        @error="handleUploadError"
        @progress="handleProgress"
        @upload-complete="handleUploadComplete"
      />

      <div v-if="dataLoaded" v-chart-resize-multi="chartInstances" class="flex flex-col">
        <div
          v-for="(series, index) in availableSeries"
          :key="index"
          class="chart-container show mb-6"
        >
          <div :id="`chart-${index}`" class="w-full h-full"></div>
        </div>
        <Button variant="destructive" class="mt-4" @click="resetData">重新上传</Button>
      </div>
    </CardContent>
  </Card>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import FileDropUpload from '@renderer/components/common/Upload/FileDropUpload.vue'
import { toast } from 'vue-sonner'
import * as XLSX from 'xlsx'
import { useFlowsStore } from '@renderer/store'
import { useNodeParams } from '@renderer/config/hooks'
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})
const flowsStore = useFlowsStore()

const isUploading = ref(false)
const dataLoaded = ref(false)
const fileData = ref(null)
const availableSeries = ref([])
const chartInstances = ref({})
const dataSaved = ref(false) //数据是否已保存

const { params, saveParams, deleteParams } = useNodeParams(props.nodeData, {
  defaultValue: {
    fileData: null,
    availableSeries: [],
  },
  autoLoad: true,
  autoSave: true,
  onLoad: (loadedParams) => {
    if (loadedParams.fileData) {
      fileData.value = loadedParams.fileData
      availableSeries.value = loadedParams.availableSeries || []
      dataLoaded.value = true
      dataSaved.value = true
      // 等待 DOM 更新后初始化图表
      nextTick(() => {
        setTimeout(() => {
          availableSeries.value.forEach((series, index) => {
            initChart(index, series)
          })
        }, 300)
      })
    }
  },
  onSave: () => {
    dataSaved.value = true // 保存成功后更新状态
  },
})

/**
 * 重置数据状态
 */
const resetData = () => {
  // 销毁所有图表实例
  Object.keys(chartInstances.value).forEach((key) => {
    if (chartInstances.value[key]) {
      chartInstances.value[key].dispose()
      chartInstances.value[key] = null
    }
  })

  chartInstances.value = {}
  fileData.value = null
  availableSeries.value = []
  dataLoaded.value = false

  // 清除存储的数据
  deleteParams()
}
//保存数据到 store
const saveDataToStore = () => {
  if (fileData.value) {
    // 将数据转换为CSV字符串格式
    let formattedData = fileData.value

    // 如果是对象数组，转换为CSV字符串
    if (
      Array.isArray(fileData.value) &&
      fileData.value.length > 0 &&
      typeof fileData.value[0] === 'object'
    ) {
      // 获取表头
      const headers = Object.keys(fileData.value[0])

      // 创建CSV字符串
      let csvString = headers.join(',') + '\n'

      // 添加类型行（使用原始文件中的类型行或根据数据类型推断）
      if (window._csvTypeRow && window._csvTypeRow.length === headers.length) {
        // 使用原始文件中的类型行
        csvString += window._csvTypeRow.join(',') + '\n'
      } else {
        // 根据数据类型推断
        csvString +=
          headers
            .map((header) => {
              // 查找第一个非空值来确定类型
              const firstNonNullValue = fileData.value.find(
                (row) => row[header] !== null && row[header] !== undefined,
              )
              if (firstNonNullValue) {
                const value = firstNonNullValue[header]
                if (typeof value === 'number') return 'float'
                if (typeof value === 'string') return 'string'
                if (typeof value === 'boolean') return 'boolean'
                return 'float' // 默认为float
              }
              return 'float' // 如果没有找到非空值，默认为float
            })
            .join(',') + '\n'
      }

      // 添加数据行
      fileData.value.forEach((row) => {
        const rowValues = headers.map((header) => {
          const value = row[header]
          return value !== null && value !== undefined ? value : ''
        })
        csvString += rowValues.join(',') + '\n'
      })

      formattedData = csvString.trim()
    }

    // 准备要保存的数据
    saveParams({
      fileData: formattedData,
      availableSeries: availableSeries.value,
    })
  }
}
/**
 * 处理文件上传错误
 * @param {string} errorMsg - 错误信息
 */
const handleUploadError = (errorMsg) => {
  console.error('文件上传错误:', errorMsg)
  // 可以添加错误提示UI
}

/**
 * 处理文件上传
 * @param {File} file - 上传的文件
 */
const handleFile = async (file) => {
  const reader = new FileReader()

  reader.onload = async (e) => {
    try {
      // 解析文件数据
      const data = parseFileData(e.target.result, file.name)
      fileData.value = data

      // 获取可用的数据系列
      const headers = Object.keys(data[0] || {})
      // 第一列通常是时间，其余列是数据系列
      const timeColumn = headers[0]
      availableSeries.value = headers.filter((h) => h !== timeColumn)

      // 保存数据到 store
      await saveDataToStore()
      return true
    } catch (error) {
      toast.error('文件解析失败', {
        description: error.message,
      })
      throw error
    }
  }

  reader.onerror = () => {
    toast.error('读取文件失败')
    throw new Error('读取文件失败')
  }

  // 根据文件类型选择读取方式
  if (file.name.endsWith('.csv')) {
    reader.readAsText(file)
  } else {
    reader.readAsArrayBuffer(file)
  }
}

// 添加进度处理函数
const handleProgress = (progress) => {
  console.log('Upload progress:', progress)
}

// 添加上传完成处理函数
const handleUploadComplete = () => {
  dataLoaded.value = true
  toast.success('文件解析成功')
}

/**
 * 解析文件数据
 * @param {any} fileContent - 文件内容
 * @param {string} fileName - 文件名
 * @returns {Array} 解析后的数据数组
 */
const parseFileData = (fileContent, fileName) => {
  if (fileName.endsWith('.csv')) {
    // 解析CSV文件
    return parseCSV(fileContent)
  } else {
    // 解析Excel文件
    return parseExcel(fileContent)
  }
}

/**
 * 解析CSV文件
 * @param {string} csvContent - CSV文件内容
 * @returns {Array} 解析后的数据数组
 */
const parseCSV = (csvContent) => {
  try {
    // 手动解析CSV内容，以处理特殊格式
    const lines = csvContent.trim().split('\n')

    // 获取表头
    const headers = lines[0].split(',').map((h) => h.trim())

    // 获取类型行（第二行）但不使用它来解析数据
    const typeRow = lines[1].split(',').map((t) => t.trim())

    // 保存类型行信息，以便在保存数据时使用
    window._csvTypeRow = typeRow

    // 解析数据行
    const data = []
    for (let i = 2; i < lines.length; i++) {
      if (!lines[i].trim()) continue // 跳过空行

      const values = lines[i].split(',').map((v) => v.trim())
      const row = {}

      headers.forEach((header, index) => {
        // 将值转换为适当的类型
        let value = values[index]
        if (value === undefined || value === '') {
          value = null
        } else if (!isNaN(Number(value))) {
          value = Number(value)
        }
        row[header] = value
      })

      data.push(row)
    }

    return data
  } catch (error) {
    console.error('解析CSV文件失败:', error)
    throw new Error('CSV文件格式错误')
  }
}

/**
 * 解析Excel文件
 * @param {ArrayBuffer} excelContent - Excel文件内容
 * @returns {Array} 解析后的数据数组
 */
const parseExcel = (excelContent) => {
  try {
    // 使用XLSX库解析Excel
    const workbook = XLSX.read(excelContent, { type: 'array' })
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]

    // 转换为JSON对象数组
    const data = XLSX.utils.sheet_to_json(worksheet)
    return data
  } catch (error) {
    console.error('解析Excel文件失败:', error)
    throw new Error('Excel文件格式错误')
  }
}

/**
 * 为单个数据系列生成图表配置
 * @param {string} seriesName - 系列名称
 * @returns {Object} ECharts配置对象
 */
const generateChartOption = (seriesName) => {
  if (!fileData.value) {
    return null
  }

  let headers, timeColumn, data

  // 处理字符串格式的CSV数据
  if (typeof fileData.value === 'string') {
    const lines = fileData.value.trim().split('\n')
    headers = lines[0].split(',').map((h) => h.trim())
    timeColumn = headers[0] // 假设第一列是时间

    // 解析数据行（跳过表头和类型行）
    data = []
    for (let i = 2; i < lines.length; i++) {
      if (!lines[i].trim()) continue // 跳过空行

      const values = lines[i].split(',').map((v) => v.trim())
      const row = {}

      headers.forEach((header, index) => {
        // 将值转换为适当的类型
        let value = values[index]
        if (value === undefined || value === '') {
          value = null
        } else if (!isNaN(Number(value))) {
          value = Number(value)
        }
        row[header] = value
      })

      data.push(row)
    }
  } else if (Array.isArray(fileData.value) && fileData.value.length > 0) {
    // 处理对象数组格式的数据
    data = fileData.value
    headers = Object.keys(data[0])
    timeColumn = headers[0] // 假设第一列是时间
  } else {
    return null
  }

  // 提取数据
  const xAxisData = []
  const seriesData = []

  data.forEach((item) => {
    if (item[timeColumn] !== undefined && item[timeColumn] !== null) {
      xAxisData.push(item[timeColumn])

      // 确保数据是数值类型
      let value = item[seriesName]
      if (value !== undefined && value !== null) {
        value = isNaN(Number(value)) ? 0 : Number(value)
      } else {
        value = 0
      }
      seriesData.push(value)
    }
  })

  // 获取对应系列的颜色
  let color = '#5470c6' // 默认蓝色
  if (seriesName.toLowerCase().includes('current')) {
    color = '#91cc75' // 绿色
  } else if (seriesName.toLowerCase().includes('voltage')) {
    color = '#fac858' // 黄色
  }

  return {
    title: {
      text: seriesName,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        return `${timeColumn}: ${params[0].axisValue}<br>${seriesName}: ${params[0].value}`
      },
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        name: 'time',
        nameLocation: 'middle', // 名称位置居中
        nameGap: 35, // 名称与轴线之间的距离
        nameTextStyle: {
          fontSize: 14,
          fontWeight: 'normal',
        },
        axisTick: {
          alignWithLabel: true,
        },
        axisLabel: {
          rotate: 0,
          interval: 'auto',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: seriesName,
        nameLocation: 'middle', // 名称位置居中
        nameGap: 40, // 名称与轴线之间的距离
        nameTextStyle: {
          fontSize: 14,
          fontWeight: 'normal',
        },
        axisLabel: {
          formatter: '{value}', // 使用默认格式显示数值
        },
      },
    ],
    series: [
      {
        name: seriesName,
        type: 'line',
        data: seriesData,
        smooth: true,
        itemStyle: {
          color: color,
        },
        emphasis: {
          focus: 'series',
        },
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: true,
      },
    ],
  }
}

/**
 * 初始化图表
 */
const initChart = (index, seriesName) => {
  const chartDom = document.getElementById(`chart-${index}`)
  if (!chartDom) {
    console.error(`找不到图表容器: chart-${index}`)
    return
  }

  // 如果已存在图表实例，先销毁
  if (chartInstances.value[index]) {
    chartInstances.value[index].dispose()
  }

  // 创建新的图表实例
  const chart = echarts.init(chartDom)
  chartInstances.value[index] = chart

  // 生成图表配置
  const option = generateChartOption(seriesName)
  if (!option) {
    console.warn(`系列 ${seriesName} 的数据无效，跳过图表初始化`)
    return
  }

  // 设置图表配置
  chart.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 页面关闭前保存
const handleBeforeUnload = () => {
  if (fileData.value && dataLoaded.value) {
    saveDataToStore()
  }
}

// 监听数据加载状态，初始化图表
watch(dataLoaded, (newVal) => {
  if (newVal) {
    // 等待DOM更新完成
    nextTick(() => {
      // 延迟初始化图表，确保DOM已经渲染
      setTimeout(() => {
        availableSeries.value.forEach((series, index) => {
          initChart(index, series)
        })
      }, 300)
    })
  }
})
watch(
  [() => fileData.value, () => availableSeries.value],
  ([newFileData, newSeries]) => {
    if (newFileData && !isUploading.value) {
      saveDataToStore()
    }
  },
  { deep: true },
)

onMounted(() => {
  if (fileData.value && !dataSaved.value) {
    saveDataToStore()
  }
  // 监听页面关闭事件
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 销毁所有图表实例
  Object.keys(chartInstances.value).forEach((key) => {
    if (chartInstances.value[key]) {
      chartInstances.value[key].dispose()
    }
  })

  // 保存数据
  if (fileData.value && dataLoaded.value) {
    saveDataToStore()
  }
  // 移除事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 300px;
  border-radius: 0.5rem;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.chart-container.show {
  opacity: 1;
  transform: translateY(0);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
