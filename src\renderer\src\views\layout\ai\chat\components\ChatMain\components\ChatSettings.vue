<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button variant="ghost" size="sm" class="text-gray-500 hover:text-gray-700 rounded-full p-1">
        <Settings class="w-5 h-5" />
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-72 p-4" side="top">
      <div class="space-y-4">
        <h3 class="text-sm font-medium">AI 设置</h3>

        <!-- 浮动窗口模式下显示服务器和模型选择 -->
        <template v-if="isFloating">
          <!-- 服务器选择 -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Label for="server-select" class="text-xs">服务器:</Label>
            </div>
            <Select v-model="selectedServerId" @update:model-value="handleServerChange">
              <SelectTrigger class="w-full h-8 text-xs">
                <SelectValue :placeholder="selectedServerName || '选择服务器'" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>可用服务器</SelectLabel>
                  <SelectItem
                    v-for="server in runningServers"
                    :key="server.serverId"
                    :value="server.serverId"
                  >
                    {{ server.serverName }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <!-- 模型选择 -->
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Label for="model-select" class="text-xs">AI 模型:</Label>
            </div>
            <Select v-model="selectedModel" :disabled="isLoadingModels">
              <SelectTrigger class="w-full h-8 text-xs">
                <div class="flex items-center">
                  <Loader v-if="isLoadingModels" class="w-4 h-4 mr-2 animate-spin" />
                  <SelectValue :placeholder="selectedModel || '选择模型'" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <!-- 按提供商分组显示模型 -->
                <template v-for="(models, provider) in modelGroups" :key="provider">
                  <SelectGroup>
                    <SelectLabel>{{ provider }}</SelectLabel>
                    <SelectItem
                      v-for="(item, index) in models"
                      :key="index"
                      :value="`${provider}/${Object.values(item)[0]}`"
                    >
                      {{ Object.keys(item)[0] }}
                    </SelectItem>
                  </SelectGroup>
                  <SelectSeparator
                    v-if="
                      Object.keys(modelGroups).length > 1 &&
                      Object.keys(modelGroups).indexOf(provider) <
                        Object.keys(modelGroups).length - 1
                    "
                  />
                </template>
              </SelectContent>
            </Select>
          </div>
        </template>

        <!-- 温度设置 -->
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <Label for="temperature" class="text-xs">温度:</Label>
            <span class="text-xs text-muted-foreground w-7 text-center tabular-nums">
              {{
                Array.isArray(modelTemperature) && modelTemperature.length > 0
                  ? modelTemperature[0].toFixed(1)
                  : '0.7'
              }}
            </span>
          </div>
          <Slider
            id="temperature"
            v-model="modelTemperature"
            :min="0"
            :max="1"
            :step="0.1"
            class="w-full h-2.5"
            @update:model-value="handleTemperatureChange"
          />
          <p class="text-xs text-muted-foreground mt-1">
            较低的值使输出更确定，较高的值使输出更随机创造
          </p>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { Loader, Settings } from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'

const props = defineProps({
  modelTemperature: {
    type: Array as () => number[],
    default: () => [0.7],
  },
  streamEnabled: {
    type: Boolean,
    default: true,
  },
  // 浮动窗口模式下的额外属性
  isFloating: {
    type: Boolean,
    default: false,
  },
  selectedServerId: {
    type: String,
    default: '',
  },
  selectedModel: {
    type: String,
    default: '',
  },
  runningServers: {
    type: Array,
    default: () => [],
  },
  modelGroups: {
    type: Object,
    default: () => ({}),
  },
  isLoadingModels: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'update:temperature',
  'update:stream-enabled',
  'update:selectedServerId',
  'update:selectedModel',
  'server-change',
])

const modelTemperature = ref(props.modelTemperature)
const streamEnabled = ref(props.streamEnabled)

// 计算属性 - 仅在浮动模式下使用
const selectedServerId = computed({
  get: () => props.selectedServerId,
  set: (value) => emit('update:selectedServerId', value),
})

const selectedModel = computed({
  get: () => props.selectedModel,
  set: (value) => emit('update:selectedModel', value),
})

const selectedServerName = computed(() => {
  const server = props.runningServers.find((s) => s.serverId === props.selectedServerId)
  return server ? server.serverName : ''
})

// 监听props变化
watch(
  () => props.modelTemperature,
  (newVal) => {
    modelTemperature.value = newVal
  },
  { deep: true },
)

watch(
  () => props.streamEnabled,
  (newVal) => {
    streamEnabled.value = newVal
  },
)

const handleTemperatureChange = (value: number[]) => {
  emit('update:temperature', value)
}

const handleStreamChange = (value: boolean) => {
  emit('update:stream-enabled', value)
}

// 浮动模式下的服务器选择处理
const handleServerChange = (serverId: string) => {
  emit('server-change', serverId)

  // 如果有模型组，自动选择第一个模型
  if (props.modelGroups && Object.keys(props.modelGroups).length > 0) {
    const firstProvider = Object.keys(props.modelGroups)[0]
    const firstModelGroup = props.modelGroups[firstProvider]

    if (firstModelGroup && firstModelGroup.length > 0) {
      const firstModel = firstModelGroup[0]
      const modelKey = Object.keys(firstModel)[0]
      const modelValue = Object.values(firstModel)[0]

      // 设置选中的模型
      selectedModel.value = `${firstProvider}/${modelValue}`
      emit('update:selectedModel', selectedModel.value)

      window.logger.info(`自动选择模型: ${modelKey} (${selectedModel.value})`)
    }
  }
}
</script>
