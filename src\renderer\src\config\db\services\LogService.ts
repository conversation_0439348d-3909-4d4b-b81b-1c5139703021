import { LogRepository } from '../repositories/LogRepository'
import { LogEntry, TaskStatus } from '../models/LogEntry'
import { useWorkflowStore, useTaskStore } from '@renderer/store'

export class LogService {
  // 获取所有日志
  static async getAll(): Promise<LogEntry[]> {
    return await LogRepository.getAll()
  }
  // 根据ID获取日志
  static async getById(id: number): Promise<LogEntry | undefined> {
    return await LogRepository.get(id)
  }

  // 删除指定日志
  static async delete(id: number): Promise<void> {
    try {
      await LogRepository.delete(id)
    } catch (error) {
      console.error('删除日志失败:', error)
      throw error
    }
  }

  // 清除所有日志
  static async clear(): Promise<void> {
    try {
      await LogRepository.clear()
    } catch (error) {
      console.error('清除所有日志失败:', error)
      throw error
    }
  }
  // 更新日志结果
  static async updateLogResult(id: number, result: string): Promise<void> {
    try {
      await LogRepository.update(id, {
        result,
        lastUpdated: Date.now(),
      })
    } catch (error) {
      console.error('更新日志结果失败:', error)
      throw error
    }
  }
  // 创建临时日志记录
  static async createTempLog(logData: Partial<LogEntry>): Promise<number> {
    try {
      // 检查是否已存在相同任务ID的日志
      const existingLog = await this.findByTaskId(logData.taskId || '')
      if (existingLog) {
        return existingLog.id as number
      }

      // 创建新的日志记录
      return await LogRepository.add({
        ...logData,
        lastUpdated: Date.now(),
      })
    } catch (error) {
      console.error('创建临时日志失败:', error)
      throw error
    }
  }

  // 记录任务提交
  static async logTaskSubmit(
    workflow: string,
    workflowId: string,
    service_name: string,
    server_id: string,
    inputData: any,
    nodeData: any,
    message?: string,
  ): Promise<number> {
    return await LogRepository.add({
      workflow,
      workflowId,
      service_name,
      server_id,
      createdAt: Date.now(),
      taskStatus: TaskStatus.PENDING,
      taskProgress: 0,
      input_data: JSON.stringify(inputData),
      nodeData: JSON.stringify(nodeData),
      message,
      taskId: '', // 初始为空，后续更新
      nodeId: nodeData.id || '',
      lastUpdated: Date.now(),
    })
  }

  // 更新任务开始状态和task_id
  static async updateTaskStart(id: number, taskId: string, message?: string): Promise<void> {
    await LogRepository.update(id, {
      taskId,
      taskStatus: TaskStatus.RUNNING,
      message,
      lastUpdated: Date.now(),
    })
  }

  // 更新任务进度
  static async updateTaskProgress(id: number, progress: number, message?: string): Promise<void> {
    await LogRepository.update(id, {
      taskProgress: progress,
      message,
      lastUpdated: Date.now(),
    })
  }

  // 更新任务完成
  static async updateTaskCompleted(
    id: number,
    result: string,
    computationTime: number,
    message?: string,
  ): Promise<void> {
    await LogRepository.update(id, {
      taskStatus: TaskStatus.COMPLETED,
      taskProgress: 100,
      result,
      computationTime,
      message,
      lastUpdated: Date.now(),
    })
  }

  // 更新任务失败
  static async updateTaskFailed(
    id: number,
    message: string,
    computationTime?: number,
  ): Promise<void> {
    await LogRepository.update(id, {
      taskStatus: TaskStatus.FAILED,
      message,
      computationTime,
      lastUpdated: Date.now(),
    })
  }

  // 更新任务取消
  static async updateTaskCanceled(id: number, message?: string): Promise<void> {
    await LogRepository.update(id, {
      taskStatus: TaskStatus.CANCELED,
      message,
      lastUpdated: Date.now(),
    })
  }

  // 根据任务ID查找日志
  static async findByTaskId(taskId: string): Promise<LogEntry | undefined> {
    return await LogRepository.findByTaskId(taskId)
  }

  // 根据节点ID查找日志
  static async findByNodeId(nodeId: string): Promise<LogEntry[]> {
    return await LogRepository.findByNodeId(nodeId)
  }

  // 同步任务状态 - 新增方法
  static async syncTaskStatus(taskId: string): Promise<void> {
    try {
      const taskStore = useTaskStore()
      const logEntry = await this.findByTaskId(taskId)

      if (!logEntry || !logEntry.id) return

      // 完成、任务失败、任务终止，这三个状态的任务日志将不再更新，所以排除
      if (
        logEntry.taskStatus === TaskStatus.FINISHED ||
        logEntry.taskStatus === TaskStatus.ERROR ||
        logEntry.taskStatus === TaskStatus.ABORT
      ) {
        return
      }

      // 获取最新的任务信息
      await taskStore.updateTaskList(taskId)
      const task = taskStore.tasks.find((t) => t.taskId === taskId)

      if (!task) return

      // 更新日志条目
      await LogRepository.update(logEntry.id, {
        taskStatus: task.taskStatus,
        taskProgress: task.taskProcess ? parseInt(String(task.taskProcess)) : logEntry.taskProgress,
        computationTime:
          task.endTime && task.startTime
            ? (Number(task.endTime) - Number(task.startTime)) * 1000
            : undefined,
        message: task.taskLog || logEntry.message,
        lastUpdated: Date.now(),
      })
    } catch (error) {
      console.error('同步任务状态失败:', error)
    }
  }

  // 根据workflowId获取工作流名称的方法
  static async getWorkflowName(workflowId: string): Promise<string> {
    const workflowStore = useWorkflowStore()
    const workflow = workflowStore.workflows.find((w) => w.id === workflowId)
    return workflow ? workflow.title : workflowId
  }
}
