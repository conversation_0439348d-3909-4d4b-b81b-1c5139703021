<template>
  <div class="space-y-4">
    <!-- 布局配置 -->
    <ConfigGroup title="布局配置" icon="Layout">
      <ConfigItem
        label="侧边栏"
        :enabled="layoutConfig.showSidebar"
        description="控制主界面侧边栏的显示"
      />
      <ConfigItem
        label="标题栏"
        :enabled="layoutConfig.showTitleBar"
        description="控制应用标题栏的显示"
      />
      <ConfigItem
        label="状态栏"
        :enabled="layoutConfig.showStatusBar"
        description="控制底部状态栏的显示"
      />
    </ConfigGroup>

    <!-- 工作流编辑器配置 -->
    <ConfigGroup title="工作流编辑器" icon="Workflow">
      <ConfigItem
        label="AI浮动框"
        :enabled="workflowEditorConfig.showAIFloatingBox"
        description="控制AI聊天浮动框的显示"
        :highlight="!workflowEditorConfig.showAIFloatingBox"
      />
      <ConfigItem
        label="工具栏"
        :enabled="workflowEditorConfig.showToolbar"
        description="控制编辑器工具栏的显示"
      />
      <ConfigItem
        label="小地图"
        :enabled="workflowEditorConfig.showMiniMap"
        description="控制工作流小地图的显示"
      />
      <ConfigItem
        label="左侧控制面板"
        :enabled="workflowEditorConfig.showLeftControls"
        description="控制左侧操作控制面板"
      />
      <ConfigItem
        label="右侧控制面板"
        :enabled="workflowEditorConfig.showRightControls"
        description="控制右侧缩放控制面板"
      />
      <div class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div class="text-xs text-blue-700 dark:text-blue-300 mb-1">背景设置</div>
        <div class="text-sm text-gray-700 dark:text-gray-300">
          类型:
          <span class="font-mono">{{ workflowEditorConfig.backgroundType }}</span>
        </div>
      </div>
    </ConfigGroup>

    <!-- 导航配置 -->
    <ConfigGroup title="导航配置" icon="Navigation">
      <ConfigItem
        label="主导航"
        :enabled="navigationConfig.showMainNavigation"
        description="控制主导航菜单的显示"
      />
      <ConfigItem
        label="面包屑"
        :enabled="navigationConfig.showBreadcrumb"
        description="控制面包屑导航的显示"
      />
      <ConfigItem
        label="路由守卫"
        :enabled="navigationConfig.enableRouteGuard"
        description="启用路由访问控制"
      />
      <div class="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <div class="text-xs text-green-700 dark:text-green-300 mb-1">默认路由</div>
        <div class="text-sm font-mono text-gray-700 dark:text-gray-300">
          {{ navigationConfig.defaultRoute }}
        </div>
      </div>
    </ConfigGroup>

    <!-- 节点工具栏配置 -->
    <ConfigGroup title="节点工具栏" icon="Boxes">
      <ConfigItem
        label="节点工具栏"
        :enabled="nodeToolbarConfig.showNodeToolbar"
        description="控制节点工具栏的显示"
      />
      <ConfigItem
        label="自定义节点"
        :enabled="nodeToolbarConfig.enableCustomNodes"
        description="允许使用自定义节点"
      />
      <div class="mt-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
        <div class="text-xs text-purple-700 dark:text-purple-300 mb-2">允许的节点分类</div>
        <div class="flex flex-wrap gap-1">
          <span
            v-for="category in nodeToolbarConfig.enableNodeCategories"
            :key="category"
            class="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 rounded"
          >
            {{ getCategoryDisplayName(category) }}
          </span>
        </div>
      </div>
    </ConfigGroup>

    <!-- 认证配置 -->
    <ConfigGroup title="认证系统" icon="Shield">
      <ConfigItem
        label="认证系统"
        :enabled="authConfig.enableAuth"
        description="启用用户认证功能"
      />
      <ConfigItem label="登录表单" :enabled="authConfig.showLoginForm" description="显示登录表单" />
      <ConfigItem
        label="注册表单"
        :enabled="authConfig.showRegisterForm"
        description="显示注册表单"
      />
      <ConfigItem
        label="访客模式"
        :enabled="authConfig.enableGuestMode"
        description="允许访客模式访问"
      />
    </ConfigGroup>

    <!-- 其他功能 -->
    <ConfigGroup title="其他功能" icon="Settings">
      <ConfigItem
        label="国际化"
        :enabled="featuresConfig.enableI18n"
        description="启用多语言支持"
      />
      <ConfigItem
        label="通知系统"
        :enabled="featuresConfig.enableNotifications"
        description="启用系统通知"
      />
      <ConfigItem
        label="快捷键"
        :enabled="featuresConfig.enableHotkeys"
        description="启用键盘快捷键"
      />
      <ConfigItem
        label="自动保存"
        :enabled="featuresConfig.enableAutoSave"
        description="启用自动保存功能"
      />
      <ConfigItem
        label="导出功能"
        :enabled="featuresConfig.enableExport"
        description="启用数据导出功能"
      />
      <ConfigItem
        label="导入功能"
        :enabled="featuresConfig.enableImport"
        description="启用数据导入功能"
      />
    </ConfigGroup>
  </div>
</template>

<script setup lang="ts">
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import ConfigGroup from './ConfigGroup.vue'
import ConfigItem from './ConfigItem.vue'

const {
  layoutConfig,
  workflowEditorConfig,
  navigationConfig,
  nodeToolbarConfig,
  authConfig,
  featuresConfig,
} = useAppConfig()

// 节点分类显示名称映射
const getCategoryDisplayName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    materialDesign: '材料设计',
    batterySimulation: '电池模拟',
    dataAnalysis: '数据分析',
    processOptimization: '流程优化',
  }
  return categoryMap[category] || category
}
</script>
