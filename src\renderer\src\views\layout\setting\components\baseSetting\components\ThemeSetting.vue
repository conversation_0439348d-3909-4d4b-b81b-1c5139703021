<template>
  <Card :class="['mb-6', !isThemeSwitchEnabled && 'border-orange-200 dark:border-orange-800']">
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        {{ t('settings.baseSetting.theme.title') }}
        <span v-if="!isThemeSwitchEnabled" class="text-sm text-orange-600 dark:text-orange-400">
          ({{ t('settings.baseSetting.theme.disabled', '已禁用') }})
        </span>
      </CardTitle>
      <CardDescription>
        <span v-if="isThemeSwitchEnabled">
          {{ t('settings.baseSetting.theme.description') }}
        </span>
        <span v-else class="text-orange-600 dark:text-orange-400">
          {{
            t(
              'settings.baseSetting.theme.disabledDescription',
              '主题切换功能已被应用配置禁用。请在配置面板中启用主题切换功能。',
            )
          }}
        </span>
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div class="space-y-6">
        <!-- 主题模式切换 -->
        <div class="space-y-2">
          <Label class="flex items-center gap-2">
            {{ t('settings.baseSetting.theme.mode') }}
            <span v-if="!isThemeSwitchEnabled" class="text-xs text-orange-600 dark:text-orange-400">
              ({{ t('settings.baseSetting.theme.disabled', '已禁用') }})
            </span>
          </Label>
          <ToggleGroup
            type="single"
            :value="mode"
            :class="[
              'grid grid-cols-3 gap-3',
              !isThemeSwitchEnabled && 'opacity-50 pointer-events-none',
            ]"
          >
            <ToggleGroupItem
              value="light"
              aria-label="Toggle light"
              :class="[
                'relative h-24 rounded-lg border-2 transition-all duration-200',
                'bg-gradient-to-br from-blue-50 to-orange-50',
                'hover:scale-105',
                mode === 'light' ? 'border-primary shadow-lg scale-105' : 'border-transparent',
                mode === 'dark' ? 'text-black' : '',
              ]"
              @click="setMode('light')"
            >
              <div class="flex flex-col items-center justify-center gap-2 h-full">
                <div class="p-2 rounded-full bg-orange-100">
                  <Icon icon="solar:sun-bold" class="w-6 h-6 text-orange-500" />
                </div>
                <span class="text-sm font-medium">
                  {{ t('settings.baseSetting.theme.light') }}
                </span>
              </div>
            </ToggleGroupItem>

            <ToggleGroupItem
              value="dark"
              aria-label="Toggle dark"
              :class="[
                'relative h-24 rounded-lg border-2 transition-all duration-200',
                'bg-gradient-to-br from-gray-900 to-slate-800',
                isThemeSwitchEnabled && isDarkModeEnabled
                  ? 'hover:scale-105'
                  : 'cursor-not-allowed',
                mode === 'dark' ? 'border-primary shadow-lg scale-105' : 'border-transparent',
                !isDarkModeEnabled && 'opacity-50',
              ]"
              @click="setMode('dark')"
            >
              <div class="flex flex-col items-center justify-center gap-2 h-full">
                <div class="p-2 rounded-full bg-slate-700">
                  <Icon icon="solar:moon-bold" class="w-6 h-6 text-indigo-300" />
                </div>
                <span class="text-sm font-medium text-white">
                  {{ t('settings.baseSetting.theme.dark') }}
                </span>
                <span v-if="!isDarkModeEnabled" class="text-xs text-orange-300 text-center">
                  {{ t('settings.baseSetting.theme.disabled', '已禁用') }}
                </span>
              </div>
            </ToggleGroupItem>

            <ToggleGroupItem
              value="auto"
              aria-label="Toggle system"
              :class="[
                'relative h-24 rounded-lg border-2 transition-all duration-200',
                'bg-gradient-to-br from-slate-100 via-slate-200 to-slate-300',
                isThemeSwitchEnabled && isDarkModeEnabled
                  ? 'hover:scale-105'
                  : 'cursor-not-allowed',
                mode === 'auto' ? 'border-primary shadow-lg scale-105' : 'border-transparent',
                !isDarkModeEnabled && 'opacity-50',
              ]"
              @click="setMode('auto')"
            >
              <div class="flex flex-col items-center justify-center gap-2 h-full">
                <div class="p-2 rounded-full bg-white/80 backdrop-blur">
                  <Icon icon="solar:monitor-bold" class="w-6 h-6 text-slate-700" />
                </div>
                <span class="text-sm font-medium">
                  {{ t('settings.baseSetting.theme.system') }}
                </span>
                <span v-if="!isDarkModeEnabled" class="text-xs text-orange-600 text-center">
                  {{ t('settings.baseSetting.theme.disabled', '已禁用') }}
                </span>
              </div>
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        <!-- 主题色调选择 -->
        <div class="space-y-2">
          <Label class="flex items-center gap-2">
            {{ t('settings.baseSetting.theme.colors.title') }}
            <span v-if="!isThemeSwitchEnabled" class="text-xs text-orange-600 dark:text-orange-400">
              ({{ t('settings.baseSetting.theme.disabled', '已禁用') }})
            </span>
          </Label>
          <div
            :class="[
              'grid grid-cols-3 sm:grid-cols-6 gap-2',
              !isThemeSwitchEnabled && 'opacity-50 pointer-events-none',
            ]"
          >
            <Button
              v-for="color in filteredThemeColors"
              :key="color.value"
              variant="outline"
              :class="[
                'relative h-16 w-full rounded-md border-2 p-1 ',
                currentTheme === color.value
                  ? 'border-primary'
                  : 'border-transparent hover:border-muted',
              ]"
              @click="setTheme(color.value)"
            >
              <div
                class="absolute inset-0 opacity-10 rounded-md"
                :style="{
                  backgroundColor: `${color.previewColor}`,
                }"
              ></div>
              <div class="relative flex flex-col items-center gap-1">
                <div class="flex items-center gap-1">
                  <span
                    class="h-5 w-5 rounded-full"
                    :style="{
                      backgroundColor: `${color.previewColor}`,
                    }"
                  />
                </div>
                <span class="text-xs">
                  {{ t(`settings.baseSetting.theme.colors.${color.value}`) }}
                </span>
              </div>
              <span
                v-if="currentTheme === color.value"
                class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-primary-foreground"
              >
                <Icon icon="solar:check-circle-bold" class="h-3 w-3" />
              </span>
            </Button>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useLanguage } from '@renderer/config/hooks'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { useSettingsStore } from '@renderer/store'
import { computed } from 'vue'
import { toast } from 'vue-sonner'

const { t } = useLanguage()
const settingsStore = useSettingsStore()
const { themeConfig } = useAppConfig()

// 使用 store 中的状态
const mode = computed(() => settingsStore.themeMode)
const currentTheme = computed(() => settingsStore.theme)

// 使用 store 中的配置
const filteredThemeColors = computed(() => settingsStore.filteredThemeColors)

// 检查主题功能是否启用
const isThemeSwitchEnabled = computed(() => themeConfig.value.enableThemeSwitch)
const isDarkModeEnabled = computed(() => themeConfig.value.enableDarkMode)

// 设置主题
const setTheme = (theme: string) => {
  if (!isThemeSwitchEnabled.value) {
    toast.error(t('settings.baseSetting.theme.errors.themeSwitchDisabled', '主题切换功能已禁用'))
    return
  }
  settingsStore.setTheme(theme)
}

// 设置模式的方法
const setMode = (newMode: 'light' | 'dark' | 'auto') => {
  if (!isThemeSwitchEnabled.value) {
    toast.error(t('settings.baseSetting.theme.errors.themeSwitchDisabled', '主题切换功能已禁用'))
    return
  }

  if (newMode !== 'light' && !isDarkModeEnabled.value) {
    toast.error(t('settings.baseSetting.theme.errors.darkModeDisabled', '暗色模式功能已禁用'))
    return
  }

  settingsStore.setThemeMode(newMode)
}
</script>
