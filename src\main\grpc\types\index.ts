export enum ValueType {
  INT32 = 0,
  INT64 = 1,
  UINT32 = 2,
  UINT64 = 3,
  FLOAT = 4,
  DOUBLE = 5,
  BOOL = 6,
  STRING = 7,
  JSON = 8,
  JSONARRAY = 9,
}

export interface ServerListConfig {
  [key: string]: string
}

export interface ServerList {
  [key: string]: any
}

export interface GeneralRequest {
  package_name: string
  function_name: string
  key_type_pairs?: Record<string, ValueType>
  key_value_pairs?: Record<string, string>
}

export interface GeneralResponse {
  status_code: number
  message: string
  key_type_pairs?: Record<string, ValueType>
  key_value_pairs?: Record<string, string>
}
