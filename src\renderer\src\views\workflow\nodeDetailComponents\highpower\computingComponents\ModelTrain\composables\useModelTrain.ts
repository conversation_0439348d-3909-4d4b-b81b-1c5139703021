// ModelTrain 组合式函数
import { computed, reactive, ref, watch, nextTick, shallowRef, triggerRef } from 'vue'
import { toast } from 'vue-sonner'
import { useDebounceFn, useThrottleFn } from '@vueuse/core'
import {
  getNodeParams,
  saveNodeParams,
  updateNodeData as updateNodeDataUtil,
} from '@renderer/utils/nodeUtils'
import { useFlowsStore, useTaskStore } from '@renderer/store'
import { createStudyService } from '@renderer/config/api/grpc/studyService'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { downloadModelFile } from '@renderer/utils/utils'

import {
  type DatasetData,
  type DatasetRatios,
  hasAnyAvailableData,
  convertApiDataToDataset,
  generateTrainingData,
} from '../utils/datasetUtils'
import {
  type ModelParams,
  type ModelResult,
  createDefaultModelParams,
  createDefaultModelResult,
  updateModelParamsFromConfig,
  updateModelResultFromConfig,
} from '../utils/paramUtils'

export interface ModelTrainState {
  isProcessing: boolean
  taskProgress: number
  modelResult: ModelResult
  datasetData: DatasetData
  datasetRatios: DatasetRatios
  trainParams: {
    batchSize: number
    epoch: number
  }
  checkInputBatteryData: {
    taskId: string
    inputDatas: string
    result: any
  }
  trainTask: {
    taskId: string
    result: any
  }
  exportTask: {
    taskId: string
    result: any
    isExporting: boolean
  }
  downloadStatus: {
    isDownloading: boolean
    downloadProgress: number
    downloadFileName: string
  }
}

export const useModelTrain = (props: { nodeData: any }) => {
  const taskStore = useTaskStore()
  const flowsStore = useFlowsStore()
  const service = createStudyService()
  const taskService = createTaskService()

  // 状态管理 - 使用 shallowRef 优化大型数据集
  const state = reactive<ModelTrainState>({
    isProcessing: false,
    taskProgress: 0,
    modelResult: createDefaultModelResult(),
    datasetData: {
      allData: [],
      trainData: [],
      testData: [],
      valData: [],
      supportData: [],
    },
    datasetRatios: { train: 60, test: 20, val: 10, support: 10 },
    trainParams: {
      batchSize: 32,
      epoch: 8,
    },
    checkInputBatteryData: {
      taskId: '',
      inputDatas: '',
      result: null,
    },
    trainTask: {
      taskId: '',
      result: null,
    },
    exportTask: {
      taskId: '',
      result: null,
      isExporting: false,
    },
    downloadStatus: {
      isDownloading: false,
      downloadProgress: 0,
      downloadFileName: '',
    },
  })

  // 对话框状态
  const showDatasetRatioDialog = ref(false)
  const showCustomDatasetDialog = ref(false)
  const showSubmitDialog = ref(false)

  // 缓存计算属性
  const trainModelConfig = computed(() => {
    if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
      const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
      return params?.TrainModelConfig || {}
    }
    return {}
  })

  const trainInputData = computed(() => {
    if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
      const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
      return params?.TrainInputData?.input_datas || ''
    }
    return ''
  })

  // 优化的加载状态计算
  const isLoadingBatteryData = computed(() => {
    if (hasAnyAvailableData(state.datasetData, null)) {
      return false
    }
    const taskId = state.checkInputBatteryData.taskId
    if (!taskId) return false
    const taskStatus = taskStore.getTaskStatus(taskId).value
    return taskStatus && !['Finished', 'Error', 'Abort'].includes(taskStatus)
  })

  // 缓存任务相关计算
  const trainTaskProgress = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return 0
    return taskStore.getTaskProgress(taskId).value || 0
  })

  const isTrainTaskProcessing = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return false
    const taskStatus = taskStore.getTaskStatus(taskId).value
    return ['Computing', 'Pending', 'Initializing'].includes(taskStatus)
  })

  const trainTaskStatus = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return 'Unknown'
    return taskStore.getTaskStatus(taskId).value
  })

  const trainTaskCompleted = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return false
    const taskStatus = taskStore.getTaskStatus(taskId).value
    const isTaskFinished = ['Finished', 'Error', 'Abort'].includes(taskStatus)
    const hasResult = !!state.trainTask.result
    return isTaskFinished && hasResult
  })

  const trainTaskDuration = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return '--'
    const task = taskStore.tasks.find((t) => t.taskId === taskId)
    return task?.duration || '--'
  })

  const canShowExportButton = computed(() => {
    if (!state.trainTask.result) return false
    const epoch = state.trainTask.result.epoch
    return epoch && epoch > 0
  })

  // 服务器信息
  const lastServerInfo = ref({ server_id: '', service_name: '' })

  // 优化的状态保存 - 只保存变化的部分
  const savePartialState = useDebounceFn((changes: Record<string, any>) => {
    const savedParams = getNodeParams(props.nodeData) || {}
    saveNodeParams(props.nodeData, { ...savedParams, ...changes })
    console.log('已保存部分状态:', changes)
  }, 1000)

  // 立即保存状态
  const saveCurrentStateImmediate = () => {
    const paramsToSave = {
      modelResult: state.modelResult,
      modelParamsModified: true,
      datasetData: state.datasetData,
      datasetRatios: state.datasetRatios,
      trainParams: state.trainParams,
      checkInputBatteryData: {
        taskId: state.checkInputBatteryData.taskId,
        inputDatas: state.checkInputBatteryData.inputDatas,
        result: state.checkInputBatteryData.result,
      },
      trainTask: {
        taskId: state.trainTask.taskId,
        result: state.trainTask.result,
      },
      exportTask: {
        taskId: state.exportTask.taskId,
        result: state.exportTask.result,
        isExporting: state.exportTask.isExporting,
      },
      downloadStatus: {
        isDownloading: state.downloadStatus.isDownloading,
        downloadProgress: state.downloadStatus.downloadProgress,
        downloadFileName: state.downloadStatus.downloadFileName,
      },
      lastServerInfo: {
        server_id: lastServerInfo.value.server_id,
        service_name: lastServerInfo.value.service_name,
      },
    }
    saveNodeParams(props.nodeData, paramsToSave)
    console.log('已立即保存当前状态')
  }

  // 从存储中恢复状态
  const restoreStateFromStorage = () => {
    const savedParams = getNodeParams(props.nodeData)
    console.log('从存储中获取的参数:', savedParams)

    if (savedParams) {
      // 批量恢复状态
      Object.assign(state.modelResult, savedParams.modelResult || createDefaultModelResult())
      Object.assign(state.datasetData, savedParams.datasetData || state.datasetData)
      Object.assign(state.datasetRatios, savedParams.datasetRatios || state.datasetRatios)
      Object.assign(state.trainParams, savedParams.trainParams || state.trainParams)

      // 恢复任务状态
      if (savedParams.checkInputBatteryData) {
        state.checkInputBatteryData.taskId = savedParams.checkInputBatteryData.taskId || ''
        state.checkInputBatteryData.inputDatas = savedParams.checkInputBatteryData.inputDatas || ''
        state.checkInputBatteryData.result = savedParams.checkInputBatteryData.result || null
      }

      if (savedParams.trainTask) {
        state.trainTask.taskId = savedParams.trainTask.taskId || ''
        state.trainTask.result = savedParams.trainTask.result || null
      }

      if (savedParams.exportTask) {
        state.exportTask.taskId = savedParams.exportTask.taskId || ''
        state.exportTask.isExporting = savedParams.exportTask.isExporting || false
        state.exportTask.result = savedParams.exportTask.result || null
      }

      // 恢复下载状态
      if (savedParams.downloadStatus) {
        Object.assign(state.downloadStatus, savedParams.downloadStatus)
      }

      // 恢复服务器信息
      if (savedParams.lastServerInfo) {
        lastServerInfo.value = { ...savedParams.lastServerInfo }
      }

      // 恢复下载状态提示
      if (state.downloadStatus.isDownloading) {
        toast.info('下载状态已恢复', {
          description: `检测到之前的下载状态 (${state.downloadStatus.downloadProgress.toFixed(1)}%)。下载在后台继续进行。`,
          duration: 5000,
        })
      }

      return true
    }
    return false
  }

  // 更新数据集数据
  const updateDatasetFromApiResult = useDebounceFn((ratios?: DatasetRatios) => {
    if (state.checkInputBatteryData.result) {
      const useRatios = ratios || state.datasetRatios
      const convertedData = convertApiDataToDataset(state.checkInputBatteryData.result, useRatios)

      // 批量更新数据 - 减少响应式更新次数
      Object.assign(state.datasetData, {
        trainData: convertedData.trainData,
        testData: convertedData.testData,
        valData: convertedData.valData,
        supportData: convertedData.supportData,
        allData: [
          ...convertedData.trainData,
          ...convertedData.testData,
          ...convertedData.valData,
          ...convertedData.supportData,
        ],
      })

      console.log('数据集已更新，使用比例:', useRatios)
      savePartialState({ datasetData: state.datasetData })
    } else {
      // 尝试使用存储的数据
      const savedParams = getNodeParams(props.nodeData)
      if (savedParams?.datasetData) {
        const hasStoredData = hasAnyAvailableData(savedParams.datasetData, null)
        if (hasStoredData) {
          Object.assign(state.datasetData, savedParams.datasetData)
          console.log('使用存储的数据集数据')
        }
      }
    }
  }, 100)

  // 更新 allData 缓存
  const updateAllDataCache = () => {
    state.datasetData.allData = [
      ...state.datasetData.trainData,
      ...state.datasetData.testData,
      ...state.datasetData.valData,
      ...state.datasetData.supportData,
    ]
  }

  // 检查输入电池数据
  const checkInputBatteryData = async (inputs: any) => {
    const inputsRes = await service.checkInputBatteryData(inputs)
    if (Number(inputsRes.statusCode) === 200) {
      const taskId = inputsRes.taskId
      state.checkInputBatteryData.taskId = taskId
      taskStore.startPolling(taskId)
    }
  }

  // 数据验证函数
  const validateDataAvailability = (): boolean => {
    return hasAnyAvailableData(state.datasetData, state.checkInputBatteryData.result)
  }

  // 业务逻辑函数
  const handleUseRecommendedDataset = () => {
    console.log('打开数据集比例配置对话框')
    if (!validateDataAvailability()) {
      toast.error('暂无数据，请先等待数据加载完成')
      return
    }
    showDatasetRatioDialog.value = true
  }

  const handleOpenCustomDataset = () => {
    console.log('打开自定义数据集对话框')
    if (!validateDataAvailability()) {
      toast.error('暂无数据，请先等待数据加载完成')
      return
    }
    showCustomDatasetDialog.value = true
  }

  const handleDatasetRatioConfirm = (ratios: DatasetRatios) => {
    console.log('确认数据集比例配置:', ratios)

    Object.assign(state.datasetRatios, ratios)
    updateDatasetFromApiResult(ratios)

    // 批量设置推荐状态
    const allDataArrays = [
      state.datasetData.trainData,
      state.datasetData.testData,
      state.datasetData.valData,
      state.datasetData.supportData,
    ]

    allDataArrays.forEach((items) => {
      items.forEach((item) => {
        item.isRecommended = true
      })
    })

    updateAllDataCache()

    toast.success(
      `已按照 ${ratios.train}%:${ratios.test}%:${ratios.val}%:${ratios.support}% 的比例划分数据集`,
    )

    saveCurrentStateImmediate()
  }

  const handleCustomDatasetConfirm = (newDatasetData: any) => {
    console.log('确认自定义数据集配置:', newDatasetData)

    // 更新数据集数据
    state.datasetData.trainData = newDatasetData.trainData || []
    state.datasetData.testData = newDatasetData.testData || []
    state.datasetData.valData = newDatasetData.valData || []
    state.datasetData.supportData = newDatasetData.supportData || []
    updateAllDataCache()

    // 重置推荐状态
    const resetRecommendedStatus = (items: any[]) => {
      items.forEach((item) => {
        item.isRecommended = false
      })
    }

    resetRecommendedStatus(state.datasetData.trainData)
    resetRecommendedStatus(state.datasetData.testData)
    resetRecommendedStatus(state.datasetData.valData)
    resetRecommendedStatus(state.datasetData.supportData)

    toast.success('自定义数据集配置已保存')
  }

  const handleChartSettingsUpdate = (itemId: string, startCycle: number, endCycle: number) => {
    const updateDatasetItem = (items: any[]) => {
      const item = items.find((item) => item.id === itemId)
      if (item) {
        item.startCycle = startCycle
        item.endCycle = endCycle
        return true
      }
      return false
    }

    const updated =
      updateDatasetItem(state.datasetData.trainData) ||
      updateDatasetItem(state.datasetData.testData) ||
      updateDatasetItem(state.datasetData.valData) ||
      updateDatasetItem(state.datasetData.supportData)

    if (updated) {
      console.log(`更新图表设置 - ${itemId}`)
      savePartialState({ datasetData: state.datasetData })
    }
  }

  // 打开提交对话框
  const openSubmitDialog = () => {
    if (isTrainTaskProcessing.value) {
      toast.warning('训练正在进行中，请稍候')
      return
    }

    if (!state.datasetData.trainData || state.datasetData.trainData.length === 0) {
      toast.error('请先配置训练数据集')
      return
    }

    if (!state.datasetData.valData || state.datasetData.valData.length === 0) {
      toast.error('请先配置验证数据集')
      return
    }

    showSubmitDialog.value = true
  }

  // 处理提交任务
  const handleSubmit = async (serverInfo: any) => {
    try {
      showSubmitDialog.value = false

      lastServerInfo.value = {
        server_id: serverInfo.server_id,
        service_name: serverInfo.service_name,
      }

      const trainingData = generateTrainingData(state.datasetData)

      // 构造模型参数
      const modelParamsData = {
        model_name: state.modelResult.modelName,
        model_desc: state.modelResult.modelDesc,
        model_params: {
          in_channels: state.modelResult.modelParams.inChannels.value,
          channels: state.modelResult.modelParams.channels.value,
          input_segment_height: state.modelResult.modelParams.inputSegmentHeight.value,
          input_segment_width: state.modelResult.modelParams.inputSegmentWidth.value,
          alpha: state.modelResult.modelParams.alpha.value,
          kernel_size: state.modelResult.modelParams.kernelSize.value,
          train_support_size: state.modelResult.modelParams.trainSupportSize.value,
          test_support_size: state.modelResult.modelParams.testSupportSize.value,
          act_fn: state.modelResult.modelParams.actFn.value,
          use_fc_for_prediction: state.modelResult.modelParams.useFcForPrediction.value,
          filter_cycles_flag: state.modelResult.modelParams.filterCyclesFlag.value,
          features_to_drop: state.modelResult.modelParams.featuresToDrop.value,
          cycles_to_drop_in_segment: state.modelResult.modelParams.cyclesToDropInSegment.value,
          return_pointwise_predictions:
            state.modelResult.modelParams.returnPointwisePredictions.value,
        },
      }

      const trainParamsData = {
        batch_size: state.trainParams.batchSize,
        epoch: state.trainParams.epoch,
      }

      const inputDatasData = {
        train_files: trainingData.train_data,
        val_files: trainingData.val_data,
        test_files: trainingData.test_data,
        support_files: trainingData.support_data,
      }

      console.log('模型训练提交参数', {
        service_name: serverInfo.service_name,
        server_id: serverInfo.server_id,
        is_save: serverInfo.is_save,
      })

      const response = await service.eolTrainApi(
        serverInfo.service_name,
        serverInfo.server_id,
        serverInfo.is_save,
        JSON.stringify(modelParamsData),
        JSON.stringify(trainParamsData),
        JSON.stringify(inputDatasData),
      )

      if (response.statusCode === 200 || response.statusCode === '200') {
        const taskId = response.taskId
        state.trainTask.taskId = taskId
        taskStore.startPolling(taskId)

        const taskInputData = {
          model_params: JSON.stringify(modelParamsData),
          train_params: JSON.stringify(trainParamsData),
          input_datas: JSON.stringify(inputDatasData),
        }

        updateNodeDataUtil(props.nodeData, {
          taskId: taskId,
          taskInputData: taskInputData,
          taskOutputData: null,
        })

        saveCurrentStateImmediate()

        toast.success('模型训练任务已提交', {
          description: `任务ID: ${taskId}`,
        })

        console.log('训练任务提交成功，任务ID:', taskId)
      } else {
        throw new Error(response.message || '提交任务失败')
      }
    } catch (error: any) {
      console.error('提交训练任务失败：', error)
      toast.error('提交失败', {
        description: error.message || '未知错误',
      })
    }
  }

  // 任务操作配置
  const taskOperations: Record<
    string,
    {
      name: string
      action: (taskId: string) => Promise<any>
      successMessage: string
      successDescription: string
      errorMessage: string
      warningMessage: string
      warningDescription: string
      requiresProcessing: boolean
      afterSuccess?: (taskId: string) => void
    }
  > = {
    pause: {
      name: '暂停',
      action: (taskId: string) => taskService.pauseTask(taskId),
      successMessage: '任务已暂停',
      successDescription: '训练任务已暂停',
      errorMessage: '暂停失败',
      warningMessage: '无法暂停',
      warningDescription: '没有正在进行的任务',
      requiresProcessing: true,
    },
    stop: {
      name: '终止',
      action: (taskId: string) => taskService.stopTask(taskId),
      successMessage: '任务已终止',
      successDescription: '训练任务已成功终止',
      errorMessage: '终止失败',
      warningMessage: '无法终止',
      warningDescription: '没有正在进行的任务',
      requiresProcessing: true,
      afterSuccess: (taskId: string) => taskStore.stopPolling(taskId),
    },
    resume: {
      name: '恢复',
      action: (taskId: string) => taskService.resumeTask(taskId),
      successMessage: '任务已恢复',
      successDescription: '训练任务已恢复运行',
      errorMessage: '恢复失败',
      warningMessage: '无法恢复',
      warningDescription: '没有可恢复的任务',
      requiresProcessing: false,
      afterSuccess: (taskId: string) => taskStore.startPolling(taskId),
    },
  }

  // 通用任务操作处理函数
  const handleTaskOperation = async (operationType: keyof typeof taskOperations) => {
    const operation = taskOperations[operationType]

    if (operation.requiresProcessing && !isTrainTaskProcessing.value) {
      return
    }

    const taskId = state.trainTask.taskId
    if (!taskId) {
      toast.warning(operation.warningMessage, {
        description: operation.warningDescription,
      })
      return
    }

    try {
      console.log(`开始${operation.name}任务:`, taskId)
      const result = await operation.action(taskId)

      if (result.status === 'Success') {
        await taskStore.updateTaskList(taskId)

        if (operation.afterSuccess) {
          operation.afterSuccess(taskId)
        }

        toast.success(operation.successMessage, {
          description: operation.successDescription,
        })

        saveCurrentStateImmediate()
        console.log(`${operation.name}任务成功:`, taskId)
      } else {
        toast.error(operation.errorMessage, {
          description: result.message || `${operation.name}任务时发生错误`,
        })
      }
    } catch (error: any) {
      console.error(`${operation.name}任务失败:`, error)
      toast.error(operation.errorMessage, {
        description: error.message || `${operation.name}任务时发生错误`,
      })
    }
  }

  // 具体的处理函数
  const handlePause = () => handleTaskOperation('pause')
  const handleStop = () => handleTaskOperation('stop')
  const handleResume = () => handleTaskOperation('resume')

  // 下载导出的文件
  const downloadExportedFile = async (filePath: string) => {
    try {
      console.log('=== downloadExportedFile 开始 ===')
      console.log('文件路径:', filePath)

      const success = await downloadModelFile(filePath, (progress, fileName) => {
        console.log(`下载进度: ${progress.toFixed(1)}% - ${fileName}`)
      })

      console.log('downloadModelFile 返回结果:', success)
      if (!success) {
        console.log('用户取消了下载或下载失败')
      }

      return success
    } catch (error: any) {
      console.error('下载文件失败:', error)
      toast.error('下载失败', {
        description: error.message || '下载文件时发生错误',
      })
      return false
    }
  }

  // 解析导出结果中的文件路径
  const parseFilePath = (result: any): string => {
    if (typeof result === 'string') return result
    if (result?.values?.result) return result.values.result
    if (result?.result) return result.result
    return ''
  }

  // 提交新的导出任务
  const submitExportTask = async (trainTaskId: string): Promise<void> => {
    state.exportTask.isExporting = true

    const response = await service.exportEoLModelApi(trainTaskId)

    if (response.statusCode !== 200 && response.statusCode !== '200') {
      throw new Error(response.message || '提交导出任务失败')
    }

    const exportTaskId = response.taskId
    state.exportTask.taskId = exportTaskId
    taskStore.startPolling(exportTaskId)

    console.log('导出任务提交成功，任务ID:', exportTaskId)
  }

  const handleExport = async () => {
    console.log('=== handleExport 开始 ===')

    try {
      if (!state.exportTask.result) {
        toast.error('导出失败', {
          description: '暂无可导出的文件，请等待训练完成至少一轮',
        })
        return
      }

      const filePath = parseFilePath(state.exportTask.result)
      if (!filePath) {
        toast.error('导出失败', { description: '无法获取导出文件路径' })
        return
      }

      state.downloadStatus.isDownloading = true
      state.downloadStatus.downloadProgress = 0
      state.downloadStatus.downloadFileName = ''

      saveCurrentStateImmediate()

      console.log('准备下载文件:', filePath)

      const success = await downloadModelFile(filePath, (progress, fileName) => {
        console.log(`下载进度: ${progress.toFixed(1)}% - ${fileName}`)

        state.downloadStatus.downloadProgress = progress
        state.downloadStatus.downloadFileName = fileName || ''

        // 使用节流保存下载进度
        throttledSaveDownloadProgress()
      })

      console.log('下载结果:', success)

      if (success) {
        toast.success('下载完成', {
          description: '文件下载完成',
        })
      }

      state.downloadStatus.isDownloading = false
      state.downloadStatus.downloadProgress = 0
      state.downloadStatus.downloadFileName = ''
      saveCurrentStateImmediate()
    } catch (error: any) {
      console.error('导出操作失败:', error)

      state.downloadStatus.isDownloading = false
      state.downloadStatus.downloadProgress = 0
      state.downloadStatus.downloadFileName = ''
      saveCurrentStateImmediate()

      toast.error('导出失败', {
        description: error.message || '导出过程中发生未知错误',
      })
    }
  }

  // 节流保存下载进度
  const throttledSaveDownloadProgress = useThrottleFn(() => {
    savePartialState({
      downloadStatus: {
        isDownloading: state.downloadStatus.isDownloading,
        downloadProgress: state.downloadStatus.downloadProgress,
        downloadFileName: state.downloadStatus.downloadFileName,
      },
    })
  }, 1000)

  // 优化的监听器设置
  const setupWatchers = () => {
    // 监听 trainModelConfig 变化 - 使用浅层监听
    watch(
      () => trainModelConfig.value?.modelResult || trainModelConfig.value?.model_params,
      (newConfig) => {
        if (newConfig) {
          const savedParams = getNodeParams(props.nodeData)
          const hasUserModified = savedParams?.modelParamsModified === true

          if (hasUserModified && savedParams?.modelResult) {
            console.log('用户已修改过参数，使用存储的配置')
            Object.assign(state.modelResult, savedParams.modelResult)
            if (savedParams.datasetData) {
              Object.assign(state.datasetData, savedParams.datasetData)
            }
          } else {
            state.modelResult = updateModelResultFromConfig(
              trainModelConfig.value,
              state.modelResult,
              hasUserModified,
            )
          }
        }
      },
      { immediate: true },
    )

    // 监听 trainInputData 变化
    watch(
      trainInputData,
      (newInputData) => {
        state.checkInputBatteryData.inputDatas = newInputData
        if (newInputData) {
          checkInputBatteryData(newInputData)
        }
      },
      { immediate: true },
    )

    // 监听任务状态变化
    watch(
      () =>
        state.checkInputBatteryData.taskId
          ? taskStore.getTaskStatus(state.checkInputBatteryData.taskId).value
          : null,
      async (newStatus) => {
        if (!state.checkInputBatteryData.taskId || !newStatus) return

        if (['Finished', 'Error', 'Abort'].includes(newStatus)) {
          await taskStore.updateTaskResult(state.checkInputBatteryData.taskId)
          const result = taskStore.getTaskResultById(state.checkInputBatteryData.taskId).value
          state.checkInputBatteryData.result = result

          console.info('checkInputBatteryData 任务完成')
          updateDatasetFromApiResult()
        }
      },
    )

    // 监听训练任务结果变化
    watch(
      () => {
        const taskId = state.trainTask.taskId
        if (!taskId) return null
        return taskStore.getTaskResultById(taskId).value
      },
      async (newResult, oldResult) => {
        if (!state.trainTask.taskId || !newResult || newResult === oldResult) return

        console.log('监听到训练任务结果更新')

        state.trainTask.result = newResult

        if (newResult.epoch > 0 && !state.exportTask.taskId) {
          console.log(`训练进行中但 epoch > 0 (${newResult.epoch})，自动提交导出任务`)
          try {
            const trainTaskId = state.trainTask.taskId!
            await submitExportTask(trainTaskId)
          } catch (error: any) {
            console.error('自动提交导出任务失败:', error)
            toast.error('自动导出失败', {
              description: error.message || '自动提交导出任务时发生错误',
            })
          }
        }

        savePartialState({ trainTask: state.trainTask })
      },
      { deep: false },
    )

    // 监听训练任务状态变化
    watch(
      () => (state.trainTask.taskId ? taskStore.getTaskStatus(state.trainTask.taskId).value : null),
      async (newStatus, oldStatus) => {
        if (!state.trainTask.taskId || !newStatus || newStatus === oldStatus) return

        console.log('监听训练任务状态变化:', { newStatus, oldStatus })

        if (['Finished', 'Error', 'Abort'].includes(newStatus)) {
          await taskStore.updateTaskResult(state.trainTask.taskId)
          const latestResult = taskStore.getTaskResultById(state.trainTask.taskId).value

          if (latestResult) {
            state.trainTask.result = latestResult
            console.log('任务完成，获取到最新结果')
          }

          if (newStatus === 'Finished') {
            toast.success('模型训练完成', {
              description: '训练任务已成功完成',
            })

            if (state.trainTask.result && state.trainTask.result.epoch > 0) {
              console.log('训练完成且 epoch > 0，自动提交导出任务')
              try {
                const trainTaskId = state.trainTask.taskId!
                await submitExportTask(trainTaskId)
              } catch (error: any) {
                console.error('自动提交导出任务失败:', error)
                toast.error('自动导出失败', {
                  description: error.message || '自动提交导出任务时发生错误',
                })
              }
            }
          } else if (newStatus === 'Error') {
            toast.error('模型训练失败', {
              description: '训练任务执行出错',
            })
          } else if (newStatus === 'Abort') {
            toast.warning('模型训练已终止', {
              description: '训练任务被用户终止',
            })
          }

          saveCurrentStateImmediate()
        }
      },
    )

    // 监听导出任务结果变化
    watch(
      () => {
        const taskId = state.exportTask.taskId
        if (!taskId) return null
        return taskStore.getTaskResultById(taskId).value
      },
      (newResult, oldResult) => {
        if (!state.exportTask.taskId || !newResult || newResult === oldResult) return

        console.log('监听到导出任务结果更新')

        state.exportTask.result = newResult
        savePartialState({ exportTask: state.exportTask })
      },
      { deep: false },
    )

    // 选择性监听状态变化，避免深度监听
    watch(() => state.modelResult.modelName, savePartialState)
    watch(() => state.modelResult.modelDesc, savePartialState)
    watch(() => state.trainParams.batchSize, savePartialState)
    watch(() => state.trainParams.epoch, savePartialState)
    watch(
      () => [
        state.datasetData.trainData.length,
        state.datasetData.testData.length,
        state.datasetData.valData.length,
        state.datasetData.supportData.length,
      ],
      savePartialState,
    )
    watch(() => state.datasetRatios.train, savePartialState)
    watch(() => state.datasetRatios.test, savePartialState)
    watch(() => state.datasetRatios.val, savePartialState)
    watch(() => state.datasetRatios.support, savePartialState)
  }

  // 初始化时恢复状态
  restoreStateFromStorage()

  return {
    state,
    showDatasetRatioDialog,
    showCustomDatasetDialog,
    showSubmitDialog,
    isLoadingBatteryData,
    trainModelConfig,
    trainInputData,
    trainTaskProgress,
    isTrainTaskProcessing,
    trainTaskStatus,
    trainTaskCompleted,
    trainTaskDuration,
    canShowExportButton,
    lastServerInfo,

    // 方法
    handleExport: useDebounceFn(handleExport, 1000),
    downloadExportedFile,
    handlePause,
    handleStop,
    handleResume,
    handleUseRecommendedDataset,
    handleOpenCustomDataset,
    handleDatasetRatioConfirm,
    handleCustomDatasetConfirm,
    handleChartSettingsUpdate,
    restoreStateFromStorage,
    setupWatchers,
    openSubmitDialog,
    handleSubmit: useDebounceFn(handleSubmit, 1000),

    // 使用计算属性缓存
    getTotalDataCount: computed(() => {
      if (state.checkInputBatteryData.result && Array.isArray(state.checkInputBatteryData.result)) {
        return state.checkInputBatteryData.result.length
      }
      return 0
    }),
  }
}
