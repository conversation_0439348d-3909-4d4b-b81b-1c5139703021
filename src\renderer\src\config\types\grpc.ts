export enum UserStatus {
  Activated = 0,
  Deactivated = 1,
  Deleted = 2,
}

// 用户角色
export enum UserRole {
  Super = 0,
  Admin = 1,
  Normal = 2,
}

// 服务器状态
export enum ServerStatus {
  Running = 0,
  Stopped = 1,
  Expired = 2,
  Overloaded = 3,
  Stay = 4,
  Unknown = 5,
}

// 任务状态
export enum TaskStatus {
  Initializing = 0,
  Computing = 1,
  Pending = 2,
  Paused = 3,
  Finished = 4,
  Error = 5,
  TaskStay = 6,
  Abort = 7,
}

// 响应状态
export type ResponseStatus = 'Success' | 'Failed'
// export enum ResponseStatus {
//   Success = 0,
//   Failed = 1,
// }

// 实体结构
export interface UserInfoEntity {
  user_id: string
  user_name: string
  password: string
  access_level: number
  user_status: UserStatus
  role: UserRole
}

export interface UserEntity {
  user_id: string
  token: string
  expired_time: number
}

export interface ServerEntity {
  server_id: string
  server_name: string
  url: string
  server_status: ServerStatus
  region: string
  access_level: number
  create_time: number
  update_time: number
}

export interface ServiceEntity {
  service_id: string
  service_name: string
  server_id: string
  version: string
  protocol_type: string
  access_level: number
}

export interface TaskEntity {
  task_id: string
  user_id: string
  service_id: string
  start_time: number
  end_time: number
  task_log: string
  task_status: TaskStatus
  task_process: number
  task_pid: number
  result: string
  file_path: string
  create_time: number
  update_time: number
}
export interface UserInfoResult {
  user_info_id: string
  user_info_list: UserInfoEntity[]
}

export interface UserResult {
  token: string
  user_list: UserEntity[]
}

export interface ServerResult {
  server_id: string
  server_list: ServerEntity[]
}

export interface ServiceResult {
  service_id: string
  service_list: ServiceEntity[]
}
export interface TaskResult {
  task_id: string
  task_list: TaskEntity[]
}

export interface GetResponse {
  status: ResponseStatus
  message: string
  result: string
  result_type: string
}

export interface OperateResponse {
  status: ResponseStatus
  message: string
}

interface DbBaseResponse {
  status: ResponseStatus
  message: string
}

export type DbResponse = DbBaseResponse &
  (
    | { result: { user_info_result: UserInfoResult } }
    | { result: { user_result: UserResult } }
    | { result: { server_result: ServerResult } }
    | { result: { service_result: ServiceResult } }
    | { result: { task_result: TaskResult } }
  )
