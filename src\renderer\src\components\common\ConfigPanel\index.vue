<template>
  <div class="fixed bottom-4 right-4 z-[1000]">
    <!-- 浮动按钮 -->
    <Button
      v-if="!showPanel"
      size="icon"
      class="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
      title="打开配置面板"
      @click="showPanel = true"
    >
      <Settings class="h-5 w-5" />
    </Button>

    <!-- 配置面板 -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 scale-95 translate-y-4"
      enter-to-class="opacity-100 scale-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100 translate-y-0"
      leave-to-class="opacity-0 scale-95 translate-y-4"
    >
      <Card v-if="showPanel" class="w-96 max-h-[80vh] overflow-hidden flex flex-col shadow-2xl">
        <!-- 头部 -->
        <CardHeader class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <Settings class="h-5 w-5" />
              <CardTitle class="text-lg text-white">配置面板</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="icon"
              class="text-white/80 hover:text-white hover:bg-white/10"
              @click="showPanel = false"
            >
              <X class="h-5 w-5" />
            </Button>
          </div>
        </CardHeader>

        <!-- 内容区域 -->
        <CardContent class="flex-1 overflow-y-auto p-0">
          <!-- 标签页 -->
          <Tabs v-model="activeTab" class="w-full">
            <TabsList class="grid w-full grid-cols-4 rounded-none border-b">
              <TabsTrigger
                v-for="tab in tabs"
                :key="tab.id"
                :value="tab.id"
                class="flex items-center space-x-2"
              >
                <component :is="tab.icon" class="h-4 w-4" />
                <span class="hidden sm:inline">{{ tab.label }}</span>
              </TabsTrigger>
            </TabsList>

            <!-- 标签页内容 -->
            <div class="p-4">
              <TabsContent value="info" class="mt-0">
                <AppInfoSection />
              </TabsContent>

              <TabsContent value="features" class="mt-0">
                <FeatureConfigSection />
              </TabsContent>

              <TabsContent value="theme" class="mt-0">
                <ThemeConfigSection />
              </TabsContent>

              <TabsContent value="dev" class="mt-0">
                <DevToolsSection />
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>

        <!-- 底部操作栏 -->
        <CardFooter class="border-t bg-muted/50 p-4">
          <div class="flex w-full justify-between items-center">
            <div class="text-xs text-muted-foreground">
              当前: {{ appType }} v{{ appMeta.version }}
            </div>
            <div class="flex space-x-2">
              <Button size="sm" variant="outline" class="h-7 text-xs" @click="exportConfig">
                导出配置
              </Button>
              <Button size="sm" variant="destructive" class="h-7 text-xs" @click="resetConfig">
                重置配置
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@renderer/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@renderer/components/ui/tabs'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { Code, Info, Palette, Settings, X, Zap } from 'lucide-vue-next'
import { ref } from 'vue'
import { toast } from 'vue-sonner'

// 子组件
import AppInfoSection from './ConfigPanel/AppInfoSection.vue'
import DevToolsSection from './ConfigPanel/DevToolsSection.vue'
import FeatureConfigSection from './ConfigPanel/FeatureConfigSection.vue'
import ThemeConfigSection from './ConfigPanel/ThemeConfigSection.vue'

const showPanel = ref(false)
const activeTab = ref('info')

const { appType, appMeta, resetConfig: resetAppConfig } = useAppConfig()

// 标签页配置
const tabs = [
  { id: 'info', label: '应用信息', icon: Info },
  { id: 'features', label: '功能配置', icon: Zap },
  { id: 'theme', label: '主题配置', icon: Palette },
  { id: 'dev', label: '开发工具', icon: Code },
]

// 导出配置
const exportConfig = () => {
  try {
    const { appConfig } = useAppConfig()
    const configData = JSON.stringify(appConfig.value, null, 2)

    // 创建下载链接
    const blob = new Blob([configData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${appType.value}-config.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('配置已导出')
  } catch (error) {
    toast.error(`导出失败: ${error}`)
  }
}

// 重置配置
const resetConfig = () => {
  try {
    resetAppConfig()
    toast.success('配置已重置')
  } catch (error) {
    toast.error(`重置失败: ${error}`)
  }
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + Shift + C 打开/关闭配置面板
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
    event.preventDefault()
    showPanel.value = !showPanel.value
  }

  // ESC 关闭面板
  if (event.key === 'Escape' && showPanel.value) {
    showPanel.value = false
  }
}

// 监听键盘事件
if (typeof window !== 'undefined') {
  window.addEventListener('keydown', handleKeydown)
}
</script>

<style scoped>
/* 确保面板在最顶层 */
.z-\[1000\] {
  z-index: 1000;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 暗色模式下的滚动条 */
.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}
</style>
