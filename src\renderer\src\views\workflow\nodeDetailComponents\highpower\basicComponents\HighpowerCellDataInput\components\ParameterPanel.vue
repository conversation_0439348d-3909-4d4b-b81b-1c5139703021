<template>
  <div
    class="bg-background rounded-xl shadow-sm p-6 border border-gray-200 dark:border-muted-foreground dark:bg-muted-foreground"
  >
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold text-foreground">{{ title }}</h2>
      <Button
        v-if="Object.keys(parameters).length"
        variant="destructive"
        size="sm"
        :disabled="isParamsExtract"
        @click="$emit('reset')"
      >
        重新上传
      </Button>
    </div>
    <FileDropUpload
      v-if="!Object.keys(parameters).length || isResetting"
      :accept-types="['.xlsx', '.xls']"
      :max-size="20"
      @file-selected="(file) => $emit('file-selected', file)"
      @error="(error) => $emit('upload-error', error)"
      @progress="(progress) => $emit('upload-progress', progress)"
      @upload-complete="$emit('upload-complete')"
    />
    <div v-if="Object.keys(parameters).length && !isResetting" class="space-y-4">
      <div v-for="(params, category, categoryIndex) in parameters" :key="categoryIndex">
        <Collapsible
          :open="localOpenStates[categoryIndex]"
          @update:open="updateOpenState(categoryIndex, $event)"
        >
          <CollapsibleTrigger class="transition-colors w-full">
            <div
              class="flex items-center justify-between p-4 bg-gary-foreground rounded-lg shadow-sm border border-gray-200 dark:border-muted-foreground dark:bg-muted"
            >
              <div class="flex items-center">
                <span
                  class="w-2 h-2 rounded-full mr-2"
                  :class="{
                    'bg-blue-500': categoryIndex % 4 === 0,
                    'bg-green-500': categoryIndex % 4 === 1,
                    'bg-red-500': categoryIndex % 4 === 2,
                    'bg-yellow-500': categoryIndex % 4 === 3,
                  }"
                ></span>
                <div
                  class="text-xl font-semibold text-foreground/90 hover:text-foreground/70 transition-colors"
                >
                  {{ category }}
                  <Badge variant="secondary">({{ params.length }}) 个参数</Badge>
                </div>
              </div>
              <ChevronDown
                class="w-5 h-5 transform transition-transform"
                :class="{ 'rotate-180': openStates[categoryIndex] }"
              />
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent class="mt-2">
            <div class="p-4 border border-gray-200 rounded-b-lg">
              <div class="grid grid-cols-2 gap-4">
                <div v-for="(param, paramIndex) in params" :key="paramIndex" class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">
                    {{ param.parameter }}
                    <span
                      v-if="param.variableName"
                      class="block text-xs text-gray-500 mt-0.5 truncate max-w-6xl"
                      :title="param.variableName"
                    >
                      {{ param.variableName }}
                    </span>
                  </label>
                  <div class="relative">
                    <Input
                      v-model="param.value"
                      :disabled="isParamsExtract"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      @input="
                        (e) =>
                          $emit('param-change', categoryType, category, paramIndex, e.target.value)
                      "
                    />
                    <span v-if="param.unit" class="absolute right-3 top-2 text-blue-500 text-sm">
                      {{ param.unit }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  </div>
</template>

<script setup>
import { FileDropUpload } from '@renderer/components'
import { ChevronDown } from 'lucide-vue-next'
import { ref, watch } from 'vue'

const props = defineProps({
  // 面板标题
  title: {
    type: String,
    required: true,
  },
  // 参数对象，包含多个分类和每个分类下的参数列表
  parameters: {
    type: Object,
    required: true,
  },
  // 各分类的展开状态数组
  openStates: {
    type: Array,
    required: true,
  },
  // 是否处于重置状态
  isResetting: {
    type: Boolean,
    default: false,
  },
  // 参数分类类型，用于区分不同的参数面板
  categoryType: {
    type: String,
    required: true,
  },
  //参数是否提取
  isParamsExtract: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'reset', // 重置事件
  'file-selected', // 文件选择事件
  'param-change', // 参数变更事件
  'upload-error', // 上传错误事件
  'upload-progress', // 上传进度事件
  'upload-complete', // 上传完成事件
  'update:openStates', // 更新展开状态事件
])

const localOpenStates = ref([...props.openStates])

const updateOpenState = (index, isOpen) => {
  localOpenStates.value[index] = isOpen
  emit('update:openStates', [...localOpenStates.value])
}

watch(
  () => props.openStates,
  (newOpenStates) => {
    localOpenStates.value = [...newOpenStates]
  },
  { deep: true },
)
</script>
