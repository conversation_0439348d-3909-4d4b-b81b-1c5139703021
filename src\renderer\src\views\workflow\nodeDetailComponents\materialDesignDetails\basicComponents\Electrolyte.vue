<template>
  <div class="space-y-4">
    <div class="grid gap-4">
      <div class="grid gap-2">
        <Label for="concentration">浓度 (mol/L)</Label>
        <Input
          id="concentration"
          v-model="formData.concentration"
          type="number"
          placeholder="请输入浓度"
        />
      </div>

      <div class="grid gap-2">
        <Label for="volume">体积 (mL)</Label>
        <Input id="volume" v-model="formData.volume" type="number" placeholder="请输入体积" />
      </div>
    </div>
    <div>
      <h3>环境变量检查</h3>
      <div>Material Design API: {{ materialDesignApi }}</div>
      <div>Battery Simulation API: {{ batterySimulationApi }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:nodeData'])
const materialDesignApi = ref('未知')
const batterySimulationApi = ref('未知')

const formData = ref({})

onMounted(() => {
  console.log(props.nodeData)
})

// 设置组件的对话框配置
// defineOptions({
//   dialogSize: 'sm:max-w-[425px]',
//   showHeader: true,
//   showFooter: true,
// })
</script>
