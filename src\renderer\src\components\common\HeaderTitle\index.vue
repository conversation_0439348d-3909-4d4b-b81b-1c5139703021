<template>
  <div class="flex flex-row items-center text-2xl font-semibold capitalize">
    <div>
      {{ title }}
    </div>
    <div v-if="showIcon" class="flex items-center ml-2">
      <Icon
        v-tooltip="tooltip"
        icon="flowbite:exclamation-circle-outline"
        :width="20"
        :height="20"
      ></Icon>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Icon } from '@iconify/vue'
interface Props {
  title: string
  tooltip?: string | Partial<Props>
  showIcon?: boolean
}
withDefaults(defineProps<Props>(), {
  title: '标题',
  tooltip: '提示',
  showIcon: true,
})
</script>
<style lang=""></style>
