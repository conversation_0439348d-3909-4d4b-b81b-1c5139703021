<template>
  <div class="space-y-4">
    <!-- 布局配置 -->
    <ConfigGroup title="布局配置" icon="Layout">
      <ConfigItem
        label="侧边栏"
        :enabled="layoutConfig.showSidebar"
        description="控制主界面侧边栏的显示"
        :editable="true"
        config-path="features.layout.showSidebar"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="标题栏"
        :enabled="layoutConfig.showTitleBar"
        description="控制应用标题栏的显示"
        :editable="true"
        config-path="features.layout.showTitleBar"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="状态栏"
        :enabled="layoutConfig.showStatusBar"
        description="控制底部状态栏的显示"
        :editable="true"
        config-path="features.layout.showStatusBar"
        @toggle="handleConfigToggle"
      />
    </ConfigGroup>

    <!-- 侧边栏菜单配置 -->
    <ConfigGroup title="侧边栏菜单" icon="Menu">
      <ConfigItem
        label="侧边栏显示"
        :enabled="sidebarConfig.showSidebar"
        description="全局控制侧边栏的显示/隐藏"
        :editable="true"
        config-path="features.sidebar.showSidebar"
        @toggle="handleConfigToggle"
      />

      <!-- 主菜单项配置 -->
      <div class="mt-3 space-y-2">
        <div class="text-sm font-medium text-foreground">主菜单项</div>
        <div class="grid grid-cols-2 gap-2">
          <ConfigItem
            label="工作流"
            :enabled="sidebarConfig.menuItems.workflow"
            description="工作流编辑器菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.workflow"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="任务"
            :enabled="sidebarConfig.menuItems.task"
            description="任务管理菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.task"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="服务器"
            :enabled="sidebarConfig.menuItems.server"
            description="服务器管理菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.server"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="日志"
            :enabled="sidebarConfig.menuItems.logger"
            description="日志查看菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.logger"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="工具"
            :enabled="sidebarConfig.menuItems.tools"
            description="工具集合菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.tools"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="设置"
            :enabled="sidebarConfig.menuItems.setting"
            description="应用设置菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.setting"
            size="sm"
            @toggle="handleConfigToggle"
          />
        </div>
      </div>

      <!-- 底部菜单项配置 -->
      <div class="mt-3 space-y-2">
        <div class="text-sm font-medium text-foreground">底部菜单项</div>
        <div class="grid grid-cols-3 gap-2">
          <ConfigItem
            label="AI"
            :enabled="sidebarConfig.menuItems.ai"
            description="AI助手菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.ai"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="用户"
            :enabled="sidebarConfig.menuItems.user"
            description="用户中心菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.user"
            size="sm"
            @toggle="handleConfigToggle"
          />
          <ConfigItem
            label="关于"
            :enabled="sidebarConfig.menuItems.about"
            description="关于应用菜单"
            :editable="true"
            config-path="features.sidebar.menuItems.about"
            size="sm"
            @toggle="handleConfigToggle"
          />
        </div>
      </div>

      <!-- 菜单统计信息 -->
      <Card
        class="mt-3 border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-900/20"
      >
        <CardContent class="p-3">
          <div class="text-xs text-indigo-700 dark:text-indigo-300 mb-2">菜单统计</div>
          <div class="grid grid-cols-2 gap-3 text-xs">
            <div class="text-center">
              <div class="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                {{ getEnabledMainMenuCount() }}
              </div>
              <div class="text-muted-foreground">主菜单项</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                {{ getEnabledBottomMenuCount() }}
              </div>
              <div class="text-muted-foreground">底部菜单项</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </ConfigGroup>

    <!-- 工作流编辑器配置 -->
    <ConfigGroup title="工作流编辑器" icon="Workflow">
      <ConfigItem
        label="工作流导航栏"
        :enabled="workflowEditorConfig.showNavbar"
        description="控制工作流页面顶部导航栏的显示"
        :editable="true"
        config-path="features.workflowEditor.showNavbar"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="AI浮动框"
        :enabled="workflowEditorConfig.showAIFloatingBox"
        description="控制AI聊天浮动框的显示"
        :highlight="!workflowEditorConfig.showAIFloatingBox"
        :editable="true"
        config-path="features.workflowEditor.showAIFloatingBox"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="工具栏"
        :enabled="workflowEditorConfig.showToolbar"
        description="控制编辑器工具栏的显示"
        :editable="true"
        config-path="features.workflowEditor.showToolbar"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="小地图"
        :enabled="workflowEditorConfig.showMiniMap"
        description="控制工作流小地图的显示"
        :editable="true"
        config-path="features.workflowEditor.showMiniMap"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="左侧控制面板"
        :enabled="workflowEditorConfig.showLeftControls"
        description="控制左侧操作控制面板"
        :editable="true"
        config-path="features.workflowEditor.showLeftControls"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="右侧控制面板"
        :enabled="workflowEditorConfig.showRightControls"
        description="控制右侧缩放控制面板"
        :editable="true"
        config-path="features.workflowEditor.showRightControls"
        @toggle="handleConfigToggle"
      />
      <Card class="mt-3 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20">
        <CardContent class="p-3">
          <div class="text-xs text-blue-700 dark:text-blue-300 mb-1">背景设置</div>
          <div class="text-sm">
            类型:
            <Badge variant="outline" class="font-mono">
              {{ workflowEditorConfig.backgroundType }}
            </Badge>
          </div>
        </CardContent>
      </Card>
    </ConfigGroup>

    <!-- 导航配置 -->
    <ConfigGroup title="导航配置" icon="Navigation">
      <ConfigItem
        label="主导航"
        :enabled="navigationConfig.showMainNavigation"
        description="控制主导航菜单的显示"
        :editable="true"
        config-path="features.navigation.showMainNavigation"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="面包屑"
        :enabled="navigationConfig.showBreadcrumb"
        description="控制面包屑导航的显示"
        :editable="true"
        config-path="features.navigation.showBreadcrumb"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="路由守卫"
        :enabled="navigationConfig.enableRouteGuard"
        description="启用路由访问控制"
        :editable="true"
        config-path="features.navigation.enableRouteGuard"
        @toggle="handleConfigToggle"
      />
      <Card class="mt-3 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20">
        <CardContent class="p-3">
          <div class="text-xs text-green-700 dark:text-green-300 mb-1">默认路由</div>
          <div class="text-sm font-mono break-all">
            {{ navigationConfig.defaultRoute }}
          </div>
        </CardContent>
      </Card>
    </ConfigGroup>

    <!-- 节点工具栏配置 -->
    <ConfigGroup title="节点工具栏" icon="Boxes">
      <ConfigItem
        label="节点工具栏"
        :enabled="nodeToolbarConfig.showNodeToolbar"
        description="控制节点工具栏的显示"
        :editable="true"
        config-path="features.nodeToolbar.showNodeToolbar"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="自定义节点"
        :enabled="nodeToolbarConfig.enableCustomNodes"
        description="允许使用自定义节点"
        :editable="true"
        config-path="features.nodeToolbar.enableCustomNodes"
        @toggle="handleConfigToggle"
      />
      <Card
        class="mt-3 border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-900/20"
      >
        <CardContent class="p-3">
          <div class="text-xs text-purple-700 dark:text-purple-300 mb-2">允许的节点分类</div>
          <div class="flex flex-wrap gap-1">
            <Badge
              v-for="category in nodeToolbarConfig.enableNodeCategories"
              :key="category"
              variant="secondary"
              class="text-xs"
            >
              {{ getCategoryDisplayName(category) }}
            </Badge>
          </div>
        </CardContent>
      </Card>
    </ConfigGroup>

    <!-- 认证配置 -->
    <ConfigGroup title="认证系统" icon="Shield">
      <ConfigItem
        label="认证系统"
        :enabled="authConfig.enableAuth"
        description="启用用户认证功能"
        :editable="true"
        config-path="features.auth.enableAuth"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="登录表单"
        :enabled="authConfig.showLoginForm"
        description="显示登录表单"
        :editable="true"
        config-path="features.auth.showLoginForm"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="注册表单"
        :enabled="authConfig.showRegisterForm"
        description="显示注册表单"
        :editable="true"
        config-path="features.auth.showRegisterForm"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="访客模式"
        :enabled="authConfig.enableGuestMode"
        description="允许访客模式访问"
        :editable="true"
        config-path="features.auth.enableGuestMode"
        @toggle="handleConfigToggle"
      />
    </ConfigGroup>

    <!-- 工具面板配置 -->
    <ConfigGroup title="工具面板" icon="Wrench">
      <ConfigItem
        label="工具面板"
        :enabled="toolsConfig.showToolsPanel"
        description="显示工具面板"
        :editable="true"
        config-path="features.tools.showToolsPanel"
        @toggle="handleConfigToggle"
      />

      <Card
        v-if="toolsConfig.enabledTools.length > 0"
        class="mt-3 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20"
      >
        <CardContent class="p-3">
          <div class="text-xs text-orange-700 dark:text-orange-300 mb-2">启用的工具</div>
          <div class="flex flex-wrap gap-1">
            <Badge
              v-for="tool in toolsConfig.enabledTools"
              :key="tool"
              variant="secondary"
              class="text-xs"
            >
              {{ getToolDisplayName(tool) }}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card v-else class="mt-3">
        <CardContent class="p-3">
          <div class="text-xs text-muted-foreground text-center">当前配置未启用任何工具</div>
        </CardContent>
      </Card>
    </ConfigGroup>

    <!-- 其他功能 -->
    <ConfigGroup title="其他功能" icon="Settings">
      <ConfigItem
        label="国际化"
        :enabled="featuresConfig.enableI18n"
        description="启用多语言支持"
        :editable="true"
        config-path="features.features.enableI18n"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="通知系统"
        :enabled="featuresConfig.enableNotifications"
        description="启用系统通知"
        :editable="true"
        config-path="features.features.enableNotifications"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="快捷键"
        :enabled="featuresConfig.enableHotkeys"
        description="启用键盘快捷键"
        :editable="true"
        config-path="features.features.enableHotkeys"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="自动保存"
        :enabled="featuresConfig.enableAutoSave"
        description="启用自动保存功能"
        :editable="true"
        config-path="features.features.enableAutoSave"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="导出功能"
        :enabled="featuresConfig.enableExport"
        description="启用数据导出功能"
        :editable="true"
        config-path="features.features.enableExport"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="导入功能"
        :enabled="featuresConfig.enableImport"
        description="启用数据导入功能"
        :editable="true"
        config-path="features.features.enableImport"
        @toggle="handleConfigToggle"
      />
    </ConfigGroup>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent } from '@renderer/components/ui/card'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { toast } from 'vue-sonner'
import ConfigGroup from './ConfigGroup.vue'
import ConfigItem from './ConfigItem.vue'

const {
  layoutConfig,
  sidebarConfig,
  workflowEditorConfig,
  navigationConfig,
  nodeToolbarConfig,
  authConfig,
  featuresConfig,
  toolsConfig,
  updateConfigValue,
} = useAppConfig()

// 处理配置切换
const handleConfigToggle = (configPath: string, newValue: boolean) => {
  try {
    updateConfigValue(configPath, newValue)
    toast.success(`配置已更新: ${configPath.split('.').pop()} = ${newValue ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('配置更新失败:', error)
    toast.error(`配置更新失败: ${error}`)
  }
}

// 节点分类显示名称映射
const getCategoryDisplayName = (category: string): string => {
  const categoryMap: Record<string, string> = {
    materialDesign: '材料设计',
    batterySimulation: '电池模拟',
    dataAnalysis: '数据分析',
    processOptimization: '流程优化',
  }
  return categoryMap[category] || category
}

// 工具显示名称映射
const getToolDisplayName = (tool: string): string => {
  const toolMap: Record<string, string> = {
    calculator: '计算器',
    converter: '转换器',
    analyzer: '分析器',
    'custom-tool': '自定义工具',
  }
  return toolMap[tool] || tool
}

// 获取启用的主菜单项数量
const getEnabledMainMenuCount = (): number => {
  const mainMenuItems = ['workflow', 'task', 'server', 'logger', 'tools', 'setting']
  return mainMenuItems.filter(
    (item) => sidebarConfig.value.menuItems[item as keyof typeof sidebarConfig.value.menuItems],
  ).length
}

// 获取启用的底部菜单项数量
const getEnabledBottomMenuCount = (): number => {
  const bottomMenuItems = ['ai', 'user', 'about']
  return bottomMenuItems.filter(
    (item) => sidebarConfig.value.menuItems[item as keyof typeof sidebarConfig.value.menuItems],
  ).length
}
</script>
