<template>
  <Panel position="top-left" class="z-10">
    <div
      class="flex items-center gap-3 bg-background/80 backdrop-blur-sm p-2 rounded-lg shadow-lg dark:bg-background/60 dark:shadow-none border border-border/30"
    >
      <div class="flex gap-2 px-1">
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80"
          title="切换侧边栏"
          @click="toggleToolbar"
        >
          <Icon
            :icon="
              isToolbarVisible ? 'flowbite:close-sidebar-outline' : 'flowbite:open-sidebar-outline'
            "
            :width="20"
            :height="20"
          />
        </button>
        <button
          class="flex items-center justify-center w-8 h-8 rounded-md bg-background/50 text-foreground border border-border/20 cursor-pointer transition-all duration-200 hover:bg-muted hover:border-border/40 active:bg-muted/80"
          title="日志"
          @click="openLogger"
        >
          <Icon icon="flowbite:clock-arrow-outline" :width="20" :height="20" />
        </button>
      </div>
    </div>
  </Panel>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { Panel } from '@vue-flow/core'

interface Props {
  isToolbarVisible: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'toggle-toolbar': []
  'open-logger': []
}>()

const toggleToolbar = () => {
  emit('toggle-toolbar')
}

const openLogger = () => {
  emit('open-logger')
}
</script>
