/**
 * GRPC连接重连逻辑
 */
import { ConnectionConfig } from '../types'

let reconnectTimer: NodeJS.Timeout | null = null
let reconnectAttempts: number = 0
const reconnectInterval: number = 5000
const maxReconnectAttempts: number = 10

// 保存连接配置信息
let connectionConfig: ConnectionConfig | null = null
// 保存连接回调函数
let connectCallback: ((config: ConnectionConfig) => void) | null = null

/**
 * 设置连接配置和回调
 */
export function setConnectionInfo(
  config: ConnectionConfig,
  callback: (config: ConnectionConfig) => void,
): void {
  connectionConfig = config
  connectCallback = callback
}

/**
 * 安排重连
 */
export function scheduleReconnect(): void {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
  }

  if (reconnectAttempts < maxReconnectAttempts) {
    window.logger?.info(`安排重连，尝试次数: ${reconnectAttempts + 1}/${maxReconnectAttempts}`)
    reconnectTimer = setTimeout(tryReconnect, reconnectInterval)
  } else {
    window.logger?.warn(`已达到最大重连次数 (${maxReconnectAttempts})，停止重连`)
  }
}

/**
 * 尝试重连
 */
function tryReconnect(): void {
  reconnectAttempts++

  if (!connectionConfig || !connectCallback) {
    window.logger?.warn('无法重连：缺少连接配置信息或回调函数')
    return
  }

  window.logger?.info(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})...`)

  // 调用回调函数进行重连
  connectCallback(connectionConfig)
}

/**
 * 重置重连尝试次数
 */
export function resetReconnectAttempts(): void {
  reconnectAttempts = 0
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
    reconnectTimer = null
  }
}
