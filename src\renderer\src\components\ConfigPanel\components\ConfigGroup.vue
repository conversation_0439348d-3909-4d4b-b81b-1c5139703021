<template>
  <Card class="border-muted">
    <CardHeader class="pb-3">
      <CardTitle class="text-sm flex items-center">
        <component :is="iconComponent" class="h-4 w-4 mr-2" />
        {{ title }}
      </CardTitle>
    </CardHeader>
    <CardContent class="space-y-2">
      <slot />
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import {
  Boxes,
  Layout,
  Navigation,
  Palette,
  Settings,
  Shield,
  Workflow,
  Wrench,
} from 'lucide-vue-next'
import { computed } from 'vue'

interface Props {
  title: string
  icon: string
}

const props = defineProps<Props>()

// 图标组件映射
const iconMap = {
  Layout,
  Workflow,
  Navigation,
  Boxes,
  Shield,
  Settings,
  Palette,
  Wrench,
}

const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || Settings
})
</script>
