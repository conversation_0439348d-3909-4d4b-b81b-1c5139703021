<template>
  <div class="space-y-4">
    <!-- 应用基本信息 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Info class="h-4 w-4 mr-2" />
          应用信息
        </CardTitle>
      </CardHeader>
      <CardContent class="grid grid-cols-2 gap-3 text-sm">
        <div>
          <span class="text-muted-foreground">类型:</span>
          <span class="ml-2 font-medium">{{ appType }}</span>
        </div>
        <div>
          <span class="text-muted-foreground">名称:</span>
          <span class="ml-2 font-medium">{{ appMeta.name }}</span>
        </div>
        <div class="col-span-2">
          <span class="text-muted-foreground">标题:</span>
          <span class="ml-2 font-medium">{{ appMeta.title }}</span>
        </div>
        <div class="col-span-2">
          <span class="text-muted-foreground">描述:</span>
          <span class="ml-2 text-muted-foreground">{{ appMeta.description }}</span>
        </div>
        <div>
          <span class="text-muted-foreground">版本:</span>
          <span class="ml-2 font-medium">{{ appMeta.version }}</span>
        </div>
        <div>
          <span class="text-muted-foreground">作者:</span>
          <span class="ml-2 font-medium">{{ appMeta.author }}</span>
        </div>
      </CardContent>
    </Card>

    <!-- 应用Logo和图标 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Image class="h-4 w-4 mr-2" />
          图标资源
        </CardTitle>
      </CardHeader>
      <CardContent class="flex items-center space-x-4">
        <!-- 应用图标 -->
        <div class="text-center">
          <div
            class="w-12 h-12 bg-background rounded-lg border flex items-center justify-center mb-2"
          >
            <img
              :src="iconUrl"
              :alt="`${appMeta.name} icon`"
              class="w-8 h-8 object-contain"
              @error="handleImageError"
            />
          </div>
          <span class="text-xs text-muted-foreground">图标</span>
        </div>

        <!-- 应用Logo -->
        <div class="text-center">
          <div
            class="w-12 h-12 bg-background rounded-lg border flex items-center justify-center mb-2"
          >
            <img
              :src="logoUrl"
              :alt="`${appMeta.name} logo`"
              class="w-10 h-8 object-contain"
              @error="handleImageError"
            />
          </div>
          <span class="text-xs text-muted-foreground">Logo</span>
        </div>

        <!-- 文件路径信息 -->
        <div class="flex-1 text-xs text-muted-foreground">
          <div>图标: {{ appMeta.icon }}</div>
          <div>Logo: {{ appMeta.logo }}</div>
        </div>
      </CardContent>
    </Card>

    <!-- 环境信息 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Monitor class="h-4 w-4 mr-2" />
          环境信息
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span class="text-muted-foreground">VITE_APP_IS_MATT:</span>
          <span class="font-mono">{{ envIsMatt }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">NODE_ENV:</span>
          <span class="font-mono">{{ nodeEnv }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">开发模式:</span>
          <Badge :variant="isDev ? 'default' : 'secondary'">
            {{ isDev ? '是' : '否' }}
          </Badge>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">构建时间:</span>
          <span class="font-mono text-xs">{{ buildTime }}</span>
        </div>
      </CardContent>
    </Card>

    <!-- 应用统计 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <BarChart3 class="h-4 w-4 mr-2" />
          功能统计
        </CardTitle>
      </CardHeader>
      <CardContent class="grid grid-cols-2 gap-3 text-sm">
        <div class="text-center">
          <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
            {{ enabledFeaturesCount }}
          </div>
          <div class="text-muted-foreground">已启用功能</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-muted-foreground">{{ totalFeaturesCount }}</div>
          <div class="text-muted-foreground">总功能数</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-green-600 dark:text-green-400">
            {{ allowedNodeCategories.length }}
          </div>
          <div class="text-muted-foreground">节点分类</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
            {{ allowedThemes.length }}
          </div>
          <div class="text-muted-foreground">可用主题</div>
        </div>
      </CardContent>
    </Card>

    <!-- 临时调试组件 -->
    <ImageDebugger />
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { BarChart3, Image, Info, Monitor } from 'lucide-vue-next'
import { computed } from 'vue'
import ImageDebugger from './ImageDebugger.vue'

const { appType, appMeta, getAllowedNodeCategories, getAllowedThemes, features } = useAppConfig()

// 动态导入图标和Logo
const iconUrl = computed(() => {
  try {
    // 使用动态导入来获取图标URL
    return new URL(`../../../assets/logo/${appMeta.value.icon}`, import.meta.url).href
  } catch (error) {
    console.warn(`Failed to load icon: ${appMeta.value.icon}`, error)
    return ''
  }
})

const logoUrl = computed(() => {
  try {
    // 使用动态导入来获取Logo URL
    return new URL(`../../../assets/logo/${appMeta.value.logo}`, import.meta.url).href
  } catch (error) {
    console.warn(`Failed to load logo: ${appMeta.value.logo}`, error)
    return ''
  }
})

// 环境变量
const envIsMatt = import.meta.env.VITE_APP_IS_MATT
const nodeEnv = import.meta.env.NODE_ENV
const isDev = import.meta.env.DEV
const buildTime = new Date().toLocaleString()

// 功能统计
const enabledFeaturesCount = computed(() => {
  let count = 0
  const featuresObj = features.value

  // 递归计算启用的功能数量
  const countFeatures = (obj: any): void => {
    for (const key in obj) {
      if (typeof obj[key] === 'boolean' && obj[key]) {
        count++
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        countFeatures(obj[key])
      }
    }
  }

  countFeatures(featuresObj)
  return count
})

const totalFeaturesCount = computed(() => {
  let count = 0
  const featuresObj = features.value

  // 递归计算总功能数量
  const countFeatures = (obj: any): void => {
    for (const key in obj) {
      if (typeof obj[key] === 'boolean') {
        count++
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        countFeatures(obj[key])
      }
    }
  }

  countFeatures(featuresObj)
  return count
})

const allowedNodeCategories = computed(() => getAllowedNodeCategories.value)
const allowedThemes = computed(() => getAllowedThemes.value)

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  console.warn(`Failed to load image: ${target.src}`)

  // 隐藏图片并显示占位符
  target.style.display = 'none'

  // 可以在这里添加一个默认图标或占位符
  const parent = target.parentElement
  if (parent && !parent.querySelector('.image-placeholder')) {
    const placeholder = document.createElement('div')
    placeholder.className =
      'image-placeholder flex items-center justify-center w-8 h-8 bg-muted rounded text-muted-foreground text-xs'
    placeholder.textContent = '?'
    parent.appendChild(placeholder)
  }
}
</script>
