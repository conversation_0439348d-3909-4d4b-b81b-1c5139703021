import { screen } from 'electron'
import logger from '../utils/logger'
import { getMainWindow } from '../window/mainWindow'
/**
 * 窗口服务类
 * 封装与窗口控制相关的所有功能
 */
export class WindowService {
  /**
   * 最小化窗口
   */
  minimize(): boolean {
    const window = getMainWindow()
    if (window) {
      window.minimize()
      logger.info('Window minimized')
      return true
    }
    return false
  }

  /**
   * 最大化或还原窗口
   */
  toggleMaximize(): boolean {
    const window = getMainWindow()
    if (window) {
      if (window.isMaximized()) {
        window.unmaximize()
        logger.info('Window unmaximized')
      } else {
        window.maximize()
        logger.info('Window maximized')
      }
      return true
    }
    return false
  }

  /**
   * 关闭窗口
   */
  close(): boolean {
    const window = getMainWindow()
    if (window) {
      window.close()
      logger.info('Window closed')
      return true
    }
    return false
  }

  /**
   * 检查窗口是否最大化
   */
  isMaximized(): boolean {
    const window = getMainWindow()
    return window ? window.isMaximized() : false
  }

  /**
   * 设置登录窗口大小
   */
  setLoginSize(): boolean {
    const window = getMainWindow()
    if (window) {
      window.unmaximize() // 取消最大化状态

      const workAreaSize = screen.getPrimaryDisplay().workAreaSize
      const width = 1000
      const height = 650
      const x = Math.floor(workAreaSize.width / 2 - width / 2)
      const y = Math.floor(workAreaSize.height / 2 - height / 2)

      window.setBounds({ x, y, width, height })
      logger.info('Window resized for login')
      return true
    }
    return false
  }

  /**
   * 登录后最大化窗口
   */
  maximizeAfterLogin(): boolean {
    const window = getMainWindow()
    if (window) {
      window.maximize()
      logger.info('Window maximized after login')
      return true
    }
    return false
  }
  /**
   * 打开开发者工具
   */
  openDevTools(): boolean {
    const window = getMainWindow()
    if (window) {
      window.webContents.openDevTools()
      logger.info('Developer tools opened')
      return true
    }
    return false
  }

  /**
   * 切换开发者工具
   */
  toggleDevTools(): boolean {
    const window = getMainWindow()
    if (window) {
      if (window.webContents.isDevToolsOpened()) {
        window.webContents.closeDevTools()
        logger.info('Developer tools closed')
      } else {
        window.webContents.openDevTools()
        logger.info('Developer tools opened')
      }
      return true
    }
    return false
  }
}

// 导出单例实例
export const windowService = new WindowService()
