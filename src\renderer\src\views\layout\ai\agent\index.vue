<template>
  <ScrollArea class="h-screen">
    <div class="p-2 md:p-4 lg:p-6 h-full flex flex-col bg-background">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-2xl font-semibold text-foreground">流程模板</h1>
          <p class="text-foreground mt-1">创建、管理和运行专为新能源电池行业设计的流程模板。</p>
        </div>
        <Button @click="openCreateTemplateModal">
          <LucideIcon name="Plus" class="w-4 h-4 mr-2" />
          创建模板
        </Button>
      </div>

      <!-- 模板统计信息 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">活跃模板</CardTitle>
            <LucideIcon name="Activity" class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ activeTemplatesCount }}</div>
            <p class="text-xs text-muted-foreground">当前运行中的模板数量</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">模板总数</CardTitle>
            <LucideIcon name="Cpu" class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ templates.length }}</div>
            <p class="text-xs text-muted-foreground">已创建的模板总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">节点类型</CardTitle>
            <LucideIcon name="Workflow" class="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">{{ availableNodeTypesCount }}</div>
            <p class="text-xs text-muted-foreground">可用的节点类型数量</p>
          </CardContent>
        </Card>
      </div>

      <!-- 模板列表 -->
      <div class="flex-1 overflow-y-auto">
        <div class="p-1 pb-24">
          <div v-if="templates.length === 0" class="text-center text-gray-500 py-10">
            <p>暂无模板。点击"创建模板"开始。</p>
          </div>
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card
              v-for="template in templates"
              :key="template.id"
              class="template-card group relative"
            >
              <!-- 删除图标 -->
              <div
                class="absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-8 w-8 rounded-full hover:bg-destructive/10 hover:text-destructive"
                  @click.stop="confirmDeleteTemplate(template)"
                >
                  <LucideIcon name="Trash2" class="h-4 w-4" />
                </Button>
              </div>

              <CardHeader>
                <CardTitle class="flex items-center justify-between">
                  <span>{{ template.name }}</span>
                  <Badge
                    v-if="false"
                    :variant="
                      template.status === 'active'
                        ? 'default'
                        : template.status === 'running'
                          ? 'secondary'
                          : 'outline'
                    "
                  >
                    <span :class="['status-badge', templateStatusClass(template.status)]"></span>
                    {{ templateStatusText(template.status) }}
                  </Badge>
                </CardTitle>
                <CardDescription>{{ template.description }}</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="h-40 border rounded-md bg-gray-50 p-2 mb-3">
                  <VueFlow
                    v-model:nodes="template.nodes"
                    v-model:edges="template.edges"
                    :fit-view-on-init="true"
                    :nodes-draggable="false"
                    :nodes-connectable="false"
                    :edges-updatable="false"
                    class="basicflow"
                  >
                    <Background />
                    <MiniMap
                      :pannable="true"
                      :zoomable="true"
                      :position="'bottom-right'"
                      :width="50"
                      :height="40"
                    />
                    <Controls />
                  </VueFlow>
                </div>
                <div class="flex items-center justify-between text-sm text-muted-foreground">
                  <div>
                    <span class="font-medium">{{ template.nodes.length }}</span>
                    节点
                  </div>
                  <div>
                    <span class="font-medium">{{ template.edges.length }}</span>
                    连接
                  </div>
                  <div>创建于 {{ formatDate(template.createdAt) }}</div>
                </div>
              </CardContent>
              <CardFooter class="flex justify-end space-x-2">
                <Button variant="outline" size="sm" @click="openEditTemplateModal(template)">
                  <LucideIcon name="FilePenLine" class="w-4 h-4 mr-1" />
                  编辑模板
                </Button>
                <Button
                  size="sm"
                  :disabled="template.status === 'running'"
                  @click="runTemplate(template)"
                >
                  <LucideIcon name="Play" class="w-4 h-4 mr-1" />
                  添加到工作流
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>

      <!-- 模板编辑器 -->
      <TemplateEditor
        :open="isTemplateEditorOpen"
        :template="currentTemplate"
        @update:open="isTemplateEditorOpen = $event"
        @submit="handleTemplateSubmit"
      />

      <!-- 工作流选择对话框 -->
      <WorkflowSelectionDialog
        :open="isWorkflowSelectionOpen"
        :template="templateToAddToWorkflow"
        @update:open="isWorkflowSelectionOpen = $event"
        @added="handleWorkflowAdded"
      />

      <!-- 删除确认对话框 -->
      <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除模板</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除模板 "{{ templateToDelete?.name }}" 吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
            <AlertDialogAction
              class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              @click="deleteTemplate"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  </ScrollArea>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'
import { Controls } from '@vue-flow/controls'

import { useNodeModulesStore } from '@renderer/store'
import { useTemplatesStore, type Template } from '@renderer/store/modules/templates'
import TemplateEditor from './components/TemplateEditor.vue'
import WorkflowSelectionDialog from './components/WorkflowSelectionDialog.vue'
import { toast } from 'vue-sonner'
import { LucideIcon } from '@renderer/components'
import { useRouter } from 'vue-router'
import svg from '@renderer/config/constants/svg'

// 导入样式
import '@vue-flow/core/dist/style.css'
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/minimap/dist/style.css'

const nodeModulesStore = useNodeModulesStore()

const templatesStore = useTemplatesStore()

// 模板列表
const templates = computed(() => templatesStore.allTemplates)

// 模板编辑器状态
const isTemplateEditorOpen = ref(false)
const currentTemplate = ref<Template | null>(null)

// 统计信息
const activeTemplatesCount = computed(() => templatesStore.activeTemplatesCount)

const availableNodeTypesCount = computed(() => {
  const modules = nodeModulesStore.enabledNodeModules
  let count = 0
  Object.values(modules).forEach((module) => {
    module.categories.forEach((category) => {
      count += category.nodes.length
    })
  })
  return count
})

// 状态样式和文本
const templateStatusClass = (status: Template['status']) => {
  if (status === 'active') return 'status-active'
  if (status === 'running') return 'status-running'
  if (status === 'idle') return 'status-idle'
  return 'bg-red-500' // Error status
}

const templateStatusText = (status: Template['status']) => {
  const map = {
    active: '活跃',
    running: '运行中',
    idle: '空闲',
    error: '错误',
  }
  return map[status] || '未知'
}

// 打开创建模板对话框
const openCreateTemplateModal = () => {
  currentTemplate.value = null
  isTemplateEditorOpen.value = true
}

// 打开编辑模板对话框
const openEditTemplateModal = (template: Template) => {
  currentTemplate.value = template
  isTemplateEditorOpen.value = true
}

// 处理模板提交
const handleTemplateSubmit = (templateData: any) => {
  if (currentTemplate.value) {
    // 编辑现有模板
    const updatedTemplate = templatesStore.updateTemplate(templateData.id, {
      name: templateData.name,
      description: templateData.description,
      nodes: templateData.nodes,
      edges: templateData.edges,
    })

    if (updatedTemplate) {
      toast.success('更新成功', {
        description: `模板 "${templateData.name}" 已更新`,
      })
    }
  } else {
    // 创建新模板
    const newTemplate = templatesStore.createTemplate({
      name: templateData.name,
      description: templateData.description,
      nodes: templateData.nodes,
      edges: templateData.edges,
    })

    toast.success('创建成功', {
      description: `模板 "${newTemplate.name}" 已创建`,
    })
  }
}

// 工作流选择对话框状态
const isWorkflowSelectionOpen = ref(false)
const templateToAddToWorkflow = ref<Template | null>(null)
const router = useRouter()

// 运行模板
const runTemplate = (template: Template) => {
  // 打开工作流选择对话框
  templateToAddToWorkflow.value = template
  isWorkflowSelectionOpen.value = true
}

// 处理工作流添加完成
const handleWorkflowAdded = (workflowId: string) => {
  // 可以在这里处理添加完成后的逻辑，比如跳转到工作流页面
  router.push(`/workflow?id=${workflowId}`)
}

// 格式化日期
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

// 删除模板
const isDeleteDialogOpen = ref(false)
const templateToDelete = ref<Template | null>(null)

const confirmDeleteTemplate = (template: Template) => {
  templateToDelete.value = template
  isDeleteDialogOpen.value = true
}

const deleteTemplate = () => {
  if (templateToDelete.value) {
    templatesStore.deleteTemplate(templateToDelete.value.id)
    toast.success('删除成功', {
      description: `模板 "${templateToDelete.value.name}" 已删除`,
    })
    isDeleteDialogOpen.value = false
  }
}

onMounted(() => {
  // 如果没有模板数据，可以添加一些示例模板
  if (templatesStore.templates.length === 0) {
    // 创建示例模板
    const exampleTemplate = {
      name: '豪鹏电池寿命预测模板',
      description: '基于豪鹏电芯数据进行模型标定和寿命预测的流程模板',
      nodes: [
        {
          id: 'node-1',
          type: 'custom',
          position: { x: 100, y: 150 },
          data: {
            label: '豪鹏电芯数据输入',
            type: 'HighpowerCellDataInput',
            icon: { type: 'svg', value: svg.batteryDataInput },
            nodeType: 'Basic',
            inputType: [],
            outputType: ['HighpowerBatteryModelCalibration'],
          },
        },
        {
          id: 'node-2',
          type: 'custom',
          position: { x: 350, y: 150 },
          data: {
            label: '豪鹏电池模型标定',
            type: 'HighpowerBatteryModelCalibration',
            icon: { type: 'svg', value: svg.batteryCalibration },
            nodeType: 'Compute',
            inputType: ['HighpowerCellDataInput'],
            outputType: ['HighpowerBatteryModel'],
          },
        },
        {
          id: 'node-3',
          type: 'custom',
          position: { x: 600, y: 150 },
          data: {
            label: '豪鹏电池模型',
            type: 'HighpowerBatteryModel',
            icon: { type: 'svg', value: svg.batteryModel },
            nodeType: 'Basic',
            inputType: ['HighpowerBatteryModelCalibration'],
            outputType: ['HighpowerCellLifePrediction'],
          },
        },
        {
          id: 'node-4',
          type: 'custom',
          position: { x: 850, y: 150 },
          data: {
            label: '豪鹏电芯寿命预测',
            type: 'HighpowerCellLifePrediction',
            icon: { type: 'svg', value: svg.lifePrediction },
            nodeType: 'Compute',
            inputType: ['DataEntry', 'HighpowerBatteryModel'],
            outputType: [],
          },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          source: 'node-1',
          target: 'node-2',
          animated: true,
          markerEnd: { type: 'arrowclosed' },
        },
        {
          id: 'edge-2',
          source: 'node-2',
          target: 'node-3',
          animated: true,
          markerEnd: { type: 'arrowclosed' },
        },
        {
          id: 'edge-3',
          source: 'node-3',
          target: 'node-4',
          animated: true,
          markerEnd: { type: 'arrowclosed' },
        },
      ],
    }

    templatesStore.createTemplate(exampleTemplate)
  }
})
</script>

<style lang="scss" scoped>
.template-card {
  @apply transition-all duration-200 ease-in-out hover:shadow-lg hover:-translate-y-1;
}

.status-badge {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.status-active {
  background-color: #10b981; /* green-500 */
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-running {
  background-color: #3b82f6; /* blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.status-idle {
  background-color: #9ca3af; /* gray-400 */
  box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.2);
}

.basicflow :deep(.vue-flow__node) {
  @apply bg-white border-gray-300 rounded-md shadow-sm text-sm p-2;
}

.basicflow :deep(.vue-flow__node.vue-flow__node-input) {
  @apply bg-blue-50 border-blue-500;
}

.basicflow :deep(.vue-flow__node.vue-flow__node-output) {
  @apply bg-green-50 border-green-500;
}

.basicflow :deep(.vue-flow__edge-path) {
  @apply stroke-gray-400;
}

.basicflow :deep(.vue-flow__controls) {
  @apply shadow-md;
}

.basicflow :deep(.vue-flow__minimap) {
  @apply shadow-md;
}
</style>
