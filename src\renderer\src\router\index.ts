import Layout from '@renderer/views/layout/index.vue'
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { authGuard } from './guard'
// 静态路由配置
export const routes: RouteRecordRaw[] = [
  {
    path: '/auth',
    name: 'auth',
    component: () => import('@renderer/views/layout/auth/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/',
    component: Layout,
    redirect: '/workflow',
    children: [
      {
        name: 'workflow',
        path: '/workflow',
        component: () => import('@renderer/views/workflow/index.vue'),
        meta: {
          title: 'workflows.title',
          icon: 'flowbite:draw-square-outline',
          showTopLine: false,
          showInMenu: true,
          sort: 1,
          tooltip: {
            content: 'workflows.tooltip',
            placement: 'right',
            theme: 'light',
          },
        },
        children: [
          {
            name: 'workflow-editor',
            path: 'editor/:id', // 注意这里改为相对路径
            component: () => import('@renderer/views/workflow/components/Editor/index.vue'),
            meta: {
              title: '工作流编辑',
              icon: 'flowbite:draw-square-outline',
              showInMenu: false,
            },
          },
        ],
      },
      // },
      {
        name: 'task',
        path: '/task',
        component: () => import('@renderer/views/layout/task/index.vue'),
        meta: {
          title: 'tasks.title',
          icon: 'flowbite:clock-outline',
          showTopLine: false,
          showInMenu: true,
          sort: 2,
          tooltip: {
            content: 'tasks.tooltip',
            placement: 'right',
            theme: 'light',
          },
        },
      },
      {
        name: 'server',
        path: '/server',
        component: () => import('@renderer/views/layout/server/index.vue'),
        meta: {
          title: 'server.title',
          icon: 'flowbite:database-outline',
          showTopLine: false,
          showInMenu: true,
          sort: 3,
          tooltip: {
            content: 'server.tooltip',
            placement: 'right',
            theme: 'light',
          },
        },
      },
      {
        name: 'logger',
        path: '/logger',
        component: () => import('@renderer/views/layout/logger/index.vue'),
        meta: {
          title: 'logger.title',
          icon: 'flowbite:clock-arrow-outline',
          showTopLine: false,
          showInMenu: true,
          sort: 4,
          tooltip: {
            content: 'logger.tooltip',
            placement: 'right',
            theme: 'light',
          },
        },
      },
      {
        name: 'tools',
        path: '/tools',
        component: () => import('@renderer/views/layout/tools/index.vue'),
        meta: {
          title: 'tools.title',
          icon: 'flowbite:tools-outline',
          showTopLine: false,
          showInMenu: true,
          sort: 4,
          tooltip: {
            content: 'tools.tooltip',
            placement: 'right',
            theme: 'light',
          },
        },
      },
      {
        name: 'setting',
        path: '/setting',
        component: () => import('@renderer/views/layout/setting/index.vue'),
        meta: {
          title: 'settings.title',
          icon: 'flowbite:cog-outline',
          showTopLine: true,
          showInMenu: true,
          sort: 5,
          tooltip: {
            content: 'settings.tooltip',
            placement: 'right',
            theme: 'light',
          },
        },
      },
      {
        name: 'ai',
        path: '/ai',
        component: () => import('@renderer/views/layout/ai/index.vue'),
        meta: {
          title: 'AI',
          icon: 'flowbite:message-dots-outline',
          showTopLine: false,
          isBottomMenu: true,
          showInMenu: true,
          sort: 6,
          tooltip: {
            content: 'matt AI',
            placement: 'right',
            theme: 'light',
          },
        },
        children: [
          {
            name: 'chat',
            path: 'chat',
            component: () => import('@renderer/views/layout/ai/chat/index.vue'),
            meta: {
              title: 'Chat',
              icon: 'flowbite:message-dots-outline',
              showTopLine: false,
              isBottomMenu: false,
              showInMenu: false,
              sort: 1,
            },
          },
          {
            name: 'agent',
            path: 'agent',
            component: () => import('@renderer/views/layout/ai/agent/index.vue'),
            meta: {
              title: 'agent',
              icon: 'flowbite:message-dots-outline',
              showTopLine: false,
              isBottomMenu: false,
              showInMenu: false,
              sort: 1,
            },
          },
          {
            name: 'knowledge',
            path: 'knowledge',
            component: () => import('@renderer/views/layout/ai/knowledge/index.vue'),
            meta: {
              title: 'knowledge',
              icon: 'flowbite:message-dots-outline',
              showTopLine: false,
              isBottomMenu: false,
              showInMenu: false,
              sort: 1,
            },
          },
          {
            name: 'aiSetting',
            path: 'aiSetting',
            component: () => import('@renderer/views/layout/ai/setting/index.vue'),
            meta: {
              title: 'aiSetting',
              icon: 'flowbite:message-dots-outline',
              showTopLine: false,
              isBottomMenu: false,
              showInMenu: false,
              sort: 1,
            },
          },
        ],
      },
      {
        name: 'user',
        path: '/user',
        component: () => import('@renderer/views/layout/user/index.vue'),
        meta: {
          title: '用户',
          icon: 'flowbite:user-circle-outline',
          showTopLine: false,
          isBottomMenu: true,
          showInMenu: true,
          sort: 6,
          tooltip: {
            content: '用户',
            placement: 'right',
            theme: 'light',
          },
        },
      },
      {
        name: 'about',
        path: '/about',
        component: () => import('@renderer/views/layout/about/index.vue'),
        meta: {
          title: '关于',
          icon: 'flowbite:exclamation-circle-outline',
          showTopLine: false,
          isBottomMenu: true,
          showInMenu: true,
          sort: 7,
          tooltip: {
            content: '关于',
            placement: 'right',
            theme: 'light',
          },
        },
      },
    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})
// 应用全局守卫
router.beforeEach(authGuard)
export default router
