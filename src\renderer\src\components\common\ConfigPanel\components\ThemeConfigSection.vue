<template>
  <div class="space-y-4">
    <!-- 主题配置 -->
    <ConfigGroup title="主题设置" icon="Palette">
      <ConfigItem
        label="主题切换"
        :enabled="themeConfig.enableThemeSwitch"
        description="允许用户切换主题"
      />
      <ConfigItem
        label="暗色模式"
        :enabled="themeConfig.enableDarkMode"
        description="启用暗色模式支持"
      />

      <div class="mt-3 p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
        <div class="text-xs text-indigo-700 dark:text-indigo-300 mb-2">默认主题</div>
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
          {{ getThemeDisplayName(themeConfig.defaultTheme) }}
        </div>
      </div>
    </ConfigGroup>

    <!-- 可用主题列表 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400"
          >
            <Palette class="w-3.5 h-3.5" />
          </div>
          可用主题
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <Card
          v-for="theme in themeConfig.allowedThemes"
          :key="theme"
          :class="[
            'transition-all duration-200 hover:shadow-sm',
            theme === themeConfig.defaultTheme ? 'border-primary bg-primary/5' : 'border-muted/50',
          ]"
        >
          <CardContent class="p-3">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <!-- 主题颜色预览 -->
                <div class="flex space-x-1">
                  <div
                    v-for="color in getThemeColors(theme)"
                    :key="color"
                    :style="{ backgroundColor: color }"
                    class="w-3 h-3 rounded-full border border-muted shadow-sm"
                  ></div>
                </div>

                <div>
                  <div class="text-sm font-medium">
                    {{ getThemeDisplayName(theme) }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    {{ getThemeDescription(theme) }}
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <!-- 默认主题标识 -->
                <Badge v-if="theme === themeConfig.defaultTheme" variant="default" class="text-xs">
                  默认
                </Badge>

                <!-- 主题类型标识 -->
                <Badge :variant="theme.includes('dark') ? 'secondary' : 'outline'" class="text-xs">
                  {{ theme.includes('dark') ? '暗色' : '浅色' }}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>

    <!-- 工具配置 -->
    <ConfigGroup title="工具面板" icon="Wrench">
      <ConfigItem
        label="工具面板"
        :enabled="toolsConfig.showToolsPanel"
        description="显示工具面板"
      />

      <div
        v-if="toolsConfig.enabledTools.length > 0"
        class="mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
      >
        <div class="text-xs text-orange-700 dark:text-orange-300 mb-2">启用的工具</div>
        <div class="flex flex-wrap gap-1">
          <span
            v-for="tool in toolsConfig.enabledTools"
            :key="tool"
            class="px-2 py-1 text-xs bg-orange-100 dark:bg-orange-800 text-orange-700 dark:text-orange-300 rounded"
          >
            {{ getToolDisplayName(tool) }}
          </span>
        </div>
      </div>

      <div v-else class="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
          当前配置未启用任何工具
        </div>
      </div>
    </ConfigGroup>

    <!-- 主题预览 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-pink-100 text-pink-600 dark:bg-pink-900/20 dark:text-pink-400"
          >
            <Eye class="w-3.5 h-3.5" />
          </div>
          主题预览
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 当前主题预览 -->
        <Card class="border-muted/50">
          <CardContent class="p-3">
            <div class="flex items-center justify-between mb-3">
              <Label class="text-sm font-medium">当前主题效果</Label>
              <Badge variant="outline" class="text-xs">
                {{ getThemeDisplayName(themeConfig.defaultTheme) }}
              </Badge>
            </div>
            <div class="flex space-x-2">
              <div class="flex-1 h-3 bg-blue-500 rounded-full"></div>
              <div class="flex-1 h-3 bg-green-500 rounded-full"></div>
              <div class="flex-1 h-3 bg-purple-500 rounded-full"></div>
              <div class="flex-1 h-3 bg-orange-500 rounded-full"></div>
            </div>
          </CardContent>
        </Card>

        <Separator />

        <!-- 主题统计 -->
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center space-y-1">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ themeConfig.allowedThemes.length }}
            </div>
            <Label class="text-xs text-muted-foreground">可用主题</Label>
          </div>
          <div class="text-center space-y-1">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
              {{ darkThemeCount }}
            </div>
            <Label class="text-xs text-muted-foreground">暗色主题</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { Label } from '@renderer/components/ui/label'
import { Separator } from '@renderer/components/ui/separator'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { Eye, Palette } from 'lucide-vue-next'
import { computed } from 'vue'
import ConfigGroup from './ConfigGroup.vue'
import ConfigItem from './ConfigItem.vue'

const { themeConfig, toolsConfig } = useAppConfig()

// 暗色主题数量
const darkThemeCount = computed(() => {
  return themeConfig.value.allowedThemes.filter((theme) => theme.includes('dark')).length
})

// 主题显示名称映射
const getThemeDisplayName = (theme: string): string => {
  const themeMap: Record<string, string> = {
    'city-light': '城市浅色',
    'city-dark': '城市暗色',
    'forest-light': '森林浅色',
    'forest-dark': '森林暗色',
    'lake-light': '湖泊浅色',
    'lake-dark': '湖泊暗色',
  }
  return themeMap[theme] || theme
}

// 主题描述
const getThemeDescription = (theme: string): string => {
  const descriptionMap: Record<string, string> = {
    'city-light': '现代都市风格，简洁明亮',
    'city-dark': '现代都市风格，深色护眼',
    'forest-light': '自然森林风格，清新舒适',
    'forest-dark': '自然森林风格，深邃宁静',
    'lake-light': '湖泊风格，宁静优雅',
    'lake-dark': '湖泊风格，深邃神秘',
  }
  return descriptionMap[theme] || '自定义主题'
}

// 主题颜色预览
const getThemeColors = (theme: string): string[] => {
  const colorMap: Record<string, string[]> = {
    'city-light': ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
    'city-dark': ['#1e40af', '#059669', '#d97706', '#dc2626'],
    'forest-light': ['#22c55e', '#84cc16', '#eab308', '#f97316'],
    'forest-dark': ['#16a34a', '#65a30d', '#ca8a04', '#ea580c'],
    'lake-light': ['#06b6d4', '#0ea5e9', '#3b82f6', '#8b5cf6'],
    'lake-dark': ['#0891b2', '#0284c7', '#2563eb', '#7c3aed'],
  }
  return colorMap[theme] || ['#6b7280', '#9ca3af', '#d1d5db', '#e5e7eb']
}

// 工具显示名称映射
const getToolDisplayName = (tool: string): string => {
  const toolMap: Record<string, string> = {
    calculator: '计算器',
    converter: '转换器',
    analyzer: '分析器',
    'custom-tool': '自定义工具',
  }
  return toolMap[tool] || tool
}
</script>
