<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent
      :class="['overflow-hidden', currentDetailComponent?.dialogSize || 'sm:max-w-[500px]']"
    >
      <DialogHeader v-if="showHeader">
        <DialogTitle>{{ `节点设置 - ${nodeData?.data?.label}` }}</DialogTitle>
        <DialogDescription v-if="nodeData?.data?.description">
          {{ nodeData.data.description }}
        </DialogDescription>
      </DialogHeader>

      <div class="dialog-content">
        <component
          :is="currentDetailComponent?.component"
          v-if="currentDetailComponent"
          :node-data="nodeData"
          @update:node-data="handleNodeDataUpdate"
          @save="handleSave"
          @close="handleClose"
          @custom-action="handleCustomAction"
        />
      </div>

      <!-- <DialogFooter v-if="showFooter">
        <Button variant="outline" @click="handleClose">取消</Button>
        <Button @click="handleSave">确认</Button>
      </DialogFooter> -->
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import nodeDetailComponents from '@renderer/views/workflow/nodeDetailComponents'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  nodeData: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:isOpen', 'save'])

// 根据节点类型获取对应的详情组件配置
const currentDetailComponent = computed(() => {
  if (!props.nodeData?.data?.type) return null
  const componentName = `batterySimulationDetails-${props.nodeData.data.type}`
  const component = nodeDetailComponents[componentName]

  if (!component) return null

  // 返回组件配置
  return {
    component,
    dialogSize: component.dialogSize || 'sm:max-w-[500px]',
    showHeader: component.showHeader !== false,
    showFooter: component.showFooter !== false,
  }
})

// 是否显示头部
const showHeader = computed(() => currentDetailComponent.value?.showHeader !== false)

// 是否显示底部
const showFooter = computed(() => currentDetailComponent.value?.showFooter !== false)

const handleOpenChange = (open: boolean) => {
  emit('update:isOpen', open)
}

const handleClose = () => {
  emit('update:isOpen', false)
}

const handleNodeDataUpdate = (newData: any) => {
  console.log('Node data updated:', newData)
}

const handleSave = () => {
  emit('save', props.nodeData)
  handleClose()
}
const handleCustomAction = (action: string, data?: any) => {
  console.log('Custom action:', action, data)
}

// 本地状态
const localNodeData = ref(props.nodeData)

// 监听 props 变化
watch(
  () => props.nodeData,
  (newValue) => {
    localNodeData.value = { ...newValue }
  },
  { deep: true, immediate: true },
)
</script>

<style lang="scss" scoped>
.dialog-content {
  @apply py-4;
}
</style>
