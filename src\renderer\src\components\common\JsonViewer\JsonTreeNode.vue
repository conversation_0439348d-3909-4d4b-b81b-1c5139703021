<template>
  <div class="json-tree-node" :class="{ 'search-match': isMatch }">
    <div
      class="node-content"
      :class="{
        'cursor-pointer': true,
        'selected-node': isSelected,
      }"
      @click.stop="handleNodeClick"
    >
      <span v-if="isExpandable" class="toggle-icon mr-1">
        <ChevronRight v-if="!isExpanded" class="h-3 w-3 transition-transform" />
        <ChevronDown v-else class="h-3 w-3 transition-transform" />
      </span>

      <span v-if="keyName" class="key-name">
        <span :class="{ 'search-highlight': keyNameMatch }">{{ keyName }}</span>
        :
      </span>

      <span class="value" :class="getValueClass(data)">
        <template v-if="isExpandable">
          {{ getCollapsedPreview(data) }}
        </template>
        <template v-else>
          {{ getFormattedValue(data) }}
        </template>
      </span>
    </div>

    <div v-if="isExpanded" class="children ml-4">
      <template v-if="isArray">
        <div v-for="(item, index) in data" :key="index" class="array-item">
          <JsonTreeNode
            :data="item"
            :level="level + 1"
            :path="`${path}[${index}]`"
            :key-name="index.toString()"
            :expanded-paths="expandedPaths"
            :search-text="searchText"
            :selected-path="selectedPath"
            @toggle="handleToggle"
            @select="handleSelect"
          />
        </div>
      </template>

      <template v-else-if="isObject">
        <div v-for="key in sortedKeys" :key="key" class="object-property">
          <JsonTreeNode
            :data="data[key]"
            :level="level + 1"
            :path="`${path}.${key}`"
            :key-name="key"
            :expanded-paths="expandedPaths"
            :search-text="searchText"
            :selected-path="selectedPath"
            @toggle="handleToggle"
            @select="handleSelect"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChevronRight, ChevronDown } from 'lucide-vue-next'

interface Props {
  data: any
  level: number
  path: string
  keyName?: string
  expandedPaths: Set<string>
  searchText: string
  selectedPath: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'toggle', path: string): void
  (e: 'select', path: string, data: any): void
}>()

// 计算属性
const isObject = computed(
  () => props.data !== null && typeof props.data === 'object' && !Array.isArray(props.data),
)
const isArray = computed(() => Array.isArray(props.data))
const isExpandable = computed(() => isObject.value || isArray.value)
const isExpanded = computed(() => props.expandedPaths.has(props.path))
const isSelected = computed(() => props.selectedPath === props.path)

// 对象的键按字母顺序排序
const sortedKeys = computed(() => {
  if (!isObject.value) return []
  return Object.keys(props.data).sort()
})

// 搜索匹配
const keyNameMatch = computed(() => {
  if (!props.searchText || !props.keyName) return false
  return props.keyName.toLowerCase().includes(props.searchText.toLowerCase())
})

const isMatch = computed(() => {
  if (!props.searchText) return false

  // 检查键名是否匹配
  if (keyNameMatch.value) return true

  // 检查值是否匹配（对于简单类型）
  if (!isExpandable.value) {
    const valueStr = String(props.data)
    return valueStr.toLowerCase().includes(props.searchText.toLowerCase())
  }

  return false
})

// 处理节点点击
const handleNodeClick = (event: MouseEvent) => {
  event.stopPropagation()

  // 选择当前节点
  emit('select', props.path, props.data)

  // 如果是可展开的节点，切换展开状态
  if (isExpandable.value) {
    emit('toggle', props.path)
  }
}

// 处理子节点的 toggle 事件
const handleToggle = (path: string) => {
  emit('toggle', path)
}

// 处理子节点的 select 事件
const handleSelect = (path: string, data: any) => {
  emit('select', path, data)
}

// 获取值的类型样式
const getValueClass = (value: any) => {
  if (value === null) return 'null-value'
  if (typeof value === 'string') return 'string-value'
  if (typeof value === 'number') return 'number-value'
  if (typeof value === 'boolean') return 'boolean-value'
  if (Array.isArray(value)) return 'array-value'
  if (typeof value === 'object') return 'object-value'
  return ''
}

// 获取格式化的值
const getFormattedValue = (value: any) => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'string') return `"${value}"`
  return String(value)
}

// 获取折叠状态下的预览
const getCollapsedPreview = (value: any) => {
  if (Array.isArray(value)) {
    return `Array(${value.length})`
  }
  if (typeof value === 'object' && value !== null) {
    const keys = Object.keys(value)
    return `Object{${keys.length}}`
  }
  return ''
}
</script>

<style scoped>
.json-tree-node {
  @apply py-0.5;
}

.node-content {
  @apply flex items-center rounded px-1 hover:bg-gray-100 dark:hover:bg-gray-800/50;
}

.selected-node {
  @apply bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-800/30;
}

.key-name {
  @apply text-blue-600 dark:text-blue-400 mr-1;
}

.string-value {
  @apply text-green-600 dark:text-green-400;
}

.number-value {
  @apply text-orange-600 dark:text-orange-400;
}

.boolean-value {
  @apply text-purple-600 dark:text-purple-400;
}

.null-value {
  @apply text-gray-500 dark:text-gray-400;
}

.array-value,
.object-value {
  @apply text-gray-600 dark:text-gray-300;
}

.search-match {
  @apply bg-yellow-100 dark:bg-yellow-900/30;
}

.search-highlight {
  @apply bg-yellow-200 dark:bg-yellow-800/50 px-0.5 rounded;
}

.toggle-icon {
  @apply text-gray-500 dark:text-gray-400;
}
</style>
