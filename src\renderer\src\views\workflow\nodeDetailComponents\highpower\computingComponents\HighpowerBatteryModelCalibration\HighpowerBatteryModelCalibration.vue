<template>
  <div class="relative">
    <!-- 加载状态覆盖层 -->
    <!-- <div
      v-if="isProcessing"
      class="absolute inset-0 bg-black/30 backdrop-blur-sm z-50 flex flex-col items-center justify-center rounded-lg"
    >
      <Loader2 class="h-8 w-8 animate-spin text-primary mb-2" />
      <p class="text-white font-medium">
        {{ currentStep === 0 ? '电化学参数标定中...' : '老化参数标定中...' }}
      </p>
      <p class="text-white/80 text-sm mt-1">请耐心等待，任务完成后将自动更新</p>
    </div> -->
    <Card class="p-2">
      <CardHeader>
        <div class="flex flex-col space-y-2 sm:flex-row sm:justify-between sm:space-y-0">
          <CardTitle class="text-xl sm:text-2xl font-bold">电池模型标定</CardTitle>
          <!-- 操作按钮 -->
          <div class="flex flex-wrap gap-1">
            <Button
              size="xs"
              class="flex items-center relative"
              :disabled="isProcessing"
              @click="openSubmitDialog"
            >
              <Check v-if="!isProcessing" class="w-4 h-4 mr-1" />
              <span v-if="isProcessing" class="animate-spin mr-1">
                <RefreshCw class="h-4 w-4" />
              </span>
              <span class="whitespace-nowrap">
                {{ currentStep === 0 ? '标定电化学参数' : '标定老化参数' }}
              </span>
              <span
                v-if="totalSelectedCount > 0"
                class="absolute -top-3 -right-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5"
              >
                {{ currentStep === 0 ? selectedElecCount : selectedAgingCount }}
              </span>
            </Button>
            <Button
              v-if="!isProcessing && agingResult"
              size="xs"
              class="dark:text-muted-foreground dark:bg-muted-foreground"
              @click="handleExport"
            >
              <FileUp class="w-4 h-4 mr-1" />
              <span class="hidden sm:inline">导出结果</span>
              <span class="sm:hidden">导出</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="outline" size="xs" class="p-1">
                  <EllipsisVertical class="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem class="hover:bg-orange-50" @click="handlePause">
                  <CirclePause class="w-4 h-4 mr-2 text-orange-500" />
                  <span class="text-orange-600 font-medium">暂停</span>
                </DropdownMenuItem>
                <DropdownMenuItem class="hover:bg-red-50" @click="handleStop">
                  <Ban class="w-4 h-4 mr-2 text-red-500" />
                  <span class="text-red-600 font-medium">终止</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  :disabled="isProcessing"
                  class="hover:bg-blue-50"
                  @click="resetSelection"
                >
                  <RefreshCw class="w-4 h-4 mr-2 text-blue-500" />
                  <span class="text-blue-600 font-medium">重置</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <!-- 进度条显示 -->
        <div
          v-if="isProcessing"
          class="mb-4 space-y-2 bg-blue-50 p-3 rounded-lg border border-blue-200"
        >
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="animate-spin mr-2">
                <RefreshCw class="h-5 w-5 text-blue-500" />
              </span>
              <span class="font-medium text-blue-700">
                <span class="flex items-center justify-center">
                  {{ currentStep === 0 ? '电化学参数标定中' : '老化参数标定中' }}
                  <span class="inline-block w-3 overflow-hidden animate-ellipsis">...</span>
                </span>
              </span>
            </div>
            <span class="text-blue-600">处理中 {{ taskProgress && taskProgress.toFixed(2) }}%</span>
          </div>
          <Progress v-model="taskProgress" class="w-full h-2" />
          <div class="flex items-center justify-between cursor-pointer">
            <p class="text-xs text-blue-600 mr-2">请耐心等待，任务完成后将自动更新</p>
            <!-- <Badge size="xs" class="cursor-pointer" @click="openLogViewer">
              <FileText class="w-4 h-4 text-green-400" />
              <span class="text-green-400">详细日志</span>
            </Badge> -->
          </div>
        </div>
        <!-- 步骤条 -->
        <CalibrationStepper
          v-model:current-step="currentStep"
          :steps="steps"
          :elec-params-completed="elecParamsCompleted"
          :aging-params-completed="agingParamsCompleted"
          :is-processing="isProcessing"
          :elec-task-id="elecTaskId"
          :aging-task-id="agingTaskId"
          @prev-step="prevStep"
          @next-step="nextStep"
        />

        <!-- 状态提示 -->
        <AlertNotification
          :current-step="currentStep"
          :elec-params-completed="elecParamsCompleted"
          :aging-params-completed="agingParamsCompleted"
          :selected-elec-count="selectedElecCount"
          :selected-aging-count="selectedAgingCount"
          class="mt-4"
        />

        <!-- 基本参数配置 -->
        <BasicSettings
          :basic-params="basicParams"
          :is-processing="isProcessing"
          :show-aging-options="currentStep === 1"
          :battery-types="batteryTypes"
          :charge-types="charges"
          @update:basic-params="updateBasicParams"
        />

        <!-- 电化学参数组件 -->
        <ElectrochemicalParams
          v-if="currentStep === 0"
          :elec-params="elecParams"
          :elec-params-completed="elecParamsCompleted"
          :algorithm-params="algorithmParams"
          :elec-open-states="elecOpenStates"
          :is-processing="isProcessing"
          :task-id="elecTaskId"
          :completed="elecTaskCompleted"
          :server-info="lastServerInfo"
          :total-samples="totalSamples"
          :result-data="elecResultData"
          :task-status="elecTaskStatus"
          :task-duration="elecTaskDuration"
          @toggle-params="toggleParams"
          @update-param-selection="updateParamSelection"
          @use-recommended-params="useRecommendedParams"
          @update:algorithm-params="updateAlgorithmParams"
          @update:elec-open-states="updateElecOpenStates"
          @get-params="handleGetParams"
          @update:elec-params="handleElecParamsUpdate"
        />
        <!-- 老化参数组件 -->
        <AgingParams
          v-if="currentStep === 1"
          :aging-params="agingParams"
          :elec-params-completed="elecParamsCompleted"
          :aging-params-completed="agingParamsCompleted"
          :aging-open-states="agingOpenStates"
          :algorithm-params="algorithmParams"
          :is-processing="isProcessing"
          :task-id="agingTaskId"
          :completed="agingTaskCompleted"
          :server-info="lastServerInfo"
          :total-samples="totalSamples"
          :result-data="agingResultData"
          :task-status="agingTaskStatus"
          :task-duration="agingTaskDuration"
          @toggle-params="toggleParams"
          @update-param-selection="updateParamSelection"
          @use-recommended-params="useRecommendedParams"
          @update:aging-open-states="updateAgingOpenStates"
          @update:algorithm-params="updateAlgorithmParams"
          @get-params="handleGetParams"
          @update:aging-params="handleAgingParamsUpdate"
        />
      </CardContent>
    </Card>

    <!-- 提交任务对话框 -->
    <SubmitTaskDialog
      v-model:is-open="showSubmitDialog"
      service-name="parameterIdentification"
      @submit="handleSubmit"
    />

    <!-- 日志查看器 -->
    <!-- <LogViewer
      :is-open="showLogViewer"
      :task-id="currentStep === 0 ? elecTaskId : agingTaskId"
      :server-id="lastServerInfo.server_id"
      :total-samples="algorithmParams.nums_per_iter"
      @update:is-open="showLogViewer = $event"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { SubmitTaskDialog } from '@renderer/components'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { useFlowsStore, useTaskStore, useWorkflowStore } from '@renderer/store'
import {
  getNodeParams,
  saveNodeParams,
  updateNodeData as updateNodeDataUtil,
  getInputNodesByType,
} from '@renderer/utils/nodeUtils'
import { inputParse } from '@renderer/utils/rpcParser'
import { encryptAndSaveFile } from '@renderer/utils/utils'
import {
  Ban,
  Battery,
  Check,
  CirclePause,
  EllipsisVertical,
  FileUp,
  RefreshCw,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import {
  AgingParams,
  AlertNotification,
  BasicSettings,
  CalibrationStepper,
  ElectrochemicalParams,
} from './components'

// 类型定义
interface ParamItem {
  param_key: string
  param_name: string
  min: number
  max: number
  init_value: number
  value?: number
  is_recommended: boolean
  selected: boolean
}

interface ParamsGroup {
  [category: string]: ParamItem[]
}

interface AlgorithmParams {
  max_iter: number
  max_rmse: number
  nums_per_iter: number
  task_timeout: number
  max_workers: number
}

interface TaskResult {
  params: Record<string, any>
  rmse: number
  generation: number
}

interface NodeData {
  id: string
  data: {
    workflowId: string
    type: string
    [key: string]: any
  }
}

const props = defineProps<{
  nodeData: NodeData
}>()

// Store 实例
const flowsStore = useFlowsStore()
const taskStore = useTaskStore()
const workflowStore = useWorkflowStore()
const taskService = createTaskService()

// ==================== 响应式状态 ====================
// 当前参数类型
const currentParamType = ref<'All' | 'Elec' | 'Aging'>('All')

// 步骤设置
const currentStep = ref(0)
const steps = [
  { title: '电化学参数', description: '', icon: Zap },
  { title: '老化参数', description: '', icon: Battery },
]

// 状态管理
const elecParamsCompleted = ref(false)
const agingParamsCompleted = ref(false)
const isProcessing = ref(false)
const showSubmitDialog = ref(false)
const isSubmitting = ref(false)
const showLogViewer = ref(false) // 控制日志查看器显示
const lastServerInfo = ref({ server_id: '', service_name: '' }) // 保存最后的服务器信息

// 电池体系选项
const batteryTypes = [{ value: 'LiFePO4', label: '磷酸铁锂-石墨体系' }]
const charges = [{ value: 'CP', label: '恒功率充电' }]

// 基本参数
const basicParams = ref({
  batteryType: 'LiFePO4',
  chargeType: 'CP',
  chargeValue: 896,
})

// 算法参数
const algorithmParams = ref<AlgorithmParams>({
  max_iter: 300,
  max_rmse: 0.03, //测试用0.030基本直接收敛拿到结果，0.025可以测试进度
  nums_per_iter: 200, //每代样本数
  task_timeout: 100, //单任务超时时间
  max_workers: 50, //最大并行任务数
})

const getDefaultAlgorithmParams = (step) => ({
  max_iter: 300,
  max_rmse: step === 0 ? 0.02 : 1,
  nums_per_iter: step === 0 ? 200 : 100,
  task_timeout: step === 0 ? 60 : 1500, // 老化参数： 900 -> 1500
  max_workers: 50,
})

// 参数数据
const allParams = ref<Record<string, ParamsGroup>>({})
const elecParams = ref<ParamsGroup>({})
const agingParams = ref<ParamsGroup>({})

// 任务相关状态
const elecTaskId = ref('')
const agingTaskId = ref('')
const elecResult = ref<Record<string, any> | null>(null)
const agingResult = ref<Record<string, any> | null>(null)

// 展开状态
const elecOpenStates = ref<boolean[]>([])
const agingOpenStates = ref<boolean[]>([])

// RMSE 记录
const lastElecRmse = ref(0)
const lastAgingRmse = ref(0)

// 任务每代样本数
const totalSamples = ref(0)

// ==================== 参数管理函数 ====================
// 保存参数到节点
const saveParams = async (params: Record<string, any>) => {
  try {
    const currentParams = await getNodeParams(props.nodeData)
    const updatedParams = { ...currentParams, ...params }

    // 添加调试日志，特别关注 agingTaskId
    if (params.agingTaskId !== undefined) {
      console.log('保存 agingTaskId:', params.agingTaskId)
    }

    await saveNodeParams(props.nodeData, updatedParams)
  } catch (error) {
    console.error('保存参数失败:', error)
    throw error // 重新抛出错误，让调用者知道保存失败
  }
}

// 加载参数
const loadParams = async () => {
  const loadedParams = await getNodeParams(props.nodeData)

  if (loadedParams && Object.keys(loadedParams).length > 0) {
    console.log('加载节点参数:', loadedParams)

    // 恢复基本状态
    elecParamsCompleted.value = loadedParams.elecParamsCompleted ?? false
    agingParamsCompleted.value = loadedParams.agingParamsCompleted ?? false
    currentStep.value = loadedParams.currentStep ?? 0
    isSubmitting.value = loadedParams.isSubmitting ?? false
    isProcessing.value = loadedParams.isProcessing ?? false
    // 恢复总样本数
    totalSamples.value = loadedParams.totalSamples ?? 0
    // 恢复服务器信息
    if (loadedParams.lastServerInfo) {
      try {
        lastServerInfo.value = JSON.parse(loadedParams.lastServerInfo)
        console.log('恢复服务器信息:', JSON.stringify(lastServerInfo.value, null, 2))
      } catch (error) {
        console.error('恢复服务器信息失败:', error)
      }
    }

    // 恢复任务ID - 电化学参数
    if (loadedParams.elecTaskId) {
      elecTaskId.value = loadedParams.elecTaskId
      console.log('恢复电化学任务ID:', elecTaskId.value)
      try {
        const result = await taskStore.updateTaskResult(loadedParams.elecTaskId)
        const data = typeof result === 'string' ? JSON.parse(result) : result
        if (data?.params) {
          elecResult.value = data.params
        }
      } catch (error) {
        console.error('更新电化学任务结果失败:', error)
      }
    }

    // 恢复任务ID - 老化参数
    if (loadedParams.agingTaskId) {
      agingTaskId.value = loadedParams.agingTaskId
      console.log('恢复老化任务ID:', agingTaskId.value)
      try {
        const result = await taskStore.updateTaskResult(loadedParams.agingTaskId)
        const data = typeof result === 'string' ? JSON.parse(result) : result
        if (data?.params) {
          agingResult.value = data.params
        }
      } catch (error) {
        console.error('更新老化任务结果失败:', error)
      }
    }

    // 恢复结果数据
    if (loadedParams.elecResult) {
      elecResult.value =
        typeof loadedParams.elecResult === 'string'
          ? JSON.parse(loadedParams.elecResult)
          : loadedParams.elecResult
    }

    if (loadedParams.agingResult) {
      agingResult.value =
        typeof loadedParams.agingResult === 'string'
          ? JSON.parse(loadedParams.agingResult)
          : loadedParams.agingResult
    }

    // 恢复算法参数
    if (loadedParams.algorithmParams) {
      algorithmParams.value =
        typeof loadedParams.algorithmParams === 'string'
          ? JSON.parse(loadedParams.algorithmParams)
          : loadedParams.algorithmParams
    }

    // 恢复参数数据
    if (loadedParams.allParams) {
      allParams.value =
        typeof loadedParams.allParams === 'string'
          ? JSON.parse(loadedParams.allParams)
          : loadedParams.allParams
    }

    if (loadedParams.elecParams) {
      elecParams.value =
        typeof loadedParams.elecParams === 'string'
          ? JSON.parse(loadedParams.elecParams)
          : loadedParams.elecParams
    }

    if (loadedParams.agingParams) {
      agingParams.value =
        typeof loadedParams.agingParams === 'string'
          ? JSON.parse(loadedParams.agingParams)
          : loadedParams.agingParams
    }

    // 恢复基本参数
    basicParams.value.batteryType = loadedParams.batteryType ?? 'LiFePO4'

    // 设置展开状态
    elecOpenStates.value = new Array(Object.keys(elecParams.value).length).fill(true)
    agingOpenStates.value = new Array(Object.keys(agingParams.value).length).fill(true)
  }
}

// ==================== 计算属性 ====================
// 豪鹏电芯输入数据
const dataInputParams = ref<any>({})

// 获取豪鹏电芯输入数据
const loadDataInputParams = async () => {
  try {
    const inputNodes = await getInputNodesByType(props.nodeData, 'HighpowerCellDataInput')
    console.log('豪鹏电芯输入数据inputNodes', inputNodes)
    if (inputNodes.length > 0) {
      // 取第一个匹配的输入节点的参数
      dataInputParams.value = inputNodes[0].params || {}
    } else {
      dataInputParams.value = {}
    }
  } catch (error) {
    console.error('获取豪鹏电芯输入数据失败:', error)
    dataInputParams.value = {}
  }
}

const isParamsExtract = computed(() => dataInputParams.value.isParamsExtract || false)
const storedData = computed(() => dataInputParams.value.storedData || '')
const modelBaseParams = computed(() => dataInputParams.value.model_base_params || '')
const cycleParams = computed(() => {
  if (typeof dataInputParams.value.cycleParams === 'string') {
    return JSON.parse(dataInputParams.value.cycleParams)
  }
  return dataInputParams.value.cycleParams
})

const taskProgress = computed(() => {
  const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value
  if (!taskId) return 0

  const task = taskStore.tasks.find((t) => t.taskId === taskId)
  if (task && typeof task.taskProcess === 'number') {
    return task.taskProcess
  }
  return 0
})

const selectedElecCount = computed(() => {
  return Object.values(elecParams.value).reduce((total, params) => {
    return total + params.filter((p: ParamItem) => p.selected).length
  }, 0)
})

const selectedAgingCount = computed(() => {
  return Object.values(agingParams.value).reduce((total, params) => {
    return total + params.filter((p: ParamItem) => p.selected).length
  }, 0)
})

const totalSelectedCount = computed(() => selectedElecCount.value + selectedAgingCount.value)

// 电化学任务完成状态
const elecTaskCompleted = computed(() => {
  if (!elecTaskId.value) return false

  // 检查任务状态
  const taskStatus = taskStore.getTaskStatus(elecTaskId.value).value
  const isTaskFinished = ['Finished', 'Error', 'Abort'].includes(taskStatus)

  // 检查是否有结果
  const hasResult = !!elecResult.value

  // 任务完成且有结果，或者标记为已完成
  return (isTaskFinished && hasResult) || elecParamsCompleted.value
})

// 老化任务完成状态
const agingTaskCompleted = computed(() => {
  if (!agingTaskId.value) return false

  // 检查任务状态
  const taskStatus = taskStore.getTaskStatus(agingTaskId.value).value
  const isTaskFinished = ['Finished', 'Error', 'Abort'].includes(taskStatus)

  // 检查是否有结果
  const hasResult = !!agingResult.value

  // 任务完成且有结果，或者标记为已完成
  return (isTaskFinished && hasResult) || agingParamsCompleted.value
})

// 电化学任务状态
const elecTaskStatus = computed(() => {
  if (!elecTaskId.value) return 'Initializing'
  return taskStore.getTaskStatus(elecTaskId.value).value
})

// 老化任务状态
const agingTaskStatus = computed(() => {
  if (!agingTaskId.value) return 'Initializing'
  return taskStore.getTaskStatus(agingTaskId.value).value
})

// 电化学任务持续时间
const elecTaskDuration = computed(() => {
  if (!elecTaskId.value) return '--'
  const task = taskStore.tasks.find((t) => t.taskId === elecTaskId.value)
  return task?.duration || '--'
})

// 老化任务持续时间
const agingTaskDuration = computed(() => {
  if (!agingTaskId.value) return '--'
  const task = taskStore.tasks.find((t) => t.taskId === agingTaskId.value)
  return task?.duration || '--'
})

// 电化学结果数据
const elecResultData = computed(() => {
  if (!elecTaskId.value) return null
  const taskResult = taskStore.getTaskResultById(elecTaskId.value).value
  return taskResult || null
})

// 老化结果数据
const agingResultData = computed(() => {
  if (!agingTaskId.value) return null
  const taskResult = taskStore.getTaskResultById(agingTaskId.value).value
  return taskResult || null
})

// ==================== 事件处理函数 ====================
const handleGetParams = (paramType: 'All' | 'Elec' | 'Aging') => {
  currentParamType.value = paramType
  handleGetParamsSubmit(paramType)
}

const handleElecParamsUpdate = (updatedParams: ParamsGroup) => {
  elecParams.value = updatedParams
}

const handleAgingParamsUpdate = (updatedParams: ParamsGroup) => {
  agingParams.value = updatedParams
}
// 对话框控制
const openSubmitDialog = () => {
  if (currentStep.value === 0 && selectedElecCount.value === 0) {
    toast.info('请至少选择一个电化学参数进行标定')
    return
  }

  if (currentStep.value === 1 && selectedAgingCount.value === 0) {
    toast.info('请至少选择一个老化参数进行标定')
    return
  }

  showSubmitDialog.value = true
}

// 步骤控制
const nextStep = async () => {
  if (currentStep.value < steps.length - 1 && elecParamsCompleted.value && !isProcessing.value) {
    currentStep.value++
    algorithmParams.value = getDefaultAlgorithmParams(currentStep.value)
    // 同步更新总样本数
    totalSamples.value = Number(algorithmParams.value.nums_per_iter) || 0
    await saveParams({
      currentStep: currentStep.value,
      elecParamsCompleted: elecParamsCompleted.value,
      agingParamsCompleted: agingParamsCompleted.value,
      isProcessing: isProcessing.value,
      algorithmParams: JSON.stringify(algorithmParams.value),
      totalSamples: totalSamples.value,
    })
  }
}

const prevStep = async () => {
  if (currentStep.value > 0 && !isProcessing.value) {
    currentStep.value--
    algorithmParams.value = getDefaultAlgorithmParams(currentStep.value)
    // 同步更新总样本数
    totalSamples.value = Number(algorithmParams.value.nums_per_iter) || 0
    await saveParams({
      currentStep: currentStep.value,
      elecParamsCompleted: elecParamsCompleted.value,
      agingParamsCompleted: agingParamsCompleted.value,
      isProcessing: isProcessing.value,
      algorithmParams: JSON.stringify(algorithmParams.value),
      totalSamples: totalSamples.value,
    })
  }
}

// 参数操作函数
const toggleParams = async (paramType: string, category: string, params: ParamItem[]) => {
  if (!paramType || !category || !params || !Array.isArray(params)) return

  const allSelected = params.every((param) => param.selected)
  const newSelectedState = !allSelected

  params.forEach((param) => {
    param.selected = newSelectedState
  })

  await saveSelectedParams()
}

const updateParamSelection = async (
  paramType: string,
  category: string,
  paramIndex: number,
  value: boolean,
) => {
  if (paramType === 'elecParams') {
    elecParams.value[category][paramIndex].selected = value
  } else if (paramType === 'agingParams') {
    agingParams.value[category][paramIndex].selected = value
  }
  await saveSelectedParams()
}

const updateAlgorithmParams = async (newParams: AlgorithmParams) => {
  algorithmParams.value = { ...newParams }
  // 同步更新总样本数
  totalSamples.value = Number(newParams.nums_per_iter) || 0
  await saveSelectedParams()
}

const updateElecOpenStates = (newStates: boolean[]) => {
  elecOpenStates.value = newStates
}

const updateAgingOpenStates = (newStates: boolean[]) => {
  agingOpenStates.value = newStates
}

const useRecommendedParams = async (paramType: 'elecParams' | 'agingParams') => {
  const params = paramType === 'elecParams' ? elecParams.value : agingParams.value

  Object.keys(params).forEach((category) => {
    params[category].forEach((param) => {
      if (param.is_recommended) {
        param.selected = true
        param.value = param.init_value
      } else {
        param.selected = false
      }
    })
  })

  await saveSelectedParams()
  toast.success('已应用推荐参数', {
    description: `已选择所有推荐的${paramType === 'elecParams' ? '电化学' : '老化'}参数`,
  })
}

const saveSelectedParams = async () => {
  await saveParams({
    allParams: JSON.stringify(allParams.value),
    elecParams: JSON.stringify(elecParams.value),
    agingParams: JSON.stringify(agingParams.value),
    algorithmParams: JSON.stringify(algorithmParams.value),
    batteryType: basicParams.value.batteryType,
    totalSamples: totalSamples.value,
  })
}

// 专门用于保存任务ID的函数，确保持久化
const saveTaskIds = async () => {
  try {
    console.log('保存任务ID - elecTaskId:', elecTaskId.value, 'agingTaskId:', agingTaskId.value)
    await saveParams({
      elecTaskId: elecTaskId.value,
      agingTaskId: agingTaskId.value,
      elecParamsCompleted: elecParamsCompleted.value,
      agingParamsCompleted: agingParamsCompleted.value,
      isProcessing: isProcessing.value,
      currentStep: currentStep.value,
    })
    console.log('任务ID保存完成')
  } catch (error) {
    console.error('保存任务ID失败:', error)
    throw error
  }
}

// ==================== 数据处理函数 ====================
const formSelectedData = (data: ParamsGroup) => {
  const result: Array<{
    param_key: string
    min: number
    max: number
    init_value: number
    is_recommended?: boolean
  }> = []

  Object.values(data).forEach((group) => {
    group.forEach((param) => {
      const { param_key, min, max, init_value, is_recommended, selected } = param
      if (selected) {
        const item = { param_key, min, max, init_value }
        if (is_recommended) (item as any).is_recommended = true
        result.push(item)
      }
    })
  })
  return JSON.stringify(result)
}

// ==================== 任务提交处理 ====================
const handleSubmit = async (serverInfo: any) => {
  if (!isParamsExtract.value) {
    toast.warning('模型参数未提取', {
      description: '请先在电芯数据输入模块上进行参数提取操作',
    })
    return
  }
  if (storedData.value == '') {
    toast.warning('请先在电芯数据输入模块上传循环数据')
    return
  }

  try {
    isSubmitting.value = true
    const step = currentStep.value === 0 ? 'electrochemical' : 'aging'
    const isElectrochemical = step === 'electrochemical'

    const keyTypePairs = getKeyTypePairs(step)
    const keyValuePairs = getKeyValuePairs(step)
    const paramData = inputParse(keyValuePairs, keyTypePairs)

    // 保存服务器信息以供日志查看使用
    lastServerInfo.value = {
      server_id: serverInfo.server_id,
      service_name: serverInfo.service_name,
    }

    console.log('提交任务，保存服务器信息:', JSON.stringify(lastServerInfo.value, null, 2))
    // 保存总样本数
    totalSamples.value = Number(algorithmParams.value.nums_per_iter) || 0

    // 保存服务器信息到节点参数
    await saveParams({
      lastServerInfo: JSON.stringify(lastServerInfo.value),
      totalSamples: totalSamples.value,
    })

    const res = await taskService.submitTask(
      serverInfo.service_name,
      serverInfo.server_id,
      paramData,
    )

    if (res.status === 'Success') {
      const taskId = res.taskId
      taskStore.startPolling(taskId)

      if (isElectrochemical) {
        elecTaskId.value = taskId
        elecParamsCompleted.value = false
        await saveParams({
          all_params: formSelectedData(elecParams.value),
          elecTaskId: elecTaskId.value,
        })
      } else {
        agingTaskId.value = taskId
        agingParamsCompleted.value = false
        console.log('老化参数提交成功，设置 agingTaskId:', agingTaskId.value)

        await saveParams({
          agingTaskId: agingTaskId.value,
          agingParamsCompleted: agingParamsCompleted.value,
        })
      }

      isProcessing.value = true

      await saveTaskIds()

      await saveParams({
        isSubmitting: false, // 重置提交状态
      })

      // 更新节点数据
      updateNodeDataUtil(props.nodeData, {
        taskId: taskId,
        taskInputData: keyValuePairs,
        taskOutputData: null,
      })

      toast.success('提交成功', {
        description: `${isElectrochemical ? '电化学参数' : '老化参数'}标定任务已提交`,
      })

      if (isElectrochemical) {
        elecParamsCompleted.value = true
        await saveParams({
          elecParamsCompleted: elecParamsCompleted.value,
        })
      } else {
        agingParamsCompleted.value = true
        await saveParams({
          agingParamsCompleted: agingParamsCompleted.value,
        })
      }

      // 确保提交状态被重置
      isSubmitting.value = false
      console.log('任务提交完成，重置 isSubmitting 状态')
    } else {
      throw new Error(res.message)
    }
  } catch (error: any) {
    console.error('任务提交失败:', error)
    handleTaskError(error, '提交失败')
  } finally {
    // 确保无论成功还是失败都重置提交状态
    isSubmitting.value = false
  }
}

// 更新基本参数
const updateBasicParams = (newParams: {
  batteryType: string
  chargeType: string
  chargeValue: number
}) => {
  basicParams.value = { ...newParams }
}

// ==================== 配置生成函数 ====================
const getKeyValuePairs = (step: string) => {
  const baseConfig = `[simulation]\nmultiprocesses = no\nnum_processes = 180\nmax_core_usage_ratio = 0.5\n\n[task]\npop_size = 360\nmax_iter = ${
    algorithmParams.value.max_iter
  }\ncriteria = ${algorithmParams.value.max_rmse}\nmut_prob = 0.5\nF = 0.5\ntimeout = ${
    step === 'electrochemical' ? 200 : 2000
  }\nerror_loss = 100\nnums_per_iter = ${algorithmParams.value.nums_per_iter}\ntask_timeout = ${algorithmParams.value.task_timeout}\nmax_workers = ${algorithmParams.value.max_workers}`

  if (step === 'electrochemical') {
    return {
      params_data: formSelectedData(elecParams.value),
      config_data: baseConfig,
      input_data: storedData.value,
      model_base_params: modelBaseParams.value,
      battery_type: basicParams.value.batteryType,
      start_cycle_type: cycleParams.value.startCycleType,
      start_cycle_value: cycleParams.value.startCycleValue,
    }
  } else {
    return {
      params_data: formSelectedData(agingParams.value),
      config_data: baseConfig,
      input_data: storedData.value,
      elec_params_data: formSelectedData(elecParams.value),
      elec_params_value: elecResult.value ? JSON.stringify(elecResult.value) : '',
      model_base_params: modelBaseParams.value,
      battery_type: basicParams.value.batteryType,
      charge_type: basicParams.value.chargeType,
      charge_value: basicParams.value.chargeValue,
      start_cycle_type: cycleParams.value.startCycleType,
      start_cycle_value: cycleParams.value.startCycleValue,
      power: '1.0',
    }
  }
}

const getKeyTypePairs = (step: string) => {
  if (step === 'electrochemical') {
    return {
      input_data: 'String',
      config_data: 'String',
      params_data: 'String',
      model_base_params: 'String',
      battery_type: 'String',
      start_cycle_type: 'String',
      start_cycle_value: 'String',
    }
  } else {
    return {
      input_data: 'String',
      config_data: 'String',
      params_data: 'String',
      elec_params_data: 'String',
      elec_params_value: 'Json',
      model_base_params: 'String',
      battery_type: 'String',
      charge_type: 'String',
      charge_value: 'String',
      start_cycle_type: 'String',
      start_cycle_value: 'String',
      power: 'Float',
    }
  }
}

const handleTaskError = async (error: any, title = '操作失败') => {
  toast.error(title, {
    description: error.message || '发生未知错误',
  })

  isProcessing.value = false
  isSubmitting.value = false
  showSubmitDialog.value = false

  await saveParams({
    isProcessing: isProcessing.value,
    isSubmitting: isSubmitting.value,
  })
}

// ==================== 参数获取处理 ====================
const handleGetParamsSubmit = async (type: 'All' | 'Elec' | 'Aging' = 'All') => {
  if (
    (type === 'All' && Object.keys(allParams.value).length > 0) ||
    (type === 'Elec' && Object.keys(elecParams.value).length > 0) ||
    (type === 'Aging' && Object.keys(agingParams.value).length > 0)
  ) {
    return
  }

  try {
    const keyTypePairs = { params_type: 'String' }
    const keyValuePairs = { params_type: type }
    const paramData = inputParse(keyValuePairs, keyTypePairs)
    const res = await taskService.callTask('hpGetIdentifiableParameters', '', paramData)

    if (res.status === 'Success') {
      handleGetParamsResult(res.result)
    } else {
      toast.error('获取参数失败', {
        description: res.message || '无法解析参数数据',
      })
    }
  } catch (error: any) {
    handleTaskError(error, '获取参数失败')
  } finally {
    isSubmitting.value = false
  }
}

const handleGetParamsResult = (data: any) => {
  if (!data) return

  try {
    const result = typeof data === 'string' ? JSON.parse(data) : data

    if (currentParamType.value === 'All') {
      allParams.value = result
      if (result['电化学参数']) {
        elecParams.value = result['电化学参数']
        elecOpenStates.value = new Array(Object.keys(elecParams.value).length).fill(true)
      }
      if (result['老化参数']) {
        agingParams.value = result['老化参数']
        agingOpenStates.value = new Array(Object.keys(agingParams.value).length).fill(true)
      }
    } else if (currentParamType.value === 'Elec') {
      elecParams.value = result
      elecOpenStates.value = new Array(Object.keys(elecParams.value).length).fill(true)
    } else if (currentParamType.value === 'Aging') {
      agingParams.value = result
      agingOpenStates.value = new Array(Object.keys(agingParams.value).length).fill(true)
    }

    saveSelectedParams()
  } catch (error: any) {
    toast.error('处理参数失败', {
      description: error.message || '无法解析参数数据',
    })
  }
}

// ==================== 任务控制函数 ====================
// 暂停任务
const handlePause = async () => {
  if (!isProcessing.value) return

  // 获取当前步骤对应的任务ID
  const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value

  if (!taskId) {
    toast.warning('无法暂停', {
      description: '没有正在进行的任务',
    })
    return
  }

  try {
    const res = await taskService.pauseTask(taskId)
    if (res.status === 'Success') {
      await taskStore.updateTaskList(taskId)
      toast.success('任务已暂停', {
        description: '任务已暂停',
      })
      if (currentStep.value === 0) {
        elecParamsCompleted.value = true
      } else {
        agingParamsCompleted.value = true
      }
      await saveParams({
        isProcessing: false,
        elecParamsCompleted: elecParamsCompleted.value,
        agingParamsCompleted: agingParamsCompleted.value,
      })
      isProcessing.value = false
    } else {
      toast.error('暂停失败', {
        description: res.message || '暂停任务时发生错误',
      })
    }
  } catch (error: any) {
    toast.error('暂停失败', {
      description: error.message || '暂停任务时发生错误',
    })
  }
}

// 终止任务
const handleStop = async () => {
  if (!isProcessing.value) return

  // 获取当前步骤对应的任务ID
  const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value

  if (!taskId) {
    toast.warning('无法终止', {
      description: '没有正在进行的任务',
    })
    return
  }

  try {
    const result = await taskService.stopTask(taskId)
    if (result.status === 'Success') {
      await taskStore.updateTaskList(taskId)
      isProcessing.value = false
      if (currentStep.value === 0) {
        elecParamsCompleted.value = true
      } else {
        agingParamsCompleted.value = true
      }
      await saveParams({
        isProcessing: false,
        elecParamsCompleted: elecParamsCompleted.value,
        agingParamsCompleted: agingParamsCompleted.value,
      })

      toast.success('提示', {
        description: '任务已成功终止',
      })

      // 停止轮询
      taskStore.stopPolling(taskId)
    } else {
      toast.error('提示', {
        description: result.message || '终止任务时发生错误',
      })
    }
  } catch (error: any) {
    toast.error('提示', {
      description: error.message || '终止任务时发生错误',
    })
  }
}

// 重置选择
const resetSelection = () => {
  if (isProcessing.value) {
    toast.warning('无法重置', {
      description: '请先暂停或终止当前标定过程',
    })
    return
  }

  // 重置电化学参数选择
  Object.values(elecParams.value).forEach((categoryParams) => {
    categoryParams.forEach((param) => {
      param.selected = false
    })
  })

  // 重置老化参数选择
  Object.values(agingParams.value).forEach((categoryParams) => {
    categoryParams.forEach((param) => {
      param.selected = false
    })
  })

  // 重置完成状态
  elecParamsCompleted.value = false
  agingParamsCompleted.value = false

  // 保存重置后的状态
  saveSelectedParams()

  toast.success('重置成功', {
    description: '已重置所有参数选择和标定状态',
  })
}

// 任务状态检查函数
const checkTaskStatus = async () => {
  const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value
  if (!taskId) return

  try {
    await taskStore.updateTaskList(taskId)
    const task = taskStore.tasks.find((task) => task.taskId === taskId)

    if (task) {
      if (['Computing', 'Pending', 'Initializing'].includes(task.taskStatus)) {
        isProcessing.value = true
        taskStore.startPolling(taskId)
        await saveParams({ isProcessing: true })
      } else if (['Finished', 'Error', 'Abort', 'Paused'].includes(task.taskStatus)) {
        isProcessing.value = false
        await saveParams({ isProcessing: false })

        if (
          task.taskStatus === 'Finished' &&
          ((currentStep.value === 0 && !elecParamsCompleted.value) ||
            (currentStep.value === 1 && !agingParamsCompleted.value))
        ) {
          if (currentStep.value === 0) {
            elecParamsCompleted.value = true
          } else {
            agingParamsCompleted.value = true
          }
          await saveParams({
            elecParamsCompleted: elecParamsCompleted.value,
            agingParamsCompleted: agingParamsCompleted.value,
          })
        }
      }
    } else {
      isProcessing.value = false
      await saveParams({ isProcessing: false })
    }
  } catch (error: any) {
    isProcessing.value = false
    await saveParams({ isProcessing: false })
  }
}
// ==================== 导出数据处理 ====================
const formatExportData = (elecResultData: any, agingResultData: any, typeParams: any) => {
  const output: {
    'Battery parameters': Array<{
      param_name: string
      zh_description: string
      en_description: string
      min: number
      max: number
      value: any
    }>
    'Aging parameters': Array<{
      param_name: string
      zh_description: string
      en_description: string
      min: number
      max: number
      value: any
    }>
  } = {
    'Battery parameters': [],
    'Aging parameters': [],
  }

  const paramMap: Record<
    string,
    {
      zh_description: string
      min: number
      max: number
      category: string
    }
  > = {}

  // 构建参数映射
  const buildParamMap = () => {
    if (typeParams['电化学参数']) {
      Object.keys(typeParams['电化学参数']).forEach((group) => {
        typeParams['电化学参数'][group].forEach((param: any) => {
          paramMap[param.param_key] = {
            zh_description: param.param_name,
            min: param.min,
            max: param.max,
            category: 'Battery parameters',
          }
        })
      })
    }

    if (typeParams['老化参数']) {
      Object.keys(typeParams['老化参数']).forEach((group) => {
        typeParams['老化参数'][group].forEach((param: any) => {
          paramMap[param.param_key] = {
            zh_description: param.param_name,
            min: param.min,
            max: param.max,
            category: 'Aging parameters',
          }
        })
      })
    }
  }

  buildParamMap()

  // 处理电化学参数结果
  if (elecResultData && typeof elecResultData === 'object') {
    for (const [englishName, value] of Object.entries(elecResultData)) {
      const paramInfo = paramMap[englishName]
      if (paramInfo) {
        output['Battery parameters'].push({
          param_name: englishName,
          zh_description: paramInfo.zh_description,
          en_description: englishName,
          min: paramInfo.min,
          max: paramInfo.max,
          value: value,
        })
      }
    }
  }

  // 处理老化参数结果
  if (agingResultData && typeof agingResultData === 'object') {
    for (const [englishName, value] of Object.entries(agingResultData)) {
      const paramInfo = paramMap[englishName]
      if (paramInfo) {
        output['Aging parameters'].push({
          param_name: englishName,
          zh_description: paramInfo.zh_description,
          en_description: englishName,
          min: paramInfo.min,
          max: paramInfo.max,
          value: value,
        })
      }
    }
  }

  return output
}
// 导出标定结果
const handleExport = async () => {
  try {
    // 检查是否有结果可导出
    if (!elecResult.value || Object.keys(allParams.value).length === 0) {
      toast.error('没有可导出的标定结果', {
        description: '请先完成老化参数标定',
      })
      return
    }

    const exportData = formatExportData(elecResult.value, agingResult.value, allParams.value)
    // console.log('exportData===>', exportData)

    // 获取当前日期，格式为YYYY-MM-DD
    const currentDate = new Date().toISOString().slice(0, 10)

    // 使用encryptAndSaveFile加密保存文件
    const { success, filePath, error } = await encryptAndSaveFile({
      jsonData: JSON.stringify(exportData, null, 2),
      fileType: 'model',
      defaultPath: `highpower-${currentDate}.model`,
      title: '导出电池模型标定结果',
      filters: [
        { name: '模型文件', extensions: ['model'] },
        { name: '所有文件', extensions: ['*'] },
      ],
    })

    if (success) {
      toast.success('标定结果导出成功', {
        description: `文件已保存至: ${filePath}`,
      })
    } else {
      toast.error('标定结果导出失败', {
        description: error || '未知错误',
      })
    }
  } catch (err: any) {
    toast.error('标定结果导出失败', {
      description: err.message || '未知错误',
    })
  }
}

// 监听任务状态变化的
watch(
  () => {
    // 根据当前步骤确定应该使用哪个任务ID
    const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value
    if (!taskId) return null

    // 获取任务状态和结果
    return {
      status: taskStore.getTaskStatus(taskId).value,
      progress: taskStore.getTaskProgress(taskId).value,
      result: taskStore.getTaskResultById(taskId).value,
    }
  },
  async (newTaskInfo, oldTaskInfo) => {
    if (!newTaskInfo) return

    const { status, result } = newTaskInfo

    // 更新处理状态
    isProcessing.value = ['Computing', 'Pending', 'Initializing'].includes(status)

    // 保存状态
    await saveParams({
      isProcessing: isProcessing.value,
    })

    // 处理任务结果
    if (result && (!oldTaskInfo || result !== oldTaskInfo.result)) {
      if (currentStep.value === 0) {
        // 电化学参数更新
        elecResult.value = result.params

        if (result.rmse !== lastElecRmse.value) {
          lastElecRmse.value = result.rmse

          // toast.info('电化学参数标定中', {
          //   description: `当前 generation 值：${result.generation}\n当前参数 RMSE 值：${result.rmse}`,
          //   action: {
          //     label: '暂停',
          //     onClick: () => handlePause(),
          //   },
          //   cancel: {
          //     label: '终止',
          //     onClick: () => handleStop(),
          //   },
          // })
        }

        await saveParams({
          elecResult: JSON.stringify(elecResult.value),
        })
      } else {
        // 老化参数更新
        agingResult.value = result.params

        if (result.rmse !== lastAgingRmse.value) {
          lastAgingRmse.value = result.rmse

          // toast.info('老化参数标定中', {
          //   description: `当前 generation 值：${result.generation}\n当前参数 RMSE 值：${result.rmse}`,
          //   action: {
          //     label: '暂停',
          //     onClick: () => handlePause(),
          //   },
          //   cancel: {
          //     label: '终止',
          //     onClick: () => handleStop(),
          //   },
          // })
        }

        await saveParams({
          agingResult: JSON.stringify(agingResult.value),
        })
      }
    }

    // 处理任务完成
    if (
      ['Finished', 'Error', 'Abort'].includes(status) &&
      oldTaskInfo &&
      oldTaskInfo.status !== status
    ) {
      // 任务完成处理
      if (status === 'Finished') {
        // 立即获取最新结果
        const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value
        if (taskId) {
          taskStore.updateTaskResult(taskId).then(async () => {
            // 确保结果已更新
            const latestResult = taskStore.getTaskResultById(taskId).value
            if (latestResult) {
              if (currentStep.value === 0) {
                elecResult.value = latestResult.params
              } else {
                agingResult.value = latestResult.params
              }

              // 更新保存的结果
              await saveParams({
                elecResult: JSON.stringify(elecResult.value),
                agingResult: JSON.stringify(agingResult.value),
              })
            }
          })
        }

        if (currentStep.value === 0) {
          elecParamsCompleted.value = true
        } else {
          agingParamsCompleted.value = true
        }

        // 使用专门的函数保存任务ID和状态
        await saveTaskIds()

        // 保存结果数据
        await saveParams({
          elecResult: JSON.stringify(elecResult.value),
          agingResult: JSON.stringify(agingResult.value),
          lastServerInfo: JSON.stringify(lastServerInfo.value),
        })

        // 显示成功提示
        toast.success('标定完成', {
          description: `${currentStep.value === 0 ? '电化学参数' : '老化参数'}标定已完成`,
        })
      } else {
        // 任务失败或被终止
        toast.error('标定未完成', {
          description: `${currentStep.value === 0 ? '电化学参数' : '老化参数'}标定${
            status === 'Error' ? '失败' : '被终止'
          }`,
        })
      }
    }
  },
  { deep: true },
)

// ==================== 生命周期函数 ====================
// 初始化参数
const initParams = async () => {
  await loadParams()
  await loadDataInputParams() // 加载豪鹏电芯输入数据
  handleGetParamsSubmit()

  const taskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value
  if (taskId) {
    await checkTaskStatus()
  }

  algorithmParams.value = getDefaultAlgorithmParams(currentStep.value)
  // 同步更新总样本数
  totalSamples.value = Number(algorithmParams.value.nums_per_iter) || 0

  elecOpenStates.value = new Array(Object.keys(elecParams.value).length).fill(true)
  agingOpenStates.value = new Array(Object.keys(agingParams.value).length).fill(true)

  // 使用专门的函数保存任务ID
  await saveTaskIds()

  // 保存其他初始化参数
  await saveParams({
    algorithmParams: JSON.stringify(algorithmParams.value),
    lastServerInfo: JSON.stringify(lastServerInfo.value),
    totalSamples: totalSamples.value,
  })
}

onMounted(async () => {
  await initParams()
})

onUnmounted(() => {})

// 打开日志查看器
const openLogViewer = () => {
  const currentTaskId = currentStep.value === 0 ? elecTaskId.value : agingTaskId.value
  console.log('打开日志查看器:', {
    taskId: currentTaskId,
    serverId: lastServerInfo.value.server_id,
    step: currentStep.value === 0 ? '电化学参数' : '老化参数',
  })
  showLogViewer.value = true
}
</script>
<style lang="scss" scoped>
:deep(.collapsible-content) {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

/* 推荐参数高亮样式 */
.recommended-param {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}
</style>
