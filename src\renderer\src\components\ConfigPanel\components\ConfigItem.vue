<template>
  <Card
    :class="[
      'transition-colors',
      highlight
        ? 'border-orange-200 bg-orange-50 dark:border-orange-700 dark:bg-orange-900/20'
        : '',
      editable ? 'cursor-pointer hover:shadow-md' : '',
    ]"
    @click="editable ? toggleEnabled() : null"
  >
    <CardContent class="flex items-center justify-between p-3">
      <div class="flex-1">
        <div class="flex items-center space-x-2">
          <span
            :class="[
              'text-sm font-medium',
              highlight ? 'text-orange-900 dark:text-orange-100' : '',
            ]"
          >
            {{ label }}
          </span>

          <!-- 状态指示器 -->
          <div
            :class="['w-2 h-2 rounded-full', enabled ? 'bg-green-500' : 'bg-muted-foreground']"
          ></div>
        </div>

        <div
          v-if="description"
          :class="[
            'text-xs mt-1',
            highlight ? 'text-orange-700 dark:text-orange-300' : 'text-muted-foreground',
          ]"
        >
          {{ description }}
        </div>
      </div>

      <div class="flex items-center space-x-2">
        <!-- 切换开关 -->
        <Switch
          v-if="editable"
          :checked="enabled"
          class="data-[state=checked]:bg-green-600"
          @update:checked="toggleEnabled"
          @click.stop
        />

        <!-- 只读状态徽章 -->
        <Badge v-else :variant="enabled ? 'default' : 'secondary'" class="text-xs">
          {{ enabled ? '启用' : '禁用' }}
        </Badge>

        <!-- 高亮标识 -->
        <div
          v-if="highlight"
          class="flex items-center space-x-1"
          title="此配置项在当前应用中被特别关注"
        >
          <AlertTriangle class="h-3 w-3 text-orange-600 dark:text-orange-400" />
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Card, CardContent } from '@renderer/components/ui/card'
import { Switch } from '@renderer/components/ui/switch'
import { AlertTriangle } from 'lucide-vue-next'

interface Props {
  label: string
  enabled: boolean
  description?: string
  highlight?: boolean
  editable?: boolean
  configPath?: string
}

interface Emits {
  (e: 'toggle', configPath: string, newValue: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  editable: false,
})

const emit = defineEmits<Emits>()

// 切换启用状态
const toggleEnabled = () => {
  if (props.editable && props.configPath) {
    emit('toggle', props.configPath, !props.enabled)
  }
}
</script>
