// 任务状态类型
export type TaskStatus =
  | 'Initializing'
  | 'Computing'
  | 'Pending'
  | 'Paused'
  | 'Finished'
  | 'Error'
  | 'TaskStay'
  | 'Abort'

// 基础任务接口
export interface Task {
  taskId: string
  userId: string
  serviceId: string
  startTime: number
  endTime: number
  taskLog: string
  taskStatus: TaskStatus
  taskProcess: number
  taskPid: number
  result: string
  filePath: string
  createTime: number
  updateTime: number
  duration: string
}
