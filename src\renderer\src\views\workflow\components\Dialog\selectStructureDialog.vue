<template>
  <Dialog :open="visiable" @update:open="closeSturcture">
    <DialogContent class="sm:max-w-[800px]">
      <DialogHeader>
        <DialogTitle>从数据库中选择材料</DialogTitle>
      </DialogHeader>

      <div class="h-[500px] overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="w-[55px]">选择</TableHead>
              <TableHead class="w-[180px]">名称</TableHead>
              <TableHead class="w-[180px]">结构简式</TableHead>
              <TableHead>结构式</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow
              v-for="row in data.selectSturctureTableData"
              :key="row.name"
              :class="{ 'bg-muted': data.selectStructureName === row.name }"
              @click="clickStructureTableItem(row)"
            >
              <TableCell>
                <div class="w-[18px] h-[18px]">
                  <Checkbox :model-value="data.selectStructureName === row.name" />
                </div>
              </TableCell>
              <TableCell>{{ row.name }}</TableCell>
              <TableCell>{{ row.reduced_formula }}</TableCell>
              <TableCell>{{ row.formula }}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeSturcture">取消</Button>
        <Button @click="confirmSturcture">
          <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
          <span>确定</span>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { createMaterialService } from '@renderer/config/api/grpc/materialService'
import { Loader2 } from 'lucide-vue-next'
import { onMounted, reactive, ref } from 'vue'
defineProps({
  visiable: Boolean,
})
// 定义emit
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'ok', data?: any): void
  (e: 'update:visiable', visiable: boolean): void
}>()

// const taskStore = useTaskStore()

const data = reactive({
  // sturcturePlotData: '',
  // tmpStructure: '',
  selectSturctureTableData: [] as any[],
  selectStructureName: '',
  selectStructureData: {} as any,
})
const closeSturcture = () => {
  emit('close')
  emit('update:visiable', false)
}
const materialService = createMaterialService()
const loading = ref(false)
const confirmSturcture = async () => {
  if (loading.value) {
    return
  }
  if (data.selectStructureName) {
    loading.value = true
    const res = await materialService.getStructureInfoByName(data.selectStructureName, 'String')
    if (res) {
      const ret = res.keyValuePairs
      emit('ok', {
        selectItem: data.selectStructureData,
        structureData: ret['structureData'],
        density: ret['density'],
        'Number of Atoms': ret['Number of Atoms'],
      })
      emit('close')
      emit('update:visiable', false)
    }
    loading.value = false
  }
}
const getDatabaseList = async () => {
  const res: any = await materialService.getDatabaseList()
  const objRes = strToObj(res.keyValuePairs, 'structureList')
  if (objRes) {
    data.selectSturctureTableData = objRes
  }
}
const strToObj = (data: any, key?: string): any => {
  if (key) {
    if (!data[key]) {
      return null
    }
    return JSON.parse(data[key])
  } else {
    if (!data) {
      return null
    }
    return JSON.parse(data)
  }
}
const clickStructureTableItem = (row: any) => {
  if (data.selectStructureName === row.name) {
    data.selectStructureName = ''
  } else {
    data.selectStructureName = row.name
    data.selectStructureData = row
  }
}
onMounted(() => {
  getDatabaseList()
})
</script>
