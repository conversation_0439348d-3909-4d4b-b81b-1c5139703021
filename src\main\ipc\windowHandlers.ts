import { ipcMain } from 'electron'
import { windowService } from '../services/windowService'
import logger from '../utils/logger'

// 跟踪处理程序是否已设置
let handlersSetup = false

/**
 * 设置窗口控制相关的IPC处理程序
 * 确保只设置一次处理程序
 */
export function setupWindowHandlers(): void {
  // 如果处理程序已设置，则直接返回
  if (handlersSetup) {
    logger.info('窗口控制IPC处理程序已设置，跳过重复设置')
    return
  }

  // 窗口最小化
  ipcMain.on('window-minimize', () => {
    logger.info('Received window-minimize command')
    windowService.minimize()
  })

  // 窗口最大化/还原
  ipcMain.on('window-maximize', () => {
    logger.info('Received window-maximize command')
    windowService.toggleMaximize()
  })

  // 关闭窗口
  ipcMain.on('window-close', () => {
    logger.info('Received window-close command')
    windowService.close()
  })

  // 查询窗口是否最大化
  ipcMain.handle('is-window-maximized', () => {
    const isMaximized = windowService.isMaximized()
    logger.info('Checking if window is maximized:', isMaximized)
    return isMaximized
  })

  // 设置登录窗口大小
  ipcMain.on('set-login-window-size', () => {
    windowService.setLoginSize()
  })

  // 登录后最大化窗口
  ipcMain.on('adjust-window-after-login', () => {
    windowService.maximizeAfterLogin()
  })

  // 打开开发者工具
  ipcMain.on('open-dev-tools', () => {
    logger.info('Received open-dev-tools command')
    windowService.openDevTools()
  })

  // 切换开发者工具
  ipcMain.on('toggle-dev-tools', () => {
    logger.info('Received toggle-dev-tools command')
    windowService.toggleDevTools()
  })

  // 标记处理程序已设置
  handlersSetup = true
  logger.info('窗口控制IPC处理程序已设置')
}
