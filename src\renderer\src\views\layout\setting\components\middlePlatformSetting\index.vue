<template>
  <ScrollArea class="h-full w-full">
    <div class="middle-platform-setting">
      <Card class="mb-6">
        <CardHeader>
          <CardTitle>{{ t('settings.middlePlatformSetting.address.title') }}</CardTitle>
          <CardDescription>
            {{ t('settings.middlePlatformSetting.address.description') }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="flex items-start justify-between">
              <div class="space-y-2 flex-1">
                <Label>{{ t('settings.middlePlatformSetting.address.input') }}</Label>
                <Input
                  v-model="platformAddress"
                  :placeholder="t('settings.middlePlatformSetting.address.placeholder')"
                  class="w-full"
                />
              </div>
              <Button class="ml-4 mt-8" :disabled="!isValidUrl" @click="confirmSave">
                {{ t('settings.middlePlatformSetting.address.save') }}
              </Button>
            </div>

            <!-- 当前连接状态 -->
            <div class="mt-4 p-4 border rounded-md">
              <p class="text-sm text-muted-foreground mb-2">
                {{ t('settings.middlePlatformSetting.address.currentStatus') }}:
              </p>
              <div class="flex items-center">
                <div
                  class="w-3 h-3 rounded-full mr-2"
                  :class="grpcStatus ? 'bg-green-500' : 'bg-red-500'"
                ></div>
                <p class="text-base">
                  {{
                    grpcStatus
                      ? t('settings.middlePlatformSetting.address.connected')
                      : t('settings.middlePlatformSetting.address.disconnected')
                  }}
                </p>
              </div>
              <p class="mt-2 text-sm">
                {{ t('settings.middlePlatformSetting.address.currentAddress') }}:
                {{ currentLinkerApi }}
              </p>
              <p class="mt-2 text-sm">
                {{ t('settings.middlePlatformSetting.server.version') }}:
                {{ serverVersion }}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </ScrollArea>

  <!-- 确认对话框 -->
  <AlertDialog :open="showConfirmDialog" @update:open="showConfirmDialog = $event">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>
          {{ t('settings.middlePlatformSetting.address.confirmTitle') }}
        </AlertDialogTitle>
        <AlertDialogDescription>
          {{ t('settings.middlePlatformSetting.address.confirmDescription') }}
          <br />
          <strong>{{ platformAddress }}</strong>
          <br />
          {{ t('settings.middlePlatformSetting.address.restartWarning') }}
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="showConfirmDialog = false">
          {{ t('settings.middlePlatformSetting.address.cancel') }}
        </AlertDialogCancel>
        <AlertDialogAction @click="savePlatformAddress">
          {{ t('settings.middlePlatformSetting.address.confirm') }}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useLanguage } from '@renderer/config/hooks'
import { toast } from 'vue-sonner'
import { GetResponse } from '@renderer/config/types/grpc'

const { t } = useLanguage()

// 中台地址
const platformAddress = ref('')
const currentLinkerApi = ref('')
const grpcStatus = ref(false)
const showConfirmDialog = ref(false)

// 服务器版本
const serverVersion = ref('')

// 验证URL是否有效
const isValidUrl = computed(() => {
  if (!platformAddress.value) return false

  // 验证格式为 1.http(s)://域名或IP:端口  2.域名:端口 3.IP:端口
  const regex =
    /^(https?:\/\/)?((([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])|((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))(:([1-9][0-9]{0,4}))?$/
  return regex.test(platformAddress.value)
})

// 打开确认对话框
const confirmSave = () => {
  if (!isValidUrl.value) {
    toast.error(t('settings.middlePlatformSetting.address.invalidUrl'), {
      description: t('settings.middlePlatformSetting.address.pleaseEnterValidUrl'),
    })
    return
  }

  showConfirmDialog.value = true
}

// 保存中台地址
const savePlatformAddress = async () => {
  try {
    // 调用electron主进程保存配置
    const result = await window.grpcApi.updateLinkerApi(platformAddress.value)

    if (result.success) {
      toast.success(t('settings.middlePlatformSetting.address.saveSuccess'), {
        description: t('settings.middlePlatformSetting.address.restartNow'),
      })

      // 延迟2秒后重启应用
      setTimeout(() => {
        window.grpcApi.restartApp()
      }, 2000)
    } else {
      toast.error(t('settings.middlePlatformSetting.address.saveFailed'), {
        description: result.message,
      })
    }

    showConfirmDialog.value = false
  } catch (error) {
    toast.error(t('settings.middlePlatformSetting.address.saveFailed'), {
      description: error.message || t('settings.middlePlatformSetting.address.unknownError'),
    })
    showConfirmDialog.value = false
  }
}

// 获取当前连接状态
const getGrpcStatus = async () => {
  try {
    const status = await window.grpcApi.getStatus()
    grpcStatus.value = status.connected
    currentLinkerApi.value = status.linkerUrl
  } catch (error) {
    console.error('获取gRPC状态失败:', error)
    grpcStatus.value = false
  }
}

// 获取服务器版本信息
const getServerVersion = async () => {
  try {
    const res = await window.grpcApi.call<GetResponse>('getVersion', {})
    serverVersion.value = res.result
  } catch (error) {
    console.error('服务器版本获取失败')
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 获取环境变量
  const envVars = await window.api.getEnvVars()
  platformAddress.value = envVars.VITE_APP_LINKER_API || ''
  currentLinkerApi.value = envVars.VITE_APP_LINKER_API || ''

  // 获取gRPC连接状态
  getGrpcStatus()
  // 获取中台版本信息
  getServerVersion()
})
</script>

<style lang="scss" scoped>
.middle-platform-setting {
  @apply max-w-3xl mx-auto;
}
</style>
