// 数据集工具函数
export interface DatasetRatios {
  train: number
  test: number
  val: number
  support: number
}

export interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][] | null // 允许为 null
    error: any
  }
}

export interface DatasetData {
  allData: DatasetItem[] // 动态合并的所有数据，用于显示
  trainData: DatasetItem[]
  testData: DatasetItem[]
  valData: DatasetItem[]
  supportData: DatasetItem[]
}

/**
 * 检查是否有任何数据集数据
 */
export const hasAnyDatasetData = (datasetData: DatasetData): boolean => {
  return (
    datasetData.trainData.length > 0 ||
    datasetData.testData.length > 0 ||
    datasetData.valData.length > 0 ||
    datasetData.supportData.length > 0
  )
}

/**
 * 检查是否有原始数据
 */
export const hasRawData = (result: any): boolean => {
  return result && Array.isArray(result) && result.length > 0
}

/**
 * 检查是否有任何可用数据（数据集数据或原始数据）
 */
export const hasAnyAvailableData = (datasetData: DatasetData, rawResult: any): boolean => {
  return hasAnyDatasetData(datasetData) || hasRawData(rawResult)
}

// 存储原始数据的 Map，避免在响应式对象中存储大量数据
const originalDataMap = new Map<string, any>()

/**
 * 创建数据集项 - 优化版本，不在响应式对象中存储大量数组数据
 */
export const createDatasetItem = (
  item: any,
  index: number,
  type: DatasetItem['type'] = 'train',
): DatasetItem => {
  const fileName = item.file_path && item.file_path.split('/').pop()
  const itemId = `dataset_${index}`

  // 计算循环范围
  let startCycle = 1
  let endCycle = 100

  if (item.cycle_capacity_array && Array.isArray(item.cycle_capacity_array)) {
    const cycleArray = item.cycle_capacity_array
    startCycle = cycleArray[0]?.[0] || 1
    endCycle = cycleArray[cycleArray.length - 1]?.[0] || 100
  }

  // 将原始数据存储在 Map 中，而不是响应式对象中
  originalDataMap.set(itemId, {
    file_path: item.file_path,
    cycle_capacity_array: item.cycle_capacity_array,
    error: item.error,
  })

  return {
    id: itemId,
    name: fileName || `数据集_${index + 1}`,
    type,
    selected: true,
    isRecommended: false,
    startCycle,
    endCycle,
    rawData: {
      file_path: item.file_path,
      cycle_capacity_array: null, // 不直接存储大数组
      error: item.error,
    },
  }
}

/**
 * 获取数据集项的图表数据（懒加载）
 */
export const getChartDataForItem = (itemId: string): [number, number][] => {
  const originalData = originalDataMap.get(itemId)
  return originalData?.cycle_capacity_array || []
}

/**
 * 获取数据集项的完整原始数据
 */
export const getRawDataForItem = (itemId: string) => {
  return originalDataMap.get(itemId)
}

/**
 * 清理原始数据缓存
 */
export const clearOriginalDataCache = () => {
  originalDataMap.clear()
}

/**
 * 根据比例分配数据集
 */
export const distributeDatasetByRatios = (
  items: DatasetItem[],
  ratios: DatasetRatios,
): DatasetData => {
  const totalCount = items.length
  const trainCount = Math.floor((totalCount * ratios.train) / 100)
  const testCount = Math.floor((totalCount * ratios.test) / 100)
  const valCount = Math.floor((totalCount * ratios.val) / 100)
  const supportCount = totalCount - trainCount - testCount - valCount

  let currentIndex = 0

  const trainData = items.slice(currentIndex, currentIndex + trainCount).map((item) => ({
    ...item,
    type: 'train' as const,
    isRecommended: false,
  }))
  currentIndex += trainCount

  const testData = items.slice(currentIndex, currentIndex + testCount).map((item) => ({
    ...item,
    type: 'test' as const,
    isRecommended: false,
  }))
  currentIndex += testCount

  const valData = items.slice(currentIndex, currentIndex + valCount).map((item) => ({
    ...item,
    type: 'val' as const,
    isRecommended: false,
  }))
  currentIndex += valCount

  const supportData = items.slice(currentIndex).map((item) => ({
    ...item,
    type: 'support' as const,
    isRecommended: false,
  }))

  return {
    allData: [], // 动态计算，不在这里设置
    trainData,
    testData,
    valData,
    supportData,
  }
}

// 数据缓存 - 避免重复处理相同数据
const datasetCache = new Map<string, DatasetData>()

/**
 * 将API结果转换为数据集格式
 */
export const convertApiDataToDataset = (
  apiResult: any[],
  ratios: DatasetRatios = { train: 60, test: 20, val: 10, support: 10 },
): DatasetData => {
  if (!apiResult || !Array.isArray(apiResult)) {
    return {
      allData: [],
      trainData: [],
      testData: [],
      valData: [],
      supportData: [],
    }
  }

  // 生成缓存键
  const cacheKey = `${JSON.stringify(apiResult.map((item) => item.file_path))}_${JSON.stringify(ratios)}`

  // 检查缓存
  if (datasetCache.has(cacheKey)) {
    return datasetCache.get(cacheKey)!
  }

  // 创建数据集项
  const datasetItems = apiResult.map((item, index) => createDatasetItem(item, index))

  // 根据比例分配
  const result = distributeDatasetByRatios(datasetItems, ratios)

  // 缓存结果
  datasetCache.set(cacheKey, result)

  // 限制缓存大小
  if (datasetCache.size > 10) {
    const firstKey = datasetCache.keys().next().value
    if (firstKey) {
      datasetCache.delete(firstKey)
    }
  }

  return result
}

/**
 * 生成训练所需的数据格式 - 优化版本，使用原始数据 Map
 */
export const generateTrainingData = (datasetData: DatasetData) => {
  const formatDatasetItems = (items: DatasetItem[]) => {
    return items
      .filter((item) => item.selected)
      .map((item) => {
        const originalData = getRawDataForItem(item.id)
        return {
          file_path: originalData?.file_path || item.rawData?.file_path || '',
          start_cycle: item.startCycle || 1,
          end_cycle: item.endCycle || 100,
        }
      })
      .filter((item) => item.file_path) // 过滤掉没有文件路径的项
  }

  return {
    All_data: [], // 通常为空，根据需要可以合并所有数据
    train_data: formatDatasetItems(datasetData.trainData),
    test_data: formatDatasetItems(datasetData.testData),
    val_data: formatDatasetItems(datasetData.valData),
    support_data: formatDatasetItems(datasetData.supportData),
  }
}
