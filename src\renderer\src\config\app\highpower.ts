/**
 * Highpower 应用配置
 * 豪鹏电池寿命预测软件配置
 */

import { APP_TYPES, type AppConfig } from './types'

export const highpowerConfig: AppConfig = {
  type: APP_TYPES.HIGHPOWER,
  meta: {
    name: 'highpower',
    title: '豪鹏电池寿命预测软件',
    description: '专业的电池寿命预测分析软件',
    version: '1.0.0',
    icon: 'highpower.ico',
    logo: 'highpower.png',
    author: 'Highpower',
    homepage: 'https://highpower.com',
  },
  features: {
    // 布局配置
    layout: {
      showSidebar: false, // 不显示侧边栏
      showTitleBar: true, // 显示标题栏
      showStatusBar: false, // 不显示状态栏
    },

    // 侧边栏菜单配置
    sidebar: {
      showSidebar: false, // 全局控制侧边栏显示/隐藏
      menuItems: {
        // 主菜单项配置
        workflow: true, // 工作流
        task: false, // 任务
        server: false, // 服务器
        logger: false, // 日志
        tools: false, // 工具
        setting: true, // 设置
        // 底部菜单项配置
        ai: false, // AI（禁用）
        user: false, // 用户
        about: true, // 关于
      },
    },

    // 工作流编辑器配置
    workflowEditor: {
      showNavbar: false, // 不显示工作流导航栏
      showAIFloatingBox: false, // 不显示AI浮动框
      showToolbar: true, // 显示工具栏
      showMiniMap: false, // 不显示小地图
      showLeftControls: false, // 不显示左侧控制面板
      showRightControls: true, // 显示右侧控制面板
      enableNodeDrag: true, // 启用节点拖拽
      enableNodeResize: false, // 不启用节点调整大小
      showBackground: true, // 显示背景
      backgroundType: 'lines', // 背景类型：线条
    },

    // 导航配置
    navigation: {
      showMainNavigation: false, // 不显示主导航
      showBreadcrumb: false, // 不显示面包屑
      enableRouteGuard: true, // 启用路由守卫
      defaultRoute: '/workflow/editor/workflow-default', // 默认路由：直接进入工作流
    },

    // 节点工具栏配置
    nodeToolbar: {
      showNodeToolbar: true, // 显示节点工具栏
      enableNodeCategories: [
        // 只允许电池模拟分类
        'batterySimulation', // 电池模拟
      ],
      enableCustomNodes: false, // 不启用自定义节点
    },

    // 认证配置
    auth: {
      enableAuth: false, // 不启用认证系统
      showLoginForm: false, // 不显示登录表单
      showRegisterForm: false, // 不显示注册表单
      enableGuestMode: true, // 启用访客模式
    },

    // 主题配置
    theme: {
      enableThemeSwitch: false, // 不启用主题切换
      enableDarkMode: false, // 不启用暗色模式
      defaultTheme: 'city-light', // 默认主题：浅色
      allowedThemes: [
        // 只允许一个主题
        'city-light',
      ],
    },

    // 工具配置
    tools: {
      showToolsPanel: false, // 不显示工具面板
      enabledTools: [], // 不启用额外工具
    },

    // 其他功能配置
    features: {
      enableI18n: false, // 不启用国际化
      enableNotifications: false, // 不启用通知
      enableHotkeys: true, // 启用快捷键
      enableAutoSave: true, // 启用自动保存
      enableExport: true, // 启用导出功能
      enableImport: false, // 不启用导入功能
    },
  },
}
