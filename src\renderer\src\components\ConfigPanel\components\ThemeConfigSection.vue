<template>
  <div class="space-y-4">
    <!-- 主题配置 -->
    <ConfigGroup title="主题设置" icon="Palette">
      <ConfigItem
        label="主题切换"
        :enabled="themeConfig.enableThemeSwitch"
        description="允许用户切换主题"
        :editable="true"
        config-path="features.theme.enableThemeSwitch"
        @toggle="handleConfigToggle"
      />
      <ConfigItem
        label="暗色模式"
        :enabled="themeConfig.enableDarkMode"
        description="启用暗色模式支持"
        :editable="true"
        config-path="features.theme.enableDarkMode"
        @toggle="handleConfigToggle"
      />

      <!-- 主题模式切换 -->
      <Card
        :class="[
          'mt-3 border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-900/20',
          !isThemeSwitchEnabled && 'opacity-50',
        ]"
      >
        <CardContent class="p-3">
          <div class="text-xs text-indigo-700 dark:text-indigo-300 mb-3">
            主题模式
            <span v-if="!isThemeSwitchEnabled" class="text-orange-600 dark:text-orange-400 ml-2">
              (已禁用)
            </span>
          </div>
          <div class="grid grid-cols-3 gap-2">
            <Button
              v-for="mode in themeModes"
              :key="mode.value"
              :variant="currentThemeMode === mode.value ? 'default' : 'outline'"
              size="sm"
              class="h-8 text-xs"
              :disabled="!isThemeSwitchEnabled || (mode.value !== 'light' && !isDarkModeEnabled)"
              @click="handleThemeModeChange(mode.value)"
            >
              <component :is="mode.icon" class="h-3 w-3 mr-1" />
              {{ mode.label }}
            </Button>
          </div>
          <div class="text-xs text-muted-foreground mt-2">
            当前: {{ getCurrentModeLabel() }}
            <span v-if="!isThemeSwitchEnabled" class="text-orange-600 dark:text-orange-400 ml-2">
              - 主题切换已禁用
            </span>
            <span v-else-if="!isDarkModeEnabled" class="text-orange-600 dark:text-orange-400 ml-2">
              - 暗色模式已禁用
            </span>
          </div>
        </CardContent>
      </Card>
    </ConfigGroup>

    <!-- 可用主题列表 -->
    <Card :class="['border-muted', !isThemeSwitchEnabled && 'opacity-50']">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Palette class="h-4 w-4 mr-2" />
          可用主题
          <span
            v-if="!isThemeSwitchEnabled"
            class="text-orange-600 dark:text-orange-400 ml-2 text-xs"
          >
            (已禁用)
          </span>
        </CardTitle>
        <div class="text-xs text-muted-foreground">
          <span v-if="isThemeSwitchEnabled">
            点击主题卡片可切换主题，当前显示 {{ currentActualMode }} 模式的主题
          </span>
          <span v-else class="text-orange-600 dark:text-orange-400">
            主题切换功能已禁用，无法更改主题
          </span>
        </div>
      </CardHeader>
      <CardContent class="space-y-2">
        <Card
          v-for="theme in filteredThemes"
          :key="theme.value"
          :class="[
            'transition-all',
            isThemeSwitchEnabled ? 'cursor-pointer hover:shadow-md' : 'cursor-not-allowed',
            currentTheme === theme.value
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md'
              : isThemeSwitchEnabled
                ? 'hover:border-muted-foreground/50'
                : '',
            !isThemeSwitchEnabled && 'opacity-60',
          ]"
          @click="isThemeSwitchEnabled ? handleThemeChange(theme.value) : null"
        >
          <CardContent class="flex items-center justify-between p-3">
            <div class="flex items-center space-x-3">
              <!-- 主题颜色预览 -->
              <div class="flex space-x-1">
                <div
                  :style="{ backgroundColor: theme.previewColor }"
                  class="w-4 h-4 rounded-full border border-muted-foreground/20"
                ></div>
              </div>

              <div>
                <div class="text-sm font-medium">
                  {{ theme.label }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ getThemeDescription(theme.value) }}
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <!-- 当前主题标识 -->
              <Badge v-if="currentTheme === theme.value" variant="default" class="text-xs">
                当前
              </Badge>

              <!-- 主题类型标识 -->
              <Badge :variant="theme.type === 'dark' ? 'secondary' : 'outline'" class="text-xs">
                {{ theme.type === 'dark' ? '暗色' : '浅色' }}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>

    <!-- 主题预览 -->
    <Card class="border-muted">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <Eye class="h-4 w-4 mr-2" />
          主题预览
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <!-- 当前主题详细信息 -->
        <Card>
          <CardContent class="p-3">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium">当前主题信息</span>
              <div class="flex items-center space-x-2">
                <Badge variant="outline" class="text-xs">
                  {{ getCurrentModeLabel() }}
                </Badge>
                <Badge
                  :variant="currentActualMode === 'dark' ? 'secondary' : 'outline'"
                  class="text-xs"
                >
                  {{ currentActualMode === 'dark' ? '暗色' : '浅色' }}
                </Badge>
              </div>
            </div>

            <!-- 主题名称和描述 -->
            <div class="mb-3">
              <div class="text-sm font-medium text-foreground">
                {{ currentThemeInfo?.label || '默认主题' }}
              </div>
              <div class="text-xs text-muted-foreground">
                {{ getThemeDescription(currentTheme || '') }}
              </div>
            </div>

            <!-- 主题颜色预览 -->
            <div class="space-y-2">
              <div class="text-xs text-muted-foreground">主题色彩</div>
              <div class="flex space-x-2">
                <div
                  class="flex-1 h-3 rounded-sm border border-muted-foreground/20"
                  :style="{ backgroundColor: currentThemeInfo?.previewColor || '#3b82f6' }"
                  :title="`主色调: ${currentThemeInfo?.previewColor || '#3b82f6'}`"
                ></div>
                <div
                  class="flex-1 h-3 rounded-sm border border-muted-foreground/20"
                  :style="{ backgroundColor: getSecondaryColor() }"
                  :title="`辅助色: ${getSecondaryColor()}`"
                ></div>
                <div
                  class="flex-1 h-3 rounded-sm border border-muted-foreground/20"
                  :style="{ backgroundColor: getAccentColor() }"
                  :title="`强调色: ${getAccentColor()}`"
                ></div>
                <div
                  class="flex-1 h-3 rounded-sm border border-muted-foreground/20"
                  :style="{ backgroundColor: getWarningColor() }"
                  :title="`警告色: ${getWarningColor()}`"
                ></div>
              </div>
            </div>

            <!-- 主题模式信息 -->
            <div class="mt-3 pt-3 border-t border-muted">
              <div class="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <span class="text-muted-foreground">模式设置:</span>
                  <span class="ml-1 font-medium">{{ getCurrentModeLabel() }}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">实际模式:</span>
                  <span class="ml-1 font-medium">
                    {{ currentActualMode === 'dark' ? '暗色' : '浅色' }}
                  </span>
                </div>
                <div>
                  <span class="text-muted-foreground">主题值:</span>
                  <span class="ml-1 font-mono text-xs">{{ currentTheme || 'default' }}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">系统偏好:</span>
                  <span class="ml-1 font-medium">
                    {{ settingsStore.systemPrefersDark ? '暗色' : '浅色' }}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 主题统计和快速操作 -->
        <div class="grid grid-cols-2 gap-3">
          <!-- 主题统计 -->
          <Card>
            <CardContent class="p-3">
              <div class="text-xs text-muted-foreground mb-2">主题统计</div>
              <div class="grid grid-cols-2 gap-2 text-xs">
                <div class="text-center">
                  <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                    {{ settingsStore.allThemeColors.length }}
                  </div>
                  <div class="text-muted-foreground">总主题</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-green-600 dark:text-green-400">
                    {{ filteredThemes.length }}
                  </div>
                  <div class="text-muted-foreground">可用主题</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 快速操作 -->
          <Card>
            <CardContent class="p-3">
              <div class="text-xs text-muted-foreground mb-2">快速操作</div>
              <div class="space-y-1">
                <Button
                  size="sm"
                  variant="outline"
                  class="w-full h-6 text-xs"
                  :disabled="!isThemeSwitchEnabled"
                  @click="handleQuickToggleMode"
                >
                  切换模式
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  class="w-full h-6 text-xs"
                  :disabled="!isThemeSwitchEnabled"
                  @click="handleRandomTheme"
                >
                  随机主题
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Button } from '@renderer/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import { useSettingsStore } from '@renderer/store'
import { Eye, Monitor, Moon, Palette, Sun } from 'lucide-vue-next'
import { computed } from 'vue'
import { toast } from 'vue-sonner'
import ConfigGroup from './ConfigGroup.vue'
import ConfigItem from './ConfigItem.vue'

const { themeConfig, updateConfigValue } = useAppConfig()
const settingsStore = useSettingsStore()

// 主题模式选项
const themeModes = [
  { value: 'light', label: '浅色', icon: Sun },
  { value: 'dark', label: '暗色', icon: Moon },
  { value: 'auto', label: '自动', icon: Monitor },
]

// 当前主题模式
const currentThemeMode = computed(() => settingsStore.themeMode)
const currentActualMode = computed(() => settingsStore.currentActualMode)
const currentTheme = computed(() => settingsStore.theme)

// 过滤后的主题（根据当前模式）
const filteredThemes = computed(() => settingsStore.filteredThemeColors)

// 检查主题功能是否启用
const isThemeSwitchEnabled = computed(() => themeConfig.value.enableThemeSwitch)
const isDarkModeEnabled = computed(() => themeConfig.value.enableDarkMode)

// 当前主题信息
const currentThemeInfo = computed(() => {
  return (
    filteredThemes.value.find((t) => t.value === currentTheme.value) ||
    settingsStore.allThemeColors.find((t) => t.value === currentTheme.value)
  )
})

// 处理配置切换
const handleConfigToggle = (configPath: string, newValue: boolean) => {
  try {
    updateConfigValue(configPath, newValue)
    toast.success(`配置已更新: ${configPath.split('.').pop()} = ${newValue ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('配置更新失败:', error)
    toast.error(`配置更新失败: ${error}`)
  }
}

// 处理主题模式切换
const handleThemeModeChange = (mode: 'light' | 'dark' | 'auto') => {
  if (!isThemeSwitchEnabled.value) {
    toast.error('主题切换功能已禁用')
    return
  }

  if (mode !== 'light' && !isDarkModeEnabled.value) {
    toast.error('暗色模式功能已禁用')
    return
  }

  try {
    settingsStore.setThemeMode(mode)
    toast.success(`主题模式已切换到: ${themeModes.find((m) => m.value === mode)?.label}`)
  } catch (error) {
    console.error('主题模式切换失败:', error)
    toast.error(`主题模式切换失败: ${error}`)
  }
}

// 处理主题切换
const handleThemeChange = (themeValue: string) => {
  if (!isThemeSwitchEnabled.value) {
    toast.error('主题切换功能已禁用')
    return
  }

  try {
    settingsStore.setTheme(themeValue)
    const themeName = filteredThemes.value.find((t) => t.value === themeValue)?.label
    toast.success(`主题已切换到: ${themeName}`)
  } catch (error) {
    console.error('主题切换失败:', error)
    toast.error(`主题切换失败: ${error}`)
  }
}

// 获取当前模式标签
const getCurrentModeLabel = () => {
  const mode = themeModes.find((m) => m.value === currentThemeMode.value)
  return mode ? mode.label : '未知'
}

// 暗色主题数量
const darkThemeCount = computed(() => {
  return settingsStore.allThemeColors.filter((theme) => theme.type === 'dark').length
})

// 主题描述
const getThemeDescription = (theme: string): string => {
  const descriptionMap: Record<string, string> = {
    'city-light': '现代都市风格，简洁明亮',
    'city-dark': '现代都市风格，深色护眼',
    'forest-light': '自然森林风格，清新舒适',
    'forest-dark': '自然森林风格，深邃宁静',
    'lake-light': '湖泊风格，宁静优雅',
    'lake-dark': '湖泊风格，深邃神秘',
    'desert-light': '沙漠风格，温暖明亮',
    'desert-dark': '沙漠风格，温暖深邃',
    'farm-light': '农场风格，自然清新',
    'farm-dark': '农场风格，自然深沉',
    'garden-light': '花园风格，优雅明亮',
    'garden-dark': '花园风格，优雅神秘',
  }
  return descriptionMap[theme] || '自定义主题'
}

// 获取主题相关颜色
const getSecondaryColor = (): string => {
  const baseColor = currentThemeInfo.value?.previewColor || '#3b82f6'
  // 根据主题类型生成辅助色
  if (currentActualMode.value === 'dark') {
    return adjustColorBrightness(baseColor, -20)
  } else {
    return adjustColorBrightness(baseColor, 20)
  }
}

const getAccentColor = (): string => {
  const themeColorMap: Record<string, string> = {
    'city-light': '#10b981',
    'city-dark': '#059669',
    'forest-light': '#84cc16',
    'forest-dark': '#65a30d',
    'lake-light': '#0ea5e9',
    'lake-dark': '#0284c7',
    'desert-light': '#f59e0b',
    'desert-dark': '#d97706',
    'farm-light': '#22c55e',
    'farm-dark': '#16a34a',
    'garden-light': '#8b5cf6',
    'garden-dark': '#7c3aed',
  }
  return themeColorMap[currentTheme.value || ''] || '#10b981'
}

const getWarningColor = (): string => {
  return currentActualMode.value === 'dark' ? '#dc2626' : '#ef4444'
}

// 调整颜色亮度的辅助函数
const adjustColorBrightness = (color: string, percent: number): string => {
  const num = parseInt(color.replace('#', ''), 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) + amt
  const G = ((num >> 8) & 0x00ff) + amt
  const B = (num & 0x0000ff) + amt
  return (
    '#' +
    (
      0x1000000 +
      (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
      (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
      (B < 255 ? (B < 1 ? 0 : B) : 255)
    )
      .toString(16)
      .slice(1)
  )
}

// 快速操作方法
const handleQuickToggleMode = () => {
  if (!isThemeSwitchEnabled.value) {
    toast.error('主题切换功能已禁用')
    return
  }

  const currentMode = currentThemeMode.value
  let nextMode: 'light' | 'dark' | 'auto'

  if (currentMode === 'light') {
    nextMode = isDarkModeEnabled.value ? 'dark' : 'auto'
  } else if (currentMode === 'dark') {
    nextMode = 'auto'
  } else {
    nextMode = 'light'
  }

  handleThemeModeChange(nextMode)
}

const handleRandomTheme = () => {
  if (!isThemeSwitchEnabled.value) {
    toast.error('主题切换功能已禁用')
    return
  }

  const availableThemes = filteredThemes.value.filter((t) => t.value !== currentTheme.value)
  if (availableThemes.length === 0) {
    toast.info('没有其他可用主题')
    return
  }

  const randomTheme = availableThemes[Math.floor(Math.random() * availableThemes.length)]
  handleThemeChange(randomTheme.value)
}
</script>
