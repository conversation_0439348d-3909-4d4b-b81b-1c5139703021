<template>
  <div class="rounded-md border">
    <!-- <div class="flex justify-end mb-2" v-if="showColumnSelector">
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm">
            <Settings class="h-4 w-4 mr-2" />
            列设置
          </Button>
        </PopoverTrigger>
        <PopoverContent class="w-80">
          <div class="space-y-2">
            <h4 class="font-medium">显示列</h4>
            <div class="grid grid-cols-2 gap-2">
              <div v-for="column in processedColumns" :key="column.field" class="flex items-center space-x-2">
                <Checkbox
                  :id="`col-${column.field}`"
                  :checked="!hiddenColumns.includes(column.field)"
                  @update:checked="(checked) => toggleColumn(column.field, checked)"
                />
                <label :for="`col-${column.field}`" class="text-sm cursor-pointer">{{ column.title }}</label>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div> -->

    <div class="relative overflow-auto" :style="{ maxHeight }">
      <Loading v-if="loading" />
      <Table v-else class="min-w-full">
        <TableHeader>
          <TableRow>
            <TableHead
              v-for="column in visibleColumns"
              :key="column.field"
              :class="[
                column.headerClassName,
                column.fixed
                  ? 'sticky z-10 right-0 bg-white dark:bg-gray-950 shadow-[-4px_0_4px_rgba(0,0,0,0.05)]'
                  : '',
              ]"
              :style="{
                minWidth: column.minWidth,
                right: column.fixed ? '0' : 'auto',
              }"
            >
              {{ column.title }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="data.length === 0">
            <TableCell :colspan="visibleColumns.length" class="text-center py-8">
              {{ emptyText }}
            </TableCell>
          </TableRow>
          <template v-else>
            <TableRow v-for="(item, index) in data" :key="getItemKey(item, index)">
              <template v-for="column in visibleColumns" :key="column.field">
                <!-- 使用插槽 -->
                <slot
                  v-if="column.useSlot"
                  :name="column.field"
                  :item="item"
                  :index="index"
                  :column="column"
                ></slot>

                <!-- 普通单元格 -->
                <TableCell
                  v-else
                  v-tooltip="
                    column.showTooltip
                      ? column.formatter
                        ? column.formatter(getFieldValue(item, column.field), item, column)
                        : getFieldValue(item, column.field)
                      : ''
                  "
                  :class="[
                    column.cellClassName,
                    column.ellipsis ? 'truncate' : '',
                    column.copyable ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800' : '',
                    column.fixed
                      ? 'sticky z-10 right-0 bg-white dark:bg-gray-950 shadow-[-4px_0_4px_rgba(0,0,0,0.05)]'
                      : '',
                  ]"
                  :style="{
                    maxWidth: column.width,
                    minWidth: column.minWidth,
                    right: column.fixed ? '0' : 'auto',
                  }"
                  @click="
                    column.copyable
                      ? copyToClipboard(
                          column.formatter
                            ? column.formatter(getFieldValue(item, column.field), item, column)
                            : getFieldValue(item, column.field),
                          column.title,
                        )
                      : null
                  "
                >
                  {{
                    column.formatter
                      ? column.formatter(getFieldValue(item, column.field), item, column)
                      : getFieldValue(item, column.field) || column.defaultValue || '-'
                  }}
                </TableCell>
              </template>
            </TableRow>
          </template>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Loading } from '@renderer/components'
import { toast } from 'vue-sonner'
// import { Settings } from 'lucide-vue-next'
import vTooltip from '@renderer/config/directives/VTooltip'
import { computed, onMounted, ref, watch } from 'vue'
interface ColumnConfig {
  title: string // 列标题
  field: string // 数据字段名
  headerClassName?: string // 表头样式类
  cellClassName?: string // 单元格样式类
  width?: string // 单元格宽度
  minWidth?: string // 单元格最小宽度
  ellipsis?: boolean // 是否显示省略号
  showTooltip?: boolean // 是否显示提示
  useSlot?: boolean // 是否使用插槽
  defaultValue?: string // 默认值
  copyable?: boolean // 是否可复制
  fixed?: boolean // 是否固定列
  formatter?: (value: any, row: any, column: ColumnConfig) => any // 格式化函数
}

// 默认列配置
const defaultColumnConfig = {
  headerClassName: '', //w-32 truncate
  cellClassName: '', //max-w-32 truncate
  ellipsis: true,
  showTooltip: true,
  useSlot: false,
  copyable: false,
  minWidth: '', //80px
  fixed: false,
}

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array as () => ColumnConfig[],
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  emptyText: {
    type: String,
    default: '暂无数据',
  },
  keyField: {
    type: String,
    default: 'id',
  },
  maxHeight: {
    type: String,
    default: 'auto',
  },
  showColumnSelector: {
    type: Boolean,
    default: true,
  },
  tableId: {
    type: String,
    default: 'default',
  },
})

// 隐藏的列
const hiddenColumns = ref<string[]>([])

// 处理列配置，应用默认值
const processedColumns = computed(() => {
  return props.columns.map((column) => {
    return {
      ...defaultColumnConfig,
      ...column,
    }
  })
})

// 可见的列
const visibleColumns = computed(() => {
  return processedColumns.value.filter((column) => !hiddenColumns.value.includes(column.field))
})

// 获取存储键名
const getStorageKey = () => {
  return `dataTable_hiddenColumns_${props.tableId}`
}

// 切换列的显示/隐藏状态
// const toggleColumn = (field: string, checked: boolean) => {
//   console.log(`切换列 ${field}, 状态: ${checked}`)

//   if (checked) {
//     // 如果需要显示，从隐藏列表中移除
//     hiddenColumns.value = hiddenColumns.value.filter((col) => col !== field)
//   } else {
//     // 如果需要隐藏，且当前不在隐藏列表中，则添加到隐藏列表
//     if (!hiddenColumns.value.includes(field)) {
//       hiddenColumns.value.push(field)
//     }
//   }

//   // 保存用户选择状态到本地存储
//   try {
//     localStorage.setItem(getStorageKey(), JSON.stringify(hiddenColumns.value))
//     console.log(`已保存列设置，隐藏列:`, hiddenColumns.value)
//   } catch (error) {
//     console.error('保存列设置失败:', error)
//   }
// }

// 从本地存储加载用户的列显示偏好
const loadUserColumnPreferences = () => {
  try {
    const savedHiddenColumns = localStorage.getItem(getStorageKey())
    if (savedHiddenColumns) {
      hiddenColumns.value = JSON.parse(savedHiddenColumns)
      console.log('已加载列设置:', hiddenColumns.value)
    } else {
      // 如果没有保存的设置，确保所有列都显示
      hiddenColumns.value = []
    }
  } catch (error) {
    console.error('加载列显示偏好失败:', error)
    // 出错时也确保所有列都显示
    hiddenColumns.value = []
  }
}

// 获取项目的唯一键
const getItemKey = (item: any, index: number) => {
  return item[props.keyField] || index
}

// 获取字段值，支持嵌套属性 (如 'rawData.task_pid')
const getFieldValue = (item: any, field: string) => {
  if (!field) return ''

  const fields = field.split('.')
  let value = item

  for (const f of fields) {
    if (value === null || value === undefined) return ''
    value = value[f]
  }

  return value
}

// 复制到剪贴板
const copyToClipboard = async (text: string, fieldName: string) => {
  if (!text) return

  try {
    await navigator.clipboard.writeText(text)
    toast.success('复制成功')
  } catch (err) {
    toast.error('复制失败', {
      description: '请手动复制内容',
      duration: 3000,
    })
  }
}
// 监听 columns 变化，确保新添加的列默认显示
watch(
  () => props.columns,
  () => {
    console.log('列配置变化，重新检查显示状态')
    // 确保所有新列默认显示
    const savedHiddenColumns = localStorage.getItem(getStorageKey())
    if (!savedHiddenColumns) {
      hiddenColumns.value = []
    }
  },
  { immediate: true },
)
onMounted(() => {
  hiddenColumns.value = []
  loadUserColumnPreferences()
})
</script>

<style lang="scss" scoped>
:deep(table) {
  table-layout: fixed;
  width: 100%;
  max-width: max-content;
  /* min-width: 1200px; */
}

:deep(th),
:deep(td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保滚动条显示正常 */
.overflow-auto {
  scrollbar-width: thin;
  -webkit-overflow-scrolling: touch;
}

.overflow-auto::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
