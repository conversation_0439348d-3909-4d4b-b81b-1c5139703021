#!/usr/bin/env node

/**
 * encode_model.js
 *
 * Usage:
 *   node encode_model.js <input_json_path> <output_model_filename>
 *
 * Description:
 *   This script reads a JSON file, base64-encodes its content,
 *   wraps it in a MessagePack-encoded object under the key "data",
 *   and writes the result to a binary .model file.
 *
 * Example:
 *   node encode_model.js ./input.json result.model
 *   → Generates ./result.model in the same directory as input.json
 */

const fs = require('fs')
const path = require('path')
const msgpack = require('msgpack5')()

const [, , inputPath, outputName] = process.argv

// Help message
if (!inputPath || !outputName || inputPath === '--help' || inputPath === '-h') {
  console.log(`
Example:
  node encode_model.js ./config.json model_output.model
`)
  process.exit(0)
}

// Check input file
if (!fs.existsSync(inputPath)) {
  console.error(`❌ File not found: ${inputPath}`)
  process.exit(1)
}

try {
  const jsonData = fs.readFileSync(inputPath, 'utf-8')
  const base64Encoded = Buffer.from(jsonData, 'utf-8').toString('base64')
  const binaryData = msgpack.encode({ data: base64Encoded })

  const outputPath = path.join(path.dirname(inputPath), outputName)
  fs.writeFileSync(outputPath, binaryData)

  console.log(`✅ Successfully saved to: ${outputPath}`)
} catch (err) {
  console.error('❌ Error:', err.message)
  process.exit(1)
}
