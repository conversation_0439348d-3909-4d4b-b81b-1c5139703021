import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { nanoid } from 'nanoid'
import { decode } from '@msgpack/msgpack'
import svg from '@renderer/config/constants/svg'
import { useToolsStore } from './tools'
import defaultModules from '@renderer/config/constants/defaultModules'

// 节点模块类型定义
export interface NodeModule {
  name: string
  icon: string
  type: string
  description?: string
  categories: Array<{
    name: string
    nodes: Array<any>
  }>
  enabled?: boolean
  isBuiltin?: boolean
}

// 解析 msgpack 数据
const tryDecodeMsgPack = async (
  file: File,
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const buffer = await file.arrayBuffer()
    try {
      const decodedData = decode(new Uint8Array(buffer))
      if (decodedData && decodedData.data && typeof decodedData.data === 'string') {
        try {
          const jsonString = decodeURIComponent(escape(atob(decodedData.data)))
          const jsonData = JSON.parse(jsonString)
          return { success: true, data: jsonData }
        } catch (e) {
          console.warn('Base64 解码或 JSON 解析失败:', e)
        }
      }
      if (typeof decodedData === 'object' && decodedData !== null) {
        return { success: true, data: decodedData }
      }
    } catch (e1) {
      console.warn('文件解析失败，尝试其他方法:', e1)
    }
    try {
      const text = new TextDecoder().decode(buffer)
      if (text.trim().startsWith('{') && text.trim().endsWith('}')) {
        const jsonData = JSON.parse(text)
        return { success: true, data: jsonData }
      }
    } catch (e2) {
      console.warn('尝试解析为JSON失败:', e2)
    }
    return {
      success: false,
      error: '无法解析文件格式，请确保文件格式正确',
    }
  } catch (error) {
    console.error('文件解析错误:', error)
    return {
      success: false,
      error: `解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
    }
  }
}

// 解析 JSON 数据
const tryParseJSON = async (
  file: File,
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const content = await file.text()
    const data = JSON.parse(content)
    return { success: true, data }
  } catch (error) {
    console.error('JSON 解析错误:', error)
    return {
      success: false,
      error: '文件格式错误：请确保文件是有效的JSON格式',
    }
  }
}

export const useNodeModulesStore = defineStore(
  'nodeModules',
  () => {
    const toolsStore = useToolsStore()

    // 存储所有节点模块，初始为空
    const nodeModules = ref<Record<string, NodeModule>>({})

    // 初始化 nodeModules
    const initializeNodeModules = () => {
      const mergedModules: Record<string, NodeModule> = { ...defaultModules }

      // 只保留持久化状态中的非内置模块
      Object.entries(nodeModules.value).forEach(([key, persistedModule]) => {
        if (!mergedModules[key] || !mergedModules[key].isBuiltin) {
          mergedModules[key] = persistedModule
        }
      })

      // console.log('defaultModules:', defaultModules) // 调试：打印 defaultModules
      // console.log('初始化后的 nodeModules:', mergedModules) // 调试：打印结果
      nodeModules.value = mergedModules
    }

    // 立即调用初始化
    initializeNodeModules()

    // 获取所有已启用的节点模块
    const enabledNodeModules = computed(() => {
      const result: Record<string, NodeModule> = {}
      Object.entries(nodeModules.value).forEach(([key, module]) => {
        if (module.enabled) {
          result[key] = module
        }
      })
      return result
    })

    // 导入新的节点模块
    const importNodeModule = async (file: File): Promise<{ success: boolean; message: string }> => {
      try {
        const isMsgPack = file.name.endsWith('.plugin')
        let parseResult

        if (isMsgPack) {
          parseResult = await tryDecodeMsgPack(file)
        } else {
          parseResult = await tryParseJSON(file)
        }

        if (!parseResult.success) {
          return {
            success: false,
            message: parseResult.error || '文件解析失败',
          }
        }

        let moduleData = parseResult.data

        const keys = Object.keys(moduleData)
        if (keys.length === 1 && typeof moduleData[keys[0]] === 'object' && !moduleData.name) {
          const moduleName = keys[0]
          moduleData = {
            name: moduleName,
            ...moduleData[moduleName],
          }
        }

        if (!moduleData.name) {
          return { success: false, message: '缺少必要字段：name' }
        }

        if (!moduleData.type) {
          return { success: false, message: '缺少必要字段：type' }
        }

        if (!Array.isArray(moduleData.categories)) {
          return { success: false, message: '缺少必要字段：categories 或格式不正确' }
        }

        moduleData.categories.forEach((category: any) => {
          category.nodes.forEach((node: any) => {
            if (node.data?.icon?.type === 'svg' && typeof node.data.icon.value === 'string') {
              const svgKey = node.data.icon.value
              if (svg[svgKey]) {
                node.data.icon.value = svg[svgKey]
              } else {
                const svgPath = svgKey.split('.')
                if (svgPath.length === 2 && svgPath[0] === 'svg' && svg[svgPath[1]]) {
                  node.data.icon.value = svg[svgPath[1]]
                } else {
                  console.warn(`未找到SVG: ${svgKey}，将使用默认图标`)
                }
              }
            }

            if (!node.id) {
              node.id = nanoid()
            }
          })
        })

        nodeModules.value[moduleData.name] = {
          ...moduleData,
          enabled: true,
          isBuiltin: false,
        }

        updateToolsStatus(moduleData.type, true)

        return { success: true, message: `成功导入节点模块: ${moduleData.name}` }
      } catch (error) {
        console.error('导入节点模块失败:', error)
        return {
          success: false,
          message: `导入失败: ${error instanceof Error ? error.message : '未知错误'}`,
        }
      }
    }

    // 删除节点模块
    const deleteNodeModule = (moduleName: string): boolean => {
      if (nodeModules.value[moduleName]) {
        const moduleType = nodeModules.value[moduleName].type

        if (nodeModules.value[moduleName].isBuiltin) {
          nodeModules.value[moduleName].enabled = false
          updateToolsStatus(moduleType, false)
          return true
        } else {
          delete nodeModules.value[moduleName]
          const hasOtherSameTypeModule = Object.values(nodeModules.value).some(
            (module) => module.type === moduleType && module.enabled,
          )
          if (!hasOtherSameTypeModule) {
            removeToolStatus(moduleType)
          }
          return true
        }
      }
      return false
    }

    // 更新节点模块状态
    const updateNodeModuleStatus = (moduleName: string, enabled: boolean): boolean => {
      if (nodeModules.value[moduleName]) {
        nodeModules.value[moduleName].enabled = enabled
        const moduleType = nodeModules.value[moduleName].type
        if (!enabled) {
          const hasOtherEnabledModule = Object.values(nodeModules.value).some(
            (module) => module.type === moduleType && module.enabled && module.name !== moduleName,
          )
          updateToolsStatus(moduleType, hasOtherEnabledModule)
        } else {
          updateToolsStatus(moduleType, true)
        }
        return true
      }
      return false
    }

    // 同步更新工具状态的函数
    const updateToolsStatus = (toolType: string, enabled: boolean) => {
      try {
        if (enabled) {
          toolsStore.addTool(toolType, enabled)
        } else {
          const hasEnabledModule = Object.values(nodeModules.value).some(
            (module) => module.type === toolType && module.enabled,
          )
          if (!hasEnabledModule) {
            toolsStore.updateToolStatus(toolType, false)
          }
        }
      } catch (error) {
        console.error('更新工具状态失败:', error)
      }
    }

    // 从工具状态中移除工具
    const removeToolStatus = (toolType: string) => {
      try {
        const hasOtherModule = Object.values(nodeModules.value).some(
          (module) => module.type === toolType,
        )
        if (!hasOtherModule) {
          toolsStore.removeTool(toolType)
        }
      } catch (error) {
        console.error('从工具状态中移除工具失败:', error)
      }
    }

    return {
      nodeModules,
      enabledNodeModules,
      importNodeModule,
      deleteNodeModule,
      updateNodeModuleStatus,
    }
  },
  {
    persist: import.meta.env.VITE_APP_IS_MATT === '0' ? false : true,
  },
)
