import logger from '../utils/logger'

/**
 * 日志相关API
 */
export const loggerApi = {
  /**
   * 错误日志
   */
  error: (...args: any[]) => {
    logger.error(...args)
  },

  /**
   * 警告日志
   */
  warn: (...args: any[]) => {
    logger.warn(...args)
  },

  /**
   * 信息日志
   */
  info: (...args: any[]) => {
    logger.info(...args)
  },

  /**
   * 调试日志
   */
  debug: (...args: any[]) => {
    logger.debug(...args)
  },

  /**
   * 获取日志路径
   */
  getLogPath: () => {
    return logger.transports.file.getFile().path
  },
}
