<template>
  <Dialog :open="isOpen" @update:open="closeDialog">
    <DialogContent class="max-w-6xl mx-auto bg-background rounded-md shadow-sm border p-0">
      <!-- Header -->
      <div class="flex justify-between items-center p-4 border-b">
        <h1 class="text-xl font-bold text-foreground">日志</h1>
      </div>

      <!-- Search and filters -->
      <div class="px-2 flex flex-wrap gap-2">
        <div class="relative flex-grow max-w-xl">
          <Search
            class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"
          />
          <Input
            v-model="searchQuery"
            placeholder="搜索..."
            class="pl-10 bg-muted/50 border-gray-200"
          />
        </div>

        <!-- 创建日期 dropdown -->
        <DropdownMenu v-model:open="isDateDropdownOpen">
          <DropdownMenuTrigger as-child>
            <Button variant="outline" class="bg-muted/50 flex items-center gap-2 text-foreground">
              <div class="flex items-center">
                <Calendar class="h-4 w-4 mr-2" />
                {{ sortBy === '最新' ? '最新优先' : '最早优先' }}
              </div>
              <ChevronDown class="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent class="w-[200px] bg-popover text-popover-foreground">
            <DropdownMenuItem
              :class="{ 'bg-primary text-primary-foreground': sortBy === '最新' }"
              @click="sortBy = '最新'"
            >
              最新优先
            </DropdownMenuItem>
            <DropdownMenuItem
              :class="{ 'bg-primary text-primary-foreground': sortBy === '最早' }"
              @click="sortBy = '最早'"
            >
              最早优先
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <!-- 筛选 popover -->
        <Popover v-model:open="isFilterOpen">
          <PopoverTrigger as-child>
            <Button variant="outline" class="bg-muted/50 text-foreground">
              <Filter class="h-4 w-4 mr-2" />
              <span>筛选</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent class="w-[300px] p-0 bg-popover text-popover-foreground" align="start">
            <div class="p-4 border-b">
              <h3 class="font-bold text-lg mb-4">筛选</h3>
              <div class="space-y-4">
                <div>
                  <h4 class="text-sm text-muted-foreground mb-2">按状态</h4>
                  <RadioGroup v-model="statusFilter" class="flex flex-wrap gap-4">
                    <div class="flex items-center space-x-2">
                      <RadioGroupItem id="status-all" value="全部" class="text-foreground" />
                      <Label for="status-all">全部</Label>
                    </div>
                    <div
                      v-for="status in statusList"
                      :key="status"
                      class="flex items-center space-x-2"
                    >
                      <RadioGroupItem :id="`status-${status}`" :value="status" />
                      <Label :for="`status-${status}`" class="text-foreground">{{ status }}</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div>
                  <h4 class="text-sm text-muted-foreground mb-2">按日期</h4>
                  <Select v-model="dateFilter">
                    <SelectTrigger class="w-full bg-muted/50 text-foreground">
                      <SelectValue placeholder="全部" />
                    </SelectTrigger>
                    <SelectContent class="bg-popover text-popover-foreground">
                      <SelectItem value="全部">全部</SelectItem>
                      <SelectItem value="最近1天">最近1天</SelectItem>
                      <SelectItem value="最近7天">最近7天</SelectItem>
                      <SelectItem value="最近30天">最近30天</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Button
          variant="outline"
          class="bg-muted/50 text-foreground"
          @click="isDeleteAllDialogOpen = true"
        >
          <Trash2 class="h-4 w-4 mr-2" />
          <span>清除日志</span>
        </Button>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-t border-b bg-muted/50">
              <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                服务名称
              </th>
              <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">任务ID</th>
              <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">时间</th>
              <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">耗时</th>
              <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">状态</th>
              <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">进度</th>
              <th class="py-2 px-4 text-right text-sm font-medium text-muted-foreground">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="entry in paginatedLogs"
              :key="entry.taskId"
              class="border-t hover:bg-muted/30"
            >
              <td class="py-2 px-4 text-sm text-foreground">
                {{ entry.serviceId ? entry.serviceId.split('::')[1] || entry.serviceId : '--' }}
              </td>
              <td v-tooltip="entry.taskId" class="py-2 px-4 text-sm text-foreground">
                <div class="flex items-center">
                  <Hash class="h-4 w-4 mr-2 text-muted-foreground" />
                  <span class="truncate max-w-[100px]" :title="entry.taskId">
                    {{ entry.taskId || '--' }}
                  </span>
                </div>
              </td>
              <td class="py-2 px-4 text-sm text-foreground">
                <div class="flex items-center">
                  <Calendar class="h-4 w-4 mr-2 text-muted-foreground" />
                  {{ formatTimestamp(entry.createTime) }}
                </div>
              </td>
              <td class="py-2 px-4 text-sm text-foreground">
                <div class="flex items-center">
                  <Clock class="h-4 w-4 mr-2 text-muted-foreground" />
                  {{ formatDuration(entry.startTime, entry.endTime) }}
                </div>
              </td>
              <td class="py-2 px-4">
                <span
                  class="px-2 py-0.5 rounded-full text-sm"
                  :class="taskStore.getStatusClass(entry.taskStatus)"
                >
                  {{ taskStore.getStatusText(entry.taskStatus) }}
                </span>
              </td>
              <td class="py-2 px-4">
                <div class="flex flex-row items-center justify-center gap-1">
                  <span class="w-5/6">
                    <Progress :model-value="entry.taskProcess" class="w-full" />
                  </span>
                  <span class="w-1/6 text-xs text-muted-foreground">
                    {{
                      typeof entry.taskProcess === 'number' ? entry.taskProcess.toFixed(2) : '--'
                    }}%
                  </span>
                </div>
              </td>
              <td class="py-2 px-2 text-right flex justify-end">
                <div>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-7 w-7 text-foreground hover:text-accent-foreground hover:bg-accent"
                    title="详情"
                    @click="detailLog(entry.taskId)"
                  >
                    <ReceiptText class="h-4 w-4" />
                  </Button>
                </div>
                <div>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-7 w-7 text-destructive hover:text-destructive-foreground hover:bg-destructive/10"
                    title="删除"
                    :disabled="
                      ['计算中', '暂停'].includes(taskStore.getStatusText(entry.taskStatus))
                    "
                    @click="confirmDeleteLog(entry.taskId)"
                  >
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="p-2 border-t flex justify-between items-center text-sm">
        <div class="text-muted-foreground">显示 {{ pageSize }} 条 共 {{ totalLogs }} 条</div>
        <div class="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            class="h-7 w-7"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            <ChevronLeft class="h-4 w-4" />
          </Button>
          <span class="px-3 py-1 bg-muted rounded-md text-foreground">{{ currentPage }}</span>
          <span>共 {{ totalPages }} 页</span>
          <Button
            variant="outline"
            size="icon"
            class="h-7 w-7"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            <ChevronRight class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
  <LogDetail v-model:is-open="isDetailOpen" :log-id="currentLogId" />

  <AlertDialog v-model:open="isDeleteAllDialogOpen">
    <AlertDialogContent class="bg-background text-foreground">
      <AlertDialogHeader>
        <AlertDialogTitle>确认清除</AlertDialogTitle>
        <AlertDialogDescription class="text-muted-foreground">
          您确定要清除所有日志吗？此操作将删除所有日志记录，且无法撤销。
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="isDeleteAllDialogOpen = false">取消</AlertDialogCancel>
        <AlertDialogAction
          class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          @click="confirmClearLogs"
        >
          清除
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>

  <!-- 单条日志删除二次确认弹窗 -->
  <AlertDialog v-model:open="isDeleteConfirmDialogOpen">
    <AlertDialogContent class="bg-background text-foreground">
      <AlertDialogHeader>
        <AlertDialogTitle>确认删除</AlertDialogTitle>
        <AlertDialogDescription class="text-muted-foreground">
          您确定要删除该日志吗？此操作无法撤销。
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="isDeleteConfirmDialogOpen = false">取消</AlertDialogCancel>
        <AlertDialogAction
          class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          @click="handleDeleteLog"
        >
          删除
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { LogDetail } from '@renderer/components'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { useTaskStore } from '@renderer/store'
import { formatDuration, formatTimestamp } from '@renderer/utils/utils'
import {
  Calendar,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Clock,
  Filter,
  Hash,
  ReceiptText,
  Search,
  Trash2,
} from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'
import { toast } from 'vue-sonner'

const taskStore = useTaskStore()
const taskService = createTaskService()
const props = defineProps<{
  isOpen: boolean
  currentWorkflowId: string
}>()

const emit = defineEmits<{
  'update:isOpen': [value: boolean]
}>()

const isDetailOpen = ref(false)
const currentLogId = ref<string | undefined>(undefined)
const isDateDropdownOpen = ref(false)
const isFilterOpen = ref(false)
const isDeleteAllDialogOpen = ref(false)
const searchQuery = ref('')
const sortBy = ref('最新')
const statusFilter = ref('全部')
const dateFilter = ref('全部')
const currentPage = ref(1)
const pageSize = ref(10)
const logIdToDelete = ref<string | undefined>(undefined)
const isDeleteConfirmDialogOpen = ref(false)

const statusList = [
  '初始化中',
  '计算中',
  '等待调度',
  '暂停',
  '任务完成',
  '任务失败',
  '原始状态',
  '任务完成',
]
const DATE_RANGES = {
  全部: 0,
  最近1天: 24 * 60 * 60 * 1000,
  最近7天: 7 * 24 * 60 * 60 * 1000,
  最近30天: 30 * 24 * 60 * 60 * 1000,
} as const

const filteredLogs = computed(() => {
  return taskStore.tasks
    .filter((entry) => {
      // 按状态筛选
      if (statusFilter.value !== '全部') {
        const currentStatus = taskStore.getStatusText(entry.taskStatus)
        if (currentStatus !== statusFilter.value) {
          return false
        }
      }
      // 按日期筛选
      if (dateFilter.value !== '全部') {
        const now = new Date()
        now.setHours(0, 0, 0, 0)
        const range = DATE_RANGES[dateFilter.value as keyof typeof DATE_RANGES]
        const startTime = Date.now() - range
        if (Number(entry.createTime) * 1000 < startTime) {
          return false
        }
      }
      // 按搜索关键词筛选
      if (searchQuery.value) {
        const searchTerm = searchQuery.value.toLowerCase()
        return (
          (entry.serviceId && entry.serviceId.toLowerCase().includes(searchTerm)) ||
          (entry.taskId && entry.taskId.toLowerCase().includes(searchTerm))
        )
      }
      return true
    })
    .sort((a, b) => {
      if (sortBy.value === '最新') {
        return b.createTime - a.createTime
      } else {
        return a.createTime - b.createTime
      }
    })
})
const totalLogs = computed(() => filteredLogs.value.length)
const totalPages = computed(() => Math.ceil(totalLogs.value / pageSize.value))
const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})
const detailLog = (id: string) => {
  currentLogId.value = id
  isDetailOpen.value = true
}
const confirmDeleteLog = (id: string) => {
  logIdToDelete.value = id
  isDeleteConfirmDialogOpen.value = true
}
const handleDeleteLog = async () => {
  if (logIdToDelete.value) {
    try {
      const response = await taskService.deleteTask(logIdToDelete.value)
      if (response.status === 'Success') {
        // 从本地 store 中删除任务
        taskStore.clearTaskResult(logIdToDelete.value)
        toast.success('日志已删除')
      } else {
        toast.error('删除失败：' + (response.message || '未知错误'))
      }
    } catch (error) {
      console.error('删除任务失败:', error)
      toast.error('删除失败，请重试')
    }
    logIdToDelete.value = undefined
    isDeleteConfirmDialogOpen.value = false
  }
}
const confirmClearLogs = () => {
  taskStore.clearAllTaskResults()
  toast.info('所有日志已清除')
  isDeleteAllDialogOpen.value = false
}
const closeDialog = () => {
  emit('update:isOpen', false)
}
watch(
  () => props.isOpen,
  (open) => {
    if (open) taskStore.updateTaskList()
  },
)
</script>

<style lang="scss" scoped>
:deep(.dialog-content) {
  max-height: 80vh;
}
:deep(.close-button) {
  display: none;
}
.table {
  @apply border-collapse;
  th {
    @apply bg-muted/50 font-medium text-muted-foreground;
  }
  td {
    @apply align-middle;
  }
}
:deep(.button-sm) {
  @apply h-7 w-7;
}
:deep(.checkbox) {
  @apply h-4 w-4;
}
.bg-green-100 {
  @apply bg-opacity-20;
}
.bg-red-100 {
  @apply bg-opacity-20;
}
.bg-gray-100 {
  @apply bg-opacity-20;
}
</style>
