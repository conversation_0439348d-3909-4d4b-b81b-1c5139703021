import defaultModules from '@renderer/config/constants/defaultModules'
import { useAIChatStore, useFlowsStore, useWorkflowStore } from '@renderer/store'

import { Position } from '@vue-flow/core'
import { ipcRenderer } from 'electron'
import { nanoid } from 'nanoid'
import { onMounted, onUnmounted, ref } from 'vue'
import { toast } from 'vue-sonner'

export function useAIWorkflow() {
  const workflowStore = useWorkflowStore()
  const flowsStore = useFlowsStore()
  const aiChatStore = useAIChatStore()

  const isProcessing = ref(false)

  // 处理AI生成的工作流
  const handleAIWorkflowGenerate = async (data) => {
    isProcessing.value = true

    try {
      const { message, workflowId, operation, nodes, edges } = data

      // 确保工作流存在
      let workflow = flowsStore.getWorkflow(workflowId)
      if (!workflow) {
        // 如果工作流不存在，创建一个新的
        flowsStore.createWorkflow(workflowId)
        workflow = { nodes: [], edges: [] }
      }

      // 设置当前工作流
      workflowStore.currentWfId = workflowId
      flowsStore.setCurrentWorkflow(workflowId)

      let result = {}

      switch (operation) {
        case 'add_nodes':
          result = await addNodes(workflowId, nodes)
          break
        case 'add_edges':
          result = await addEdges(workflowId, edges)
          break
        case 'update_nodes':
          result = await updateNodes(workflowId, nodes)
          break
        case 'run_workflow':
          result = await runWorkflow(workflowId)
          break
        case 'create_workflow':
          result = await createWorkflow(message)
          break
      }

      // 返回操作结果
      ipcRenderer.send('ai-workflow-generate-result', {
        success: true,
        message: `${operation} completed successfully`,
        data: result,
      })

      toast.success('AI助手已更新工作流', {
        description: `操作 ${operation} 已完成`,
      })
    } catch (error) {
      console.error('AI工作流操作失败:', error)

      // 返回错误结果
      ipcRenderer.send('ai-workflow-generate-result', {
        success: false,
        message: error.message,
        data: {},
      })

      toast.error('AI工作流操作失败', {
        description: error.message,
      })
    } finally {
      isProcessing.value = false
    }
  }

  // 添加节点
  const addNodes = async (workflowId, aiNodes) => {
    const workflow = flowsStore.getWorkflow(workflowId)

    // 将AI生成的节点转换为vue-flow节点格式
    const newNodes = aiNodes.map((node, index) => {
      // 查找默认模块中的节点定义
      const moduleType = node.data.category || 'materialDesign'
      const nodeType = node.data.type

      // 查找默认模块中的节点定义
      let nodeDefinition = null
      Object.values(defaultModules).forEach((module) => {
        if (module.type === moduleType) {
          module.categories.forEach((category) => {
            category.nodes.forEach((n) => {
              if (n.data.type === nodeType) {
                nodeDefinition = n.data
              }
            })
          })
        }
      })

      // 如果找不到节点定义，使用AI提供的数据
      const nodeData = nodeDefinition || node.data

      return {
        id: node.id || nanoid(),
        type: 'custom',
        position: node.position || { x: 100 + index * 180, y: 100 },
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
        data: {
          ...nodeData,
          workflowId,
          label: node.data.label || nodeData.label,
          params: node.data.params || nodeData.params || {},
        },
      }
    })

    // 保存新节点到工作流
    flowsStore.saveWorkflow(workflowId, {
      nodes: [...(workflow?.nodes || []), ...newNodes],
      edges: workflow?.edges || [],
    })

    return { nodes: newNodes }
  }

  // 添加边
  const addEdges = async (workflowId, aiEdges) => {
    const workflow = flowsStore.getWorkflow(workflowId)

    // 将AI生成的边转换为vue-flow边格式
    const newEdges = aiEdges.map((edge) => {
      return {
        id: edge.id || nanoid(),
        source: edge.source,
        target: edge.target,
        type: edge.type || 'smoothstep',
        animated: edge.animated !== undefined ? edge.animated : false,
      }
    })

    // 保存新边到工作流
    flowsStore.saveWorkflow(workflowId, {
      nodes: workflow?.nodes || [],
      edges: [...(workflow?.edges || []), ...newEdges],
    })

    return { edges: newEdges }
  }

  // 更新节点
  const updateNodes = async (workflowId, aiNodes) => {
    const workflow = flowsStore.getWorkflow(workflowId)
    if (!workflow) return { nodes: [] }

    // 更新现有节点的参数
    const updatedNodes = workflow.nodes.map((node) => {
      const aiNode = aiNodes.find((n) => n.id === node.id)
      if (aiNode) {
        return {
          ...node,
          data: {
            ...node.data,
            params: {
              ...node.data.params,
              ...aiNode.data.params,
            },
            label: aiNode.data.label || node.data.label,
          },
        }
      }
      return node
    })

    // 保存更新后的节点
    flowsStore.saveWorkflow(workflowId, {
      nodes: updatedNodes,
      edges: workflow.edges,
    })

    return { nodes: updatedNodes }
  }

  // 运行工作流
  const runWorkflow = async (workflowId) => {
    // 这里可以调用现有的工作流运行逻辑
    // 例如：workflowStore.runWorkflow(workflowId)

    // 返回运行结果
    return { workflowId, status: 'running' }
  }

  // 创建新工作流
  const createWorkflow = async (message) => {
    // 创建新工作流
    const workflowId = nanoid()
    const title = message.title || `AI生成的工作流 ${new Date().toLocaleString()}`
    const description = message.description || '由AI助手生成的工作流'

    // 创建工作流
    workflowStore.addWorkflow({
      id: workflowId,
      title,
      description,
      createTime: new Date().toLocaleString(),
    })

    // 初始化工作流数据
    flowsStore.createWorkflow(workflowId)

    // 设置当前工作流
    workflowStore.currentWfId = workflowId
    flowsStore.setCurrentWorkflow(workflowId)

    // 创建关联的AI聊天会话
    aiChatStore.createNewSession(title, workflowId)

    return { workflowId, title, description }
  }

  // 处理工作流运行
  const handleRunWorkflow = async (data) => {
    isProcessing.value = true

    try {
      const { workflowId, params } = data

      // 运行工作流
      const result = await runWorkflow(workflowId)

      // 返回运行结果
      ipcRenderer.send('run-workflow-result', {
        success: true,
        message: 'Workflow run completed',
        data: result,
      })
    } catch (error) {
      console.error('工作流运行失败:', error)

      // 返回错误结果
      ipcRenderer.send('run-workflow-result', {
        success: false,
        message: error.message,
        data: {},
      })
    } finally {
      isProcessing.value = false
    }
  }

  // 发送工作流相关消息到AI助手
  const sendWorkflowMessageToAI = async (message, workflowId) => {
    // 确保有关联的会话
    let sessionId = null

    // 查找与工作流关联的会话
    const existingSession = aiChatStore.sessions.find((sess) => sess.workflowId === workflowId)

    if (existingSession) {
      sessionId = existingSession.id
      aiChatStore.selectSession(sessionId)
    } else {
      // 如果没有关联会话，创建一个
      const workflow = workflowStore.workflows.find((wf) => wf.id === workflowId)
      const title = workflow ? workflow.title : '工作流对话'
      sessionId = await aiChatStore.createNewSession(title, workflowId)
    }

    // 发送消息
    await aiChatStore.sendMessage(message)

    // 打开AI聊天窗口
    aiChatStore.toggleChatInWorkflow(true)

    return sessionId
  }

  // 设置IPC监听
  onMounted(() => {
    // 监听AI工作流生成请求
    ipcRenderer.on('ai-workflow-generate', (_, data) => {
      handleAIWorkflowGenerate(data)
    })

    // 监听工作流运行请求
    ipcRenderer.on('run-workflow', (_, data) => {
      handleRunWorkflow(data)
    })
  })

  onUnmounted(() => {
    ipcRenderer.removeAllListeners('ai-workflow-generate')
    ipcRenderer.removeAllListeners('run-workflow')
  })

  return {
    isProcessing,
    sendWorkflowMessageToAI,
  }
}
