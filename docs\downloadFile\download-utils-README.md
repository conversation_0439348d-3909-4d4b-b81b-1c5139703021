# 文件下载工具使用说明

## 概述

项目中提供了两个通用的文件下载方法，位于 `src/renderer/src/utils/utils.ts` 中：

1. `downloadFileFromServer` - 通用文件下载方法
2. `downloadModelFile` - 模型文件下载的便捷方法

这些方法封装了完整的下载流程，包括：

- 弹出文件保存对话框
- 流式下载文件
- 显示下载进度
- 文件完整性验证
- 保存到用户选择的路径

## API 说明

### downloadFileFromServer

通用的文件下载方法，支持自定义各种选项。

```typescript
downloadFileFromServer(
  filePath: string,
  options?: {
    defaultFileName?: string
    fileExtension?: string
    fileTypeDescription?: string
    showProgress?: boolean
    onProgress?: (progress: number, fileName?: string) => void
  }
): Promise<boolean>
```

**参数说明：**

- `filePath`: 服务器上的文件路径（必需）
- `options.defaultFileName`: 默认文件名，默认为 `downloaded_file_${timestamp}`
- `options.fileExtension`: 文件扩展名，默认为 `*`
- `options.fileTypeDescription`: 文件类型描述，默认为 `All Files`
- `options.showProgress`: 是否显示进度提示，默认为 `true`
- `options.onProgress`: 进度回调函数

**返回值：**

- `Promise<boolean>`: 下载是否成功

### downloadModelFile

模型文件下载的便捷方法，预设了模型文件的相关配置。

```typescript
downloadModelFile(
  filePath: string,
  onProgress?: (progress: number, fileName?: string) => void
): Promise<boolean>
```

**参数说明：**

- `filePath`: 服务器上的模型文件路径（必需）
- `onProgress`: 进度回调函数（可选）

**返回值：**

- `Promise<boolean>`: 下载是否成功

## 使用示例

### 1. 基本使用

```typescript
import { downloadModelFile } from '@renderer/utils/utils'

// 下载模型文件
const success = await downloadModelFile('1/W_7swJXiBW_EoL.ml')
if (success) {
  console.log('下载成功')
} else {
  console.log('下载失败或被用户取消')
}
```

### 2. 带进度回调

```typescript
import { downloadModelFile } from '@renderer/utils/utils'

const success = await downloadModelFile('1/W_7swJXiBW_EoL.ml', (progress, fileName) => {
  console.log(`下载进度: ${progress.toFixed(1)}% - ${fileName}`)
  // 可以在这里更新 UI 进度条
})
```

### 3. 自定义文件类型

```typescript
import { downloadFileFromServer } from '@renderer/utils/utils'

const success = await downloadFileFromServer('path/to/data.csv', {
  defaultFileName: 'exported_data.csv',
  fileExtension: 'csv',
  fileTypeDescription: 'CSV Files',
  showProgress: true,
  onProgress: (progress, fileName) => {
    console.log(`CSV下载进度: ${progress}%`)
  },
})
```

### 4. 在 Vue 组件中使用

```typescript
// 在组件的 setup 函数中
import { downloadModelFile } from '@renderer/utils/utils'
import { toast } from 'vue-sonner'

const handleDownload = async (filePath: string) => {
  try {
    const success = await downloadModelFile(filePath, (progress, fileName) => {
      // 更新进度显示
      console.log(`进度: ${progress}% - ${fileName}`)
    })

    if (success) {
      toast.success('文件下载完成')
    }
  } catch (error) {
    toast.error('下载失败', {
      description: error.message,
    })
  }
}
```

## 在 useModelTrain 中的集成

在 `useModelTrain.ts` 中，导出功能已经集成了新的下载方法：

```typescript
const handleExport = async () => {
  // 如果已经有导出结果，直接下载
  if (state.exportTask.result) {
    const filePath = state.exportTask.result.result || state.exportTask.result
    if (filePath) {
      await downloadExportedFile(filePath) // 内部使用 downloadModelFile
      return
    }
  }

  // 如果没有导出结果，先执行导出任务
  // ... 导出逻辑
}
```

## 工作流程

1. **用户点击导出按钮**
   - 如果已有导出结果 → 直接下载
   - 如果没有导出结果 → 先执行导出任务

2. **导出任务完成后**
   - 自动触发下载流程
   - 弹出文件保存对话框
   - 用户选择保存路径

3. **下载过程**
   - 显示下载进度提示
   - 流式下载文件块
   - 验证文件完整性
   - 保存到用户选择的路径

4. **完成**
   - 显示成功提示
   - 包含保存路径信息

## 错误处理

所有下载方法都包含完善的错误处理：

- **网络错误**: 自动重试机制
- **文件不存在**: 显示友好错误信息
- **权限错误**: 提示用户权限问题
- **用户取消**: 静默处理，不显示错误
- **保存失败**: 显示具体错误原因

## 注意事项

1. **用户体验**
   - 下载前会弹出文件保存对话框
   - 用户可以选择保存位置和文件名
   - 显示实时下载进度

2. **文件完整性**
   - 自动验证文件 SHA256
   - 确保下载文件的完整性

3. **内存管理**
   - 大文件采用流式下载
   - 避免内存溢出问题

4. **错误恢复**
   - 网络中断时支持重试
   - 提供详细的错误信息

## 扩展使用

可以根据需要创建更多特定类型的下载方法：

```typescript
// 下载数据文件
export const downloadDataFile = async (filePath: string) => {
  return downloadFileFromServer(filePath, {
    defaultFileName: `data_${Date.now()}.csv`,
    fileExtension: 'csv',
    fileTypeDescription: 'Data Files',
  })
}

// 下载图片文件
export const downloadImageFile = async (filePath: string) => {
  return downloadFileFromServer(filePath, {
    defaultFileName: `image_${Date.now()}.png`,
    fileExtension: 'png',
    fileTypeDescription: 'Image Files',
  })
}
```
