<template>
  <div class="w-72 bg-card border-r border-border flex flex-col overflow-hidden">
    <div class="p-4 border-b border-border">
      <div class="flex items-center justify-between mb-3">
        <h2 class="font-semibold text-lg text-foreground">工作流列表</h2>
        <Button variant="ghost" size="icon" title="新建工作流" @click="openAddWorkflowDialog">
          <Plus class="w-5 h-5 text-muted-foreground" />
        </Button>
      </div>
      <div class="relative">
        <Input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索工作流..."
          class="w-full pl-9 pr-4 py-2 text-sm bg-background border-border focus:border-primary"
        />
        <Search
          class="w-4 h-4 text-muted-foreground absolute left-3 top-1/2 transform -translate-y-1/2"
        />
      </div>
    </div>

    <ScrollArea class="flex-1">
      <!-- 空状态展示 -->
      <EmptyState
        v-if="!filteredWorkflows.length"
        title="暂无工作流"
        description="创建新的工作流开始你的科研旅程吧"
        icon="clarity:file-group-line"
      >
        <template #action>
          <Button
            variant="default"
            class="flex items-center space-x-2"
            @click="openAddWorkflowDialog"
          >
            <Plus class="w-4 h-4" />
            <span>创建工作流</span>
          </Button>
        </template>
      </EmptyState>
      <!-- 有工作流时展示列表 -->
      <div v-else class="p-2 space-y-1.5">
        <!-- 工作流项 -->
        <div
          v-for="workflow in filteredWorkflows"
          :key="workflow.id"
          class="relative group rounded-lg bg-muted/40 hover:bg-muted transition-colors duration-150 ease-in-out"
          :class="{ 'bg-primary/10': workflow.id === currentWorkflowId }"
        >
          <Button
            variant="ghost"
            class="w-full h-auto p-2.5 rounded-lg justify-start items-start flex-col text-left transition-colors duration-150 ease-in-out"
            :class="{
              'bg-primary/10 text-primary hover:bg-primary/15': workflow.id === currentWorkflowId,
              'hover:bg-muted text-foreground/80': workflow.id !== currentWorkflowId,
            }"
            @click="selectWorkflow(workflow.id)"
          >
            <div class="flex justify-between items-center w-full mb-1">
              <h3
                class="font-medium text-sm truncate"
                :class="{
                  'text-primary': workflow.id === currentWorkflowId,
                  'text-foreground': workflow.id !== currentWorkflowId,
                }"
              >
                {{ workflow.title }}
              </h3>
              <span
                class="text-xs"
                :class="{
                  'text-primary/80': workflow.id === currentWorkflowId,
                  'text-muted-foreground': workflow.id !== currentWorkflowId,
                }"
              >
                {{ formatDate(workflow.createTime) }}
              </span>
            </div>
            <p
              class="text-xs truncate w-full"
              :class="{
                'text-primary/70': workflow.id === currentWorkflowId,
                'text-muted-foreground/80': workflow.id !== currentWorkflowId,
              }"
            >
              {{ workflow.description || '暂无描述' }}
            </p>
          </Button>
          <!-- 删除按钮 -->
          <Button
            variant="ghost"
            size="icon-sm"
            class="absolute right-2 bottom-2 opacity-0 group-hover:opacity-100 transition-opacity"
            @click.stop="confirmDeleteWorkflow(workflow.id, workflow.title)"
          >
            <Trash2 class="w-4 h-4 text-destructive hover:text-destructive/80" />
          </Button>
        </div>
      </div>
    </ScrollArea>

    <div class="p-3 border-t border-border mt-auto">
      <Button
        variant="outline"
        class="w-full flex items-center justify-center space-x-2 text-sm border-border hover:bg-muted hover:text-foreground"
        @click="openAddWorkflowDialog"
      >
        <Plus class="w-4 h-4" />
        <span>新建工作流</span>
      </Button>
    </div>

    <!-- 工作流添加/编辑对话框 -->
    <WorkflowDialog ref="workflowDialogRef" @confirm="handleWorkflowConfirm" />

    <!-- 删除确认对话框 -->
    <AlertDialog v-model:open="showDeleteConfirm">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除工作流</AlertDialogTitle>
          <AlertDialogDescription>
            你确定要删除工作流 "{{ workflowToDelete.title }}"
            吗？此操作不可撤销，相关的对话记录也将被删除。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="cancelDelete">取消</AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            @click="confirmDelete"
          >
            删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { WorkflowDialog } from '@renderer/components'
import { createAIChatService } from '@renderer/config/api/grpc/aiChatService'
import { useAIChatStore, useAuthStore, useWorkflowStore } from '@renderer/store'
import { useDebounceFn } from '@vueuse/core'
import { Plus, Search, Trash2 } from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const workflowStore = useWorkflowStore()
const aiChatStore = useAIChatStore()
const aiChatService = createAIChatService()
const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()

// 搜索关键词
const searchKeyword = ref('')

// 当前选中的工作流ID
const currentWorkflowId = computed(() => workflowStore.currentWfId)

// 过滤后的工作流列表
const filteredWorkflows = computed(() => {
  if (!searchKeyword.value) {
    return workflowStore.workflows
  }
  const keyword = searchKeyword.value.toLowerCase()
  return workflowStore.workflows.filter(
    (workflow) =>
      workflow.title.toLowerCase().includes(keyword) ||
      (workflow.description && workflow.description.toLowerCase().includes(keyword)),
  )
})

// 工作流对话框引用
const workflowDialogRef = ref(null)

// 删除确认状态
const showDeleteConfirm = ref(false)
const workflowToDelete = ref({ id: '', title: '' })

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知时间'

  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 今天内
  if (
    diff < 24 * 60 * 60 * 1000 &&
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear()
  ) {
    return '今天'
  }

  // 昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    return '昨天'
  }

  // 一周内
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 其他情况显示完整日期
  return date.toLocaleDateString()
}

// 打开添加工作流对话框
const openAddWorkflowDialog = () => {
  workflowDialogRef.value.open('add')
}

// 处理工作流对话框确认
const handleWorkflowConfirm = async (data, type) => {
  if (type === 'add') {
    // 记录添加前的工作流数量
    const workflowCountBefore = workflowStore.workflows.length

    // 添加新工作流
    await workflowStore.addWorkflow({
      title: data.name,
      description: data.description,
      folderId: workflowStore.currentFolderId,
    })

    if (workflowStore.workflows.length > workflowCountBefore) {
      const newWorkflow = workflowStore.workflows[workflowStore.workflows.length - 1]
      // 选中新添加的工作流
      setTimeout(() => {
        selectWorkflow(newWorkflow.id)
      }, 100)
    }
  } else if (type === 'edit') {
    workflowStore.editWorkflow({
      id: data.id,
      title: data.name,
      description: data.description,
    })
  }
}

// 选择工作流 - 使用防抖包装
const selectWorkflowRaw = async (workflowId) => {
  // 更新当前工作流ID
  workflowStore.currentWfId = workflowId
  window.logger.info(`选择工作流: ${workflowId}`)

  try {
    // 根据工作流ID生成会话ID
    const sessionId = `0::${workflowId}`
    window.logger.info(`工作流对应的会话ID: ${sessionId}`)

    // 设置加载状态
    aiChatStore.setIsLoading(true)

    // 直接调用 getSession API 获取会话数据
    const response = await aiChatService.getSession(
      authStore.userId || '',
      authStore.token || '',
      sessionId,
    )

    window.logger.info(`获取会话数据结果: ${JSON.stringify(response)}`)

    // 获取工作流信息用于设置标题
    const workflow = workflowStore.workflows.find((wf) => wf.id === workflowId)
    const title = workflow ? `${workflow.title} 对话` : '工作流对话'

    // 获取会话列表，兼容两种格式
    const sessionList = response.session_result?.session_list || response.sessionResult?.sessionList

    if (response.status === 'Success' && sessionList && sessionList.length > 0) {
      const serverSession = sessionList[0]
      window.logger.info(`找到会话数据，开始处理消息`)

      // 获取会话消息内容，兼容两种格式
      const sessionMessages = serverSession.session_messages || serverSession.sessionMessages

      if (sessionMessages) {
        try {
          // 解析消息
          const messages = aiChatService.parseMessagesFromServer(sessionMessages, sessionId)

          window.logger.info(`解析后的消息数量: ${messages.length}`)

          // 更新当前会话
          aiChatStore.setCurrentSessionId(sessionId)

          window.logger.info(`成功加载会话 ${sessionId} 的消息，共 ${messages.length} 条`)
        } catch (parseError) {
          window.logger.error(`解析会话消息失败:`, parseError)
          await aiChatService.createNewSession(title, workflowId)
        }
      } else {
        window.logger.warn(`会话消息为空，创建新会话`)
        await aiChatService.createNewSession(title, workflowId)
      }
    } else {
      window.logger.info(`未找到工作流会话 ${sessionId}，创建新会话`)
      await aiChatService.createNewSession(title, workflowId)
    }

    // 如果是在聊天页面，更新URL参数
    if (route.path.includes('/ai/chat')) {
      router.replace({
        query: { ...route.query, workflowId },
      })
    }
  } catch (error) {
    window.logger.error(`选择工作流 ${workflowId} 失败:`, error)

    // 出错时也创建一个空会话
    const workflow = workflowStore.workflows.find((wf) => wf.id === workflowId)
    const title = workflow ? `${workflow.title} 对话` : '工作流对话'

    await aiChatService.createNewSession(title, workflowId)
  } finally {
    // 结束加载状态
    aiChatStore.setIsLoading(false)
  }
}

// 防止频繁切换
const selectWorkflow = useDebounceFn(selectWorkflowRaw, 300)

// 确认删除工作流
const confirmDeleteWorkflow = (id, title) => {
  workflowToDelete.value = { id, title }
  showDeleteConfirm.value = true
}

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false
  workflowToDelete.value = { id: '', title: '' }
}

// 确认删除
const confirmDelete = async () => {
  const workflowId = workflowToDelete.value.id

  // 删除关联的对话 - 不调用服务端API，仅清理本地状态
  if (aiChatStore.currentSessionId === `0::${workflowId}`) {
    // 如果当前会话与被删除的工作流关联，清除当前会话
    aiChatStore.setCurrentSessionId(null)
    aiChatStore.setCurrentSessionData(null)
  }

  // 删除工作流
  workflowStore.deleteWorkflow(workflowId)

  // 如果删除的是当前选中的工作流
  if (workflowId === currentWorkflowId.value) {
    // 检查是否还有其他工作流
    if (workflowStore.workflows.length > 0) {
      // 选择第一个可用的工作流
      selectWorkflow(workflowStore.workflows[0].id)
    } else {
      // 如果没有工作流了，清除当前工作流ID，让主界面显示欢迎页面
      workflowStore.currentWfId = ''

      // 如果在聊天页面，更新URL参数
      if (route.path.includes('/ai/chat')) {
        router.replace({
          query: { ...route.query, workflowId: undefined },
        })
      }
    }
  }

  showDeleteConfirm.value = false
  workflowToDelete.value = { id: '', title: '' }
}

// 组件挂载时初始化
onMounted(() => {
  // 如果有 workflowId 参数，选择对应工作流（用于从浮动窗口跳转到全屏模式）
  const workflowIdFromRoute = route.query.workflowId as string

  if (workflowIdFromRoute) {
    selectWorkflow(workflowIdFromRoute)
  } else if (workflowStore.workflows.length > 0) {
    // 没有指定时，选择第一个工作流
    if (!currentWorkflowId.value) {
      selectWorkflow(workflowStore.workflows[0].id)
    } else {
      // 有当前选中的工作流，确保在UI上标记为选中
      workflowStore.currentWfId = currentWorkflowId.value

      // 如果当前没有会话，也加载一下会话数据
      if (!aiChatStore.currentSession) {
        selectWorkflow(currentWorkflowId.value)
      }
    }
  } else {
    // 没有工作流时，清除当前工作流ID，让主界面显示欢迎页面
    workflowStore.currentWfId = ''
  }
})

// 监听路由参数变化
watch(
  () => route.query.workflowId,
  (newWorkflowId) => {
    if (newWorkflowId && typeof newWorkflowId === 'string') {
      selectWorkflow(newWorkflowId)
    }
  },
)
</script>

<style scoped></style>
