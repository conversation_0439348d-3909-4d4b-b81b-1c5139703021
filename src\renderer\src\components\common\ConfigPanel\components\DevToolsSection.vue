<template>
  <div class="space-y-4">
    <!-- 配置切换 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-cyan-100 text-cyan-600 dark:bg-cyan-900/20 dark:text-cyan-400"
          >
            <RotateCcw class="w-3.5 h-3.5" />
          </div>
          配置切换
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <Card
          v-for="configType in availableConfigs"
          :key="configType.type"
          :class="[
            'cursor-pointer transition-all duration-200 hover:shadow-sm',
            appType === configType.type
              ? 'border-primary bg-primary/5'
              : 'border-muted/50 hover:border-primary/50',
          ]"
          @click="switchConfig(configType.type)"
        >
          <CardContent class="p-3">
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium">
                  {{ configType.title }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ configType.description }}
                </div>
              </div>
              <div class="flex items-center space-x-2 flex-shrink-0">
                <Badge v-if="appType === configType.type" variant="default" class="text-xs">
                  当前
                </Badge>
                <ChevronRight class="w-4 h-4 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>

    <!-- 开发工具 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-emerald-100 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400"
          >
            <Code class="w-3.5 h-3.5" />
          </div>
          开发工具
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <Button variant="outline" class="w-full justify-between h-auto p-3" @click="exportConfig">
          <div class="flex items-center space-x-3">
            <Download class="w-4 h-4 text-green-600" />
            <div class="text-left">
              <div class="text-sm font-medium">导出配置</div>
              <div class="text-xs text-muted-foreground">下载当前配置为JSON文件</div>
            </div>
          </div>
          <ChevronRight class="w-4 h-4 text-muted-foreground" />
        </Button>

        <Button variant="outline" class="w-full justify-between h-auto p-3" @click="copyConfig">
          <div class="flex items-center space-x-3">
            <Copy class="w-4 h-4 text-blue-600" />
            <div class="text-left">
              <div class="text-sm font-medium">复制配置</div>
              <div class="text-xs text-muted-foreground">复制配置到剪贴板</div>
            </div>
          </div>
          <ChevronRight class="w-4 h-4 text-muted-foreground" />
        </Button>

        <Button
          variant="outline"
          class="w-full justify-between h-auto p-3 border-destructive/50 hover:bg-destructive/5"
          @click="resetConfig"
        >
          <div class="flex items-center space-x-3">
            <RefreshCw class="w-4 h-4 text-destructive" />
            <div class="text-left">
              <div class="text-sm font-medium text-destructive">重置配置</div>
              <div class="text-xs text-destructive/70">恢复到默认配置</div>
            </div>
          </div>
          <ChevronRight class="w-4 h-4 text-muted-foreground" />
        </Button>
      </CardContent>
    </Card>

    <!-- 调试信息 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-amber-100 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400"
          >
            <Bug class="w-3.5 h-3.5" />
          </div>
          调试信息
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <!-- 配置路径 -->
        <div class="space-y-1">
          <Label class="text-xs text-muted-foreground">配置文件路径</Label>
          <code class="block text-xs bg-muted p-2 rounded font-mono break-all">
            src/renderer/src/config/app/{{ appType }}.ts
          </code>
        </div>

        <!-- 配置哈希 -->
        <div class="space-y-1">
          <Label class="text-xs text-muted-foreground">配置哈希</Label>
          <code class="block text-xs bg-muted p-2 rounded font-mono">
            {{ configHash }}
          </code>
        </div>

        <!-- 最后更新时间 -->
        <div class="space-y-1">
          <Label class="text-xs text-muted-foreground">最后更新</Label>
          <div class="text-xs p-2 bg-muted rounded">
            {{ lastUpdateTime }}
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 快捷键说明 -->
    <Card class="border-muted/50">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm flex items-center">
          <div
            class="flex items-center justify-center w-6 h-6 rounded-md mr-2 bg-violet-100 text-violet-600 dark:bg-violet-900/20 dark:text-violet-400"
          >
            <Keyboard class="w-3.5 h-3.5" />
          </div>
          快捷键
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-3">
        <div class="flex items-center justify-between">
          <Label class="text-sm text-muted-foreground">打开/关闭配置面板</Label>
          <kbd class="px-2 py-1 text-xs bg-muted rounded font-mono">Ctrl+Shift+C</kbd>
        </div>
        <div class="flex items-center justify-between">
          <Label class="text-sm text-muted-foreground">关闭面板</Label>
          <kbd class="px-2 py-1 text-xs bg-muted rounded font-mono">Esc</kbd>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Button } from '@renderer/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { Label } from '@renderer/components/ui/label'
import { useAppConfig } from '@renderer/config/hooks/useAppConfig'
import {
  Bug,
  ChevronRight,
  Code,
  Copy,
  Download,
  Keyboard,
  RefreshCw,
  RotateCcw,
} from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'

const { appType, appConfig, switchAppConfig, resetConfig: resetAppConfig } = useAppConfig()

// 可用配置类型
const availableConfigs = [
  {
    type: 'mattverse',
    title: 'MattVerse 配置',
    description: '完整功能的电池设计自动化平台',
  },
  {
    type: 'highpower',
    title: 'Highpower 配置',
    description: '简化的电池寿命预测软件',
  },
]

// 配置哈希（简单的配置标识）
const configHash = computed(() => {
  const configStr = JSON.stringify(appConfig.value)
  let hash = 0
  for (let i = 0; i < configStr.length; i++) {
    const char = configStr.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16).toUpperCase().padStart(8, '0')
})

// 最后更新时间
const lastUpdateTime = ref(new Date().toLocaleString())

// 切换配置
const switchConfig = (type: string) => {
  try {
    switchAppConfig(type as any)
    lastUpdateTime.value = new Date().toLocaleString()
    toast.success(`已切换到 ${type} 配置`)
  } catch (error) {
    toast.error(`切换配置失败: ${error}`)
  }
}

// 导出配置
const exportConfig = () => {
  try {
    const configData = JSON.stringify(appConfig.value, null, 2)

    // 创建下载链接
    const blob = new Blob([configData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${appType.value}-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('配置已导出')
  } catch (error) {
    toast.error(`导出失败: ${error}`)
  }
}

// 复制配置
const copyConfig = async () => {
  try {
    const configData = JSON.stringify(appConfig.value, null, 2)
    await navigator.clipboard.writeText(configData)
    toast.success('配置已复制到剪贴板')
  } catch (error) {
    toast.error(`复制失败: ${error}`)
  }
}

// 重置配置
const resetConfig = () => {
  try {
    resetAppConfig()
    lastUpdateTime.value = new Date().toLocaleString()
    toast.success('配置已重置')
  } catch (error) {
    toast.error(`重置失败: ${error}`)
  }
}
</script>
