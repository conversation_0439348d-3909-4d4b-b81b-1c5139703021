import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useNodeNavbarStore } from './nodeNavbar'
export interface NavbarTag {
  id: string
  title: string
  path: string
  isFixed?: boolean
}

export const useNavbarStore = defineStore(
  'navbar',
  () => {
    const router = useRouter()
    const nodeNavbarStore = useNodeNavbarStore()

    const tags = ref<NavbarTag[]>([
      {
        id: 'home',
        title: 'workflows',
        path: '/workflow',
        isFixed: true,
      },
    ])

    const activeTagId = ref('home')
    // 右侧菜单状态
    const showContextMenu = ref(false)
    const contextMenuPosition = ref({ x: 0, y: 0 })
    const contextMenuTargetId = ref<string | null>(null)
    // 添加标签
    function addTag(tag: Omit<NavbarTag, 'isFixed'>) {
      const exists = tags.value.find((t) => t.id === tag.id)
      if (!exists) {
        tags.value.push({
          ...tag,
          isFixed: false,
        })
      }
      activeTagId.value = tag.id
    }

    // 移除标签
    function removeTag(id: string) {
      const index = tags.value.findIndex((tag) => tag.id === id)
      if (index > -1 && !tags.value[index].isFixed) {
        const tag = tags.value[index]

        // 如果关闭的是工作流编辑器标签，清理节点设置面板
        if (tag.path.includes('/workflow/editor/')) {
          nodeNavbarStore.reset() // 重置节点设置面板状态
        }

        tags.value.splice(index, 1)

        // 如果删除的是当前活动的标签，则激活前一个标签
        if (id === activeTagId.value) {
          const prevTag = tags.value[Math.max(0, index - 1)]
          activeTagId.value = prevTag.id
          router.push(prevTag.path)
        }
      }
    }

    // 移除与工作流相关的所有标签
    function removeWorkflowTags(workflowId: string) {
      const tagsToRemove = tags.value.filter((tag) =>
        tag.path.includes(`/workflow/editor/${workflowId}`),
      )

      tagsToRemove.forEach((tag) => {
        removeTag(tag.id)
      })
    }

    // 设置活动标签
    function setActiveTag(id: string) {
      activeTagId.value = id
    }
    // 显示右键菜单
    function showMenu(id: string, x: number, y: number) {
      contextMenuTargetId.value = id
      contextMenuPosition.value = { x, y }
      showContextMenu.value = true
    }

    // 隐藏右键菜单
    function hideMenu() {
      showContextMenu.value = false
    }

    // 关闭其他标签
    function closeOtherTags(id: string) {
      const currentTag = tags.value.find((tag) => tag.id === id)
      if (!currentTag) return

      // 保留固定标签和当前标签
      const tagsToKeep = tags.value.filter((tag) => tag.isFixed || tag.id === id)
      tags.value = tagsToKeep
    }

    // 关闭右侧标签
    function closeRightTags(id: string) {
      const index = tags.value.findIndex((tag) => tag.id === id)
      if (index === -1) return

      // 保留当前标签左侧的所有标签（包括当前标签）
      tags.value = tags.value.filter((tag, i) => i <= index || tag.isFixed)
    }
    // 关闭左侧标签
    function closeLeftTags(id: string) {
      const index = tags.value.findIndex((tag) => tag.id === id)
      if (index === -1) return

      // 保留当前标签右侧的所有标签（包括当前标签）
      tags.value = tags.value.filter((tag, i) => i >= index || tag.isFixed)
    }
    // 关闭所有标签（除了固定标签）
    function closeAllTags() {
      tags.value = tags.value.filter((tag) => tag.isFixed)
      // 激活第一个标签
      if (tags.value.length > 0) {
        activeTagId.value = tags.value[0].id
        router.push(tags.value[0].path)
      }
    }
    return {
      tags,
      activeTagId,
      showContextMenu,
      contextMenuPosition,
      contextMenuTargetId,
      addTag,
      removeTag,
      setActiveTag,
      removeWorkflowTags,
      showMenu,
      hideMenu,
      closeOtherTags,
      closeRightTags,
      closeLeftTags,
      closeAllTags,
    }
  },
  {
    persist: {
      // key: 'navbar-store',
      storage: localStorage,
      pick: ['tags', 'activeTagId'],
    },
  },
)
