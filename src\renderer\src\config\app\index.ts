/**
 * 应用配置管理
 * 统一导出所有应用配置和相关工具函数
 */

// 导出类型定义
export * from './types'

// 导出具体配置
export { highpowerConfig } from './highpower'
export { mattverseConfig } from './mattverse'

import { highpowerConfig } from './highpower'
import { mattverseConfig } from './mattverse'
import { APP_TYPES, type AppConfig, type AppType } from './types'

/**
 * 配置映射表
 */
const configMap: Record<AppType, AppConfig> = {
  [APP_TYPES.MATTVERSE]: mattverseConfig,
  [APP_TYPES.HIGHPOWER]: highpowerConfig,
}

/**
 * 根据环境变量获取当前应用配置
 * @returns 当前应用配置
 */
export function getCurrentAppConfig(): AppConfig {
  const isMatt = import.meta.env.VITE_APP_IS_MATT === '1'
  return isMatt ? mattverseConfig : highpowerConfig
}

/**
 * 获取指定类型的应用配置
 * @param type 应用类型
 * @returns 应用配置
 */
export function getAppConfig(type: AppType): AppConfig {
  return configMap[type] || mattverseConfig
}

/**
 * 检查功能是否启用
 * @param featurePath 功能路径，如 'layout.showSidebar'
 * @param config 可选的配置对象，不传则使用当前配置
 * @returns 是否启用
 */
export function isFeatureEnabled(featurePath: string, config?: AppConfig): boolean {
  const appConfig = config || getCurrentAppConfig()
  const keys = featurePath.split('.')
  let current: any = appConfig.features

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key]
    } else {
      return false
    }
  }

  return Boolean(current)
}

/**
 * 获取所有可用的应用类型
 * @returns 应用类型数组
 */
export function getAvailableAppTypes(): AppType[] {
  return Object.values(APP_TYPES)
}

/**
 * 验证应用类型是否有效
 * @param type 应用类型
 * @returns 是否有效
 */
export function isValidAppType(type: string): type is AppType {
  return Object.values(APP_TYPES).includes(type as AppType)
}

/**
 * 获取应用配置的摘要信息
 * @param config 应用配置
 * @returns 配置摘要
 */
export function getConfigSummary(config: AppConfig) {
  return {
    type: config.type,
    name: config.meta.name,
    title: config.meta.title,
    version: config.meta.version,
    enabledFeatures: {
      sidebar: config.features.layout.showSidebar,
      aiFloatingBox: config.features.workflowEditor.showAIFloatingBox,
      auth: config.features.auth.enableAuth,
      themeSwitch: config.features.theme.enableThemeSwitch,
      i18n: config.features.features.enableI18n,
    },
  }
}

/**
 * 获取应用图标的完整路径
 * @param config 应用配置，不传则使用当前配置
 * @returns 图标路径
 */
export function getAppIconPath(config?: AppConfig): string {
  const appConfig = config || getCurrentAppConfig()
  return `/src/renderer/src/assets/logo/${appConfig.meta.icon}`
}

/**
 * 获取应用Logo的完整路径
 * @param config 应用配置，不传则使用当前配置
 * @returns Logo路径
 */
export function getAppLogoPath(config?: AppConfig): string {
  const appConfig = config || getCurrentAppConfig()
  return `/src/renderer/src/assets/logo/${appConfig.meta.logo}`
}

/**
 * 获取应用图标URL（用于在组件中引用）
 * @param config 应用配置，不传则使用当前配置
 * @returns 图标URL
 */
export function getAppIconUrl(config?: AppConfig): string {
  const appConfig = config || getCurrentAppConfig()
  // 使用 Vite 的静态资源处理方式
  try {
    // 使用 new URL 和 import.meta.url 来正确解析资源路径
    return new URL(`../../assets/logo/${appConfig.meta.icon}`, import.meta.url).href
  } catch (error) {
    console.warn(`Failed to resolve icon URL for ${appConfig.meta.icon}:`, error)
    // 回退方案：使用相对路径
    return `./src/renderer/src/assets/logo/${appConfig.meta.icon}`
  }
}

/**
 * 获取应用Logo URL（用于在组件中引用）
 * @param config 应用配置，不传则使用当前配置
 * @returns Logo URL
 */
export function getAppLogoUrl(config?: AppConfig): string {
  const appConfig = config || getCurrentAppConfig()
  // 使用 Vite 的静态资源处理方式
  try {
    // 使用 new URL 和 import.meta.url 来正确解析资源路径
    return new URL(`../../assets/logo/${appConfig.meta.logo}`, import.meta.url).href
  } catch (error) {
    console.warn(`Failed to resolve logo URL for ${appConfig.meta.logo}:`, error)
    // 回退方案：使用相对路径
    return `./src/renderer/src/assets/logo/${appConfig.meta.logo}`
  }
}
