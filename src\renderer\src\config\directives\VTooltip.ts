import { DirectiveBinding } from 'vue'
import tippy, { Props } from 'tippy.js'
import 'tippy.js/dist/tippy.css'
import 'tippy.js/themes/light.css'

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding<string | Props>) {
    // 处理指令的值，可以是字符串或配置对象
    const value = binding.value
    const options: Partial<Props> = {
      theme: 'light',
      animation: 'scale', // 默认动画效果
    }

    // 如果传入的是字符串，则作为content
    if (typeof value === 'string') {
      options.content = value
    }
    // 如果传入的是对象，则合并配置
    else if (typeof value === 'object') {
      Object.assign(options, value)
    }

    // 创建tooltip
    tippy(el, options)
  },

  updated(el: HTMLElement, binding: DirectiveBinding<string | Props>) {
    // 获取tippy实例
    const instance = el._tippy
    if (instance) {
      const value = binding.value

      if (typeof value === 'string') {
        instance.setContent(value)
      } else if (typeof value === 'object') {
        instance.setProps(value)
      }
    }
  },

  beforeUnmount(el: HTMLElement) {
    // 销毁tippy实例
    const instance = el._tippy
    if (instance) {
      instance.destroy()
    }
  },
}
