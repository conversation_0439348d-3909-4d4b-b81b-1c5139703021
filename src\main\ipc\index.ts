import { ipcMain } from 'electron'
import { RpcMgr } from '../grpc/rpcMgr'
import { createGrpcService } from '../services/grpcService'
import { createWorkflowService } from '../services/workflowService'
import logger from '../utils/logger'
import { setupCacheHandlers } from './cacheHandlers'
import { setupConfigHandlers } from './configHandlers'
import { setupFileHandlers } from './fileHandlers'
import { setupGrpcHandlers } from './grpcHandlers'
import { setupWindowHandlers } from './windowHandlers'
import { setupWorkflowHandlers } from './workflowHandlers'
// 跟踪IPC处理程序是否已设置
let handlersSetup = false

/**
 * 设置所有IPC处理程序
 * 确保只设置一次
 */
export function setupIpcHandlers(rpcMgr: RpcMgr | null): void {
  // 如果处理程序已设置，则直接返回
  if (handlersSetup) {
    logger.info('IPC处理程序已设置，跳过重复设置')
    return
  }

  // 创建gRPC服务
  const grpcService = createGrpcService(rpcMgr)
  // 创建工作流服务
  const workflowService = createWorkflowService(rpcMgr)

  // 设置各个处理程序
  setupGrpcHandlers(grpcService)
  setupFileHandlers()
  setupWindowHandlers()
  setupCacheHandlers()
  setupConfigHandlers()
  setupWorkflowHandlers(workflowService)

  // 基本IPC测试
  ipcMain.on('ping', () => logger.info('pong'))

  // 标记处理程序已设置
  handlersSetup = true
  logger.info('所有IPC处理程序已设置')
}
