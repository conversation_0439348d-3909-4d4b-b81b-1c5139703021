<template>
  <div class="p-3 border-t border-border bg-card shadow-sm flex-shrink-0">
    <div class="flex items-center justify-between">
      <!-- 左侧：服务器和模型选择 -->
      <div class="flex items-center space-x-4">
        <!-- 服务器选择下拉框 -->
        <div class="flex items-center space-x-2">
          <Label for="server-select" class="text-sm font-medium text-muted-foreground shrink-0">
            服务器:
          </Label>
          <ServerSelect
            v-model="selectedServerId"
            :servers="runningServers"
            :trigger-class="'w-[140px] h-8 text-sm bg-background border-border focus:border-primary'"
            @update:model-value="handleServerChange"
          />
        </div>

        <!-- AI 模型选择 -->
        <div v-if="selectedServerId" class="flex items-center space-x-2">
          <Label for="ai-model" class="text-sm font-medium text-muted-foreground shrink-0">
            模型:
          </Label>
          <Select id="ai-model" v-model="selectedModel">
            <SelectTrigger
              class="w-[140px] h-8 text-sm bg-background border-border focus:border-primary"
              :disabled="isLoadingModels"
            >
              <div class="flex items-center">
                <LucideIcon
                  v-if="isLoadingModels"
                  name="Loader"
                  class="w-4 h-4 mr-2 animate-spin"
                />
                <SelectValue :placeholder="selectedModel || '选择模型'" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <!-- 按提供商分组显示模型 -->
              <template v-for="(models, provider) in modelGroups" :key="provider">
                <SelectGroup>
                  <SelectLabel>{{ provider }}</SelectLabel>
                  <SelectItem
                    v-for="(item, index) in models"
                    :key="index"
                    :value="`${provider}/${Object.values(item)[0]}`"
                  >
                    {{ Object.keys(item)[0] }}
                  </SelectItem>
                </SelectGroup>
                <SelectSeparator
                  v-if="
                    Object.keys(modelGroups).length > 1 &&
                    Object.keys(modelGroups).indexOf(provider) < Object.keys(modelGroups).length - 1
                  "
                />
              </template>
            </SelectContent>
          </Select>
        </div>

        <!-- 未选择服务器时的提示 -->
        <div v-else class="text-sm text-destructive font-medium">请先选择服务器</div>
      </div>

      <!-- 右侧：模式切换 -->
      <div class="flex items-center">
        <ToggleGroup
          type="single"
          :model-value="chatMode"
          variant="outline"
          class="toggle-success"
          size="sm"
          @update:model-value="setChatMode"
        >
          <ToggleGroupItem
            value="chat"
            class="flex items-center space-x-1 data-[state=on]:bg-green-500 data-[state=on]:text-white"
          >
            <LucideIcon name="MessageSquare" class="w-3.5 h-3.5" />
            <span class="hidden sm:inline-block">聊天</span>
          </ToggleGroupItem>
          <ToggleGroupItem
            value="work"
            class="flex items-center space-x-1 data-[state=on]:bg-blue-500 data-[state=on]:text-white"
          >
            <LucideIcon name="Network" class="w-3.5 h-3.5" />
            <span class="hidden sm:inline-block">工作</span>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon, ServerSelect } from '@renderer/components'
import { computed } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  selectedServerId: {
    type: String,
    default: '',
  },
  selectedModel: {
    type: String,
    default: '',
  },
  chatMode: {
    type: String,
    default: 'chat',
  },
  runningServers: {
    type: Array,
    default: () => [],
  },
  modelGroups: {
    type: Object,
    default: () => ({}),
  },
  isLoadingModels: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'update:selectedServerId',
  'update:selectedModel',
  'update:chatMode',
  'server-change',
])

// 内部状态，用于双向绑定
const selectedServerId = computed({
  get: () => props.selectedServerId,
  set: (value) => emit('update:selectedServerId', value),
})

const selectedModel = computed({
  get: () => props.selectedModel,
  set: (value) => emit('update:selectedModel', value),
})

const selectedServerName = computed(() => {
  const server = props.runningServers.find((s) => s.serverId === props.selectedServerId)
  return server ? server.serverName : ''
})

// 处理服务器选择变化
const handleServerChange = (serverId: string) => {
  selectedServerId.value = serverId
  emit('server-change', serverId)

  // 如果有模型组，自动选择第一个模型
  if (props.modelGroups && Object.keys(props.modelGroups).length > 0) {
    const firstProvider = Object.keys(props.modelGroups)[0]
    const firstModelGroup = props.modelGroups[firstProvider]

    if (firstModelGroup && firstModelGroup.length > 0) {
      const firstModel = firstModelGroup[0]
      const modelKey = Object.keys(firstModel)[0]
      const modelValue = Object.values(firstModel)[0]

      // 设置选中的模型
      selectedModel.value = `${firstProvider}/${modelValue}`
      emit('update:selectedModel', selectedModel.value)

      window.logger.info(`自动选择模型: ${modelKey} (${selectedModel.value})`)
    }
  }

  toast.success('已选择服务器', {
    description: `当前服务器: ${selectedServerName.value}`,
  })
}

// 设置聊天模式
const setChatMode = (mode: string) => {
  emit('update:chatMode', mode)
  toast.success(`已切换到${mode === 'chat' ? '聊天' : '工作'}模式`)
}
</script>

<style lang="scss" scoped>
:deep(.slider-thumb) {
  @apply ring-offset-background;
}
:deep(.slider-range) {
  @apply bg-primary;
}
</style>
