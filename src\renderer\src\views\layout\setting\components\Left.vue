<template>
  <div class="setting-left p-2">
    <template v-for="(menu, index) in menuList" :key="menu.title">
      <div
        :class="
          cn(
            'menu-item flex flex-row p-2 rounded-lg mb-2 hover:g-accent d  cursor-pointer select-none',
            menu.isActive ? 'bg-accent' : 'bg-transparent',
          )
        "
        @click="handleClick(menu, index)"
      >
        <div>
          <Icon :icon="menu.icon" :width="24" :height="24" />
        </div>
        <div class="ml-2">{{ menu.title }}</div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { cn } from '@renderer/utils/utils'
import { useRoute } from 'vue-router'
import { useLanguage } from '@renderer/config/hooks'

const { t } = useLanguage()
const route = useRoute()
const emit = defineEmits(['update:component'])

const activeComponentName = ref('BaseSetting')
const menuList = computed(() => [
  {
    title: t('settings.baseSetting.title'),
    name: 'BaseSetting',
    icon: 'flowbite:cog-outline',
    isActive: activeComponentName.value === 'BaseSetting',
  },
  {
    title: t('settings.middlePlatformSetting.title'),
    name: 'MiddlePlatformSetting',
    icon: 'flowbite:terminal-outline',
    isActive: activeComponentName.value === 'MiddlePlatformSetting',
  },
  {
    title: t('settings.flowSetting.title'),
    name: 'FlowSetting',
    icon: 'flowbite:draw-square-outline',
    isActive: activeComponentName.value === 'FlowSetting',
  },
  {
    title: t('settings.about.title'),
    name: 'AboutSetting',
    icon: 'flowbite:exclamation-circle-outline',
    isActive: activeComponentName.value === 'AboutSetting',
  },
])

const handleClick = (menu, index) => {
  activeComponentName.value = menu.name
  emit('update:component', menu.name)
}

// 根据组件名称激活对应菜单项
const activateMenuByName = (componentName) => {
  activeComponentName.value = componentName
  emit('update:component', componentName)
}

// 监听路由查询参数变化
watch(
  () => route.query.component,
  (newComponent) => {
    if (newComponent) {
      activateMenuByName(newComponent)
    }
  },
  { immediate: true },
)

// 组件挂载时初始化
onMounted(() => {
  const componentFromQuery = route.query.component
  if (componentFromQuery) {
    // 如果 URL 中有指定组件，则激活该组件
    activateMenuByName(componentFromQuery)
  } else if (menuList.value.length > 0) {
    // 否则默认选中第一项
    const firstMenu = menuList.value[0]
    activateMenuByName(firstMenu.name)
  }
})
</script>

<style lang="scss" scoped></style>
