<template>
  <div class="space-y-4">
    <!-- 算法参数设置 -->
    <AlgorithmSettings
      :algorithm-params="algorithmParams"
      :is-processing="isProcessing"
      param-type="elec"
      @update:algorithm-params="updateAlgorithmParams"
    />

    <!-- 标定结果展示 -->
    <CalibrationResult
      param-type="elec"
      :task-id="taskId"
      :completed="completed"
      :server-info="serverInfo"
      :total-samples="totalSamples"
      :result-data="resultData"
      :task-status="taskStatus"
      :task-duration="taskDuration"
    />

    <div class="flex justify-between items-center my-4">
      <h2 class="text-xl font-semibold">电化学参数</h2>
      <div class="flex gap-2">
        <Button size="sm" variant="outline" :disabled="isProcessing" @click="useRecommendedParams">
          <Sparkles class="w-4 h-4 mr-2 text-blue-500 animate-pulse" />
          使用推荐参数
        </Button>
      </div>
    </div>

    <!-- 电化学参数列表 -->
    <div v-if="Object.keys(elecParams).length === 0" class="text-center py-8 text-gray-500">
      <EmptyState
        title="暂无电化学参数数据"
        description="请点击下方按钮获取参数数据"
        icon="flowbite:clipboard-list-outline"
        :icon-size="80"
      >
        <template #action>
          <Button size="sm" :disabled="isProcessing" @click="getElecParams">
            <Icon icon="tabler:refresh" class="w-4 h-4 mr-2" />
            获取电化学参数
          </Button>
        </template>
      </EmptyState>
    </div>
    <div v-for="(params, category, categoryIndex) in elecParams" :key="categoryIndex">
      <Collapsible
        :open="elecOpenStates[categoryIndex]"
        @update:open="
          (isOpen) => $emit('update:elecOpenStates', updateOpenState(categoryIndex, isOpen))
        "
      >
        <CollapsibleTrigger class="transition-colors w-full" :disabled="isProcessing">
          <div
            class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm border border-gray-200"
          >
            <div class="flex items-center">
              <span
                class="w-2 h-2 rounded-full mr-2"
                :class="{
                  'bg-blue-500': categoryIndex % 4 === 0,
                  'bg-green-500': categoryIndex % 4 === 1,
                  'bg-red-500': categoryIndex % 4 === 2,
                  'bg-yellow-500': categoryIndex % 4 === 3,
                }"
              ></span>
              <div
                class="text-xl font-semibold text-gray-900 hover:text-gray-700 transition-colors"
              >
                {{ category }}
                <Badge variant="secondary">({{ params.length }}) 个参数</Badge>
                <Badge class="ml-2 bg-red-400 hover:bg-red-300">
                  已选择 {{ params.filter((p) => p.selected).length }} 个
                </Badge>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div
                class="flex items-center cursor-pointer"
                @click.stop="toggleParams(category, params)"
              >
                <Checkbox
                  class="mr-2"
                  :model-value="params.every((param) => param.selected)"
                  :disabled="isProcessing"
                  @update:model-value="() => toggleParams('elecParams', category, params)"
                />
                全选
              </div>
            </div>
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent class="mt-2">
          <div class="p-2">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              <ParamCard
                v-for="(param, paramIndex) in params"
                :key="paramIndex"
                :param="param"
                param-type="elecParams"
                :category="category"
                :param-index="paramIndex"
                :is-processing="isProcessing"
                @update-selection="updateParamSelection"
                @update-param="updateParamValues"
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue'
import { EmptyState } from '@renderer/components'
import { Sparkles } from 'lucide-vue-next'
import { computed } from 'vue'
import AlgorithmSettings from './AlgorithmSettings.vue'
import CalibrationResult from './CalibrationResult.vue'
import ParamCard from './ParamCard.vue'
const props = defineProps({
  // 电化学参数对象，包含多个分类和每个分类下的参数列表
  elecParams: {
    type: Object,
    required: true,
  },
  // 电化学参数是否已完成标定
  elecParamsCompleted: {
    type: Boolean,
    default: false,
  },
  // 算法参数对象，包含最大迭代次数和最大允许误差等
  algorithmParams: {
    type: Object,
    required: true,
  },
  // 各分类的展开状态数组
  elecOpenStates: {
    type: Array,
    required: true,
  },
  // 是否正在处理任务
  isProcessing: {
    type: Boolean,
    default: false,
  },
  // 任务ID
  taskId: {
    type: String,
    default: '',
  },
  // 是否已完成
  completed: {
    type: Boolean,
    default: false,
  },
  // 服务器信息
  serverInfo: {
    type: Object,
    default: () => ({ server_id: '', service_name: '' }),
  },
  totalSamples: {
    type: Number,
    default: 0,
  },
  // 结果数据，从父组件传递
  resultData: {
    type: Object,
    default: null,
  },
  // 任务状态，从父组件传递
  taskStatus: {
    type: String,
    default: 'Initializing',
  },
  // 任务持续时间，从父组件传递
  taskDuration: {
    type: String,
    default: '--',
  },
})
const elecParams = computed(() => props.elecParams)
const emit = defineEmits([
  'toggle-params', // 切换参数全选/取消全选
  'update-param-selection', // 更新单个参数选择状态
  'use-recommended-params', // 使用推荐参数
  'update:algorithm-params', // 更新算法参数
  'update:elecOpenStates', // 更新展开状态
  'get-params', // 获取电化学参数
])

// 计算选中的参数数量
const selectedElecCount = computed(() => {
  return Object.values(props.elecParams).reduce((total, params) => {
    return total + params.filter((p) => p.selected).length
  }, 0)
})

// 更新参数值
const updateParamValues = ({ paramType, category, paramIndex, param }) => {
  // 更新参数值
  elecParams.value[category][paramIndex] = param
  console.log('elecParams更新参数值', elecParams.value)
  emit('update:elec-params', elecParams.value)
}
// 获取电化学参数
const getElecParams = () => {
  console.log('getElecParams')
  emit('get-params', 'Elec')
}
// 参数全选功能
const toggleParams = (paramType, category, params) => {
  if (!paramType || !category || !params || !Array.isArray(params)) {
    return
  }
  emit('toggle-params', paramType, category, params)
}

// 更新选中状态
const updateParamSelection = ({ paramType, category, paramIndex, value }) => {
  emit('update-param-selection', paramType, category, paramIndex, value)
}

// 使用推荐参数
const useRecommendedParams = () => {
  emit('use-recommended-params', 'elecParams')
}

// 更新算法参数
const updateAlgorithmParams = (newParams) => {
  emit('update:algorithm-params', newParams)
}
// 更新展开状态
const updateOpenState = (index, isOpen) => {
  const newOpenStates = [...props.elecOpenStates]
  newOpenStates[index] = isOpen
  return newOpenStates
}
</script>
