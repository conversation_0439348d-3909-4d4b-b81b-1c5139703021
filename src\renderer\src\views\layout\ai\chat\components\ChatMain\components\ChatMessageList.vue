<template>
  <div ref="chatMessagesContainerRef" class="flex-1 bg-muted/20 overflow-y-auto scrollbar min-h-0">
    <div class="p-6 space-y-6">
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['flex', message.role === 'user' ? 'justify-end' : 'justify-start']"
      >
        <div class="flex items-start space-x-3 max-w-[70vw] sm:max-w-[60vw] md:max-w-[50vw] group">
          <!-- AI 图标 -->
          <div v-if="message.role === 'assistant'" class="flex-shrink-0 pt-1">
            <Avatar class="w-8 h-8 bg-primary/10 text-primary border border-primary/20 shadow-sm">
              <AvatarFallback><LucideIcon name="Bot" class="w-4 h-4" /></AvatarFallback>
            </Avatar>
          </div>
          <!-- 消息内容 -->
          <div
            class="rounded-xl p-3.5 shadow-md transition-all duration-200 ease-in-out"
            :class="[
              message.role === 'user'
                ? 'bg-primary text-primary-foreground rounded-br-none'
                : 'bg-card text-card-foreground rounded-bl-none border border-border',
              { 'opacity-70': message.isLoading },
            ]"
          >
            <!-- 加载动画 -->
            <div v-if="message.isLoading" class="flex items-center space-x-2">
              <span>思考中</span>
              <div class="thinking-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <!-- 打字机效果内容 -->
            <MarkdownRenderer
              v-if="message.isTyping"
              :content="message.typewriterContent || ''"
              class="text-inherit"
            />
            <!-- 常规内容 -->
            <MarkdownRenderer v-else :content="message.content" class="text-inherit" />

            <div v-if="message.error" class="text-xs text-destructive mt-1">
              {{ message.error }}
            </div>
            <div class="mt-2 flex items-center justify-between">
              <span class="text-xs opacity-60">
                {{ new Date(message.timestamp).toLocaleTimeString() }}
              </span>
              <div
                v-if="!message.isLoading"
                class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <Button
                  variant="ghost"
                  size="icon-xs"
                  class="w-6 h-6 text-muted-foreground hover:text-foreground"
                  @click="copyMessage(message.content)"
                >
                  <LucideIcon name="Copy" class="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon-xs"
                  class="w-6 h-6 text-muted-foreground hover:text-foreground"
                >
                  <LucideIcon name="ThumbsUp" class="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon-xs"
                  class="w-6 h-6 text-muted-foreground hover:text-foreground"
                >
                  <LucideIcon name="ThumbsDown" class="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
          <!-- 用户图标 -->
          <div v-if="message.role === 'user'" class="flex-shrink-0 pt-1">
            <Avatar
              class="w-8 h-8 bg-secondary/20 text-secondary-foreground border border-border shadow-sm"
            >
              <AvatarFallback><LucideIcon name="User" class="w-4 h-4" /></AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LucideIcon, MarkdownRenderer } from '@renderer/components'
import { nextTick, onMounted, ref, watch } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  messages: {
    type: Array,
    required: true,
  },
})

const chatMessagesContainerRef = ref<HTMLElement | null>(null)

// 复制消息内容
const copyMessage = (content: string) => {
  navigator.clipboard
    .writeText(content)
    .then(() => {
      toast.success('复制成功')
    })
    .catch((err) => {
      console.error('Failed to copy message:', err)
    })
}

const scrollToBottom = async () => {
  await nextTick()
  const container = chatMessagesContainerRef.value
  if (container) {
    container.scrollTop = container.scrollHeight
  }
}

// 监听消息变化自动滚动到底部
watch(() => props.messages, scrollToBottom, { deep: true, immediate: true })

onMounted(() => {
  scrollToBottom()
})
</script>

<style lang="scss" scoped>
/* 思考加载动画 */
.thinking-dots {
  display: inline-flex;
  align-items: center;
}

.thinking-dots span {
  width: 5px;
  height: 5px;
  margin: 0 1px;
  background-color: currentColor;
  border-radius: 50%;
  display: inline-block;
  opacity: 0.7;
  animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) {
  animation-delay: 0s;
}

.thinking-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 处理长内容 */
:deep(.prose) {
  word-break: break-word;
  overflow-wrap: break-word;
}

:deep(.prose pre) {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
}

:deep(.prose table) {
  display: block;
  max-width: 100%;
  overflow-x: auto;
}

:deep(.prose img) {
  max-width: 100%;
  height: auto;
}

:deep(.prose code) {
  word-break: break-all;
  white-space: pre-wrap;
}
</style>
