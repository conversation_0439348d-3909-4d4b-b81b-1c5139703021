<template>
  <div class="w-full h-full">
    <div ref="rendererDom" class="w-full h-full"></div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted, Ref, ref, watch } from 'vue'
import AnalysisTool from './tools/analysis'
import CreateMesh from './tools/createMesh'
import CreateTwin from './tools/createTwin'
const rendererDom: Ref<HTMLDivElement | null> = ref(null)
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})
watch(
  () => props.data,
  (vals) => {
    if (vals) {
      draw()
    }
  },
)
let twin: any = null
const meshTool: any = new CreateMesh()
onMounted(() => {
  if (rendererDom.value) {
    twin = new CreateTwin(rendererDom.value)
  }
})
// 绘制出边界，原子，化学键
const draw = () => {
  twin.resetScene()
  const { points, lines, cylinders } = props.data
  if (points.length > 0) {
    // 原子
    drawPoints(points)
  }
  if (lines.length > 0) {
    // 边界
    drawLines(lines)
  }
  if (cylinders.length > 0) {
    // 原子之间的化学键
    drawBoundLines(cylinders)
  }
  // 渲染已经做了优化，所以不用担心性能问题，可以同时调用多次
  twin.render()
}
// 理论上都应该有原子，所以我们按照原子的集合去规定中心点
const drawPoints = (points: any) => {
  const meshList: any = []
  for (let i = 0; i < points.length; i++) {
    const point = points[i]
    const center = point.center.map((v: number) => tool.roundTo4(v))
    const color: any = tool.arrayToHex(point.color)
    const radius = point.radius
    const mesh = meshTool.createSphere(center[0], center[1], center[2], radius, color)
    meshList.push(mesh)
  }
  const meshGroup = meshTool.createGroup(meshList)
  meshTool.setCamera(meshGroup, twin.camera, twin.controls)
  twin.scene.add(meshGroup)
}
const tool = new AnalysisTool()
const drawLines = (lines: any) => {
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const start: any = line.start.map((v: number) => tool.roundTo4(v))
    const end: any = line.end.map((v: number) => tool.roundTo4(v))
    const color: any = tool.arrayToHex(line.color)
    if (meshTool) {
      const mesh = meshTool.createLine(
        { x: start[0], y: start[1], z: start[2] },
        { x: end[0], y: end[1], z: end[2] },
        color,
      )
      twin.scene.add(mesh)
    }
  }
}
const drawBoundLines = (data: any) => {
  for (let i = 0; i < data.length; i++) {
    const line = data[i]
    for (let j = 0; j < line.length; j++) {
      const boundLine = line[j]
      const start: any = boundLine.start.map((v: number) => tool.roundTo4(v))
      const end: any = boundLine.end.map((v: number) => tool.roundTo4(v))
      const color: any = '#cccccc'
      if (meshTool) {
        const mesh = meshTool.createCylinderLine(
          { x: start[1], y: start[2], z: start[3] },
          { x: end[1], y: end[2], z: end[3] },
          1 / 10,
          color,
        )
        twin.scene.add(mesh)
      }
    }
  }
}
onUnmounted(() => {
  if (twin) {
    twin.destroy()
  }
})
</script>
