<template>
  <Dialog :open="visiable" @update:open="closeSturcture">
    <DialogContent class="sm:max-w-[800px]">
      <DialogHeader>
        <DialogTitle>结构重排</DialogTitle>
      </DialogHeader>

      <div class="overflow-auto">
        <div class="flex justify-start items-center pt-1">
          <div class="w-[80px] text-base mb-2 font-bold">优化模式</div>
          <div class="w-full px-1 min-h-[44px]">
            <Select v-model="hostElement" :default-value="hostElement">
              <SelectTrigger>
                <SelectValue placeholder="请选择优化模式" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem v-for="(opt, i) in hostElementList" :key="i" :value="opt.value">
                    {{ opt.label }}
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeSturcture">取消</Button>
        <Button @click="confirmSturcture">
          <span>确定</span>
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
defineProps({
  visiable: Boolean,
})
// 定义emit
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'ok', data?: any): void
  (e: 'update:visiable', visiable: boolean): void
}>()

const hostElement = ref('BFGS')
const hostElementList: any = ref([
  {
    label: 'BFGS',
    value: 'BFGS',
  },
  {
    label: 'BFGSLineSearch',
    value: 'BFGSLineSearch',
  },
  {
    label: 'LBFGS',
    value: 'LBFGS',
  },
  {
    label: 'LBFGSLineSearch',
    value: 'LBFGSLineSearch',
  },
  {
    label: 'GPMin',
    value: 'GPMin',
  },
  {
    label: 'MDMin',
    value: 'MDMin',
  },
  {
    label: 'FIRE',
    value: 'FIRE',
  },
])

const closeSturcture = () => {
  emit('close')
  emit('update:visiable', false)
}
const confirmSturcture = async () => {
  emit('close')
  emit('ok', hostElement.value)
  emit('update:visiable', false)
}
onMounted(() => {})
</script>
